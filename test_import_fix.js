const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

const supabase = createClient(
  'https://pqgctrqxihbmanzrrjqd.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBxZ2N0cnF4aWhibWFuenJyanFkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM5ODUwNDUsImV4cCI6MjA2OTU2MTA0NX0.BmfShfx9w2PDwOLaaBZCCiqEqC2KGFJa0bIr8-E4Jck'
);

// Simulate the CSV parsing logic from CSVImport.jsx
function parsePnL(pnlStr) {
  if (!pnlStr) return 0;
  const cleaned = pnlStr.trim().replace('$', '');
  if (cleaned.startsWith('(') && cleaned.endsWith(')')) {
    return -parseFloat(cleaned.slice(1, -1));
  }
  return parseFloat(cleaned) || 0;
}

function parseTimestamp(timestampStr) {
  if (!timestampStr) return new Date().toISOString();
  
  try {
    const date = new Date(timestampStr);
    if (isNaN(date.getTime())) {
      throw new Error('Invalid date');
    }
    return date.toISOString();
  } catch (err) {
    console.error('Error parsing timestamp:', timestampStr, err);
    throw new Error(`Invalid timestamp format: ${timestampStr}`);
  }
}

// Simulate the transformLocalToSupabase logic
function transformLocalToSupabase(localTrade, userId) {
  const quantity = localTrade.quantity || localTrade.size || 1;
  const validQuantity = Math.floor(quantity);
  
  const exitPrice = localTrade.exit_price;
  const exitDate = localTrade.exitTimestamp;
  const entryDate = localTrade.timestamp;
  const entryPrice = localTrade.entry_price;
  const fees = localTrade.fees || localTrade.commission || 0;
  
  const hasValidExitPrice = exitPrice && !isNaN(parseFloat(exitPrice)) && parseFloat(exitPrice) > 0;
  const hasValidExitDate = exitDate && exitDate !== entryDate;
  const shouldBeClosed = hasValidExitPrice && hasValidExitDate;
  
  // Calculate P&L if trade is closed
  let calculatedPnL = null;
  if (shouldBeClosed && entryPrice && exitPrice) {
    const tradeType = (localTrade.side || localTrade.trade_type || 'LONG').toUpperCase();
    
    if (localTrade.profit_loss !== undefined && localTrade.profit_loss !== null) {
      calculatedPnL = parseFloat(localTrade.profit_loss);
    } else {
      if (tradeType === 'LONG') {
        calculatedPnL = (parseFloat(exitPrice) - parseFloat(entryPrice)) * validQuantity - fees;
      } else {
        calculatedPnL = (parseFloat(entryPrice) - parseFloat(exitPrice)) * validQuantity - fees;
      }
    }
    
    calculatedPnL = Math.round(calculatedPnL * 100) / 100;
  }
  
  return {
    user_id: userId,
    symbol: localTrade.instrument || localTrade.symbol,
    trade_type: (localTrade.side || localTrade.trade_type || 'LONG').toUpperCase(),
    entry_price: parseFloat(entryPrice),
    exit_price: shouldBeClosed ? parseFloat(exitPrice) : null,
    quantity: validQuantity,
    entry_date: entryDate,
    exit_date: shouldBeClosed ? exitDate : null,
    fees: fees,
    status: shouldBeClosed ? 'CLOSED' : 'OPEN',
    notes: localTrade.notes || '',
    strategy: localTrade.strategy || '',
    market_conditions: localTrade.market_conditions || '',
    pnl: calculatedPnL
  };
}

async function testImport() {
  try {
    console.log('🧪 Testing CSV import process...');
    
    // Read and parse test CSV
    const csvContent = fs.readFileSync('test_import.csv', 'utf8');
    const rows = csvContent.split('\n').map(row => row.split(','));
    const headers = rows[0];
    
    console.log('📄 CSV Headers:', headers);
    
    const trades = [];
    for (let i = 1; i < rows.length; i++) {
      const row = rows[i];
      if (row.length < headers.length || !row[0]) continue;
      
      const trade = {};
      headers.forEach((header, index) => {
        trade[header] = row[index];
      });
      
      // Transform to local format (similar to parsePerformanceCSV)
      const entryTimestamp = parseTimestamp(trade.boughtTimestamp);
      const exitTimestamp = parseTimestamp(trade.soldTimestamp);
      const quantity = parseInt(trade.qty) || 1;
      
      const localTrade = {
        timestamp: entryTimestamp,
        exitTimestamp: exitTimestamp,
        instrument: trade.symbol,
        entry_price: 100.0, // Mock entry price
        exit_price: 100.0 + (parsePnL(trade.pnl) / quantity), // Calculate exit price from P&L
        size: quantity,
        commission: 1.77,
        position_type: 'LONG',
        profit_loss: parsePnL(trade.pnl),
        duration: trade.duration,
        account: 'test'
      };
      
      trades.push(localTrade);
    }
    
    console.log('📊 Parsed trades:', trades.length);
    trades.forEach((trade, i) => {
      console.log(`Trade ${i + 1}:`, {
        symbol: trade.instrument,
        entry: trade.entry_price,
        exit: trade.exit_price,
        quantity: trade.size,
        pnl: trade.profit_loss
      });
    });
    
    // Transform to Supabase format
    const dummyUserId = '********-0000-0000-0000-********0001';
    const supabaseTrades = trades.map(trade => transformLocalToSupabase(trade, dummyUserId));
    
    console.log('🔄 Transformed for Supabase:');
    supabaseTrades.forEach((trade, i) => {
      console.log(`Supabase Trade ${i + 1}:`, {
        symbol: trade.symbol,
        type: trade.trade_type,
        entry_price: trade.entry_price,
        exit_price: trade.exit_price,
        quantity: trade.quantity,
        pnl: trade.pnl,
        status: trade.status
      });
    });
    
    console.log('✅ Import transformation test completed successfully!');
    console.log('📈 Total P&L from all trades:', supabaseTrades.reduce((sum, t) => sum + (t.pnl || 0), 0));
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testImport();
