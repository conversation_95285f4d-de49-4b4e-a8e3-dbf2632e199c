<div id="filterModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center">
  <div class="bg-white rounded-xl p-6 w-full max-w-md">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-semibold">Filter Trades</h3>
      <button class="text-slate-400 hover:text-slate-600" onclick="closeFilterModal()">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
    
    <div class="space-y-4">
      <div>
        <label class="block text-sm font-medium text-slate-700 mb-1">Date Range</label>
        <div class="grid grid-cols-2 gap-4">
          <input type="date" class="form-input" id="dateFrom">
          <input type="date" class="form-input" id="dateTo">
        </div>
      </div>
      
      <div>
        <label class="block text-sm font-medium text-slate-700 mb-1">Instrument</label>
        <select class="form-select w-full" id="instrumentFilter">
          <option value="">All Instruments</option>
        </select>
      </div>
      
      <div>
        <label class="block text-sm font-medium text-slate-700 mb-1">Trade Type</label>
        <div class="space-x-4">
          <label class="inline-flex items-center">
            <input type="checkbox" class="form-checkbox" value="long">
            <span class="ml-2">Long</span>
          </label>
          <label class="inline-flex items-center">
            <input type="checkbox" class="form-checkbox" value="short">
            <span class="ml-2">Short</span>
          </label>
        </div>
      </div>
    </div>
    
    <div class="mt-6 flex justify-end space-x-3">
      <button class="px-4 py-2 text-sm font-medium text-slate-600 hover:text-slate-800" onclick="resetFilters()">
        Reset
      </button>
      <button class="px-4 py-2 text-sm font-medium bg-blue-500 text-white rounded-lg hover:bg-blue-600" onclick="applyFilters()">
        Apply Filters
      </button>
    </div>
  </div>
</div> 