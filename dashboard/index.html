<!DOCTYPE html>
<html>
<head>
  <title>Trade Analytics Dashboard</title>
  <link href="../dist/output.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-slate-50 font-inter">
  <!-- Top Navigation -->
  <nav class="bg-white border-b border-slate-200">
    <div class="container mx-auto px-4 py-3">
      <div class="flex justify-between items-center">
        <h1 class="text-xl font-semibold text-slate-800">Trade Analytics</h1>
        <div class="flex items-center space-x-4">
          <label class="relative cursor-pointer bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
            <span>Import CSV</span>
            <input type="file" class="hidden" id="csvInput" accept=".csv">
          </label>
          <div class="text-sm text-slate-600" id="lastUpdate"></div>
        </div>
      </div>
    </div>
  </nav>

  <div class="container mx-auto px-4 py-6">
    <!-- Stats Overview -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <div class="stats-card">
        <div class="flex items-center justify-between">
          <div>
            <div class="stat-label">Total P&L</div>
            <div class="stat-value text-emerald-600" id="total-pnl">$0.00</div>
          </div>
          <div class="stat-icon bg-emerald-100">
            <svg class="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
        </div>
        <div class="mt-2 flex items-center text-sm">
          <span class="text-emerald-500" id="pnl-change">+0.00%</span>
          <span class="text-slate-500 ml-2">vs last month</span>
        </div>
      </div>

      <!-- Additional stat cards with similar structure -->
      <div class="stats-card">
        <div class="flex items-center justify-between">
          <div>
            <div class="stat-label">Win Rate</div>
            <div class="stat-value text-blue-600" id="win-rate">0%</div>
          </div>
          <div class="stat-icon bg-blue-100">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
            </svg>
          </div>
        </div>
      </div>
      <!-- Add more stat cards -->
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <div class="dashboard-card">
        <div class="flex items-center justify-between mb-4">
          <h3 class="card-title">P&L Performance</h3>
          <div class="flex space-x-2">
            <button class="chart-period-btn active" data-period="1M">1M</button>
            <button class="chart-period-btn" data-period="3M">3M</button>
            <button class="chart-period-btn" data-period="1Y">1Y</button>
          </div>
        </div>
        <canvas id="pnlChart" class="h-[300px]"></canvas>
      </div>

      <div class="dashboard-card">
        <h3 class="card-title">Trade Distribution</h3>
        <canvas id="distributionChart" class="h-[300px]"></canvas>
      </div>
    </div>

    <!-- Trades Table -->
    <div class="dashboard-card">
      <div class="flex justify-between items-center mb-4">
        <h3 class="card-title">Recent Trades</h3>
        <div class="flex space-x-2">
          <input type="text" placeholder="Search trades..." class="search-input">
          <button class="filter-btn">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
            </svg>
          </button>
        </div>
      </div>
      
      <div class="overflow-x-auto">
        <table class="trade-table">
          <thead>
            <tr>
              <th class="trade-table-header">Date</th>
              <th class="trade-table-header">Instrument</th>
              <th class="trade-table-header">Type</th>
              <th class="trade-table-header">Entry</th>
              <th class="trade-table-header">Exit</th>
              <th class="trade-table-header">P&L</th>
              <th class="trade-table-header">Actions</th>
            </tr>
          </thead>
          <tbody id="trades-table-body"></tbody>
        </table>
      </div>
    </div>
  </div>

  <script src="../dist/dashboard.bundle.js"></script>
</body>
</html> 