'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Create Users table
    await queryInterface.createTable('Users', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false
      },
      email: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      title: {
        type: Sequelize.STRING,
        defaultValue: 'Trader'
      },
      bio: {
        type: Sequelize.TEXT
      },
      tradingFirm: {
        type: Sequelize.STRING
      },
      tradingSince: {
        type: Sequelize.DATE
      },
      tradingRegion: {
        type: Sequelize.STRING
      },
      tradingStyle: {
        type: Sequelize.JSONB,
        defaultValue: {
          preferredMarkets: [],
          style: null,
          riskManagement: [],
          tradingHours: null
        }
      },
      settings: {
        type: Sequelize.JSONB,
        defaultValue: {
          timeZone: 'America/New_York',
          notifications: {
            email: true,
            desktop: true,
            tradeAlerts: true
          },
          privacy: {
            profileVisibility: 'private',
            showTradingStats: false
          }
        }
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Create TradingAccounts table
    await queryInterface.createTable('TradingAccounts', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false
      },
      initialCapital: {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: false
      },
      type: {
        type: Sequelize.ENUM('live', 'demo', 'paper'),
        allowNull: false
      },
      broker: {
        type: Sequelize.STRING
      },
      isDefault: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      status: {
        type: Sequelize.ENUM('active', 'inactive', 'closed'),
        defaultValue: 'active'
      },
      metadata: {
        type: Sequelize.JSONB,
        defaultValue: {
          platform: null,
          accountNumber: null,
          currency: 'USD'
        }
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Add indexes
    await queryInterface.addIndex('TradingAccounts', ['userId', 'name'], {
      unique: true,
      name: 'trading_accounts_user_id_name_unique'
    });

    await queryInterface.addIndex('TradingAccounts', ['userId', 'isDefault'], {
      name: 'trading_accounts_user_id_is_default'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('TradingAccounts');
    await queryInterface.dropTable('Users');
  }
};
