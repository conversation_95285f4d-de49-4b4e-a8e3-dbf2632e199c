// Test script to verify CSV import quantity validation fixes
// This can be run in the browser console or as a Node.js script

// Mock CSV data with various validation scenarios
const testCSVData = `symbol,_priceFormat,_priceFormatType,_tickSize,buyFillId,sellFillId,qty,pnl,boughtTimestamp,soldTimestamp,duration,buyPrice,sellPrice
AAPL,2,0,0.01,123,124,100,50.00,2024-01-01T10:00:00Z,2024-01-01T11:00:00Z,1h,150.00,150.50
MSFT,2,0,0.01,125,126,,25.00,2024-01-01T10:00:00Z,2024-01-01T11:00:00Z,1h,300.00,300.25
GOOGL,2,0,0.01,127,128,0,0.00,2024-01-01T10:00:00Z,2024-01-01T11:00:00Z,1h,2500.00,2500.00
TSLA,2,0,0.01,129,130,-50,100.00,2024-01-01T10:00:00Z,2024-01-01T11:00:00Z,1h,200.00,202.00
NVDA,2,0,0.01,131,132,abc,75.00,2024-01-01T10:00:00Z,2024-01-01T11:00:00Z,1h,400.00,401.88
META,2,0,0.01,133,134,50.5,30.00,2024-01-01T10:00:00Z,2024-01-01T11:00:00Z,1h,250.00,250.60
,2,0,0.01,135,136,25,15.00,2024-01-01T10:00:00Z,2024-01-01T11:00:00Z,1h,100.00,100.60
AMZN,2,0,0.01,137,138,75,-10.00,2024-01-01T10:00:00Z,2024-01-01T11:00:00Z,1h,,3200.00
NFLX,2,0,0.01,139,140,50,20.00,2024-01-01T10:00:00Z,2024-01-01T11:00:00Z,1h,450.00,`;

// Test the parseInt validation logic
function testQuantityValidation() {
  console.log('Testing quantity validation...\n');
  
  const testCases = [
    { qty: '100', expected: 100, description: 'Valid integer string' },
    { qty: '', expected: 'skip', description: 'Empty string' },
    { qty: '0', expected: 'skip', description: 'Zero quantity' },
    { qty: '-50', expected: 'skip', description: 'Negative quantity' },
    { qty: 'abc', expected: 'skip', description: 'Non-numeric string' },
    { qty: '50.5', expected: 50, description: 'Decimal number (should floor)' },
    { qty: null, expected: 'skip', description: 'Null value' },
    { qty: undefined, expected: 'skip', description: 'Undefined value' }
  ];
  
  testCases.forEach((testCase, index) => {
    const quantity = parseInt(testCase.qty);
    const isValid = !isNaN(quantity) && quantity > 0;
    const result = isValid ? quantity : 'skip';
    
    console.log(`Test ${index + 1}: ${testCase.description}`);
    console.log(`  Input: "${testCase.qty}"`);
    console.log(`  parseInt result: ${quantity}`);
    console.log(`  Valid: ${isValid}`);
    console.log(`  Final result: ${result}`);
    console.log(`  Expected: ${testCase.expected}`);
    console.log(`  ✅ ${result === testCase.expected ? 'PASS' : 'FAIL'}\n`);
  });
}

// Test the store validation logic
function testStoreValidation() {
  console.log('Testing store validation logic...\n');
  
  const testTrades = [
    { size: 100, description: 'Valid size' },
    { size: NaN, description: 'NaN size' },
    { size: 0, description: 'Zero size' },
    { size: -10, description: 'Negative size' },
    { size: 50.7, description: 'Decimal size' },
    { quantity: 75, description: 'Valid quantity (no size)' },
    { size: null, quantity: 25, description: 'Null size, valid quantity' },
    { size: undefined, quantity: undefined, description: 'Both undefined' }
  ];
  
  testTrades.forEach((trade, index) => {
    // Simulate the store validation logic
    const quantity = trade.size || trade.quantity;
    const validQuantity = (typeof quantity === 'number' && !isNaN(quantity) && quantity > 0) 
      ? Math.floor(quantity) 
      : 1; // Default to 1 if invalid
    
    console.log(`Test ${index + 1}: ${trade.description}`);
    console.log(`  Input size: ${trade.size}`);
    console.log(`  Input quantity: ${trade.quantity}`);
    console.log(`  Raw quantity: ${quantity}`);
    console.log(`  Valid quantity: ${validQuantity}`);
    console.log(`  ${validQuantity !== quantity ? '⚠️  Corrected' : '✅ Valid'}\n`);
  });
}

// Test comprehensive CSV validation
function testCSVValidation() {
  console.log('Testing comprehensive CSV validation...\n');

  const rows = testCSVData.split('\n');
  const headers = rows[0].split(',');
  const dataRows = rows.slice(1);

  console.log('Headers:', headers);
  console.log(`Total data rows: ${dataRows.length}\n`);

  const validTrades = [];
  const skippedRows = [];

  dataRows.forEach((rowStr, index) => {
    const row = rowStr.split(',');
    const trade = {};
    headers.forEach((header, j) => {
      trade[header] = row[j] ? row[j].trim() : '';
    });

    console.log(`Row ${index + 1}:`, trade);

    // Apply our validation logic
    let shouldSkip = false;
    let skipReason = '';

    // Validate symbol
    if (!trade.symbol || trade.symbol.trim() === '') {
      shouldSkip = true;
      skipReason = 'Missing or empty symbol';
    }

    // Validate quantity
    if (!shouldSkip) {
      const quantity = parseInt(trade.qty);
      if (isNaN(quantity) || quantity <= 0) {
        shouldSkip = true;
        skipReason = `Invalid quantity "${trade.qty}"`;
      }
    }

    // Validate entry price
    if (!shouldSkip) {
      const entryPrice = parseFloat(trade.buyPrice);
      if (isNaN(entryPrice) || entryPrice <= 0) {
        shouldSkip = true;
        skipReason = `Invalid entry price "${trade.buyPrice}"`;
      }
    }

    // Validate exit price
    if (!shouldSkip) {
      const exitPrice = parseFloat(trade.sellPrice);
      if (isNaN(exitPrice) || exitPrice <= 0) {
        shouldSkip = true;
        skipReason = `Invalid exit price "${trade.sellPrice}"`;
      }
    }

    if (shouldSkip) {
      console.log(`  ❌ SKIP: ${skipReason}`);
      skippedRows.push(`Row ${index + 1}: ${skipReason}`);
    } else {
      console.log(`  ✅ VALID`);
      validTrades.push(trade);
    }
    console.log('');
  });

  console.log(`Summary:`);
  console.log(`  Valid trades: ${validTrades.length}`);
  console.log(`  Skipped rows: ${skippedRows.length}`);
  console.log(`  Skipped reasons:`, skippedRows);
}

// Run tests
if (typeof window !== 'undefined') {
  // Browser environment
  console.log('=== CSV Import Validation Tests ===\n');
  testQuantityValidation();
  testStoreValidation();
  testCSVValidation();
} else {
  // Node.js environment - run tests immediately
  console.log('=== CSV Import Validation Tests ===\n');
  testQuantityValidation();
  testStoreValidation();
  testCSVValidation();

  module.exports = { testQuantityValidation, testStoreValidation, testCSVValidation };
}
