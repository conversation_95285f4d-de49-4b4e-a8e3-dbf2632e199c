import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/auth_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/notification_provider.dart';
import 'package:trade_reviewer_flutter/core/config/router.dart';
import 'package:trade_reviewer_flutter/core/theme/app_theme.dart';

void main() async {
  try {
    // Ensure Flutter bindings are initialized
    WidgetsFlutterBinding.ensureInitialized();

    // Initialize SharedPreferences
    print('Initializing SharedPreferences...');
    final prefs = await SharedPreferences.getInstance();
    print('SharedPreferences initialized successfully');

    // Initialize notification delivery service
    print('Initializing notification delivery service...');
    final container = ProviderContainer(
      overrides: [
        sharedPreferencesProvider.overrideWithValue(prefs),
      ],
    );
    await container.read(notificationDeliveryServiceProvider).initialize();
    print('Notification delivery service initialized successfully');

    // Run the app with SharedPreferences provider
    runApp(
      ProviderScope(
        parent: container,
        child: const MyApp(),
      ),
    );
  } catch (e, stackTrace) {
    print('Error initializing app: $e');
    print('Stack trace: $stackTrace');
    rethrow;
  }
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(routerProvider);
    final authState = ref.watch(authStateProvider);

    return MaterialApp.router(
      title: 'TradeREVIEWER',
      theme: AppTheme.darkTheme,
      routerConfig: router,
      builder: (context, child) {
        if (authState.isLoading) {
          return MaterialApp(
            home: Scaffold(
              body: Center(
                child: CircularProgressIndicator(),
              ),
            ),
          );
        }

        if (authState.error != null) {
          return MaterialApp(
            home: Scaffold(
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text('Error: ${authState.error}'),
                    SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        ref.refresh(authStateProvider);
                      },
                      child: Text('Retry'),
                    ),
                  ],
                ),
              ),
            ),
          );
        }

        return child!;
      },
    );
  }
}
