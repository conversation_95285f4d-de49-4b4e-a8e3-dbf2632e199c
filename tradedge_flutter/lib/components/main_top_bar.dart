import 'package:flutter/material.dart';

class MainTopBar extends StatefulWidget {
  final String currentView;
  final Function(String) onViewChange;

  const MainTopBar({
    super.key,
    required this.currentView,
    required this.onViewChange,
  });

  @override
  State<MainTopBar> createState() => _MainTopBarState();
}

class _MainTopBarState extends State<MainTopBar> {
  bool _isMenuOpen = false;

  final List<Map<String, String>> navigationItems = [
    {'id': 'overview', 'label': 'Overview', 'icon': 'analytics'},
    {'id': 'analysis', 'label': 'Analysis', 'icon': 'insert_chart'},
    {'id': 'trades', 'label': 'Trades', 'icon': 'show_chart'},
  ];

  void _handleViewChange(String view) {
    widget.onViewChange(view);
    setState(() {
      _isMenuOpen = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF0F172A),
        border: Border(
          bottom: BorderSide(
            color: const Color(0xFF1E293B),
            width: 1,
          ),
        ),
      ),
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top,
      ),
      child: Container(
        height: 64,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Logo Section
            GestureDetector(
              onTap: () => _handleViewChange('overview'),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [
                          Color(0xFF3B82F6),
                          Color(0xFF60A5FA),
                          Color(0xFF93C5FD),
                        ],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: const Text(
                      'TRADEDGE',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.w900,
                        color: Colors.white,
                        letterSpacing: -0.5,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFF3B82F6).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(
                            color: const Color(0xFF3B82F6).withOpacity(0.2),
                          ),
                        ),
                        child: const Text(
                          'BETA',
                          style: TextStyle(
                            color: Color(0xFF60A5FA),
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      const SizedBox(width: 6),
                      const Text(
                        'v1.0.0',
                        style: TextStyle(
                          color: Color(0xFF94A3B8),
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Menu Button
            GestureDetector(
              onTap: () => _showMenu(),
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF94A3B8).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _isMenuOpen ? Icons.close : Icons.menu,
                  color: const Color(0xFF94A3B8),
                  size: 24,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        color: const Color(0xFF0F172A),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 12),
              child: Column(
                children: [
                  ...navigationItems.map((item) => _buildMenuItem(
                        id: item['id']!,
                        label: item['label']!,
                        icon: item['icon']!,
                      )),
                  Container(
                    margin: const EdgeInsets.only(top: 8),
                    decoration: const BoxDecoration(
                      border: Border(
                        top: BorderSide(
                          color: Color(0xFF1E293B),
                          width: 1,
                        ),
                      ),
                    ),
                    child: Column(
                      children: [
                        _buildMenuItem(
                          id: 'profile',
                          label: 'Profile',
                          icon: 'person',
                        ),
                        _buildMenuItem(
                          id: 'settings',
                          label: 'Settings',
                          icon: 'settings',
                        ),
                        _buildMenuItem(
                          id: 'logout',
                          label: 'Log out',
                          icon: 'logout',
                          isLogout: true,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
    setState(() {
      _isMenuOpen = true;
    });
  }

  Widget _buildMenuItem({
    required String id,
    required String label,
    required String icon,
    bool isLogout = false,
  }) {
    final isActive = widget.currentView == id;
    final textColor = isLogout
        ? const Color(0xFFF87171)
        : isActive
            ? const Color(0xFF60A5FA)
            : const Color(0xFF94A3B8);

    return GestureDetector(
      onTap: () => _handleViewChange(id),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 12,
        ),
        margin: const EdgeInsets.symmetric(horizontal: 12),
        decoration: BoxDecoration(
          color: isActive ? const Color(0xFF3B82F6).withOpacity(0.1) : null,
          borderRadius: BorderRadius.circular(8),
          border: isActive
              ? Border.all(
                  color: const Color(0xFF3B82F6).withOpacity(0.2),
                )
              : null,
        ),
        child: Row(
          children: [
            Icon(
              _getIconData(icon),
              size: 20,
              color: textColor,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: textColor,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getIconData(String icon) {
    switch (icon) {
      case 'analytics':
        return Icons.analytics;
      case 'insert_chart':
        return Icons.insert_chart;
      case 'show_chart':
        return Icons.show_chart;
      case 'person':
        return Icons.person;
      case 'settings':
        return Icons.settings;
      case 'logout':
        return Icons.logout;
      default:
        return Icons.error;
    }
  }
}
