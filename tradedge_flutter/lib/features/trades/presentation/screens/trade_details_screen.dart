import 'package:flutter/material.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:intl/intl.dart';
import 'package:trade_reviewer_flutter/core/models/trade.dart';
import 'package:trade_reviewer_flutter/features/trades/presentation/screens/add_trade_screen.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';

class TradeDetailsScreen extends ConsumerWidget {
  final Trade trade;

  const TradeDetailsScreen({
    super.key,
    required this.trade,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final isOpen = trade.status == TradeStatus.OPEN;
    final profit = trade.calculatePnL();
    final isProfit = !isOpen && profit > 0;

    return Scaffold(
      appBar: AppBar(
        title: Text(trade.symbol),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () async {
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => AddTradeScreen(tradeToEdit: trade),
                ),
              );
              if (result == true) {
                Navigator.pop(
                    context, true); // Pop back to trades list with refresh flag
              }
            },
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () async {
              final confirmed = await showDialog<bool>(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Delete Trade'),
                  content:
                      const Text('Are you sure you want to delete this trade?'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context, false),
                      child: const Text('Cancel'),
                    ),
                    TextButton(
                      onPressed: () => Navigator.pop(context, true),
                      child: const Text(
                        'Delete',
                        style: TextStyle(color: Colors.red),
                      ),
                    ),
                  ],
                ),
              );

              if (confirmed == true && context.mounted) {
                try {
                  final tradeService = ref.read(tradeServiceProvider);
                  final settingsService = ref.read(settingsServiceProvider);
                  final settings = await settingsService.getSettings();
                  await tradeService.deleteTrade(settings.userId, trade.id!);

                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('Trade deleted successfully')),
                    );
                    Navigator.pop(context,
                        true); // Pop back to trades list with refresh flag
                  }
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Error deleting trade: $e')),
                    );
                  }
                }
              }
            },
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Trade Status Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Trade Status',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Status',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: Colors.grey,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            trade.status.toString().split('.').last,
                            style: theme.textTheme.bodyLarge?.copyWith(
                              color: isOpen ? Colors.blue : Colors.grey,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            'Type',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: Colors.grey,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            trade.type.toString().split('.').last,
                            style: theme.textTheme.bodyLarge?.copyWith(
                              color: trade.type == TradeType.LONG
                                  ? Colors.green
                                  : Colors.red,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Trade Details Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Trade Details',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildDetailRow(
                    'Quantity',
                    trade.quantity.toString(),
                    theme,
                  ),
                  _buildDetailRow(
                    'Entry Price',
                    '\$${NumberFormat("#,##0.00", "en_US").format(trade.entryPrice)}',
                    theme,
                  ),
                  if (!isOpen && trade.exitPrice != null)
                    _buildDetailRow(
                      'Exit Price',
                      '\$${NumberFormat("#,##0.00", "en_US").format(trade.exitPrice!)}',
                      theme,
                    ),
                  if (trade.stopLoss != null)
                    _buildDetailRow(
                      'Stop Loss',
                      '\$${trade.stopLoss!.toStringAsFixed(2)}',
                      theme,
                    ),
                  if (trade.takeProfit != null)
                    _buildDetailRow(
                      'Take Profit',
                      '\$${trade.takeProfit!.toStringAsFixed(2)}',
                      theme,
                    ),
                  _buildDetailRow(
                    'Entry Date',
                    DateFormat('MMM d, yyyy h:mm a').format(trade.entryDate),
                    theme,
                  ),
                  if (!isOpen && trade.exitDate != null)
                    _buildDetailRow(
                      'Exit Date',
                      DateFormat('MMM d, yyyy h:mm a').format(trade.exitDate!),
                      theme,
                    ),
                  if (!isOpen)
                    _buildDetailRow(
                      'P&L',
                      isProfit
                          ? '+\$${profit.toStringAsFixed(2)}'
                          : '-\$${profit.abs().toStringAsFixed(2)}',
                      theme,
                      valueColor: isProfit ? Colors.green : Colors.red,
                    ),
                  if (trade.calculateRRR() != null)
                    _buildDetailRow(
                      'Risk/Reward',
                      '${trade.calculateRRR()!.toStringAsFixed(2)}',
                      theme,
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Notes Card
          if (trade.notes != null && trade.notes!.isNotEmpty)
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Notes',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      trade.notes!,
                      style: theme.textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    String label,
    String value,
    ThemeData theme, {
    Color? valueColor,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: valueColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
