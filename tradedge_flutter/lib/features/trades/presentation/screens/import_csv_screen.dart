import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:csv/csv.dart';
import 'package:file_picker/file_picker.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_flutter/core/models/trade.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart' as core;
import 'package:intl/intl.dart';
import 'package:uuid/uuid.dart';
import 'package:trade_reviewer_flutter/core/providers/trade_data_provider.dart';
import 'package:go_router/go_router.dart';

class ImportCsvScreen extends ConsumerStatefulWidget {
  const ImportCsvScreen({super.key});

  @override
  ConsumerState<ImportCsvScreen> createState() => _ImportCsvScreenState();
}

class _ImportCsvScreenState extends ConsumerState<ImportCsvScreen> {
  File? _selectedFile;
  List<List<dynamic>>? _csvData;
  bool _isLoading = false;
  String? _selectedAccount;
  List<core.TradingAccount> _accounts = [];
  late final _dateFormat = DateFormat("MM/dd/yyyy HH:mm:ss");
  Map<String, int> _columnMapping = {};

  final Map<String, List<String>> _fieldMappings = {
    'symbol': ['symbol'],
    'quantity': ['qty'],
    'buyPrice': ['buyPrice'],
    'sellPrice': ['sellPrice'],
    'pnl': ['pnl'],
    'buyFillId': ['buyFillId'],
    'sellFillId': ['sellFillId'],
    'entryDate': ['boughtTimestamp'],
    'exitDate': ['soldTimestamp'],
  };

  @override
  void initState() {
    super.initState();
    _loadAccounts();
  }

  Future<void> _loadAccounts() async {
    try {
      final settingsService = ref.read(settingsServiceProvider);
      final settings = await settingsService.getSettings();
      setState(() {
        _accounts = settings.accounts.tradingAccounts;
        if (_accounts.isNotEmpty) {
          _selectedAccount = _accounts.first.id;
        }
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Error loading accounts')),
        );
      }
    }
  }

  void _autoMapColumns(List<String> headers) {
    Map<String, int> mapping = {};

    print('\nCSV Headers:');
    headers.asMap().forEach((index, header) {
      print('$index: $header');
    });

    // First try exact matches
    _fieldMappings.forEach((field, possibleNames) {
      for (var name in possibleNames) {
        final index =
            headers.indexWhere((h) => h.toLowerCase() == name.toLowerCase());
        if (index != -1) {
          mapping[field] = index;
          break;
        }
      }
    });

    // Then try contains matches for unmapped fields
    _fieldMappings.forEach((field, possibleNames) {
      if (!mapping.containsKey(field)) {
        for (var name in possibleNames) {
          final index = headers
              .indexWhere((h) => h.toLowerCase().contains(name.toLowerCase()));
          if (index != -1) {
            mapping[field] = index;
            break;
          }
        }
      }
    });

    // Add manual mapping for specific columns in Performance (1).csv
    if (!mapping.containsKey('pnl') && headers.contains('pnl')) {
      mapping['pnl'] = headers.indexOf('pnl');
    }

    setState(() {
      _columnMapping = mapping;
    });

    print('\nFinal Column Mapping:');
    mapping.forEach((field, index) {
      print('$field -> Column $index (${headers[index]})');
    });
  }

  Future<void> _pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv'],
      );

      if (result != null) {
        final file = File(result.files.single.path!);
        final contents = await file.readAsString();
        final csvData = const CsvToListConverter().convert(contents, eol: "\n");

        if (csvData.isNotEmpty) {
          _autoMapColumns(csvData[0].map((e) => e.toString()).toList());
        }

        setState(() {
          _selectedFile = file;
          _csvData = csvData;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error reading file: $e')),
        );
      }
    }
  }

  Future<void> _importTrades() async {
    if (_csvData == null || _selectedAccount == null) return;

    try {
      setState(() => _isLoading = true);

      final tradeService = ref.read(tradeServiceProvider);
      final settingsService = ref.read(settingsServiceProvider);
      final settings = await settingsService.getSettings();
      final userId = settings.userId;

      print('\n=== Starting import process ===');
      print('CSV Data rows: ${_csvData!.length}');
      print('Selected Account: $_selectedAccount');
      print('User ID: $userId');
      print('Column Mapping: $_columnMapping');

      // Print all CSV rows
      print('\nAll CSV rows:');
      for (var i = 0; i < _csvData!.length; i++) {
        print('Row $i: ${_csvData![i]}');
      }

      int successCount = 0;
      int errorCount = 0;

      // Skip header row
      for (var row in _csvData!.skip(1)) {
        if (!mounted) return;

        try {
          print('\n--- Processing row ---');
          print('Raw row data: $row');

          // Parse prices and quantity
          double buyPrice =
              double.parse(row[_columnMapping['buyPrice'] ?? 7].toString());
          double sellPrice =
              double.parse(row[_columnMapping['sellPrice'] ?? 8].toString());
          double quantity =
              double.parse(row[_columnMapping['quantity'] ?? 6].toString());

          print('Parsed values:');
          print('Buy Price: $buyPrice');
          print('Sell Price: $sellPrice');
          print('Quantity: $quantity');

          // Parse PnL if available
          double? pnl;
          if (_columnMapping.containsKey('pnl')) {
            String pnlStr = row[_columnMapping['pnl']!].toString();
            print('Raw PnL string: $pnlStr');
            // Remove currency symbol and handle parentheses for negative values
            pnlStr = pnlStr.replaceAll(RegExp(r'[\$,]'), '');
            if (pnlStr.startsWith('(') && pnlStr.endsWith(')')) {
              pnlStr = '-' + pnlStr.substring(1, pnlStr.length - 1);
            }
            print('Cleaned PnL string: $pnlStr');
            if (pnlStr.isNotEmpty) {
              pnl = double.tryParse(pnlStr);
              print('Parsed PnL: $pnl');
            }
          }

          // Determine if it's a long or short trade based on entry/exit prices
          bool isLong = pnl != null ? pnl > 0 : buyPrice < sellPrice;
          print('Trade type: ${isLong ? "LONG" : "SHORT"}');

          final entryDate = _dateFormat
              .parse(row[_columnMapping['entryDate'] ?? 10].toString());
          final exitDate = _dateFormat
              .parse(row[_columnMapping['exitDate'] ?? 11].toString());

          print('Entry Date: $entryDate');
          print('Exit Date: $exitDate');

          final trade = {
            'id': const Uuid().v4(),
            'symbol': row[_columnMapping['symbol'] ?? 0].toString(),
            'type': isLong ? 'LONG' : 'SHORT',
            'status': 'CLOSED',
            'entryPrice': isLong ? buyPrice : sellPrice,
            'exitPrice': isLong ? sellPrice : buyPrice,
            'quantity': quantity,
            'fees': 0.0,
            'commission': 0.0,
            'entryDate': entryDate.toIso8601String(),
            'exitDate': exitDate.toIso8601String(),
            'accountId': _selectedAccount,
            'userId': userId,
            'tags': ['Imported'],
            'notes': pnl != null
                ? 'Imported from CSV (PnL: \$${pnl.toStringAsFixed(2)})'
                : 'Imported from CSV',
            'predefinedPnL': pnl,
            'updatedAt': DateTime.now().toIso8601String(),
          };

          print('\nCreating trade:');
          print(trade);

          await tradeService.createTrade(userId, trade);
          print('Trade created successfully');
          successCount++;
        } catch (e, stack) {
          print('Error importing row: $e');
          print('Stack trace: $stack');
          errorCount++;
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error importing trade: $e')),
          );
        }
      }

      print('\n=== Import completed ===');
      print('Successfully imported: $successCount trades');
      print('Failed to import: $errorCount trades');

      // Notify that data has changed
      ref.read(tradeDataProvider.notifier).notifyDataChanged();
      ref.refresh(tradeServiceProvider);

      if (!mounted) return;

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 16),
              Text('Successfully imported ${successCount} trades'),
            ],
          ),
          backgroundColor: Colors.green,
        ),
      );

      // Use GoRouter to navigate back
      if (context.mounted) {
        context.pop(true);
      }
    } catch (e) {
      print('\nError importing trades: $e');
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error_outline, color: Colors.white),
              const SizedBox(width: 16),
              Expanded(child: Text('Import failed: ${e.toString()}')),
            ],
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
        ),
      );
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return WillPopScope(
      onWillPop: () async {
        context.pop(false);
        return false;
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Import CSV'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.pop(false),
          ),
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Select Trading Account',
                      style: theme.textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      value: _selectedAccount,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: 'Select Account',
                      ),
                      items: _accounts
                          .map((account) => DropdownMenuItem(
                                value: account.id,
                                child: Text(account.name),
                              ))
                          .toList(),
                      onChanged: (value) {
                        setState(() => _selectedAccount = value);
                      },
                    ),
                    const SizedBox(height: 24),
                    if (_selectedFile == null)
                      Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.upload_file,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Select a CSV file to import',
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 24),
                            ElevatedButton.icon(
                              onPressed: _pickFile,
                              icon: const Icon(Icons.file_upload),
                              label: const Text('Choose File'),
                            ),
                          ],
                        ),
                      )
                    else
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Selected File',
                              style: theme.textTheme.titleMedium,
                            ),
                            const SizedBox(height: 8),
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: theme.cardColor,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: theme.dividerColor.withOpacity(0.2),
                                ),
                              ),
                              child: Row(
                                children: [
                                  const Icon(Icons.description),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      _selectedFile!.path.split('/').last,
                                      style: theme.textTheme.bodyMedium,
                                    ),
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.close),
                                    onPressed: () {
                                      setState(() {
                                        _selectedFile = null;
                                        _csvData = null;
                                        _columnMapping.clear();
                                      });
                                    },
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 24),
                            if (_csvData != null) ...[
                              Text(
                                'Found ${_csvData!.length - 1} trades',
                                style: theme.textTheme.titleMedium,
                              ),
                              const SizedBox(height: 24),
                              Text(
                                'Field Mapping',
                                style: theme.textTheme.titleMedium,
                              ),
                              const SizedBox(height: 8),
                              Expanded(
                                child: ListView(
                                  children: _fieldMappings.keys.map((field) {
                                    final mappedIndex = _columnMapping[field];
                                    final mappedHeader = mappedIndex != null &&
                                            _csvData!.isNotEmpty
                                        ? _csvData![0][mappedIndex].toString()
                                        : 'Not mapped';
                                    return ListTile(
                                      title: Text(field),
                                      subtitle: Text(mappedHeader),
                                      trailing: Icon(
                                        mappedIndex != null
                                            ? Icons.check_circle
                                            : Icons.warning,
                                        color: mappedIndex != null
                                            ? Colors.green
                                            : Colors.orange,
                                      ),
                                    );
                                  }).toList(),
                                ),
                              ),
                              const SizedBox(height: 16),
                              SizedBox(
                                width: double.infinity,
                                child: FilledButton.icon(
                                  onPressed: _importTrades,
                                  icon: const Icon(Icons.check),
                                  label: const Text('Import Trades'),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                  ],
                ),
              ),
      ),
    );
  }
}
