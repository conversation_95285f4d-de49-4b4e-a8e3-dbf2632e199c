import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_flutter/core/models/trade.dart' as flutter;
import 'package:trade_reviewer_core/trade_reviewer_core.dart' as core;
import 'package:intl/intl.dart';
import 'package:trade_reviewer_flutter/features/trades/presentation/screens/import_csv_screen.dart';
import 'package:trade_reviewer_flutter/features/trades/presentation/screens/add_trade_screen.dart';
import 'package:trade_reviewer_flutter/features/trades/presentation/screens/trade_details_screen.dart';
import 'package:trade_reviewer_flutter/core/widgets/time_period_filter.dart';
import 'package:trade_reviewer_flutter/core/providers/time_period_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/account_filter_provider.dart';

class TradesScreen extends ConsumerStatefulWidget {
  const TradesScreen({super.key});

  @override
  ConsumerState<TradesScreen> createState() => _TradesScreenState();
}

class _TradesScreenState extends ConsumerState<TradesScreen> {
  List<flutter.Trade> _trades = [];
  List<flutter.Trade> _filteredTrades = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadTrades();
  }

  Future<void> _loadTrades() async {
    try {
      setState(() => _isLoading = true);

      final tradeService = ref.read(tradeServiceProvider);
      final settingsService = ref.read(settingsServiceProvider);
      final settings = await settingsService.getSettings();
      final userId = settings.userId;

      print('Loading trades for user: $userId');
      final trades = await tradeService.getTrades(userId);
      print('Loaded ${trades.length} trades from service');

      setState(() {
        _trades = trades.map((t) => flutter.Trade.fromCore(t)).toList();
        _trades.sort((a, b) => b.entryDate.compareTo(a.entryDate));
        print('Converted ${_trades.length} trades to Trade objects');
        _applyFilters();
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading trades: $e');
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading trades: $e')),
        );
      }
    }
  }

  void _applyFilters() {
    var filtered = List<flutter.Trade>.from(_trades);

    // Apply time period filter
    final timePeriodNotifier = ref.read(timePeriodProvider.notifier);
    final startDate = timePeriodNotifier.getStartDate();
    final endDate = timePeriodNotifier.getEndDate();

    if (startDate != null && endDate != null) {
      filtered = filtered.where((trade) {
        // Add time components to make the comparison inclusive
        final periodStart =
            DateTime(startDate.year, startDate.month, startDate.day, 0, 0, 0);
        final periodEnd =
            DateTime(endDate.year, endDate.month, endDate.day, 23, 59, 59);

        return trade.entryDate
                .isAfter(periodStart.subtract(const Duration(seconds: 1))) &&
            trade.entryDate.isBefore(periodEnd.add(const Duration(seconds: 1)));
      }).toList();
    }

    // Apply account filter
    final accountFilter = ref.read(accountFilterProvider);
    if (accountFilter.selectedAccountId != 'all') {
      filtered = filtered
          .where((trade) => trade.accountId == accountFilter.selectedAccountId)
          .toList();
    }

    setState(() {
      _filteredTrades = filtered;
    });
  }

  Map<String, dynamic> _getTradeStats() {
    final closedTrades = _filteredTrades
        .where((t) => t.status == flutter.TradeStatus.CLOSED)
        .toList();
    final openTrades = _filteredTrades
        .where((t) => t.status == flutter.TradeStatus.OPEN)
        .toList();

    print('Number of closed trades: ${closedTrades.length}');
    print('Number of open trades: ${openTrades.length}');

    double totalProfit = closedTrades.fold(0, (sum, trade) {
      final pnl = trade.calculatePnL();
      return sum + pnl;
    });

    double avgProfitPercent = 0.0;
    if (closedTrades.isNotEmpty) {
      avgProfitPercent = closedTrades.fold(0.0, (sum, trade) {
            if (trade.exitPrice == null) return sum;
            final profitPercent =
                ((trade.exitPrice! - trade.entryPrice) / trade.entryPrice) *
                    100;
            return sum + profitPercent;
          }) /
          closedTrades.length;
    }

    return {
      'totalProfit': totalProfit,
      'avgProfitPercent': avgProfitPercent,
      'openTrades': openTrades.length,
      'closedTrades': closedTrades.length,
    };
  }

  @override
  Widget build(BuildContext context) {
    // Listen to time period changes to reapply filters
    ref.listen(timePeriodProvider, (previous, next) {
      if (previous?.selectedTimeframe != next.selectedTimeframe ||
          previous?.startDate != next.startDate ||
          previous?.endDate != next.endDate) {
        _applyFilters();
      }
    });

    // Listen to account filter changes
    ref.listen(accountFilterProvider, (previous, next) {
      if (previous?.selectedAccountId != next.selectedAccountId) {
        _applyFilters();
      }
    });

    final theme = Theme.of(context);
    final stats = _getTradeStats();

    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.scaffoldBackgroundColor,
        elevation: 0,
        titleSpacing: 16,
        title: const Padding(
          padding: EdgeInsets.only(top: 8),
          child: TimePeriodFilter(),
        ),
        centerTitle: false,
        toolbarHeight: 56,
      ),
      body: RefreshIndicator(
        onRefresh: _loadTrades,
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: theme.scaffoldBackgroundColor,
                border: Border(
                  bottom: BorderSide(
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                ),
              ),
              child: Column(
                children: [
                  // Stats Section
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: theme.cardColor,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: theme.dividerColor.withOpacity(0.2),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildStatItem(
                          'Profit',
                          '\$${stats['totalProfit'].toStringAsFixed(2)}',
                          stats['totalProfit'] >= 0 ? Colors.green : Colors.red,
                        ),
                        _buildStatItem(
                          'Avg %',
                          '${stats['avgProfitPercent'].toStringAsFixed(2)}%',
                          stats['avgProfitPercent'] >= 0
                              ? Colors.green
                              : Colors.red,
                        ),
                        _buildStatItem(
                          'Open',
                          stats['openTrades'].toString(),
                          AppColors.primaryBlue,
                        ),
                        _buildStatItem(
                          'Closed',
                          stats['closedTrades'].toString(),
                          Colors.grey,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: _filteredTrades.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.analytics_outlined,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No trades yet',
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Add your first trade to get started',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[500],
                            ),
                          ),
                          const SizedBox(height: 24),
                          ElevatedButton.icon(
                            onPressed: () async {
                              final result = await Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const AddTradeScreen(),
                                ),
                              );
                              if (result == true) {
                                _loadTrades();
                              }
                            },
                            icon: const Icon(Icons.add),
                            label: const Text('Add Trade'),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: _filteredTrades.length,
                      itemBuilder: (context, index) {
                        final trade = _filteredTrades[index];
                        final isOpen = trade.status == flutter.TradeStatus.OPEN;
                        final profit = trade.calculatePnL();
                        final profitPercent = trade.exitPrice == null
                            ? 0.0
                            : ((trade.exitPrice! - trade.entryPrice) /
                                    trade.entryPrice) *
                                100;
                        final isProfit = !isOpen && profit > 0;

                        return Card(
                          margin: const EdgeInsets.only(bottom: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                            side: BorderSide(
                              color: theme.dividerColor.withOpacity(0.2),
                            ),
                          ),
                          child: InkWell(
                            onTap: () => _showTradeDetails(context, trade),
                            borderRadius: BorderRadius.circular(12),
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        children: [
                                          Text(
                                            trade.symbol,
                                            style: theme.textTheme.titleMedium
                                                ?.copyWith(
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                          const SizedBox(width: 8),
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 4,
                                            ),
                                            decoration: BoxDecoration(
                                              color: trade.type ==
                                                      flutter.TradeType.LONG
                                                  ? Colors.green
                                                      .withOpacity(0.1)
                                                  : Colors.red.withOpacity(0.1),
                                              borderRadius:
                                                  BorderRadius.circular(4),
                                            ),
                                            child: Text(
                                              trade.type ==
                                                      flutter.TradeType.LONG
                                                  ? 'Buy'
                                                  : 'Sell',
                                              style: theme.textTheme.bodySmall
                                                  ?.copyWith(
                                                color: trade.type ==
                                                        flutter.TradeType.LONG
                                                    ? Colors.green
                                                    : Colors.red,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      Text(
                                        DateFormat('MMM d, h:mm a')
                                            .format(trade.entryDate),
                                        style:
                                            theme.textTheme.bodySmall?.copyWith(
                                          color: theme
                                              .textTheme.bodySmall?.color
                                              ?.withOpacity(0.7),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 12),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'Entry: \$${trade.entryPrice.toStringAsFixed(2)}',
                                            style: theme.textTheme.bodyMedium,
                                          ),
                                          if (!isOpen &&
                                              trade.exitPrice != null)
                                            Text(
                                              'Exit: \$${trade.exitPrice!.toStringAsFixed(2)}',
                                              style: theme.textTheme.bodyMedium,
                                            ),
                                        ],
                                      ),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.end,
                                        children: [
                                          Text(
                                            'Qty: ${trade.quantity}',
                                            style: theme.textTheme.bodyMedium,
                                          ),
                                          if (trade.takeProfit != null &&
                                              trade.stopLoss != null)
                                            Text(
                                              'R:R ${trade.calculateRRR()?.toStringAsFixed(2) ?? 'N/A'}',
                                              style: theme.textTheme.bodyMedium,
                                            ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 12),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.label_outline,
                                            size: 16,
                                            color: theme
                                                .textTheme.bodySmall?.color
                                                ?.withOpacity(0.7),
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            trade.tags.firstOrNull ??
                                                'No Strategy',
                                            style: theme.textTheme.bodySmall
                                                ?.copyWith(
                                              color: theme
                                                  .textTheme.bodySmall?.color
                                                  ?.withOpacity(0.7),
                                            ),
                                          ),
                                        ],
                                      ),
                                      if (!isOpen)
                                        Text(
                                          isProfit
                                              ? '+\$${NumberFormat("#,##0.00", "en_US").format(profit)} (${profitPercent.toStringAsFixed(2)}%)'
                                              : '-\$${NumberFormat("#,##0.00", "en_US").format(profit.abs())} (${profitPercent.toStringAsFixed(2)}%)',
                                          style: theme.textTheme.bodyMedium
                                              ?.copyWith(
                                            color: isProfit
                                                ? Colors.green
                                                : Colors.red,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddTradeBottomSheet(context),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color valueColor) {
    final numberFormat = NumberFormat("#,##0.00", "en_US");
    String formattedValue = value;

    // If the value starts with a currency symbol and contains numbers
    if (value.startsWith('\$') &&
        value.substring(1).trim().contains(RegExp(r'[0-9]'))) {
      // Parse the number part
      double? number = double.tryParse(value.substring(1).replaceAll(',', ''));
      if (number != null) {
        formattedValue = '\$${numberFormat.format(number)}';
      }
    }
    // If the value is a percentage
    else if (value.endsWith('%')) {
      double? number = double.tryParse(value.substring(0, value.length - 1));
      if (number != null) {
        formattedValue = '${numberFormat.format(number)}%';
      }
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          formattedValue,
          style: TextStyle(
            color: valueColor,
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
      ],
    );
  }

  void _showTradeDetails(BuildContext context, flutter.Trade trade) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TradeDetailsScreen(trade: trade),
      ),
    );
    if (result == true) {
      _loadTrades();
    }
  }

  Future<bool?> _showAddTradeBottomSheet(BuildContext context) async {
    final theme = Theme.of(context);
    bool? result;

    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: theme.scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),
            // Add Trade Option
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.add_chart,
                  color: AppColors.primaryBlue,
                ),
              ),
              title: Text(
                'Add Trade',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              subtitle: const Text('Manually add a new trade'),
              onTap: () async {
                Navigator.pop(context);
                final addResult = await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AddTradeScreen(),
                  ),
                );
                if (addResult == true) {
                  result = true;
                  _loadTrades();
                }
              },
            ),
            const Divider(),
            // Import Trades Option
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.file_upload_outlined,
                  color: Colors.green,
                ),
              ),
              title: Text(
                'Import Trades',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              subtitle: const Text('Import trades from CSV or broker'),
              onTap: () {
                Navigator.pop(context);
                _showImportOptions(context);
              },
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
    return result;
  }

  void _showImportOptions(BuildContext context) {
    final theme = Theme.of(context);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: theme.scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back),
                    onPressed: () {
                      Navigator.pop(context);
                      _showAddTradeBottomSheet(context);
                    },
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Import Trades',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.file_upload_outlined,
                  color: Colors.green,
                ),
              ),
              title: Text(
                'Import from CSV',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              subtitle: const Text('Import trades from a CSV file'),
              onTap: () async {
                Navigator.pop(context);
                final result = await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ImportCsvScreen(),
                  ),
                );
                if (result == true) {
                  _loadTrades();
                }
              },
            ),
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.sync,
                  color: Colors.blue,
                ),
              ),
              title: Text(
                'Sync from Broker',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              subtitle: const Text('Import trades from your broker'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement broker sync
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Broker sync coming soon!'),
                  ),
                );
              },
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}
