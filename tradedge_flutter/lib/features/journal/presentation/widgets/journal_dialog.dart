import 'package:flutter/material.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:intl/intl.dart';

class JournalDialog extends StatefulWidget {
  final String symbol;
  final String date;
  final double pnl;
  final Map<String, String>? existingJournal;
  final Function(Map<String, String> journalData) onSave;

  const JournalDialog({
    super.key,
    required this.symbol,
    required this.date,
    required this.pnl,
    this.existingJournal,
    required this.onSave,
  });

  @override
  State<JournalDialog> createState() => _JournalDialogState();
}

class _JournalDialogState extends State<JournalDialog>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late final TextEditingController _setupController;
  late final TextEditingController _executionController;
  late final TextEditingController _managementController;
  late final TextEditingController _lessonsController;
  late final TextEditingController _emotionsController;
  late final TextEditingController _marketController;
  late final TextEditingController _improvementsController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _setupController =
        TextEditingController(text: widget.existingJournal?['setup'] ?? '');
    _executionController =
        TextEditingController(text: widget.existingJournal?['execution'] ?? '');
    _managementController = TextEditingController(
        text: widget.existingJournal?['management'] ?? '');
    _lessonsController =
        TextEditingController(text: widget.existingJournal?['lessons'] ?? '');
    _emotionsController =
        TextEditingController(text: widget.existingJournal?['emotions'] ?? '');
    _marketController =
        TextEditingController(text: widget.existingJournal?['market'] ?? '');
    _improvementsController = TextEditingController(
        text: widget.existingJournal?['improvements'] ?? '');
  }

  @override
  void dispose() {
    _tabController.dispose();
    _setupController.dispose();
    _executionController.dispose();
    _managementController.dispose();
    _lessonsController.dispose();
    _emotionsController.dispose();
    _marketController.dispose();
    _improvementsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      child: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: Container(
          constraints: const BoxConstraints(maxWidth: 600),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.symbol,
                          style: theme.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          widget.date,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.textTheme.bodyLarge?.color
                                ?.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                    Text(
                      widget.pnl >= 0
                          ? '+\$${widget.pnl.toStringAsFixed(2)}'
                          : '-\$${widget.pnl.abs().toStringAsFixed(2)}',
                      style: theme.textTheme.titleLarge?.copyWith(
                        color: widget.pnl >= 0 ? Colors.green : Colors.red,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              const Divider(height: 1),
              TabBar(
                controller: _tabController,
                tabs: const [
                  Tab(text: 'Analysis'),
                  Tab(text: 'Emotions'),
                  Tab(text: 'Market'),
                ],
              ),
              SizedBox(
                height: 400,
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildAnalysisTab(),
                    _buildEmotionsTab(),
                    _buildMarketTab(),
                  ],
                ),
              ),
              const Divider(height: 1),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Cancel'),
                    ),
                    const SizedBox(width: 8),
                    FilledButton(
                      onPressed: () {
                        widget.onSave({
                          'setup': _setupController.text,
                          'execution': _executionController.text,
                          'management': _managementController.text,
                          'lessons': _lessonsController.text,
                          'emotions': _emotionsController.text,
                          'market': _marketController.text,
                          'improvements': _improvementsController.text,
                        });
                      },
                      style: FilledButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                          widget.existingJournal != null ? 'Update' : 'Save'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnalysisTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildTextField(
            'Setup',
            'What was your trade setup?',
            _setupController,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            'Execution',
            'How did you execute the trade?',
            _executionController,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            'Management',
            'How did you manage the trade?',
            _managementController,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            'Lessons',
            'What did you learn from this trade?',
            _lessonsController,
          ),
        ],
      ),
    );
  }

  Widget _buildEmotionsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildTextField(
            'Emotions',
            'How did you feel during the trade?',
            _emotionsController,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            'Improvements',
            'What could you improve next time?',
            _improvementsController,
          ),
        ],
      ),
    );
  }

  Widget _buildMarketTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildTextField(
            'Market Analysis',
            'What was the market context?',
            _marketController,
          ),
        ],
      ),
    );
  }

  Widget _buildTextField(
    String label,
    String hint,
    TextEditingController controller,
  ) {
    return TextField(
      controller: controller,
      maxLines: 4,
      textInputAction: TextInputAction.done,
      onSubmitted: (_) {
        FocusScope.of(context).unfocus();
      },
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        alignLabelWithHint: true,
        border: const OutlineInputBorder(),
      ),
    );
  }
}
