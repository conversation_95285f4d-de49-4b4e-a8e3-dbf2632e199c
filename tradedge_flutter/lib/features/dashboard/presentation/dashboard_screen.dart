import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/theme/app_text_styles.dart';
import 'package:trade_reviewer_flutter/core/models/trade.dart';
import 'package:trade_reviewer_flutter/core/models/settings.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_flutter/core/services/analytics_service.dart';
import 'package:trade_reviewer_flutter/features/goals/presentation/widgets/goal_metrics_card.dart';
import 'package:trade_reviewer_flutter/core/providers/time_period_provider.dart';
import 'package:trade_reviewer_flutter/core/widgets/time_period_filter.dart';
import 'package:trade_reviewer_flutter/core/providers/trade_data_provider.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart' as core;
import 'package:trade_reviewer_flutter/core/providers/account_filter_provider.dart';

class DashboardScreen extends ConsumerStatefulWidget {
  const DashboardScreen({super.key});

  @override
  ConsumerState<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends ConsumerState<DashboardScreen> {
  List<Trade> _trades = [];
  Settings? _settings;
  bool _isLoading = true;
  late AnalyticsService _analyticsService;

  @override
  void initState() {
    super.initState();
    _analyticsService = ref.read(analyticsServiceProvider);
    _loadData();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Watch for trade data changes
    final version = ref.read(tradeDataProvider);
    if (version > 0) {
      _loadData();
    }
  }

  Future<void> _loadData() async {
    if (!mounted) return;

    setState(() => _isLoading = true);

    try {
      final tradeService = ref.read(tradeServiceProvider);
      final settingsService = ref.read(settingsServiceProvider);

      // Load settings first to get userId
      final settings = await settingsService.getSettings();
      if (settings == null) {
        throw Exception('Settings not found');
      }

      // Then load trades with the valid userId
      final trades = await tradeService.getTrades(settings.userId);

      if (!mounted) return;

      setState(() {
        _settings = Settings.fromCore(settings);
        _trades = trades.map((t) => Trade.fromCore(t)).toList();
        _isLoading = false;
      });

      print('Loaded ${_trades.length} trades for user ${settings.userId}');
    } catch (e) {
      print('Error loading dashboard data: $e');

      if (!mounted) return;

      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading dashboard data: $e')),
        );
      }
    }
  }

  List<Trade> _filterTradesByTimeframe(
      List<Trade> trades, TimePeriodState timePeriod) {
    final startDate = ref.read(timePeriodProvider.notifier).getStartDate();
    final endDate = ref.read(timePeriodProvider.notifier).getEndDate();

    if (startDate == null || endDate == null) return trades;

    try {
      var filteredTrades = trades.where((trade) {
        // Add time components to make the comparison inclusive
        final periodStart =
            DateTime(startDate.year, startDate.month, startDate.day, 0, 0, 0);
        final periodEnd =
            DateTime(endDate.year, endDate.month, endDate.day, 23, 59, 59);

        return trade.entryDate
                .isAfter(periodStart.subtract(const Duration(seconds: 1))) &&
            trade.entryDate.isBefore(periodEnd.add(const Duration(seconds: 1)));
      }).toList();

      // Apply account filter
      final accountFilter = ref.read(accountFilterProvider);
      if (accountFilter.selectedAccountId != 'all') {
        filteredTrades = filteredTrades
            .where(
                (trade) => trade.accountId == accountFilter.selectedAccountId)
            .toList();
      }

      print(
          'Filtered trades: ${filteredTrades.length} out of ${trades.length} trades');
      return filteredTrades;
    } catch (e) {
      print('Error filtering trades by timeframe: $e');
      return trades;
    }
  }

  Widget _buildDashboardContent(TimePeriodState timePeriod) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_settings == null) {
      return const Center(child: Text('Error loading settings'));
    }

    final filteredTrades = _filterTradesByTimeframe(_trades, timePeriod);

    try {
      // Convert goals to the correct format
      Map<String, double> goals = {...AnalyticsService.defaultMetricGoals};
      if (_settings?.goals != null) {
        final metricGoals = _settings!.goals.metricGoals;
        if (metricGoals != null) {
          goals = Map<String, double>.from(metricGoals);
        }
      }

      // Get account settings
      final accounts = _settings?.accounts;
      double? initialCapital;
      String? selectedAccountId;

      // Get the current account filter
      final accountFilter = ref.read(accountFilterProvider);

      if (accounts != null && accounts.tradingAccounts.isNotEmpty) {
        if (accountFilter.selectedAccountId == 'all') {
          // For 'All Accounts', sum up the initial capital of all accounts
          initialCapital = 0.0; // Start with 0
          for (final account in accounts.tradingAccounts) {
            if (account.initialCapital != null) {
              initialCapital = initialCapital! + account.initialCapital!;
            }
          }
          selectedAccountId = null; // null means all accounts for analytics
        } else {
          // For specific account, use that account's settings
          final selectedAccount = accounts.tradingAccounts.firstWhere(
              (acc) => acc.id == accountFilter.selectedAccountId,
              orElse: () => accounts.tradingAccounts.first);
          initialCapital = selectedAccount.initialCapital;
          selectedAccountId = selectedAccount.id;
        }
      }

      final closedTrades =
          filteredTrades.where((t) => t.status == TradeStatus.CLOSED).toList();

      final analytics = _analyticsService.calculateMetrics(
          closedTrades, goals, selectedAccountId, initialCapital);

      return RefreshIndicator(
        onRefresh: _loadData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              GoalMetricsCard(
                overallProgress: analytics.gaugeProgress.overallProgress.isNaN
                    ? 0.0
                    : analytics.gaugeProgress.overallProgress,
                profitProgress: analytics.gaugeProgress.profitProgress.isNaN
                    ? 0.0
                    : analytics.gaugeProgress.profitProgress,
                guardProgress: analytics.gaugeProgress.guardProgress.isNaN
                    ? 0.0
                    : analytics.gaugeProgress.guardProgress,
                focusProgress: analytics.gaugeProgress.focusProgress.isNaN
                    ? 0.0
                    : analytics.gaugeProgress.focusProgress,
                totalPnL: analytics.summary.netPnL.isNaN
                    ? 0.0
                    : analytics.summary.netPnL,
                winRate: analytics.summary.winRate.isNaN
                    ? 0.0
                    : analytics.summary.winRate,
                maxDrawdown: analytics.risk.maxDrawdown.isNaN
                    ? 0.0
                    : analytics.risk.maxDrawdown,
                profitFactor: analytics.summary.profitFactor.isInfinite ||
                        analytics.summary.profitFactor.isNaN
                    ? 0.0
                    : analytics.summary.profitFactor,
                metricGoals: goals,
                trades: filteredTrades,
              ),
              const SizedBox(height: 24),
            ],
          ),
        ),
      );
    } catch (e, stack) {
      print('Error calculating analytics: $e');
      print('Stack trace: $stack');
      return const Center(child: Text('Error calculating analytics'));
    }
  }

  @override
  Widget build(BuildContext context) {
    // Watch time period changes only
    final timePeriod = ref.watch(timePeriodProvider);

    // Watch account filter changes
    ref.watch(accountFilterProvider);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        titleSpacing: 16,
        title: const Padding(
          padding: EdgeInsets.only(top: 8),
          child: TimePeriodFilter(),
        ),
        centerTitle: false,
        toolbarHeight: 56,
      ),
      body: _buildDashboardContent(timePeriod),
    );
  }
}
