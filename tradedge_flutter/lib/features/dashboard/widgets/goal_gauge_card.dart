import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'main_top_bar.dart';

class GoalGaugeCard extends StatelessWidget {
  final double profitProgress;
  final double guardProgress;
  final double focusProgress;

  const GoalGaugeCard({
    super.key,
    required this.profitProgress,
    required this.guardProgress,
    required this.focusProgress,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.07),
            blurRadius: 32,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          MainTopBar(
            currentView: 'overview',
            onViewChange: (view) {
              // Handle view change
            },
          ),
          const SizedBox(height: 24),
          AspectRatio(
            aspectRatio: 1,
            child: Stack(
              children: [
                // Background circle
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: const Color(0xFFF1F5F9),
                      width: 2,
                    ),
                  ),
                ),
                // Circles in triangular formation
                // Top circle (Profit)
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: Center(
                    child: CircleGauge(
                      progress: profitProgress,
                      color: const Color(0xFF22C55E),
                      label: 'PROFIT',
                      icon: Icons.trending_up,
                    ),
                  ),
                ),
                // Bottom Left circle (Guard)
                Positioned(
                  bottom: 40,
                  left: 40,
                  child: CircleGauge(
                    progress: guardProgress,
                    color: const Color(0xFF64748B),
                    label: 'GUARD',
                    icon: Icons.shield_outlined,
                  ),
                ),
                // Bottom Right circle (Focus)
                Positioned(
                  bottom: 40,
                  right: 40,
                  child: CircleGauge(
                    progress: focusProgress,
                    color: const Color(0xFF3B82F6),
                    label: 'FOCUS',
                    icon: Icons.bolt_outlined,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class CircleGauge extends StatelessWidget {
  final double progress;
  final Color color;
  final String label;
  final IconData icon;

  const CircleGauge({
    super.key,
    required this.progress,
    required this.color,
    required this.label,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 100,
      height: 100,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Background circle with dashed border
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: color.withOpacity(0.15),
                width: 8,
              ),
            ),
          ),
          // Progress arc
          CustomPaint(
            painter: CircleGaugePainter(
              progress: progress,
              color: color,
            ),
            size: const Size.square(double.infinity),
          ),
          // Content
          Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
                const SizedBox(height: 4),
                Text(
                  '${progress.toInt()}',
                  style: TextStyle(
                    color: color,
                    fontSize: 24,
                    fontWeight: FontWeight.w800,
                    height: 1,
                  ),
                ),
                Text(
                  label,
                  style: TextStyle(
                    color: color,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 0.5,
                    height: 1.2,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class CircleGaugePainter extends CustomPainter {
  final double progress;
  final Color color;

  CircleGaugePainter({
    required this.progress,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.42;
    final rect = Rect.fromCircle(center: center, radius: radius);
    final startAngle = -math.pi / 2;
    final sweepAngle = 2 * math.pi * (progress / 100);

    final paint = Paint()
      ..shader = SweepGradient(
        colors: [
          color,
          color.withOpacity(0.7),
        ],
        stops: const [0.0, 1.0],
        startAngle: startAngle,
        endAngle: startAngle + sweepAngle,
      ).createShader(rect)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 8
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(rect, startAngle, sweepAngle, false, paint);
  }

  @override
  bool shouldRepaint(CircleGaugePainter oldDelegate) =>
      oldDelegate.progress != progress || oldDelegate.color != color;
}
