import 'package:flutter/material.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../shared/widgets/app_card.dart';

class GoalsScreen extends StatelessWidget {
  const GoalsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Goals',
              style: AppTextStyles.h1,
            ),
            const SizedBox(height: 16),
            AppCard(
              title: 'Current Goals',
              child: const Center(
                child: Text('Goal tracking will go here'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
