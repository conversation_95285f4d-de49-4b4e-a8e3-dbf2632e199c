import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/theme/app_text_styles.dart';
import 'package:trade_reviewer_flutter/core/services/analytics_service.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_flutter/core/models/trade.dart' as flutter;
import 'package:trade_reviewer_flutter/core/providers/account_filter_provider.dart';
import 'package:trade_reviewer_flutter/core/models/settings.dart';

final _currencyFormat = NumberFormat("#,##0.0", "en_US");

class GoalCalendarScreen extends ConsumerStatefulWidget {
  const GoalCalendarScreen({super.key});

  @override
  ConsumerState<GoalCalendarScreen> createState() => _GoalCalendarScreenState();
}

class _GoalCalendarScreenState extends ConsumerState<GoalCalendarScreen> {
  DateTime _selectedDate = DateTime.now();
  DateTime _currentMonth = DateTime.now();
  String _viewMode = 'week';
  bool _isLoading = true;
  List<flutter.Trade> _trades = [];
  final _analyticsService = AnalyticsService();
  double _dragOffset = 0.0;

  Map<String, Map<String, dynamic>> _monthData = {};

  @override
  void initState() {
    super.initState();
    _loadTrades();
  }

  Future<void> _loadTrades() async {
    if (!mounted) return;

    try {
      setState(() => _isLoading = true);

      final tradeService = ref.read(tradeServiceProvider);
      final settingsService = ref.read(settingsServiceProvider);

      // Load settings first to get userId
      final settings = await settingsService.getSettings();
      if (settings == null) {
        throw Exception('Settings not found');
      }

      // Then load trades with the valid userId
      final trades = await tradeService.getTrades(settings.userId);

      if (!mounted) return;

      // Filter trades by selected account
      final accountFilter = ref.read(accountFilterProvider);
      var filteredTrades =
          trades.map((t) => flutter.Trade.fromCore(t)).toList();
      if (accountFilter.selectedAccountId != null &&
          accountFilter.selectedAccountId != 'all') {
        filteredTrades = filteredTrades
            .where((t) => t.accountId == accountFilter.selectedAccountId)
            .toList();
      }

      setState(() {
        _trades = filteredTrades;
        _isLoading = false;
      });

      _updateCalendarData();

      print('Loaded ${_trades.length} trades for analytics');
    } catch (e) {
      print('Error loading trades: $e');

      if (!mounted) return;

      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading trades: $e')),
      );
    }
  }

  void _updateCalendarData() {
    final Map<String, Map<String, dynamic>> newData = {};

    DateTime startDate;
    DateTime endDate;

    if (_viewMode == 'week') {
      startDate =
          _selectedDate.subtract(Duration(days: _selectedDate.weekday % 7));
      endDate = startDate.add(const Duration(days: 6));
    } else {
      startDate = DateTime(_currentMonth.year, _currentMonth.month, 1);
      endDate = DateTime(_currentMonth.year, _currentMonth.month + 1, 0);
    }

    // Generate data for each day in the range
    for (DateTime date = startDate;
        date.isBefore(endDate.add(const Duration(days: 1)));
        date = date.add(const Duration(days: 1))) {
      final dateStr = DateFormat('yyyy-MM-dd').format(date);

      // Filter trades for this day
      final dayTrades = _trades.where((trade) {
        final tradeDate = DateFormat('yyyy-MM-dd').format(trade.entryDate);
        return tradeDate == dateStr;
      }).toList();

      // Calculate metrics for the day, even if there are no trades
      final metrics = dayTrades.isEmpty
          ? _analyticsService.calculateMetrics([])
          : _analyticsService.calculateMetrics(dayTrades);

      final totalTrades = dayTrades.length.toDouble();
      final winningTrades =
          dayTrades.where((t) => t.calculatePnL() > 0).length.toDouble();
      final netProfit =
          dayTrades.fold(0.0, (sum, trade) => sum + trade.calculatePnL());
      final accountValue = 100000.0; // TODO: Get actual account value
      final netProfitPercent = (netProfit / accountValue) * 100;

      newData[dateStr] = {
        'performance': {
          'totalTrades': totalTrades,
          'winningTrades': winningTrades,
          'losingTrades': totalTrades - winningTrades,
          'netProfit': netProfit,
          'netProfitPercent': netProfitPercent,
        },
        'profitGoals': {
          'winRate': metrics.summary.winRate,
          'profitFactor': metrics.summary.profitFactor,
          'avgRR': metrics.risk.riskRewardRatio,
          'progress': metrics.gaugeProgress.profitProgress,
        },
        'guardGoals': {
          'maxDrawdown': metrics.risk.maxDrawdown,
          'riskPerTrade': metrics.risk.avgRisk,
          'stopLossAdherence': metrics.compliance.stopLossCompliance,
          'progress': metrics.gaugeProgress.guardProgress,
        },
        'focusGoals': {
          'planAdherence': metrics.compliance.positionSizeCompliance,
          'emotionalControl': metrics.compliance.riskRewardCompliance,
          'journalCompletion':
              100.0, // TODO: Implement journal completion tracking
          'progress': metrics.gaugeProgress.focusProgress,
        },
      };
    }

    setState(() {
      _monthData = newData;
    });
  }

  final List<Map<String, dynamic>> _summaryData = [
    {
      'title': 'Profit Goals',
      'icon': Icons.trending_up,
      'color': AppColors.successGreen,
      'progress': 85,
      'description':
          'Track your profit-related goals including win rate and profit factor'
    },
    {
      'title': 'Guard Goals',
      'icon': Icons.shield_outlined,
      'color': AppColors.warningYellow,
      'progress': 78,
      'description': 'Monitor risk management metrics and protective measures'
    },
    {
      'title': 'Focus Goals',
      'icon': Icons.bolt_outlined,
      'color': AppColors.primaryBlue,
      'progress': 92,
      'description': 'Track trading consistency and focus metrics'
    }
  ];

  @override
  Widget build(BuildContext context) {
    final isLargeScreen = MediaQuery.of(context).size.width >= 1024;
    final isMediumScreen = MediaQuery.of(context).size.width >= 640;

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.backgroundAlt,
        centerTitle: true,
        title: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // View toggle
            Container(
              height: 36,
              decoration: BoxDecoration(
                color: AppColors.backgroundLight,
                borderRadius: BorderRadius.circular(8),
              ),
              child: SegmentedButton<String>(
                segments: const [
                  ButtonSegment(
                    value: 'week',
                    icon: Icon(Icons.view_week_outlined, size: 18),
                  ),
                  ButtonSegment(
                    value: 'month',
                    icon: Icon(Icons.calendar_month_outlined, size: 18),
                  ),
                ],
                selected: {_viewMode},
                onSelectionChanged: (values) {
                  setState(() {
                    _viewMode = values.first;
                    _updateCalendarData();
                  });
                },
                style: ButtonStyle(
                  backgroundColor:
                      MaterialStateProperty.resolveWith<Color>((states) {
                    if (states.contains(MaterialState.selected)) {
                      return AppColors.primaryBlue;
                    }
                    return Colors.transparent;
                  }),
                  visualDensity: VisualDensity.compact,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ),
            ),
            const SizedBox(width: 16),
            // Navigation
            IconButton(
              onPressed: () => _navigatePrevious(),
              icon: const Icon(Icons.chevron_left, size: 18),
              color: AppColors.textPrimary,
              visualDensity: VisualDensity.compact,
              padding: const EdgeInsets.all(4),
              constraints: const BoxConstraints(
                minHeight: 32,
                minWidth: 32,
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppColors.backgroundLight,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                _viewMode == 'week'
                    ? _formatWeekRange()
                    : DateFormat('MMM yyyy').format(_currentMonth),
                style: TextStyle(
                  color: AppColors.textPrimary,
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            IconButton(
              onPressed: () => _navigateNext(),
              icon: const Icon(Icons.chevron_right, size: 18),
              color: AppColors.textPrimary,
              visualDensity: VisualDensity.compact,
              padding: const EdgeInsets.all(4),
              constraints: const BoxConstraints(
                minHeight: 32,
                minWidth: 32,
              ),
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Calendar Header with legend
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: AppColors.backgroundAlt,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: AppColors.borderLight),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                _buildLegendItem('Profit Goals',
                                    AppColors.successGreen, Icons.trending_up),
                                const SizedBox(width: 24),
                                _buildLegendItem(
                                    'Guard Goals',
                                    AppColors.warningYellow,
                                    Icons.shield_outlined),
                                const SizedBox(width: 24),
                                _buildLegendItem('Focus Goals',
                                    AppColors.primaryBlue, Icons.bolt_outlined),
                              ],
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Main content area
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                child: Column(
                                  children: [
                                    // Calendar View
                                    Container(
                                      decoration: BoxDecoration(
                                        color: AppColors.backgroundAlt,
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(
                                            color: AppColors.borderLight),
                                      ),
                                      child: _viewMode == 'week'
                                          ? _buildWeekView()
                                          : _buildMonthView(),
                                    ),
                                  ],
                                ),
                              ),
                              if (isLargeScreen) ...[
                                const SizedBox(width: 16),
                                SizedBox(
                                  width: 320,
                                  child: _buildDetailsPanel(),
                                ),
                              ],
                            ],
                          ),
                          if (!isLargeScreen) ...[
                            const SizedBox(height: 16),
                            _buildDetailsPanel(),
                          ],
                        ],
                      ),
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeekView() {
    final startOfWeek =
        _selectedDate.subtract(Duration(days: _selectedDate.weekday % 7));

    return GestureDetector(
      onHorizontalDragEnd: (details) {
        if (details.primaryVelocity == null) return;

        if (details.primaryVelocity! > 0) {
          // Swipe right - go to previous day
          _navigatePreviousDay();
        } else {
          // Swipe left - go to next day
          _navigateNextDay();
        }
      },
      child: Container(
        color: Colors.transparent,
        child: Column(
          children: [
            // Week days header
            Row(
              children: List.generate(7, (index) {
                final date = startOfWeek.add(Duration(days: index));
                final isSelected = _selectedDate.year == date.year &&
                    _selectedDate.month == date.month &&
                    _selectedDate.day == date.day;

                return Expanded(
                  child: GestureDetector(
                    onTap: () => setState(() => _selectedDate = date),
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: AppColors.borderLight,
                          ),
                        ),
                        color: isSelected
                            ? AppColors.primaryBlue.withOpacity(0.1)
                            : Colors.transparent,
                      ),
                      child: Text(
                        DateFormat('E').format(date),
                        style: TextStyle(
                          color: AppColors.textSecondary,
                          fontSize: 11,
                          fontWeight:
                              isSelected ? FontWeight.w600 : FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                );
              }),
            ),
            // Week days content
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: List.generate(7, (index) {
                  final date = startOfWeek.add(Duration(days: index));
                  return Expanded(
                    child: _buildWeekDayCell(date),
                  );
                }),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWeekDayCell(DateTime date) {
    final isSelected = DateUtils.isSameDay(date, _selectedDate);
    final isToday = DateUtils.isSameDay(date, DateTime.now());
    final dateStr = DateFormat('yyyy-MM-dd').format(date);
    final dayData = _monthData[dateStr];

    return GestureDetector(
      onTap: () => setState(() => _selectedDate = date),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 1.5),
        padding: const EdgeInsets.fromLTRB(8, 12, 4, 12),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.primaryBlue.withOpacity(0.1)
              : isToday
                  ? AppColors.backgroundLight
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? AppColors.primaryBlue
                : isToday
                    ? AppColors.slate500
                    : Colors.transparent,
            width: isSelected || isToday ? 2 : 0,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Date number
            Container(
              width: 28,
              height: 28,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isSelected ? AppColors.primaryBlue : Colors.transparent,
              ),
              child: Text(
                '${date.day}',
                style: TextStyle(
                  color: isSelected
                      ? Colors.white
                      : isToday
                          ? AppColors.primaryBlue
                          : AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
            ),
            if (dayData != null) ...[
              const SizedBox(height: 8),
              // Goal metrics
              _buildDayMetrics(dayData),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDayMetrics(Map<String, dynamic> dayData) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildGaugeCircle(
          dayData['profitGoals']?['progress'] ?? 0,
          AppColors.successGreen,
        ),
        const SizedBox(width: 4),
        _buildGaugeCircle(
          dayData['guardGoals']?['progress'] ?? 0,
          AppColors.warningYellow,
        ),
        const SizedBox(width: 4),
        _buildGaugeCircle(
          dayData['focusGoals']?['progress'] ?? 0,
          AppColors.primaryBlue,
        ),
      ],
    );
  }

  Widget _buildGaugeCircle(double progress, Color color) {
    return Container(
      width: 8,
      height: 8,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: color.withOpacity(0.15),
      ),
      child: Stack(
        children: [
          Center(
            child: Container(
              width: 6,
              height: 6,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: progress >= 80 ? color : Colors.transparent,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMonthView() {
    final daysInMonth =
        DateUtils.getDaysInMonth(_currentMonth.year, _currentMonth.month);
    final firstDayOfMonth =
        DateTime(_currentMonth.year, _currentMonth.month, 1);
    final firstWeekday = firstDayOfMonth.weekday % 7;

    // Calculate the number of weeks needed
    final totalDays = firstWeekday + daysInMonth;
    final numberOfWeeks = ((totalDays + 6) ~/ 7);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Weekday headers
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Row(
            children: ['S', 'M', 'T', 'W', 'T', 'F', 'S']
                .map((day) => Expanded(
                      child: Text(
                        day,
                        style: TextStyle(
                          color: AppColors.textSecondary,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ))
                .toList(),
          ),
        ),
        // Calendar grid
        GridView.builder(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 7,
            childAspectRatio: 0.9,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
          ),
          itemCount: numberOfWeeks * 7,
          itemBuilder: (context, index) {
            final dayOffset = index - firstWeekday;
            final date = firstDayOfMonth.add(Duration(days: dayOffset));
            final isCurrentMonth = date.month == _currentMonth.month;

            return isCurrentMonth ? _buildMonthDayCell(date) : const SizedBox();
          },
        ),
      ],
    );
  }

  Widget _buildMonthDayCell(DateTime date) {
    final isSelected = DateUtils.isSameDay(date, _selectedDate);
    final isToday = DateUtils.isSameDay(date, DateTime.now());
    final dateStr = DateFormat('yyyy-MM-dd').format(date);
    final dayData = _monthData[dateStr];

    return GestureDetector(
      onTap: () => setState(() => _selectedDate = date),
      child: Container(
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.primaryBlue.withOpacity(0.1)
              : isToday
                  ? AppColors.backgroundLight
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? AppColors.primaryBlue
                : isToday
                    ? AppColors.slate500
                    : Colors.transparent,
            width: isSelected || isToday ? 2 : 0,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Date number
            Container(
              width: 28,
              height: 28,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isSelected ? AppColors.primaryBlue : Colors.transparent,
              ),
              child: Text(
                '${date.day}',
                style: TextStyle(
                  color: isSelected
                      ? Colors.white
                      : isToday
                          ? AppColors.primaryBlue
                          : AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
            ),
            if (dayData != null) ...[
              const SizedBox(height: 8),
              _buildDayMetrics(dayData),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color, IconData icon) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 8),
        Text(
          label,
          style: TextStyle(
            color: AppColors.textSecondary,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  String _formatWeekRange() {
    final startOfWeek =
        _selectedDate.subtract(Duration(days: _selectedDate.weekday % 7));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));

    // More compact format without year
    if (startOfWeek.month == endOfWeek.month) {
      return '${DateFormat('MMM d').format(startOfWeek)}-${DateFormat('d').format(endOfWeek)}';
    } else {
      return '${DateFormat('M/d').format(startOfWeek)}-${DateFormat('M/d').format(endOfWeek)}';
    }
  }

  void _navigatePrevious() {
    setState(() {
      if (_viewMode == 'week') {
        _selectedDate = _selectedDate.subtract(const Duration(days: 7));
      } else {
        _currentMonth = DateTime(_currentMonth.year, _currentMonth.month - 1);
      }
      _updateCalendarData();
    });
  }

  void _navigateNext() {
    setState(() {
      if (_viewMode == 'week') {
        _selectedDate = _selectedDate.add(const Duration(days: 7));
      } else {
        _currentMonth = DateTime(_currentMonth.year, _currentMonth.month + 1);
      }
      _updateCalendarData();
    });
  }

  void _navigatePreviousDay() {
    setState(() {
      _selectedDate = _selectedDate.subtract(const Duration(days: 1));
      if (_viewMode == 'month') {
        _currentMonth = DateTime(_selectedDate.year, _selectedDate.month);
      }
      _updateCalendarData();
    });
  }

  void _navigateNextDay() {
    setState(() {
      _selectedDate = _selectedDate.add(const Duration(days: 1));
      if (_viewMode == 'month') {
        _currentMonth = DateTime(_selectedDate.year, _selectedDate.month);
      }
      _updateCalendarData();
    });
  }

  Widget _buildDetailsPanel() {
    final dateStr = DateFormat('yyyy-MM-dd').format(_selectedDate);
    final dayData = _monthData[dateStr];

    Widget buildDayContent(DateTime date, Map<String, dynamic>? data) {
      return Container(
        decoration: BoxDecoration(
          color: AppColors.backgroundAlt,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.borderLight),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      DateFormat('MMM d, yyyy').format(date).toUpperCase(),
                      style: TextStyle(
                        color: AppColors.textPrimary,
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                  if (data != null)
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: data['performance']['netProfitPercent'] >= 0
                            ? AppColors.successGreen.withOpacity(0.1)
                            : AppColors.warningYellow.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '\$${_currencyFormat.format(data['performance']['netProfit'])} (${data['performance']['netProfitPercent'] >= 0 ? '+' : ''}${data['performance']['netProfitPercent'].toStringAsFixed(1)}%)',
                        style: TextStyle(
                          color: data['performance']['netProfitPercent'] >= 0
                              ? AppColors.successGreen
                              : AppColors.warningYellow,
                          fontSize: 13,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            if (data != null) ...[
              const Divider(height: 1),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Trading stats row
                    Row(
                      children: [
                        _buildStatBox(
                          'Trades',
                          data['performance']['totalTrades'].toInt().toString(),
                          AppColors.textPrimary,
                        ),
                        const SizedBox(width: 12),
                        _buildStatBox(
                          'Win Rate',
                          '${((data['performance']['winningTrades'] / data['performance']['totalTrades']) * 100).toStringAsFixed(1)}%',
                          AppColors.primaryBlue,
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    // Goals sections
                    _buildGoalSection(
                      'Profit Goals',
                      Icons.trending_up,
                      AppColors.successGreen,
                      data['profitGoals'],
                      {
                        'Win Rate': '${data['profitGoals']['winRate']}%',
                        'Profit Factor':
                            data['profitGoals']['profitFactor'].toString(),
                        'Avg R:R': data['profitGoals']['avgRR'].toString(),
                      },
                    ),
                    const SizedBox(height: 16),
                    _buildGoalSection(
                      'Guard Goals',
                      Icons.shield_outlined,
                      AppColors.warningYellow,
                      data['guardGoals'],
                      {
                        'Max DD': '${data['guardGoals']['maxDrawdown']}%',
                        'Risk/Trade': '${data['guardGoals']['riskPerTrade']}%',
                        'SL Adherence':
                            '${data['guardGoals']['stopLossAdherence']}%',
                      },
                    ),
                    const SizedBox(height: 16),
                    _buildGoalSection(
                      'Focus Goals',
                      Icons.bolt_outlined,
                      AppColors.primaryBlue,
                      data['focusGoals'],
                      {
                        'Plan': '${data['focusGoals']['planAdherence']}%',
                        'Emotion': '${data['focusGoals']['emotionalControl']}%',
                        'Journal':
                            '${data['focusGoals']['journalCompletion']}%',
                      },
                    ),
                  ],
                ),
              ),
            ] else
              Padding(
                padding: const EdgeInsets.all(24),
                child: Center(
                  child: Text(
                    'No data available for this date',
                    style: TextStyle(
                      color: AppColors.textSecondary,
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
          ],
        ),
      );
    }

    return GestureDetector(
      onHorizontalDragUpdate: (details) {
        setState(() {
          _dragOffset +=
              details.primaryDelta! / MediaQuery.of(context).size.width;
        });
      },
      onHorizontalDragEnd: (details) {
        if (details.primaryVelocity == null) return;

        final velocity =
            details.primaryVelocity! / MediaQuery.of(context).size.width;
        if (_dragOffset.abs() > 0.3 || velocity.abs() > 1.0) {
          if (_dragOffset > 0) {
            _navigatePreviousDay();
          } else {
            _navigateNextDay();
          }
        }
        setState(() {
          _dragOffset = 0;
        });
      },
      onHorizontalDragCancel: () {
        setState(() {
          _dragOffset = 0;
        });
      },
      child: Stack(
        children: [
          // Previous day panel
          Transform.translate(
            offset: Offset(
                _dragOffset * MediaQuery.of(context).size.width -
                    MediaQuery.of(context).size.width,
                0),
            child: Opacity(
              opacity: (_dragOffset).clamp(0.0, 1.0),
              child: buildDayContent(
                _selectedDate.subtract(const Duration(days: 1)),
                _monthData[DateFormat('yyyy-MM-dd')
                    .format(_selectedDate.subtract(const Duration(days: 1)))],
              ),
            ),
          ),
          // Current day panel
          Transform.translate(
            offset: Offset(_dragOffset * MediaQuery.of(context).size.width, 0),
            child: buildDayContent(_selectedDate, dayData),
          ),
          // Next day panel
          Transform.translate(
            offset: Offset(
                _dragOffset * MediaQuery.of(context).size.width +
                    MediaQuery.of(context).size.width,
                0),
            child: Opacity(
              opacity: (-_dragOffset).clamp(0.0, 1.0),
              child: buildDayContent(
                _selectedDate.add(const Duration(days: 1)),
                _monthData[DateFormat('yyyy-MM-dd')
                    .format(_selectedDate.add(const Duration(days: 1)))],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatBox(String label, String value, Color color) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: AppColors.backgroundLight,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                color: color,
                fontSize: 16,
                fontWeight: FontWeight.w700,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalSection(
    String title,
    IconData icon,
    Color color,
    Map<String, dynamic> data,
    Map<String, String> metrics,
  ) {
    final progress = data['progress'] as double;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.backgroundLight,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // Circular gauge with icon
              SizedBox(
                width: 40,
                height: 40,
                child: CustomPaint(
                  painter: CircularGaugePainter(
                    progress: progress / 100,
                    color: color,
                    backgroundColor: color.withOpacity(0.15),
                  ),
                  child: Center(
                    child: Icon(icon, color: color, size: 16),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        color: AppColors.textPrimary,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${progress.toStringAsFixed(1)}% Achieved',
                      style: TextStyle(
                        color: color,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Metrics in a row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: metrics.entries.map((entry) {
              final value = entry.value.contains('%')
                  ? '${double.parse(entry.value.replaceAll('%', '')).toStringAsFixed(1)}%'
                  : double.parse(entry.value).toStringAsFixed(1);
              return Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      entry.key,
                      style: TextStyle(
                        color: AppColors.textSecondary,
                        fontSize: 11,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      value,
                      style: TextStyle(
                        color: AppColors.textPrimary,
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildCalendarContent(bool isLargeScreen, bool isMediumScreen) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Calendar Header with legend
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.backgroundAlt,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppColors.borderLight),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildLegendItem('Profit Goals', AppColors.successGreen,
                      Icons.trending_up),
                  const SizedBox(width: 24),
                  _buildLegendItem('Guard Goals', AppColors.warningYellow,
                      Icons.shield_outlined),
                  const SizedBox(width: 24),
                  _buildLegendItem('Focus Goals', AppColors.primaryBlue,
                      Icons.bolt_outlined),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Main content area
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    children: [
                      // Calendar View
                      Container(
                        decoration: BoxDecoration(
                          color: AppColors.backgroundAlt,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: AppColors.borderLight),
                        ),
                        child: _viewMode == 'week'
                            ? _buildWeekView()
                            : _buildMonthView(),
                      ),
                    ],
                  ),
                ),
                if (isLargeScreen) ...[
                  const SizedBox(width: 16),
                  SizedBox(
                    width: 320,
                    child: _buildDetailsPanel(),
                  ),
                ],
              ],
            ),
            if (!isLargeScreen) ...[
              const SizedBox(height: 16),
              _buildDetailsPanel(),
            ],
          ],
        ),
      ),
    );
  }
}

class CircularGaugePainter extends CustomPainter {
  final double progress;
  final Color color;
  final Color backgroundColor;

  CircularGaugePainter({
    required this.progress,
    required this.color,
    required this.backgroundColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    final strokeWidth = 4.5;

    // Draw background circle
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    canvas.drawCircle(center, radius - strokeWidth / 2, backgroundPaint);

    // Draw progress arc
    final progressPaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius - strokeWidth / 2),
      -1.5708, // Start from top (-90 degrees)
      progress * 2 * 3.14159, // Convert progress to radians
      false,
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(CircularGaugePainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.color != color ||
        oldDelegate.backgroundColor != backgroundColor;
  }
}
