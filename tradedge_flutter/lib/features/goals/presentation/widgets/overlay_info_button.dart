import 'package:flutter/material.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/theme/app_text_styles.dart';

class OverlayInfoButton extends StatefulWidget {
  final String description;

  const OverlayInfoButton({
    super.key,
    required this.description,
  });

  @override
  State<OverlayInfoButton> createState() => _OverlayInfoButtonState();
}

class _OverlayInfoButtonState extends State<OverlayInfoButton> {
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();
  bool _isTooltipVisible = false;

  @override
  void dispose() {
    _hideTooltip();
    super.dispose();
  }

  void _showTooltip() {
    if (_overlayEntry != null) return;

    final overlay = Overlay.of(context);
    final renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;

    _overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          // Full screen gesture detector to handle click-away
          Positioned.fill(
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: _hideTooltip,
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),
          // Tooltip content
          Positioned(
            width: 200,
            child: CompositedTransformFollower(
              link: _layerLink,
              offset: Offset(0, size.height + 4),
              child: Material(
                color: Colors.transparent,
                child: Container(
                  margin: const EdgeInsets.only(top: 4),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.backgroundAlt,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AppColors.border),
                  ),
                  child: Text(
                    widget.description,
                    style: AppTextStyles.body2.copyWith(
                      color: AppColors.textPrimary,
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );

    setState(() => _isTooltipVisible = true);
    overlay.insert(_overlayEntry!);
  }

  void _hideTooltip() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    if (mounted) {
      setState(() => _isTooltipVisible = false);
    }
  }

  void _toggleTooltip() {
    if (_isTooltipVisible) {
      _hideTooltip();
    } else {
      _showTooltip();
    }
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: GestureDetector(
        onTap: _toggleTooltip,
        child: Container(
          width: 20,
          height: 20,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.05),
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Icon(
              Icons.info_outline,
              size: 12,
              color: AppColors.textSecondary,
            ),
          ),
        ),
      ),
    );
  }
}
