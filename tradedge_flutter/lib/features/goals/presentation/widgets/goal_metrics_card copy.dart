import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/theme/app_text_styles.dart';
import 'package:trade_reviewer_flutter/shared/widgets/app_card.dart';
import 'package:trade_reviewer_flutter/features/goals/presentation/widgets/performance_trends_card.dart';
import 'dart:math' as math;

class GoalMetricsCard extends StatefulWidget {
  const GoalMetricsCard({super.key});

  @override
  State<GoalMetricsCard> createState() => _GoalMetricsCardState();
}

class _GoalMetricsCardState extends State<GoalMetricsCard> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: const Color(0xFF1E293B),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 16,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              SmallGauge(
                progress: 0.85,
                color: const Color(0xFF22C55E),
                label: 'PROFIT',
                icon: Icons.monetization_on,
              ),
              SmallGauge(
                progress: 0.72,
                color: const Color(0xFF3B82F6),
                label: 'GUARD',
                icon: Icons.shield,
              ),
              SmallGauge(
                progress: 0.93,
                color: const Color(0xFF8B5CF6),
                label: 'FOCUS',
                icon: Icons.center_focus_strong,
              ),
            ],
          ),
          const SizedBox(height: 24),
          const Divider(
            color: Colors.white12,
            height: 1,
          ),
          const SizedBox(height: 16),
          _buildMetricRow('Profit', '\$1,250.00'),
          _buildMetricRow('Guard', '72%'),
          _buildMetricRow('Focus', '93%'),
        ],
      ),
    );
  }

  Widget _buildMetricRow(String label, String value) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 8),
          OverlayInfoButton(
            message: _getMetricDescription(label),
            icon: _getMetricIcon(label),
            color: Colors.white70,
            label: label,
          ),
          const Spacer(),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  String _getMetricDescription(String label) {
    switch (label) {
      case 'Total P&L':
        return 'Your total profit and loss for the selected period\n'
            'Includes both realized and unrealized gains/losses\n'
            'Key indicator of overall trading performance';
      case 'Win Rate':
        return 'Percentage of profitable trades vs total trades\n'
            'Higher win rates indicate more consistent profitability\n'
            'Should be analyzed alongside average win/loss ratio';
      case 'Max Drawdown':
        return 'Largest peak-to-trough decline in account value\n'
            'Important measure of risk and capital preservation\n'
            'Helps in setting appropriate position sizes';
      case 'Profit Factor':
        return 'Ratio of gross profits to gross losses\n'
            'Values above 1.5 indicate a profitable strategy\n'
            'Higher values suggest more robust trading systems';
      case 'Profit':
        return 'Your net trading profit for the period\n'
            'Calculated after all fees and commissions\n'
            'Key metric for evaluating trading success';
      case 'Guard':
        return 'Risk management metric for capital protection\n'
            'Helps prevent excessive losses per trade\n'
            'Essential for maintaining trading longevity';
      case 'Focus':
        return 'Measure of trading concentration and discipline\n'
            'Indicates adherence to your trading strategy\n'
            'Higher focus suggests better strategy execution';
      default:
        return 'No description available';
    }
  }

  IconData _getMetricIcon(String label) {
    switch (label) {
      case 'Total P&L':
        return Icons.attach_money;
      case 'Win Rate':
        return Icons.bar_chart;
      case 'Max Drawdown':
        return Icons.trending_down;
      case 'Profit Factor':
        return Icons.trending_up;
      case 'Profit':
        return Icons.monetization_on;
      case 'Guard':
        return Icons.shield;
      case 'Focus':
        return Icons.center_focus_strong;
      default:
        return Icons.info_outline;
    }
  }
}

class SmallGauge extends StatefulWidget {
  final double progress;
  final Color color;
  final String label;
  final IconData icon;

  const SmallGauge({
    Key? key,
    required this.progress,
    required this.color,
    required this.label,
    required this.icon,
  }) : super(key: key);

  @override
  State<SmallGauge> createState() => _SmallGaugeState();
}

class _SmallGaugeState extends State<SmallGauge>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );
    _progressAnimation = Tween<double>(
      begin: 0,
      end: widget.progress,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));
    _controller.forward();
  }

  @override
  void didUpdateWidget(SmallGauge oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.progress != widget.progress) {
      _progressAnimation = Tween<double>(
        begin: oldWidget.progress,
        end: widget.progress,
      ).animate(CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOutCubic,
      ));
      _controller.forward(from: 0);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              widget.label,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.white70,
                letterSpacing: 1.2,
              ),
            ),
            const SizedBox(width: 4),
            OverlayInfoButton(
              message: _getGaugeDescription(widget.label),
              icon: widget.icon,
              color: widget.color,
              label: widget.label,
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: const Color(0xFF0F172A),
            border: Border.all(
              color: widget.color.withOpacity(0.3),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: widget.color.withOpacity(0.2),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Stack(
            children: [
              // Outer circle with gradient border
              Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: const Color(0xFF0F172A),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      widget.color.withOpacity(0.2),
                      widget.color.withOpacity(0.1),
                    ],
                  ),
                ),
              ),
              // Background circle with dashed border
              Container(
                margin: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: const Color(0xFF0F172A),
                  border: Border.all(
                    color: widget.color.withOpacity(0.15),
                    width: 8,
                  ),
                ),
              ),
              // Animated progress arc
              Padding(
                padding: const EdgeInsets.all(6),
                child: AnimatedBuilder(
                  animation: _progressAnimation,
                  builder: (context, child) {
                    return CustomPaint(
                      painter: CircleGaugePainter(
                        progress: _progressAnimation.value,
                        color: widget.color,
                      ),
                      size: const Size.square(double.infinity),
                    );
                  },
                ),
              ),
              // Content
              Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      widget.icon,
                      color: widget.color,
                      size: 24,
                    ),
                    const SizedBox(height: 4),
                    AnimatedBuilder(
                      animation: _progressAnimation,
                      builder: (context, child) {
                        return Text(
                          '${(_progressAnimation.value * 100).toInt()}',
                          style: TextStyle(
                            color: widget.color,
                            fontSize: 22,
                            fontWeight: FontWeight.w700,
                            height: 1,
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _getGaugeDescription(String title) {
    switch (title) {
      case 'PROFIT':
        return 'Measures your trading profitability goals\n'
            'Combines win rate, profit factor, and average returns\n'
            'Higher values indicate better profit generation';
      case 'GUARD':
        return 'Evaluates risk management effectiveness\n'
            'Includes drawdown control and position sizing\n'
            'Higher values show better capital protection';
      case 'FOCUS':
        return 'Tracks trading discipline and consistency\n'
            'Monitors trade frequency and duration\n'
            'Higher values indicate better trading habits';
      default:
        return 'No description available';
    }
  }
}

class CircleGaugePainter extends CustomPainter {
  final double progress;
  final Color color;

  CircleGaugePainter({
    required this.progress,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;
    final startAngle = -math.pi / 2;
    final sweepAngle = 2 * math.pi * progress;

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 8
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle,
      sweepAngle,
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(CircleGaugePainter oldDelegate) =>
      oldDelegate.progress != progress || oldDelegate.color != color;
}

class OverlayInfoButton extends StatefulWidget {
  final String message;
  final IconData icon;
  final Color color;
  final String label;

  const OverlayInfoButton({
    Key? key,
    required this.message,
    required this.icon,
    required this.color,
    required this.label,
  }) : super(key: key);

  @override
  State<OverlayInfoButton> createState() => _OverlayInfoButtonState();
}

class _OverlayInfoButtonState extends State<OverlayInfoButton> {
  OverlayEntry? _overlayEntry;
  final _layerLink = LayerLink();
  bool _isHovered = false;
  bool _isOpen = false;

  @override
  void dispose() {
    _removeOverlay();
    super.dispose();
  }

  void _showOverlay() {
    if (_isOpen) {
      _removeOverlay();
      return;
    }

    _isOpen = true;
    _removeOverlay();

    final overlay = Overlay.of(context);
    final renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final screenWidth = MediaQuery.of(context).size.width;
    final position = renderBox.localToGlobal(Offset.zero);

    // Check if the tooltip would be cut off on the right side
    final isRightSide = position.dx > screenWidth / 2;
    final tooltipOffset = isRightSide
        ? Offset(-280 + size.width,
            size.height + 8) // Align to right edge for right-side items
        : Offset(-280 / 2 + size.width / 2,
            size.height + 8); // Center for left-side items

    _overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          Positioned.fill(
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: _removeOverlay,
              child: Container(color: Colors.transparent),
            ),
          ),
          Positioned(
            width: 280,
            child: CompositedTransformFollower(
              link: _layerLink,
              showWhenUnlinked: false,
              offset: tooltipOffset,
              child: Material(
                color: Colors.transparent,
                child: MouseRegion(
                  onExit: (_) {
                    if (!_isOpen) _removeOverlay();
                  },
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: isDarkMode(context)
                          ? const Color(0xFF1E293B)
                          : Colors.white.withOpacity(0.95),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isDarkMode(context)
                            ? Colors.white.withOpacity(0.1)
                            : Colors.black.withOpacity(0.1),
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          children: [
                            Icon(
                              widget.icon,
                              color: widget.color,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'About ${widget.label}',
                              style: TextStyle(
                                color: widget.color,
                                fontSize: 13,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const Spacer(),
                            GestureDetector(
                              onTap: _removeOverlay,
                              child: Icon(
                                Icons.close,
                                size: 16,
                                color: isDarkMode(context)
                                    ? Colors.white.withOpacity(0.7)
                                    : Colors.black.withOpacity(0.5),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Text(
                          widget.message,
                          style: TextStyle(
                            color: isDarkMode(context)
                                ? Colors.white.withOpacity(0.9)
                                : Colors.black.withOpacity(0.8),
                            fontSize: 13,
                            height: 1.5,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );

    overlay.insert(_overlayEntry!);
  }

  bool isDarkMode(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark;
  }

  void _removeOverlay() {
    setState(() {
      _isOpen = false;
      _isHovered = false;
      _overlayEntry?.remove();
      _overlayEntry = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _showOverlay,
          onHover: (isHovered) {
            if (isHovered && !_isOpen) {
              _showOverlay();
            } else if (!isHovered && !_isOpen) {
              _removeOverlay();
            }
          },
          borderRadius: BorderRadius.circular(12),
          child: Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.05),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(
                Icons.info_outline,
                size: 12,
                color: Colors.white.withOpacity(0.5),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
