import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/theme/app_text_styles.dart';
import 'package:trade_reviewer_flutter/shared/widgets/app_card.dart';
import 'package:trade_reviewer_flutter/features/goals/presentation/widgets/performance_trends_card.dart';
import 'package:trade_reviewer_flutter/features/goals/presentation/widgets/overlay_info_button.dart';
import 'package:trade_reviewer_flutter/core/models/trade.dart';
import 'dart:math' as math;

class GoalMetricsCard extends ConsumerStatefulWidget {
  final double overallProgress;
  final double profitProgress;
  final double guardProgress;
  final double focusProgress;
  final double totalPnL;
  final double winRate;
  final double maxDrawdown;
  final double profitFactor;
  final Map<String, double> metricGoals;
  final List<Trade> trades;

  const GoalMetricsCard({
    super.key,
    this.overallProgress = 0.0,
    this.profitProgress = 0.0,
    this.guardProgress = 0.0,
    this.focusProgress = 0.0,
    this.totalPnL = 0.0,
    this.winRate = 0.0,
    this.maxDrawdown = 0.0,
    this.profitFactor = 0.0,
    required this.metricGoals,
    this.trades = const [],
  });

  @override
  ConsumerState<GoalMetricsCard> createState() => _GoalMetricsCardState();
}

class _GoalMetricsCardState extends ConsumerState<GoalMetricsCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _progressAnimation;
  String? expandedSection;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );
    _progressAnimation = Tween<double>(
      begin: 0,
      end: widget.overallProgress.clamp(0.0, 100.0),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    _animationController.forward();
  }

  @override
  void didUpdateWidget(GoalMetricsCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.overallProgress != widget.overallProgress) {
      _progressAnimation = Tween<double>(
        begin: oldWidget.overallProgress.clamp(0.0, 100.0),
        end: widget.overallProgress.clamp(0.0, 100.0),
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutCubic,
      ));
      _animationController.forward(from: 0);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // First row of metric cards
        Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  icon: Icons.attach_money,
                  label: 'Total P&L',
                  value: '\$${widget.totalPnL.toStringAsFixed(0)}',
                  target:
                      '\$${widget.metricGoals['targetPnL']?.toStringAsFixed(0) ?? '1,000'}',
                  iconColor: const Color(0xFF22C55E),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildMetricCard(
                  icon: Icons.bar_chart,
                  label: 'Win Rate',
                  value: widget.winRate.toStringAsFixed(1),
                  suffix: '%',
                  target:
                      '${widget.metricGoals['winRate']?.toStringAsFixed(1) ?? '60.0'}%',
                  iconColor: const Color(0xFF3B82F6),
                ),
              ),
            ],
          ),
        ),
        // Second row of metric cards
        Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  icon: Icons.trending_down,
                  label: 'Max Drawdown',
                  value: widget.maxDrawdown.toStringAsFixed(1),
                  suffix: '%',
                  target:
                      '${widget.metricGoals['maxDrawdown']?.toStringAsFixed(1) ?? '10.0'}%',
                  iconColor: const Color(0xFFEF4444),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildMetricCard(
                  icon: Icons.trending_up,
                  label: 'Profit Factor',
                  value: widget.profitFactor == double.infinity
                      ? '∞'
                      : widget.profitFactor.toStringAsFixed(2),
                  target:
                      '${widget.metricGoals['profitFactor']?.toStringAsFixed(1) ?? '2.0'}',
                  iconColor: const Color(0xFF8B5CF6),
                ),
              ),
            ],
          ),
        ),
        // Main gauge card
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: const Color(0xFF0F172A),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 32,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Stack(
                children: [
                  // Main gauge container
                  Padding(
                    padding: const EdgeInsets.only(top: 72, bottom: 24),
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: AspectRatio(
                        aspectRatio: 1.6,
                        child: Stack(
                          children: [
                            // Outer gauge ticks
                            CustomPaint(
                              painter: GaugeTicksPainter(),
                              size: const Size.square(double.infinity),
                            ),
                            // Main gauge with clock hand
                            CustomPaint(
                              painter: MainGaugePainter(
                                progress: widget.overallProgress,
                                animation: _progressAnimation,
                              ),
                              size: const Size.square(double.infinity),
                            ),
                            // Small gauges in triangular formation
                            // Top gauge (Profit)
                            Positioned(
                              top: 0,
                              left: 0,
                              right: 0,
                              child: Center(
                                child: SmallGauge(
                                  progress: widget.profitProgress,
                                  color: const Color(0xFF22C55E),
                                  label: 'PROFIT',
                                  icon: Icons.monetization_on,
                                ),
                              ),
                            ),
                            // Bottom Left gauge (Guard)
                            Positioned(
                              bottom: 30,
                              left: 50,
                              child: SmallGauge(
                                progress: widget.guardProgress,
                                color: const Color(0xFFB7C1CF),
                                label: 'GUARD',
                                icon: Icons.shield,
                              ),
                            ),
                            // Bottom Right gauge (Focus)
                            Positioned(
                              bottom: 30,
                              right: 50,
                              child: SmallGauge(
                                progress: widget.focusProgress,
                                color: const Color(0xFF3B82F6),
                                label: 'FOCUS',
                                icon: Icons.center_focus_strong,
                              ),
                            ),
                            // Clock hand on top of everything
                            CustomPaint(
                              painter: ClockHandPainter(
                                progress: widget.overallProgress,
                                animation: _progressAnimation,
                              ),
                              size: const Size.square(double.infinity),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  // Overall performance label at top left
                  Positioned(
                    top: 0,
                    left: 0,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Overall',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.white70,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                Text(
                                  '${widget.overallProgress.toInt()}%',
                                  style: const TextStyle(
                                    fontSize: 28,
                                    fontWeight: FontWeight.w700,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // Action icons at top right
                  Positioned(
                    top: 0,
                    right: 0,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () => context.push('/goals/calendar'),
                            borderRadius: BorderRadius.circular(20),
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(20),
                                color: Colors.black12,
                              ),
                              child: Icon(
                                Icons.calendar_today_outlined,
                                size: 18,
                                color: Colors.white.withOpacity(0.8),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () => context.push('/settings/goals'),
                            borderRadius: BorderRadius.circular(20),
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(20),
                                color: Colors.black12,
                              ),
                              child: Icon(
                                Icons.settings,
                                size: 18,
                                color: Colors.white.withOpacity(0.8),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Accordion sections
              _buildExpandableSection(
                title: 'Profit',
                icon: Icons.monetization_on,
                color: const Color(0xFF22C55E),
                content: Column(
                  children: [
                    _buildMetricRow(
                        'Total P&L', '\$${widget.totalPnL.toStringAsFixed(0)}'),
                    _buildMetricRow(
                        'Win Rate', '${widget.winRate.toStringAsFixed(1)}%'),
                    _buildMetricRow('Target Win Rate',
                        '${widget.metricGoals['winRate']?.toStringAsFixed(1) ?? '60.0'}%'),
                  ],
                ),
              ),
              _buildExpandableSection(
                title: 'Guard',
                icon: Icons.shield,
                color: const Color(0xFFB7C1CF),
                content: Column(
                  children: [
                    _buildMetricRow('Max Drawdown',
                        '${widget.maxDrawdown.toStringAsFixed(1)}%'),
                    _buildMetricRow('Target Drawdown',
                        '${widget.metricGoals['maxDrawdown']?.toStringAsFixed(1) ?? '10.0'}%'),
                    _buildMetricRow('Risk per Trade',
                        '${widget.metricGoals['riskPerTrade']?.toStringAsFixed(1) ?? '5.0'}%'),
                  ],
                ),
              ),
              _buildExpandableSection(
                title: 'Focus',
                icon: Icons.center_focus_strong,
                color: const Color(0xFF3B82F6),
                content: Column(
                  children: [
                    _buildMetricRow(
                      'Profit Factor',
                      widget.profitFactor == double.infinity
                          ? '∞'
                          : widget.profitFactor.toStringAsFixed(2),
                    ),
                    _buildMetricRow('Target Profit Factor',
                        '${widget.metricGoals['profitFactor']?.toStringAsFixed(1) ?? '2.0'}'),
                    _buildMetricRow('Plan Adherence',
                        '${widget.metricGoals['planAdherence']?.toStringAsFixed(1) ?? '80.0'}%'),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 24),
        // Performance Trends Card
        PerformanceTrendsCard(
          trades: widget.trades,
        ),
      ],
    );
  }

  Widget _buildExpandableSection({
    required String title,
    required IconData icon,
    required Color color,
    required Widget content,
  }) {
    final isExpanded = expandedSection == title;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        InkWell(
          onTap: () {
            setState(() {
              expandedSection = isExpanded ? null : title;
            });
          },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 6),
            child: Row(
              children: [
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Stack(
                    children: [
                      // Dashed background
                      CustomPaint(
                        painter: DashedCirclePainter(color: color),
                        size: const Size.square(double.infinity),
                      ),
                      // Progress arc
                      CustomPaint(
                        painter: SmallGaugePainter(
                          progress: isExpanded ? 100 : 0,
                          color: color,
                        ),
                        size: const Size.square(double.infinity),
                      ),
                      // Icon
                      Center(
                        child: Icon(
                          icon,
                          color: color,
                          size: 16,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    color: color,
                    fontSize: 15,
                    fontWeight: FontWeight.w700,
                    letterSpacing: 0.5,
                  ),
                ),
                const Spacer(),
                Icon(
                  isExpanded ? Icons.expand_less : Icons.expand_more,
                  color: color.withOpacity(0.7),
                  size: 18,
                ),
              ],
            ),
          ),
        ),
        if (isExpanded)
          Padding(
            padding: const EdgeInsets.only(left: 48, bottom: 8),
            child: content,
          ),
        if (!isExpanded) const SizedBox(height: 2),
      ],
    );
  }

  Widget _buildMetricRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 4),
          OverlayInfoButton(
            description: _getMetricDescription(label),
          ),
          const Spacer(),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricCard({
    required IconData icon,
    required String label,
    required String value,
    String? suffix,
    required String target,
    required Color iconColor,
  }) {
    return Container(
      width: 220,
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: const Color(0xFF1E293B),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 32,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(5),
                decoration: BoxDecoration(
                  color: iconColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: 16,
                  color: iconColor,
                ),
              ),
              const SizedBox(width: 6),
              Text(
                label,
                style: const TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                  color: Colors.white70,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Row(
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Text(
                value,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                  color: Colors.white,
                ),
              ),
              if (suffix != null)
                Text(
                  suffix,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: Colors.white70,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              OverlayInfoButton(
                description: _getMetricDescription(label),
              ),
              const SizedBox(width: 4),
              const Text(
                'Target: ',
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                  color: Colors.white38,
                ),
              ),
              Text(
                target,
                style: const TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w600,
                  color: Colors.white70,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTimePeriodButton(String label, bool isSelected) {
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: TextButton(
        onPressed: () {
          // TODO: Implement period selection
        },
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          backgroundColor:
              isSelected ? const Color(0xFFF1F5F9) : Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isSelected ? const Color(0xFF3B82F6) : Colors.grey[600],
          ),
        ),
      ),
    );
  }

  String _getMetricDescription(String label) {
    switch (label) {
      case 'Total P&L':
        return 'Your total profit and loss for the selected period\n'
            'Includes both realized and unrealized gains/losses\n'
            'Key indicator of overall trading performance';
      case 'Win Rate':
        return 'Percentage of profitable trades vs total trades\n'
            'Higher win rates indicate more consistent profitability\n'
            'Should be analyzed alongside average win/loss ratio';
      case 'Max Drawdown':
        return 'Largest peak-to-trough decline in account value\n'
            'Important measure of risk and capital preservation\n'
            'Helps in setting appropriate position sizes';
      case 'Profit Factor':
        return 'Ratio of gross profits to gross losses\n'
            'Values above 1.5 indicate a profitable strategy\n'
            'Higher values suggest more robust trading systems';
      case 'Profit':
        return 'Your net trading profit for the period\n'
            'Calculated after all fees and commissions\n'
            'Key metric for evaluating trading success';
      case 'Guard':
        return 'Risk management metric for capital protection\n'
            'Helps prevent excessive losses per trade\n'
            'Essential for maintaining trading longevity';
      case 'Focus':
        return 'Measure of trading concentration and discipline\n'
            'Indicates adherence to your trading strategy\n'
            'Higher focus suggests better strategy execution';
      case 'Average Trade':
        return 'Mean profit or loss per trade taken\n'
            'Helps evaluate trade sizing and consistency\n'
            'Important for position sizing decisions';
      case 'Trade Count':
        return 'Total number of trades executed\n'
            'Indicates trading frequency and activity\n'
            'Used to assess strategy implementation';
      case 'Daily Win Rate':
        return 'Percentage of profitable days vs total days\n'
            'Measures day-over-day consistency\n'
            'Important for evaluating daily performance';
      case 'Risk/Reward':
        return 'Ratio of potential profit to potential loss\n'
            'Higher ratios indicate better trade setups\n'
            'Key for trade quality assessment';
      case 'Position Size':
        return 'Amount of capital allocated per trade\n'
            'Critical for risk management\n'
            'Based on account size and risk tolerance';
      case 'Stop Loss':
        return 'Maximum acceptable loss per trade\n'
            'Protects capital from large drawdowns\n'
            'Essential risk management tool';
      default:
        return 'No description available';
    }
  }
}

class SmallGauge extends StatefulWidget {
  final double progress;
  final Color color;
  final String label;
  final IconData icon;

  const SmallGauge({
    Key? key,
    required this.progress,
    required this.color,
    required this.label,
    required this.icon,
  }) : super(key: key);

  @override
  State<SmallGauge> createState() => _SmallGaugeState();
}

class _SmallGaugeState extends State<SmallGauge>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );
    _progressAnimation = Tween<double>(
      begin: 0,
      end: widget.progress,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));
    _controller.forward();
  }

  @override
  void didUpdateWidget(SmallGauge oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.progress != widget.progress) {
      _progressAnimation = Tween<double>(
        begin: oldWidget.progress,
        end: widget.progress,
      ).animate(CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOutCubic,
      ));
      _controller.forward(from: 0);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 100,
      height: 100,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: const Color(0xFF0F172A),
        border: Border.all(
          color: widget.color.withOpacity(0.3),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: widget.color.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Outer circle with gradient border
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: const Color(0xFF0F172A),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  widget.color.withOpacity(0.2),
                  widget.color.withOpacity(0.1),
                ],
              ),
            ),
          ),
          // Background circle with dashed border
          Container(
            margin: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: const Color(0xFF0F172A),
              border: Border.all(
                color: widget.color.withOpacity(0.15),
                width: 8,
              ),
            ),
          ),
          // Animated progress arc
          Padding(
            padding: const EdgeInsets.all(6),
            child: AnimatedBuilder(
              animation: _progressAnimation,
              builder: (context, child) {
                return CustomPaint(
                  painter: CircleGaugePainter(
                    progress: _progressAnimation.value,
                    color: widget.color,
                  ),
                  size: const Size.square(double.infinity),
                );
              },
            ),
          ),
          // Content
          Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  widget.icon,
                  color: widget.color,
                  size: 24,
                ),
                const SizedBox(height: 4),
                AnimatedBuilder(
                  animation: _progressAnimation,
                  builder: (context, child) {
                    return Text(
                      '${_progressAnimation.value.toInt()}',
                      style: TextStyle(
                        color: widget.color,
                        fontSize: 22,
                        fontWeight: FontWeight.w700,
                        height: 1,
                      ),
                    );
                  },
                ),
                Text(
                  widget.label,
                  style: TextStyle(
                    color: widget.color,
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 0.5,
                    height: 1.2,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class CircleGaugePainter extends CustomPainter {
  final double progress;
  final Color color;

  CircleGaugePainter({
    required this.progress,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.45;

    // Draw progress arc with gradient
    final progressPaint = Paint()
      ..shader = SweepGradient(
        colors: [
          color,
          color.withOpacity(0.8),
          color.withOpacity(0.6),
        ],
        stops: const [0.0, 0.5, 1.0],
        startAngle: -math.pi / 2,
        endAngle: 3 * math.pi / 2,
      ).createShader(Rect.fromCircle(center: center, radius: radius))
      ..style = PaintingStyle.stroke
      ..strokeWidth = 8
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -math.pi / 2, // Start from top
      progress / 100 * 2 * math.pi, // Convert progress to radians
      false,
      progressPaint,
    );

    // Add subtle glow effect
    final glowPaint = Paint()
      ..color = color.withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 12
      ..maskFilter = const MaskFilter.blur(BlurStyle.outer, 4);

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -math.pi / 2,
      progress / 100 * 2 * math.pi,
      false,
      glowPaint,
    );
  }

  @override
  bool shouldRepaint(CircleGaugePainter oldDelegate) {
    return oldDelegate.progress != progress || oldDelegate.color != color;
  }
}

class GaugeTicksPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.45;
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    // Draw single dashed circle
    canvas.drawCircle(center, radius, paint);
  }

  @override
  bool shouldRepaint(GaugeTicksPainter oldDelegate) => false;
}

class MainGaugePainter extends CustomPainter {
  final double progress;
  final Animation<double>? animation;

  MainGaugePainter({
    required this.progress,
    this.animation,
  }) : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.45;
    final progressValue = animation?.value ?? progress;

    // Draw progress arc with blue gradient
    final progressPaint = Paint()
      ..shader = SweepGradient(
        colors: [
          const Color(0xFF3B82F6), // Primary blue
          const Color(0xFF60A5FA), // Mid blue
          const Color(0xFF93C5FD), // Light blue
        ],
        stops: const [0.0, 0.5, 1.0],
        startAngle: -math.pi / 2,
        endAngle: 3 * math.pi / 2,
      ).createShader(Rect.fromCircle(center: center, radius: radius))
      ..style = PaintingStyle.stroke
      ..strokeWidth = 12
      ..strokeCap = StrokeCap.round;

    final startAngle = -math.pi / 2;
    final sweepAngle = 2 * math.pi * (progressValue / 100);
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle,
      sweepAngle,
      false,
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(MainGaugePainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.animation?.value != animation?.value;
  }
}

class SmallGaugePainter extends CustomPainter {
  final double progress;
  final Color color;

  SmallGaugePainter({
    required this.progress,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;
    final startAngle = -math.pi / 2;
    final sweepAngle = 2 * math.pi * (progress / 100);

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 8 // Increased thickness
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle,
      sweepAngle,
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(SmallGaugePainter oldDelegate) =>
      oldDelegate.progress != progress || oldDelegate.color != color;
}

class DashedCirclePainter extends CustomPainter {
  final Color color;

  DashedCirclePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;
    final paint = Paint()
      ..color = color.withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    const dashCount = 20;
    const dashLength = 0.1;
    for (var i = 0; i < dashCount; i++) {
      final startAngle = (i * 2 * math.pi / dashCount);
      final endAngle = startAngle + (2 * math.pi * dashLength / dashCount);
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        endAngle - startAngle,
        false,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(DashedCirclePainter oldDelegate) =>
      oldDelegate.color != color;
}

class ClockHandPainter extends CustomPainter {
  final double progress;
  final Animation<double>? animation;

  ClockHandPainter({
    required this.progress,
    this.animation,
  }) : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.45;
    final progressValue = animation?.value ?? progress;

    final startAngle = -math.pi / 2;
    final handAngle = startAngle + (2 * math.pi * progressValue / 100);
    final handLength = radius * 0.85;

    // Calculate points for the needle
    final handEnd = Offset(
      center.dx + handLength * math.cos(handAngle),
      center.dy + handLength * math.sin(handAngle),
    );

    // Draw thin line for the hand with gradient
    final handPaint = Paint()
      ..shader = LinearGradient(
        colors: [
          const Color(0xFF3B82F6), // Primary blue
          const Color(0xFF60A5FA), // Mid blue
        ],
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
      ).createShader(Rect.fromPoints(center, handEnd))
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.5
      ..strokeCap = StrokeCap.round;

    canvas.drawLine(center, handEnd, handPaint);

    // Draw minimalistic arrow tip with gradient
    final tipLength = 12.0;
    final tipWidth = 6.0;

    final tipVector = Offset(
      math.cos(handAngle),
      math.sin(handAngle),
    );
    final perpVector = Offset(
      -tipVector.dy,
      tipVector.dx,
    );

    final tipPoint = handEnd;
    final basePoint1 = Offset(
      tipPoint.dx - tipLength * tipVector.dx + tipWidth * perpVector.dx,
      tipPoint.dy - tipLength * tipVector.dy + tipWidth * perpVector.dy,
    );
    final basePoint2 = Offset(
      tipPoint.dx - tipLength * tipVector.dx - tipWidth * perpVector.dx,
      tipPoint.dy - tipLength * tipVector.dy - tipWidth * perpVector.dy,
    );

    final arrowPaint = Paint()
      ..shader = LinearGradient(
        colors: [
          const Color(0xFF60A5FA), // Mid blue
          const Color(0xFF93C5FD), // Light blue
        ],
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
      ).createShader(Rect.fromPoints(
        Offset(tipPoint.dx - tipLength, tipPoint.dy - tipLength),
        Offset(tipPoint.dx + tipLength, tipPoint.dy + tipLength),
      ));

    final arrowPath = Path()
      ..moveTo(tipPoint.dx, tipPoint.dy)
      ..lineTo(basePoint1.dx, basePoint1.dy)
      ..lineTo(basePoint2.dx, basePoint2.dy)
      ..close();

    canvas.drawPath(arrowPath, arrowPaint);

    // Draw small center dot with gradient
    final centerDotPaint = Paint()
      ..shader = RadialGradient(
        colors: [
          const Color(0xFF60A5FA), // Mid blue
          const Color(0xFF3B82F6), // Primary blue
        ],
      ).createShader(Rect.fromCircle(center: center, radius: 4));

    canvas.drawCircle(center, 4, centerDotPaint);
  }

  @override
  bool shouldRepaint(ClockHandPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.animation?.value != animation?.value;
  }
}
