import 'package:flutter/material.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/theme/app_text_styles.dart';
import 'package:trade_reviewer_flutter/shared/widgets/app_card.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';

class GoalGaugeCard extends StatelessWidget {
  final String title;
  final double currentValue;
  final double targetValue;
  final String unit;
  final Color color;

  const GoalGaugeCard({
    Key? key,
    required this.title,
    required this.currentValue,
    required this.targetValue,
    required this.unit,
    required this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final progress = (currentValue / targetValue).clamp(0.0, 1.0);
    final formattedCurrent = unit == '\$'
        ? '\$${currentValue.toStringAsFixed(2)}'
        : '${currentValue.toStringAsFixed(1)}$unit';
    final formattedTarget = unit == '\$'
        ? '\$${targetValue.toStringAsFixed(2)}'
        : '${targetValue.toStringAsFixed(1)}$unit';

    return AppCard(
      padding: const EdgeInsets.all(16),
      child: SizedBox(
        width: 160,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: AppTextStyles.subtitle2.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 100,
              child: SfRadialGauge(
                axes: <RadialAxis>[
                  RadialAxis(
                    minimum: 0,
                    maximum: 100,
                    showLabels: false,
                    showTicks: false,
                    startAngle: 180,
                    endAngle: 0,
                    radiusFactor: 1.0,
                    canScaleToFit: true,
                    axisLineStyle: AxisLineStyle(
                      thickness: 0.15,
                      color: AppColors.backgroundLight.withOpacity(0.3),
                      thicknessUnit: GaugeSizeUnit.factor,
                      cornerStyle: CornerStyle.bothCurve,
                    ),
                    pointers: <GaugePointer>[
                      RangePointer(
                        value: progress * 100,
                        width: 0.15,
                        sizeUnit: GaugeSizeUnit.factor,
                        color: color,
                        cornerStyle: CornerStyle.bothCurve,
                        enableAnimation: true,
                        animationDuration: 1000,
                        animationType: AnimationType.ease,
                      ),
                    ],
                    annotations: <GaugeAnnotation>[
                      GaugeAnnotation(
                        widget: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              formattedCurrent,
                              style: AppTextStyles.h6.copyWith(
                                color: AppColors.textPrimary,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              'of $formattedTarget',
                              style: AppTextStyles.caption.copyWith(
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                        angle: 90,
                        positionFactor: 0.5,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            // Progress percentage
            Center(
              child: Text(
                '${(progress * 100).toStringAsFixed(1)}% Complete',
                style: AppTextStyles.caption.copyWith(
                  color: color,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
