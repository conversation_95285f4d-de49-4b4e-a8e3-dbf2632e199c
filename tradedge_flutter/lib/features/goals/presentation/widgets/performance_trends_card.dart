import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/models/trade.dart';
import 'package:trade_reviewer_flutter/core/models/settings.dart';
import 'package:trade_reviewer_flutter/core/providers/time_period_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/account_filter_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'dart:math' as math;

class PerformanceTrendsCard extends ConsumerStatefulWidget {
  final List<Trade> trades;

  const PerformanceTrendsCard({
    super.key,
    required this.trades,
  });

  @override
  ConsumerState<PerformanceTrendsCard> createState() =>
      _PerformanceTrendsCardState();
}

class _PerformanceTrendsCardState extends ConsumerState<PerformanceTrendsCard>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _showDescription = true;

  List<Trade> _filterTradesByPeriod() {
    if (widget.trades.isEmpty) return [];

    final timePeriodNotifier = ref.read(timePeriodProvider.notifier);
    final startDate = timePeriodNotifier.getStartDate();
    final endDate = timePeriodNotifier.getEndDate();

    var filtered = widget.trades;

    // Apply date filter
    if (startDate != null && endDate != null) {
      filtered = filtered.where((trade) {
        return trade.entryDate.isAfter(startDate) &&
            trade.entryDate.isBefore(endDate.add(const Duration(days: 1)));
      }).toList();
    }

    // Apply account filter
    final accountFilter = ref.read(accountFilterProvider);
    if (accountFilter.selectedAccountId != 'all') {
      filtered = filtered
          .where((trade) => trade.accountId == accountFilter.selectedAccountId)
          .toList();
    }

    return filtered;
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // Listen to time period changes
    ref.listen(timePeriodProvider, (previous, next) {
      if (previous?.selectedTimeframe != next.selectedTimeframe ||
          previous?.startDate != next.startDate ||
          previous?.endDate != next.endDate) {
        setState(() {
          // This will trigger a rebuild with the new filtered trades
        });
      }
    });

    final filteredTrades = _filterTradesByPeriod();

    // Sort trades by date for chart calculations
    filteredTrades.sort((a, b) => a.entryDate.compareTo(b.entryDate));

    return FutureBuilder<Settings?>(
      future: ref.read(settingsServiceProvider).getSettings().then(
          (settings) => settings != null ? Settings.fromCore(settings) : null),
      builder: (context, snapshot) {
        // Get account settings
        double? initialCapital;
        String? selectedAccountId;
        final accountFilter = ref.read(accountFilterProvider);

        if (snapshot.hasData && snapshot.data?.accounts != null) {
          final accounts = snapshot.data!.accounts;
          if (accounts.tradingAccounts.isNotEmpty) {
            if (accountFilter.selectedAccountId == 'all') {
              // For 'All Accounts', sum up the initial capital
              initialCapital = 0.0;
              for (final account in accounts.tradingAccounts) {
                if (account.initialCapital != null) {
                  initialCapital = initialCapital! + account.initialCapital!;
                }
              }
              selectedAccountId = null;
            } else {
              // For specific account
              final selectedAccount = accounts.tradingAccounts.firstWhere(
                  (acc) => acc.id == accountFilter.selectedAccountId,
                  orElse: () => accounts.tradingAccounts.first);
              initialCapital = selectedAccount.initialCapital;
              selectedAccountId = selectedAccount.id;
            }
          }
        }

        // Calculate metrics with account context
        final metrics = _calculateMetrics(
            filteredTrades, selectedAccountId, initialCapital);

        // Calculate chart data points
        final cumulativePnLSpots = _calculateCumulativePnLSpots(filteredTrades);
        final drawdownSpots = _calculateDrawdownSpots(filteredTrades);
        final equityCurveSpots = _calculateEquityCurveSpots(filteredTrades);
        final volatilitySpots = _calculateVolatilitySpots(filteredTrades);
        final riskAdjustedReturnSpots =
            _calculateRiskAdjustedReturnSpots(filteredTrades);
        final calmarRatioSpots = _calculateCalmarRatioSpots(filteredTrades);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.fromLTRB(24, 20, 24, 20),
              decoration: BoxDecoration(
                color: isDarkMode
                    ? Colors.white.withOpacity(0.02)
                    : Colors.black.withOpacity(0.01),
                border: Border(
                  bottom: BorderSide(
                    color: isDarkMode
                        ? Colors.white.withOpacity(0.1)
                        : Colors.black.withOpacity(0.05),
                  ),
                ),
              ),
              child: Row(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Performance Trends',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: isDarkMode ? Colors.white : Colors.black87,
                          height: 1.2,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        'Track your trading performance metrics over time',
                        style: TextStyle(
                          fontSize: 13,
                          color: isDarkMode ? Colors.white60 : Colors.black54,
                          height: 1.2,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.all(24),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    _buildMetricItem(
                      'Avg Win/Loss',
                      '\$${metrics.avgWin.toStringAsFixed(2)} / \$${metrics.avgLoss.toStringAsFixed(2)}',
                      isDarkMode,
                    ),
                    _buildMetricItem(
                      'Trade Count',
                      '${metrics.tradeCount} trades',
                      isDarkMode,
                    ),
                    _buildMetricItem(
                      'Net P&L',
                      '\$${metrics.netPnL.toStringAsFixed(2)}',
                      isDarkMode,
                    ),
                    _buildMetricItem(
                      'Daily Win Rate',
                      '${metrics.winRate.toStringAsFixed(1)}%',
                      isDarkMode,
                    ),
                    _buildMetricItem(
                      'Average Trade',
                      '\$${metrics.avgTrade.toStringAsFixed(2)}',
                      isDarkMode,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            // Tab Bar
            Padding(
              padding: EdgeInsets.zero,
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width,
                ),
                child: Transform.translate(
                  offset: const Offset(0, 0),
                  child: TabBar(
                    controller: _tabController,
                    isScrollable: true,
                    padding: EdgeInsets.zero,
                    labelPadding: EdgeInsets.zero,
                    indicator: BoxDecoration(
                      color: const Color(0xFF3B82F6).withOpacity(0.08),
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8),
                      ),
                      border: Border(
                        top: BorderSide(
                          color: const Color(0xFF3B82F6).withOpacity(0.2),
                          width: 1,
                        ),
                        left: BorderSide(
                          color: const Color(0xFF3B82F6).withOpacity(0.2),
                          width: 1,
                        ),
                        right: BorderSide(
                          color: const Color(0xFF3B82F6).withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                    ),
                    overlayColor: MaterialStateProperty.resolveWith<Color?>(
                      (Set<MaterialState> states) {
                        return Colors.transparent;
                      },
                    ),
                    labelColor: const Color(0xFF3B82F6),
                    unselectedLabelColor:
                        isDarkMode ? Colors.white60 : Colors.grey[600],
                    labelStyle: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                    unselectedLabelStyle: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    dividerColor: Colors.transparent,
                    tabs: [
                      _buildTab('Cumulative P&L', const Color(0xFF22C55E), true,
                          isDarkMode),
                      _buildTab('Drawdown', const Color(0xFFEF4444), false,
                          isDarkMode),
                      _buildTab('Equity Curve', const Color(0xFF3B82F6), false,
                          isDarkMode),
                      _buildTab('Volatility', const Color(0xFF8B5CF6), false,
                          isDarkMode),
                      _buildTab('Risk-Adjusted Return', const Color(0xFFF59E0B),
                          false, isDarkMode),
                      _buildTab('Calmar Ratio', const Color(0xFF10B981), false,
                          isDarkMode),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 24),
            // Chart Container
            SizedBox(
              height: 380,
              child: TabBarView(
                controller: _tabController,
                children: [
                  ClipRect(
                    child: _buildChartWithDescription(
                      isDarkMode,
                      'Cumulative P&L',
                      const Color(0xFF22C55E),
                      cumulativePnLSpots,
                      isCurrency: true,
                    ),
                  ),
                  ClipRect(
                    child: _buildChartWithDescription(
                      isDarkMode,
                      'Drawdown',
                      const Color(0xFFEF4444),
                      drawdownSpots,
                      isPercentage: true,
                    ),
                  ),
                  ClipRect(
                    child: _buildChartWithDescription(
                      isDarkMode,
                      'Equity Curve',
                      const Color(0xFF3B82F6),
                      equityCurveSpots,
                      isCurrency: true,
                    ),
                  ),
                  ClipRect(
                    child: _buildChartWithDescription(
                      isDarkMode,
                      'Volatility',
                      const Color(0xFF8B5CF6),
                      volatilitySpots,
                      isPercentage: true,
                    ),
                  ),
                  ClipRect(
                    child: _buildChartWithDescription(
                      isDarkMode,
                      'Risk-Adjusted Return',
                      const Color(0xFF3B82F6),
                      riskAdjustedReturnSpots,
                    ),
                  ),
                  ClipRect(
                    child: _buildChartWithDescription(
                      isDarkMode,
                      'Calmar Ratio',
                      const Color(0xFF22C55E),
                      calmarRatioSpots,
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildChartWithDescription(
    bool isDarkMode,
    String title,
    Color color,
    List<FlSpot> spots, {
    bool isCurrency = false,
    bool isPercentage = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AnimatedCrossFade(
          duration: const Duration(milliseconds: 200),
          crossFadeState: _showDescription
              ? CrossFadeState.showFirst
              : CrossFadeState.showSecond,
          firstChild: Container(
            margin: const EdgeInsets.fromLTRB(8, 0, 8, 16),
            decoration: BoxDecoration(
              color: isDarkMode
                  ? Colors.white.withOpacity(0.03)
                  : Colors.black.withOpacity(0.02),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isDarkMode
                    ? Colors.white.withOpacity(0.05)
                    : Colors.black.withOpacity(0.05),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            _getTabIcon(title),
                            color: color.withOpacity(0.5),
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'About this metric',
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w600,
                              color:
                                  isDarkMode ? Colors.white70 : Colors.black87,
                            ),
                          ),
                          const Spacer(),
                          IconButton(
                            onPressed: () {
                              setState(() {
                                _showDescription = false;
                              });
                            },
                            icon: Icon(
                              Icons.remove_circle_outline,
                              size: 20,
                              color: isDarkMode
                                  ? Colors.white.withOpacity(0.5)
                                  : Colors.black.withOpacity(0.3),
                            ),
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                            splashRadius: 16,
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        _getTabDescription(title),
                        style: TextStyle(
                          fontSize: 13,
                          color: isDarkMode ? Colors.white60 : Colors.black54,
                          height: 1.5,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          secondChild: Padding(
            padding: const EdgeInsets.fromLTRB(8, 0, 8, 16),
            child: TextButton(
              onPressed: () {
                setState(() {
                  _showDescription = true;
                });
              },
              style: TextButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                backgroundColor: isDarkMode
                    ? Colors.white.withOpacity(0.03)
                    : Colors.black.withOpacity(0.02),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 16,
                    color: isDarkMode ? Colors.white60 : Colors.black54,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Show metric details',
                    style: TextStyle(
                      fontSize: 13,
                      color: isDarkMode ? Colors.white60 : Colors.black54,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        Expanded(
          child: _buildLineChart(
            isDarkMode,
            title,
            color,
            true,
            spots,
            isCurrency: isCurrency,
            isPercentage: isPercentage,
          ),
        ),
      ],
    );
  }

  Widget _buildTab(String text, Color color, bool isFirst, bool isDarkMode) {
    final isSelected = _tabController.index == _getTabIndex(text);

    return Tab(
      height: 36,
      child: Container(
        margin: EdgeInsets.only(right: 4),
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFF3B82F6).withOpacity(0.08)
              : (isDarkMode
                  ? Colors.white.withOpacity(0.02)
                  : Colors.black.withOpacity(0.02)),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(8),
            topRight: Radius.circular(8),
          ),
          border: Border(
            top: BorderSide(
              color: isSelected
                  ? const Color(0xFF3B82F6).withOpacity(0.2)
                  : (isDarkMode
                      ? Colors.white.withOpacity(0.05)
                      : Colors.black.withOpacity(0.05)),
              width: 1,
            ),
            left: BorderSide(
              color: isSelected
                  ? const Color(0xFF3B82F6).withOpacity(0.2)
                  : (isDarkMode
                      ? Colors.white.withOpacity(0.05)
                      : Colors.black.withOpacity(0.05)),
              width: 1,
            ),
            right: BorderSide(
              color: isSelected
                  ? const Color(0xFF3B82F6).withOpacity(0.2)
                  : (isDarkMode
                      ? Colors.white.withOpacity(0.05)
                      : Colors.black.withOpacity(0.05)),
              width: 1,
            ),
          ),
        ),
        child: Container(
          padding: EdgeInsets.only(
            left: isFirst ? 48 : 16,
            right: 16,
            top: 8,
            bottom: 8,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                _getTabIcon(text),
                color: isSelected ? color : color.withOpacity(0.5),
                size: 18,
              ),
              const SizedBox(width: 8),
              Text(
                text,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  height: 1.2,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  int _getTabIndex(String text) {
    switch (text) {
      case 'Cumulative P&L':
        return 0;
      case 'Drawdown':
        return 1;
      case 'Equity Curve':
        return 2;
      case 'Volatility':
        return 3;
      case 'Risk-Adjusted Return':
        return 4;
      case 'Calmar Ratio':
        return 5;
      default:
        return 0;
    }
  }

  IconData _getTabIcon(String text) {
    switch (text) {
      case 'Cumulative P&L':
        return Icons.trending_up;
      case 'Drawdown':
        return Icons.trending_down;
      case 'Equity Curve':
        return Icons.show_chart;
      case 'Volatility':
        return Icons.ssid_chart;
      case 'Risk-Adjusted Return':
        return Icons.analytics;
      case 'Calmar Ratio':
        return Icons.assessment;
      default:
        return Icons.show_chart;
    }
  }

  String _getTabDescription(String text) {
    switch (text) {
      case 'Cumulative P&L':
        return 'Track your total profit and loss over time\n'
            'Visualize your account growth trajectory\n'
            'Identify periods of consistent profitability';
      case 'Drawdown':
        return 'Monitor peak-to-trough decline percentages\n'
            'Assess risk management effectiveness\n'
            'Identify periods of capital preservation';
      case 'Equity Curve':
        return 'View your account balance progression\n'
            'Analyze trading system performance\n'
            'Spot trends in capital growth';
      case 'Volatility':
        return 'Measure trading return fluctuations\n'
            'Gauge risk levels in your strategy\n'
            'Compare stability across time periods';
      case 'Risk-Adjusted Return':
        return 'Evaluate returns relative to risk taken\n'
            'Compare performance to benchmarks\n'
            'Assess strategy efficiency';
      case 'Calmar Ratio':
        return 'Compare returns to maximum drawdown\n'
            'Evaluate risk-adjusted performance\n'
            'Measure strategy robustness';
      default:
        return '';
    }
  }

  Widget _buildLineChart(
    bool isDarkMode,
    String title,
    Color color,
    bool showArea,
    List<FlSpot> spots, {
    bool isCurrency = false,
    bool isPercentage = false,
  }) {
    // Determine appropriate interval based on the chart type and data range
    double determineInterval() {
      if (spots.isEmpty) return 1.0;

      final maxY = spots.reduce((max, spot) => spot.y > max.y ? spot : max).y;
      final minY = spots.reduce((min, spot) => spot.y < min.y ? spot : min).y;
      final range = maxY - minY;

      if (title == 'Drawdown') {
        return range <= 10
            ? 2.0
            : range <= 20
                ? 5.0
                : 10.0;
      } else if (title == 'Volatility') {
        return range <= 10
            ? 2.0
            : range <= 20
                ? 4.0
                : 5.0;
      } else if (title == 'Calmar Ratio') {
        return range <= 1
            ? 0.2
            : range <= 2
                ? 0.5
                : 1.0;
      } else if (isCurrency) {
        // Adjust currency intervals based on range
        if (range <= 1000) return 200;
        if (range <= 5000) return 1000;
        if (range <= 10000) return 2000;
        return 5000;
      } else if (isPercentage) {
        return range <= 20 ? 5 : 10;
      }
      return 0.5;
    }

    // Determine Y-axis range with better padding
    double determineMinY() {
      if (spots.isEmpty) return 0;
      final minY = spots.reduce((min, spot) => spot.y < min.y ? spot : min).y;

      if (title == 'Drawdown') {
        // For drawdown, ensure we show negative values properly
        return math.min(-5.0, minY * 1.1);
      }
      if (title == 'Volatility' || title == 'Calmar Ratio') return 0;

      // Add 10% padding below the minimum
      return minY - (minY.abs() * 0.1);
    }

    double determineMaxY() {
      if (spots.isEmpty) {
        if (title == 'Drawdown') return 5;
        if (title == 'Volatility') return 20;
        if (title == 'Calmar Ratio') return 5;
        return 10;
      }

      final maxY = spots.reduce((max, spot) => spot.y > max.y ? spot : max).y;

      if (title == 'Drawdown') {
        return math.max(5.0, maxY * 1.1);
      }
      if (title == 'Volatility') {
        return math.min(100, maxY * 1.2);
      }
      if (title == 'Calmar Ratio') {
        // Improved Calmar Ratio scaling
        final cappedMax = math.min(10.0, maxY);
        return (cappedMax * 1.2 / 0.5).ceil() * 0.5;
      }

      // Add 10% padding above the maximum
      return maxY + (maxY.abs() * 0.1);
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: LineChart(
        LineChartData(
          gridData: FlGridData(
            show: true,
            drawVerticalLine: false,
            horizontalInterval: determineInterval(),
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: isDarkMode
                    ? Colors.white.withOpacity(0.05)
                    : Colors.black.withOpacity(0.05),
                strokeWidth: 1,
              );
            },
          ),
          titlesData: FlTitlesData(
            show: true,
            rightTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            topTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
                interval: 1,
                getTitlesWidget: (value, meta) {
                  if (value == 8.5) return const SizedBox.shrink();
                  final date = DateTime.now()
                      .subtract(Duration(days: (8 - value.toInt()) * 30));
                  return SideTitleWidget(
                    axisSide: meta.axisSide,
                    child: Text(
                      '${date.month}/${date.day}',
                      style: TextStyle(
                        color: isDarkMode
                            ? Colors.white.withOpacity(0.5)
                            : Colors.black.withOpacity(0.5),
                        fontSize: 10,
                      ),
                    ),
                  );
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 50,
                interval: determineInterval(),
                getTitlesWidget: (value, meta) {
                  String text;
                  if (isCurrency) {
                    text = value >= 1000 || value <= -1000
                        ? '\$${(value / 1000).toStringAsFixed(1)}K'
                        : '\$${value.toInt()}';
                  } else if (isPercentage || title == 'Drawdown') {
                    text = '${value.toStringAsFixed(1)}%';
                  } else {
                    text = value.toStringAsFixed(1);
                  }
                  return SideTitleWidget(
                    axisSide: meta.axisSide,
                    child: Text(
                      text,
                      style: TextStyle(
                        color: isDarkMode
                            ? Colors.white.withOpacity(0.5)
                            : Colors.black.withOpacity(0.5),
                        fontSize: 10,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
          borderData: FlBorderData(
            show: true,
            border: Border(
              bottom: BorderSide(
                color: isDarkMode
                    ? Colors.white.withOpacity(0.05)
                    : Colors.black.withOpacity(0.05),
              ),
              left: BorderSide(
                color: isDarkMode
                    ? Colors.white.withOpacity(0.05)
                    : Colors.black.withOpacity(0.05),
              ),
            ),
          ),
          minX: 0,
          maxX: 8.5,
          minY: determineMinY(),
          maxY: determineMaxY(),
          lineBarsData: [
            LineChartBarData(
              spots: spots,
              isCurved: true,
              color: color,
              barWidth: 2,
              isStrokeCapRound: true,
              dotData: const FlDotData(show: false),
              belowBarData: showArea
                  ? BarAreaData(
                      show: true,
                      gradient: LinearGradient(
                        colors: [
                          color.withOpacity(0.2),
                          color.withOpacity(0.0),
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                    )
                  : null,
            ),
          ],
          lineTouchData: LineTouchData(
            enabled: true,
            touchTooltipData: LineTouchTooltipData(
              tooltipBgColor:
                  isDarkMode ? const Color(0xFF2D3748) : Colors.white,
              tooltipBorder: BorderSide(
                color: isDarkMode ? Colors.white24 : Colors.black12,
              ),
              tooltipRoundedRadius: 8,
              tooltipPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
              getTooltipItems: (spots) {
                return spots.map((spot) {
                  final date = DateTime.now()
                      .subtract(Duration(days: (6 - spot.x.toInt()) * 30));
                  final dateStr = '${date.month}/${date.day}';
                  final value = isCurrency
                      ? '\$${spot.y.toInt()}'
                      : isPercentage
                          ? '${spot.y.toStringAsFixed(1)}%'
                          : spot.y.toStringAsFixed(2);
                  return LineTooltipItem(
                    '$dateStr\n$value',
                    TextStyle(
                      color: isDarkMode ? Colors.white : Colors.black,
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                      height: 1.4,
                    ),
                  );
                }).toList();
              },
            ),
            getTouchedSpotIndicator: (barData, spotIndexes) {
              return spotIndexes.map((spotIndex) {
                return TouchedSpotIndicatorData(
                  FlLine(
                    color: Colors.transparent,
                  ),
                  FlDotData(
                    show: true,
                    getDotPainter: (spot, percent, barData, index) {
                      return FlDotCirclePainter(
                        radius: 4,
                        color: color,
                        strokeWidth: 2,
                        strokeColor: isDarkMode ? Colors.white : Colors.white,
                      );
                    },
                  ),
                );
              }).toList();
            },
            touchCallback: (event, response) {
              if (response?.lineBarSpots != null) {
                // Handle touch event if needed
              }
            },
          ),
        ),
      ),
    );
  }

  Widget _buildMetricItem(String label, String value, bool isDarkMode) {
    Color getMetricColor() {
      switch (label) {
        case 'Avg Win/Loss':
          return const Color(0xFF22C55E); // Green
        case 'Trade Count':
          return const Color(0xFF3B82F6); // Blue
        case 'Net P&L':
          return const Color(0xFFF59E0B); // Orange
        case 'Daily Win Rate':
          return const Color(0xFF8B5CF6); // Purple
        case 'Average Trade':
          return const Color(0xFF10B981); // Teal
        default:
          return const Color(0xFF3B82F6);
      }
    }

    final color = getMetricColor();

    return Container(
      margin: const EdgeInsets.only(right: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E293B) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDarkMode ? Colors.white10 : Colors.black.withOpacity(0.05),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                  color: isDarkMode ? Colors.white60 : Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? Colors.white : const Color(0xFF1F2937),
            ),
          ),
        ],
      ),
    );
  }

  _MetricsData _calculateMetrics(
      List<Trade> trades, String? selectedAccountId, double? initialCapital) {
    if (trades.isEmpty) {
      return _MetricsData(
        avgWin: 0,
        avgLoss: 0,
        tradeCount: 0,
        netPnL: 0,
        winRate: 0,
        avgTrade: 0,
      );
    }

    // Calculate win rate based on filtered trades
    final winningTrades = trades.where((t) => t.calculatePnL() > 0).toList();
    final losingTrades = trades.where((t) => t.calculatePnL() < 0).toList();

    // Group trades by day
    final dailyResults = <DateTime, bool>{};
    for (final trade in trades) {
      final date = DateTime(
        trade.exitDate?.year ?? trade.entryDate.year,
        trade.exitDate?.month ?? trade.entryDate.month,
        trade.exitDate?.day ?? trade.entryDate.day,
      );

      // For each day, track if it's a winning day (all trades positive)
      if (dailyResults.containsKey(date)) {
        dailyResults[date] = dailyResults[date]! && trade.calculatePnL() > 0;
      } else {
        dailyResults[date] = trade.calculatePnL() > 0;
      }
    }

    final winningDays = dailyResults.values.where((isWin) => isWin).length;
    final winRate = dailyResults.isEmpty
        ? 0.0
        : (winningDays / dailyResults.length * 100).toDouble();

    final avgWin = winningTrades.isEmpty
        ? 0.0
        : (winningTrades
                    .map((t) => t.calculatePnL())
                    .fold<double>(0, (a, b) => a + b.toDouble()) /
                winningTrades.length)
            .toDouble();

    final avgLoss = losingTrades.isEmpty
        ? 0.0
        : (losingTrades
                    .map((t) => t.calculatePnL())
                    .fold<double>(0, (a, b) => a + b.toDouble())
                    .abs() /
                losingTrades.length)
            .toDouble();

    final netPnL = trades.isEmpty
        ? 0.0
        : trades
            .map((t) => t.calculatePnL())
            .fold<double>(0, (a, b) => a + b.toDouble());

    final avgTrade = trades.isEmpty ? 0.0 : (netPnL / trades.length).toDouble();

    return _MetricsData(
      avgWin: avgWin,
      avgLoss: avgLoss,
      tradeCount: trades.length,
      netPnL: netPnL,
      winRate: winRate,
      avgTrade: avgTrade,
    );
  }

  List<FlSpot> _calculateCumulativePnLSpots(List<Trade> trades) {
    if (trades.isEmpty) return [];

    double cumulativePnL = 0;
    final spots = <FlSpot>[];

    try {
      for (var i = 0; i < trades.length; i++) {
        cumulativePnL += trades[i].calculatePnL().toDouble();
        spots.add(FlSpot(i.toDouble(), cumulativePnL));
      }
    } catch (e) {
      // If any calculation fails, return empty list
      return [];
    }

    return spots;
  }

  List<FlSpot> _calculateDrawdownSpots(List<Trade> trades) {
    if (trades.isEmpty) return [];

    double peak = 0;
    double currentBalance = 0;
    final spots = <FlSpot>[];

    try {
      for (var i = 0; i < trades.length; i++) {
        currentBalance += trades[i].calculatePnL().toDouble();
        peak = currentBalance > peak ? currentBalance : peak;

        final drawdown =
            peak > 0 ? ((peak - currentBalance) / peak * 100).toDouble() : 0.0;
        spots.add(FlSpot(i.toDouble(), drawdown));
      }
    } catch (e) {
      // If any calculation fails, return empty list
      return [];
    }

    return spots;
  }

  List<FlSpot> _calculateEquityCurveSpots(List<Trade> trades) {
    if (trades.isEmpty) return [];

    double balance = 10000; // Starting balance
    final spots = <FlSpot>[];
    spots.add(FlSpot(0.0, balance.toDouble())); // Initial point

    try {
      for (var i = 0; i < trades.length; i++) {
        balance += trades[i].calculatePnL().toDouble();
        spots.add(FlSpot((i + 1).toDouble(), balance));
      }
    } catch (e) {
      // If any calculation fails, return list with only initial point
      return [FlSpot(0.0, 10000.0)];
    }

    return spots;
  }

  List<FlSpot> _calculateVolatilitySpots(List<Trade> trades) {
    if (trades.isEmpty) return [];

    final spots = <FlSpot>[];
    const windowSize = 20; // Rolling window size for volatility calculation

    for (var i = 0; i < trades.length; i++) {
      final startIdx = i >= windowSize ? i - windowSize + 1 : 0;
      final windowTrades = trades.sublist(startIdx, i + 1);

      if (windowTrades.isEmpty || windowTrades.length < 2) {
        spots.add(FlSpot(i.toDouble(), 0.0));
        continue;
      }

      try {
        // Calculate returns as percentage changes
        final returns = <double>[];
        double prevBalance = 10000.0; // Starting balance

        for (final trade in windowTrades) {
          final pnl = trade.calculatePnL().toDouble();
          final returnPct = (pnl / prevBalance) * 100;
          returns.add(returnPct);
          prevBalance += pnl;
        }

        final volatility = _calculateStandardDeviation(returns);
        // Annualized volatility (approximately)
        final annualizedVolatility = volatility * math.sqrt(252 / windowSize);
        spots.add(FlSpot(i.toDouble(), annualizedVolatility));
      } catch (e) {
        spots.add(FlSpot(i.toDouble(), 0.0));
      }
    }

    return spots;
  }

  List<FlSpot> _calculateRiskAdjustedReturnSpots(List<Trade> trades) {
    if (trades.isEmpty) return [];

    final spots = <FlSpot>[];
    const windowSize = 20;

    for (var i = 0; i < trades.length; i++) {
      final startIdx = i >= windowSize ? i - windowSize + 1 : 0;
      final windowTrades = trades.sublist(startIdx, i + 1);

      if (windowTrades.isEmpty) {
        spots.add(FlSpot(i.toDouble(), 0.0));
        continue;
      }

      final returns =
          windowTrades.map((t) => t.calculatePnL().toDouble()).toList();

      if (returns.isEmpty) {
        spots.add(FlSpot(i.toDouble(), 0.0));
        continue;
      }

      final avgReturn =
          returns.fold<double>(0.0, (a, b) => a + b) / returns.length;
      final volatility = _calculateStandardDeviation(returns);

      // Sharpe-like ratio (without risk-free rate)
      final riskAdjustedReturn =
          volatility != 0 ? (avgReturn / volatility).toDouble() : 0.0;
      spots.add(FlSpot(i.toDouble(), riskAdjustedReturn));
    }

    return spots;
  }

  List<FlSpot> _calculateCalmarRatioSpots(List<Trade> trades) {
    if (trades.isEmpty) return [];

    final spots = <FlSpot>[];
    const windowSize = 20;

    // Use a reasonable default initial capital if not provided
    double initialCapital = 10000.0;

    for (var i = 0; i < trades.length; i++) {
      final startIdx = i >= windowSize ? i - windowSize + 1 : 0;
      final windowTrades = trades.sublist(startIdx, i + 1);

      if (windowTrades.isEmpty || windowTrades.length < 2) {
        spots.add(FlSpot(i.toDouble(), 0.0));
        continue;
      }

      try {
        // Calculate returns and balances
        double balance = initialCapital;
        double peak = balance;
        double maxDrawdownPct = 0.0;
        double totalReturn = 0.0;

        for (final trade in windowTrades) {
          final pnl = trade.calculatePnL().toDouble();
          balance += pnl;
          totalReturn += pnl;

          peak = math.max(peak, balance);
          final drawdownPct = peak > 0 ? ((peak - balance) / peak) * 100 : 0.0;
          maxDrawdownPct = math.max(maxDrawdownPct, drawdownPct);
        }

        // Calculate annualized return using actual time window
        final returnPct = (totalReturn / initialCapital) * 100;
        final daysInWindow = windowTrades.last.exitDate
                ?.difference(windowTrades.first.entryDate)
                .inDays ??
            windowTrades.last.entryDate
                .difference(windowTrades.first.entryDate)
                .inDays;
        final annualizedReturn = returnPct * (252 / math.max(daysInWindow, 1));

        // Calculate Calmar ratio (annualized return / maximum drawdown)
        // Cap at 10.0 for better visualization while maintaining meaningful scale
        final calmarRatio = maxDrawdownPct > 0
            ? math.min(10.0, annualizedReturn / maxDrawdownPct)
            : 0.0;
        spots.add(FlSpot(i.toDouble(), calmarRatio));
      } catch (e) {
        spots.add(FlSpot(i.toDouble(), 0.0));
      }
    }

    return spots;
  }

  double _calculateStandardDeviation(List<double> values) {
    if (values.isEmpty) return 0.0;
    if (values.length == 1) return 0.0;

    try {
      final mean = values.fold<double>(0.0, (a, b) => a + b) / values.length;
      final squaredDiffs =
          values.map((x) => ((x - mean) * (x - mean)).toDouble()).toList();

      if (squaredDiffs.isEmpty) return 0.0;

      final variance =
          squaredDiffs.fold<double>(0.0, (a, b) => a + b) / values.length;

      // Manual implementation of square root using Newton's method
      if (variance <= 0) return 0.0;

      double x = variance;
      double root = variance / 2.0;

      // A few iterations are usually enough for good precision
      for (int i = 0; i < 10; i++) {
        if (root == 0.0) break;
        root = (root + x / root) / 2.0;
      }

      return root;
    } catch (e) {
      return 0.0;
    }
  }
}

class _MetricsData {
  final double avgWin;
  final double avgLoss;
  final int tradeCount;
  final double netPnL;
  final double winRate;
  final double avgTrade;

  _MetricsData({
    required this.avgWin,
    required this.avgLoss,
    required this.tradeCount,
    required this.netPnL,
    required this.winRate,
    required this.avgTrade,
  });
}
