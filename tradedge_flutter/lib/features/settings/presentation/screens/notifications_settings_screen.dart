import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/notification_provider.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart'
    show
        Settings,
        NotificationSettings,
        ReminderFrequency,
        NotificationPriority,
        NotificationType,
        NotificationChannel;
import '../../../../core/providers/auth_provider.dart';

class NotificationsSettingsScreen extends ConsumerStatefulWidget {
  const NotificationsSettingsScreen({super.key});

  @override
  ConsumerState<NotificationsSettingsScreen> createState() =>
      _NotificationsSettingsScreenState();
}

class _NotificationsSettingsScreenState
    extends ConsumerState<NotificationsSettingsScreen> {
  bool _isLoading = true;
  Settings? _settings;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final settingsService = ref.read(settingsServiceProvider);
      final settings = await settingsService.getSettings();
      setState(() {
        _settings = settings;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading settings: $e');
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _updateNotificationSettings(
      NotificationSettings newSettings) async {
    if (_settings == null) return;

    try {
      final settingsService = ref.read(settingsServiceProvider);
      final updatedSettings =
          await settingsService.updateNotificationSettings(newSettings);
      setState(() => _settings = updatedSettings);
    } catch (e) {
      print('Error updating notification settings: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to update notification settings'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _sendTestNotification() async {
    try {
      final notificationService = ref.read(notificationServiceProvider);
      final userId = ref.read(currentUserIdProvider);
      final now = DateTime.now();

      await notificationService.addNotification(
        userId: userId,
        title: 'Test Notification',
        message:
            'This is a test notification to verify the notification system is working.',
        recipient: '<EMAIL>',
        priority: NotificationPriority.medium,
        type: NotificationType.systemUpdate,
        channels: [NotificationChannel.inApp],
        expiresAt: now.add(const Duration(minutes: 5)),
        data: {
          'test': true,
          'timestamp': now.toIso8601String(),
        },
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Test notification sent!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      print('Error sending test notification: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sending test notification: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<void> _runNotificationTests() async {
    try {
      final notificationService = ref.read(notificationServiceProvider);
      final scheduler = ref.read(notificationSchedulerProvider);
      final userId = ref.read(currentUserIdProvider);
      final now = DateTime.now();

      // Test 1: Basic notification
      await notificationService.addNotification(
        userId: userId,
        title: 'Test Notification',
        message: 'Basic test notification',
        recipient: '<EMAIL>',
        priority: NotificationPriority.medium,
        type: NotificationType.systemUpdate,
        channels: [NotificationChannel.inApp],
      );

      // Test 2: High priority trade alert
      await notificationService.addNotification(
        userId: userId,
        title: 'Urgent Trade Alert',
        message: 'AAPL has reached your stop loss',
        recipient: '<EMAIL>',
        priority: NotificationPriority.high,
        type: NotificationType.tradeAlert,
        channels: [NotificationChannel.inApp],
        data: {
          'symbol': 'AAPL',
          'price': 150.0,
        },
      );

      // Test 3: Scheduled notification (10 seconds)
      await scheduler.scheduleTradeAlert(
        title: 'Scheduled Alert',
        message: 'This alert was scheduled for 10 seconds',
        scheduledDate: now.add(const Duration(seconds: 10)),
      );

      // Test 4: Risk warning (urgent priority)
      await notificationService.addNotification(
        userId: userId,
        title: 'Risk Warning',
        message: 'Position size exceeds 2% account risk',
        recipient: '<EMAIL>',
        priority: NotificationPriority.urgent,
        type: NotificationType.riskWarning,
        channels: [NotificationChannel.inApp],
      );

      // Test 5: Multiple channel notification
      await notificationService.addNotification(
        userId: userId,
        title: 'Multi-Channel Test',
        message: 'This notification uses multiple channels',
        recipient: '<EMAIL>',
        priority: NotificationPriority.medium,
        type: NotificationType.systemUpdate,
        channels: [
          NotificationChannel.inApp,
          NotificationChannel.push,
          NotificationChannel.email,
        ],
      );

      // Test 6: Short expiry notification
      await notificationService.addNotification(
        userId: userId,
        title: 'Quick Expiry',
        message: 'This notification expires in 30 seconds',
        recipient: '<EMAIL>',
        priority: NotificationPriority.low,
        type: NotificationType.systemUpdate,
        channels: [NotificationChannel.inApp],
        expiresAt: now.add(const Duration(seconds: 30)),
      );

      // Test 7: Schedule multiple notifications
      for (var i = 1; i <= 3; i++) {
        await scheduler.scheduleTradeStats(
          title: 'Stats Update $i',
          message: 'Scheduled stats update $i',
          scheduledDate: now.add(Duration(seconds: i * 5)),
          stats: {
            'win_rate': 0.65 + (i * 0.1),
            'profit_factor': 1.5 + (i * 0.2),
          },
        );
      }

      // Force refresh the notifications list
      ref.invalidate(activeNotificationsProvider);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content:
                Text('Test notifications sent! Check the notification bell.'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      print('Error running notification tests: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error running notification tests: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Widget _buildSettingsSection(String title, List<Widget> children) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: theme.cardColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: theme.dividerColor.withOpacity(0.2),
            ),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return ListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Switch.adaptive(
        value: value,
        onChanged: onChanged,
        activeColor: AppColors.primaryBlue,
      ),
    );
  }

  Widget _buildSelectionTile({
    required String title,
    required String subtitle,
    required String value,
    required List<Map<String, String>> options,
    required Function(String?) onChanged,
  }) {
    return ListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: DropdownButton<String>(
        value: value,
        items: options.map((option) {
          return DropdownMenuItem(
            value: option['value'],
            child: Text(option['label'] ?? ''),
          );
        }).toList(),
        onChanged: onChanged,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_settings == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Notifications'),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text('Settings not available'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadSettings,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    final notificationSettings = _settings!.notifications;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Notifications'),
        actions: [
          IconButton(
            icon: const Icon(Icons.science),
            tooltip: 'Run Notification Tests',
            onPressed: _runNotificationTests,
          ),
          IconButton(
            icon: const Icon(Icons.send),
            tooltip: 'Send Test Notification',
            onPressed: _sendTestNotification,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Global Settings
            _buildSettingsSection(
              'Global Settings',
              [
                _buildSwitchTile(
                  title: 'Enable Notifications',
                  subtitle: 'Turn all notifications on/off',
                  value: notificationSettings.enabled,
                  onChanged: (value) {
                    _updateNotificationSettings(
                      notificationSettings.copyWith(enabled: value),
                    );
                  },
                ),
                const Divider(height: 1),
                _buildSwitchTile(
                  title: 'Sound',
                  subtitle: 'Play sound for notifications',
                  value: notificationSettings.soundEnabled,
                  onChanged: (value) {
                    _updateNotificationSettings(
                      notificationSettings.copyWith(soundEnabled: value),
                    );
                  },
                ),
                const Divider(height: 1),
                _buildSwitchTile(
                  title: 'Vibration',
                  subtitle: 'Vibrate for notifications',
                  value: notificationSettings.hapticEnabled,
                  onChanged: (value) {
                    _updateNotificationSettings(
                      notificationSettings.copyWith(hapticEnabled: value),
                    );
                  },
                ),
              ],
            ),
            const SizedBox(height: 32),

            // Delivery Methods
            _buildSettingsSection(
              'Delivery Methods',
              [
                _buildSwitchTile(
                  title: 'In-App Notifications',
                  subtitle: 'Show notifications within the app',
                  value: notificationSettings.channels
                      .contains(NotificationChannel.inApp),
                  onChanged: (value) {
                    final channels = List<NotificationChannel>.from(
                        notificationSettings.channels);
                    if (value) {
                      channels.add(NotificationChannel.inApp);
                    } else {
                      channels.remove(NotificationChannel.inApp);
                    }
                    _updateNotificationSettings(
                      notificationSettings.copyWith(channels: channels),
                    );
                  },
                ),
                const Divider(height: 1),
                _buildSwitchTile(
                  title: 'Push Notifications',
                  subtitle: 'Receive notifications on your device',
                  value: notificationSettings.channels
                      .contains(NotificationChannel.push),
                  onChanged: (value) {
                    final channels = List<NotificationChannel>.from(
                        notificationSettings.channels);
                    if (value) {
                      channels.add(NotificationChannel.push);
                    } else {
                      channels.remove(NotificationChannel.push);
                    }
                    _updateNotificationSettings(
                      notificationSettings.copyWith(channels: channels),
                    );
                  },
                ),
                const Divider(height: 1),
                _buildSwitchTile(
                  title: 'Email Notifications',
                  subtitle: 'Receive notifications via email',
                  value: notificationSettings.channels
                      .contains(NotificationChannel.email),
                  onChanged: (value) {
                    final channels = List<NotificationChannel>.from(
                        notificationSettings.channels);
                    if (value) {
                      channels.add(NotificationChannel.email);
                    } else {
                      channels.remove(NotificationChannel.email);
                    }
                    _updateNotificationSettings(
                      notificationSettings.copyWith(channels: channels),
                    );
                  },
                ),
              ],
            ),
            const SizedBox(height: 32),

            // Notification Types
            _buildSettingsSection(
              'Notification Types',
              [
                _buildSwitchTile(
                  title: 'Trade Alerts',
                  subtitle:
                      'Get notified about trade executions and opportunities',
                  value: notificationSettings.enabledTypes
                      .contains(NotificationType.tradeAlert),
                  onChanged: (value) {
                    final types = List<NotificationType>.from(
                        notificationSettings.enabledTypes);
                    if (value) {
                      types.add(NotificationType.tradeAlert);
                    } else {
                      types.remove(NotificationType.tradeAlert);
                    }
                    _updateNotificationSettings(
                      notificationSettings.copyWith(enabledTypes: types),
                    );
                  },
                ),
                const Divider(height: 1),
                _buildSwitchTile(
                  title: 'Price Alerts',
                  subtitle: 'Get notified about price movements',
                  value: notificationSettings.enabledTypes
                      .contains(NotificationType.priceAlert),
                  onChanged: (value) {
                    final types = List<NotificationType>.from(
                        notificationSettings.enabledTypes);
                    if (value) {
                      types.add(NotificationType.priceAlert);
                    } else {
                      types.remove(NotificationType.priceAlert);
                    }
                    _updateNotificationSettings(
                      notificationSettings.copyWith(enabledTypes: types),
                    );
                  },
                ),
                const Divider(height: 1),
                _buildSwitchTile(
                  title: 'Goal Alerts',
                  subtitle: 'Get notified about goal achievements',
                  value: notificationSettings.enabledTypes
                      .contains(NotificationType.goalAchieved),
                  onChanged: (value) {
                    final types = List<NotificationType>.from(
                        notificationSettings.enabledTypes);
                    if (value) {
                      types.add(NotificationType.goalAchieved);
                    } else {
                      types.remove(NotificationType.goalAchieved);
                    }
                    _updateNotificationSettings(
                      notificationSettings.copyWith(enabledTypes: types),
                    );
                  },
                ),
                const Divider(height: 1),
                _buildSwitchTile(
                  title: 'Risk Warnings',
                  subtitle: 'Get notified about risk management issues',
                  value: notificationSettings.enabledTypes
                      .contains(NotificationType.riskWarning),
                  onChanged: (value) {
                    final types = List<NotificationType>.from(
                        notificationSettings.enabledTypes);
                    if (value) {
                      types.add(NotificationType.riskWarning);
                    } else {
                      types.remove(NotificationType.riskWarning);
                    }
                    _updateNotificationSettings(
                      notificationSettings.copyWith(enabledTypes: types),
                    );
                  },
                ),
                const Divider(height: 1),
                _buildSwitchTile(
                  title: 'System Updates',
                  subtitle: 'Get notified about system maintenance and updates',
                  value: notificationSettings.enabledTypes
                      .contains(NotificationType.systemUpdate),
                  onChanged: (value) {
                    final types = List<NotificationType>.from(
                        notificationSettings.enabledTypes);
                    if (value) {
                      types.add(NotificationType.systemUpdate);
                    } else {
                      types.remove(NotificationType.systemUpdate);
                    }
                    _updateNotificationSettings(
                      notificationSettings.copyWith(enabledTypes: types),
                    );
                  },
                ),
                const Divider(height: 1),
                _buildSwitchTile(
                  title: 'Journal Reminders',
                  subtitle: 'Get reminded to update your trading journal',
                  value: notificationSettings.enabledTypes
                      .contains(NotificationType.journalReminder),
                  onChanged: (value) {
                    final types = List<NotificationType>.from(
                        notificationSettings.enabledTypes);
                    if (value) {
                      types.add(NotificationType.journalReminder);
                    } else {
                      types.remove(NotificationType.journalReminder);
                    }
                    _updateNotificationSettings(
                      notificationSettings.copyWith(enabledTypes: types),
                    );
                  },
                ),
                const Divider(height: 1),
                _buildSwitchTile(
                  title: 'Trading Statistics',
                  subtitle: 'Get notified about your trading performance',
                  value: notificationSettings.enabledTypes
                      .contains(NotificationType.tradeStats),
                  onChanged: (value) {
                    final types = List<NotificationType>.from(
                        notificationSettings.enabledTypes);
                    if (value) {
                      types.add(NotificationType.tradeStats);
                    } else {
                      types.remove(NotificationType.tradeStats);
                    }
                    _updateNotificationSettings(
                      notificationSettings.copyWith(enabledTypes: types),
                    );
                  },
                ),
              ],
            ),
            const SizedBox(height: 32),

            // Priority Settings
            _buildSettingsSection(
              'Priority Settings',
              [
                _buildSelectionTile(
                  title: 'Default Priority',
                  subtitle: 'Set the default priority for notifications',
                  value: notificationSettings.defaultPriority
                      .toString()
                      .split('.')
                      .last,
                  options: [
                    {'value': 'low', 'label': 'Low'},
                    {'value': 'medium', 'label': 'Medium'},
                    {'value': 'high', 'label': 'High'},
                    {'value': 'urgent', 'label': 'Urgent'},
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      final priority = NotificationPriority.values.firstWhere(
                        (p) => p.toString().split('.').last == value,
                      );
                      _updateNotificationSettings(
                        notificationSettings.copyWith(
                            defaultPriority: priority),
                      );
                    }
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
