import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_flutter/core/models/account_settings.dart';
import 'package:trade_reviewer_flutter/core/models/settings.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart' as core;
import 'package:dio/dio.dart';
import 'dart:io';
import 'dart:convert';
import 'package:intl/intl.dart';
import 'package:trade_reviewer_flutter/core/theme/app_icons.dart';
import 'package:trade_reviewer_flutter/core/theme/app_logo.dart';

class TestConnectionResult {
  final String status;
  final String accountType;
  final List<String> availableData;
  final List<Map<String, dynamic>> availableAccounts;

  TestConnectionResult({
    required this.status,
    required this.accountType,
    required this.availableData,
    this.availableAccounts = const [],
  });
}

class AccountsSettingsScreen extends ConsumerStatefulWidget {
  const AccountsSettingsScreen({super.key});

  @override
  ConsumerState<AccountsSettingsScreen> createState() =>
      _AccountsSettingsScreenState();
}

class _AccountsSettingsScreenState
    extends ConsumerState<AccountsSettingsScreen> {
  late core.SettingsService _settingsService;
  late Settings _settings;
  bool _isLoading = true;
  final _accountIdController = TextEditingController();
  final _yearController = TextEditingController();
  final _monthController = TextEditingController();
  final _dayController = TextEditingController();
  bool _isResetting = false;
  String? _resetResponse;
  bool _resetSuccess = false;
  String? _reportResponse;
  bool _reportSuccess = false;
  final _reportStartDateController = TextEditingController();
  final _reportEndDateController = TextEditingController();
  late String _environment;

  @override
  void initState() {
    super.initState();
    _isLoading = false;
    _environment = 'demo';
    _settingsService = ref.read(settingsServiceProvider);
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final settings = await _settingsService.getSettings();
      setState(() {
        _settings = Settings.fromCore(settings);
        _isLoading = false;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Error loading settings')),
      );
    }
  }

  Future<void> _handleAccountConnection(TradingAccount account) async {
    try {
      setState(() => _isLoading = true);
      if (account.status == AccountStatus.CONNECTED) {
        await _settingsService.removeTradingAccount(account.id);
      } else {
        await _settingsService.addTradingAccount(account.toCore());
      }
      await _loadSettings();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(
                'Error ${account.status == AccountStatus.CONNECTED ? 'disconnecting' : 'connecting'} account')),
      );
    }
  }

  Future<void> _handleApiKeyAction(String action, ApiKey? apiKey) async {
    try {
      setState(() => _isLoading = true);
      switch (action) {
        case 'add':
          final result = await showDialog<ApiKey>(
            context: context,
            builder: (context) => const _ApiKeyDialog(),
          );
          if (result != null) {
            await _settingsService.addApiKey(result.toCore());
          }
          break;
        case 'edit':
          if (apiKey != null) {
            final result = await showDialog<ApiKey>(
              context: context,
              builder: (context) => _ApiKeyDialog(apiKey: apiKey),
            );
            if (result != null) {
              await _settingsService.updateApiKey(result.toCore());
            }
          }
          break;
        case 'delete':
          if (apiKey != null) {
            final confirm = await showDialog<bool>(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('Delete API Key'),
                content: Text(
                    'Are you sure you want to delete the API key for ${apiKey.name}?'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context, false),
                    child: const Text('Cancel'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.pop(context, true),
                    child: const Text('Delete'),
                  ),
                ],
              ),
            );
            if (confirm == true) {
              await _settingsService.removeApiKey(apiKey.id);
            }
          }
          break;
      }
      await _loadSettings();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Error managing API key')),
      );
    }
  }

  Future<void> _handleTradingAccountAction(
      String action, TradingAccount? account) async {
    try {
      setState(() => _isLoading = true);
      switch (action) {
        case 'add':
          final result = await showDialog<dynamic>(
            context: context,
            builder: (context) => const _TradingAccountDialog(),
          );
          if (result != null) {
            // Handle both single account and multiple accounts
            final accounts = result is List ? result : [result];

            // Add the accounts
            for (final acc in accounts) {
              await _settingsService.addTradingAccount(acc.toCore());
            }
            await ref.refresh(settingsServiceProvider);
            await ref.refresh(tradeServiceProvider);

            // Show success dialog and refresh list
            if (!mounted) return;
            Future.microtask(() {
              if (mounted) {
                _showConnectionResultDialog(
                  context,
                  true,
                  accounts.length == 1
                      ? 'Account "${accounts.first.name}" has been successfully connected.'
                      : '${accounts.length} accounts have been successfully connected.',
                );
                _loadSettings();
              }
            });
          }
          break;
        case 'edit':
          if (account != null) {
            final result = await showDialog<TradingAccount>(
              context: context,
              builder: (context) => _TradingAccountDialog(account: account),
            );
            if (result != null) {
              await _settingsService.updateTradingAccount(result.toCore());
              await ref.refresh(settingsServiceProvider);
              await ref.refresh(tradeServiceProvider);

              // Show success dialog
              if (!mounted) return;
              _showConnectionResultDialog(
                context,
                true,
                'Account "${result.name}" has been successfully updated.',
              );
            }
          }
          break;
        case 'delete':
          if (account != null) {
            final confirm = await showDialog<bool>(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('Delete Trading Account'),
                content: Text(
                    'Are you sure you want to delete the trading account "${account.name}"? This action cannot be undone.'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context, false),
                    child: const Text('Cancel'),
                  ),
                  TextButton(
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.red,
                    ),
                    onPressed: () => Navigator.pop(context, true),
                    child: const Text('Delete'),
                  ),
                ],
              ),
            );
            if (confirm == true) {
              await _settingsService.removeTradingAccount(account.id);
              await ref.refresh(settingsServiceProvider);
              await ref.refresh(tradeServiceProvider);

              // Show success message
              if (!mounted) return;
              _showConnectionResultDialog(
                context,
                true,
                'Account "${account.name}" has been successfully deleted.',
              );

              // Reload settings to refresh the accounts list
              await _loadSettings();
            }
          }
          break;
      }
      await _loadSettings();
    } catch (e) {
      if (!mounted) return;
      _showConnectionResultDialog(
        context,
        false,
        'Error ${action == 'add' ? 'connecting' : action == 'edit' ? 'updating' : 'managing'} account: ${e.toString()}',
      );
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _resetAccount() async {
    if (_accountIdController.text.isEmpty ||
        _yearController.text.isEmpty ||
        _monthController.text.isEmpty ||
        _dayController.text.isEmpty) {
      setState(() {
        _resetResponse = 'Please fill in all fields';
        _resetSuccess = false;
      });
      return;
    }

    setState(() {
      _isResetting = true;
      _resetResponse = null;
    });

    try {
      final dio = Dio();
      final response = await dio.post(
        'https://demo.tradovateapi.com/v1/account/resetdemoaccountstate',
        options: Options(
          headers: {
            'Authorization':
                'Bearer ${_settings.accounts.tradingAccounts.first.credentials['token']}',
            'Content-Type': 'application/json',
          },
        ),
        data: {
          'accountIds': [int.parse(_accountIdController.text)],
          'resetTradeDate': {
            'year': int.parse(_yearController.text),
            'month': int.parse(_monthController.text),
            'day': int.parse(_dayController.text),
          },
        },
      );

      if (!mounted) return;

      if (response.statusCode == 200) {
        setState(() {
          _resetSuccess = true;
          _resetResponse = '''
Success! Account state has been reset.
Account ID: ${_accountIdController.text}
Reset Date: ${_yearController.text}-${_monthController.text.padLeft(2, '0')}-${_dayController.text.padLeft(2, '0')}
API Response: ${response.data.toString()}''';
        });
      } else {
        throw Exception('Failed to reset account: ${response.statusCode}');
      }
    } on DioException catch (e) {
      setState(() {
        _resetSuccess = false;
        _resetResponse = '''
Error resetting account:
${e.response?.data?.toString() ?? e.message}''';
      });
    } catch (e) {
      setState(() {
        _resetSuccess = false;
        _resetResponse = '''
Error resetting account:
${e.toString()}''';
      });
    } finally {
      setState(() => _isResetting = false);
    }
  }

  Future<void> _getPositionReport() async {
    if (_settings.accounts.tradingAccounts.isEmpty) {
      setState(() {
        _reportSuccess = false;
        _reportResponse =
            'No trading account connected. Please connect a TRADOVATE account first.';
      });
      return;
    }

    final accounts = _settings.accounts.tradingAccounts;

    if (accounts.isEmpty) {
      setState(() {
        _reportSuccess = false;
        _reportResponse =
            'No trading account connected. Please connect a TRADOVATE account first.';
      });
      return;
    }

    final connectedAccount = accounts.firstWhere(
      (acc) => acc.provider == 'TRADOVATE',
      orElse: () => accounts.first,
    );

    setState(() {
      _isLoading = true;
      _reportResponse = null;
    });

    try {
      final dio = Dio();
      final response = await dio.get(
        'https://demo.tradovateapi.com/v1/account/list',
        options: Options(
          headers: {
            'Authorization': 'Bearer ${connectedAccount.credentials['token']}',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (!mounted) return;

      if (response.statusCode == 200) {
        final accounts = response.data as List<dynamic>;

        // Get positions for each account
        final positions = <Map<String, dynamic>>[];
        for (final acc in accounts) {
          try {
            final posResponse = await dio.get(
              'https://demo.tradovateapi.com/v1/position/list',
              options: Options(
                headers: {
                  'Authorization':
                      'Bearer ${connectedAccount.credentials['token']}',
                  'Content-Type': 'application/json',
                },
              ),
            );

            if (posResponse.statusCode == 200) {
              final accPositions = posResponse.data as List<dynamic>;
              positions.addAll(accPositions.cast<Map<String, dynamic>>());
            }
          } catch (e) {
            print('Error getting positions for account ${acc['id']}: $e');
          }
        }

        // Calculate statistics
        double totalPnL = 0;
        final symbols = <String>{};
        var winningTrades = 0;
        var losingTrades = 0;

        for (final pos in positions) {
          final netPl = (pos['netPl'] as num?)?.toDouble() ?? 0;
          totalPnL += netPl;
          symbols.add(pos['contractId']?.toString() ?? '');
          if (netPl > 0) {
            winningTrades++;
          } else if (netPl < 0) {
            losingTrades++;
          }
        }

        setState(() {
          _reportSuccess = true;
          _reportResponse = '''
Position Report for Accounts: ${accounts.map((a) => '${a['name']} (${a['id']})').join(', ')}
Date Range: ${_reportStartDateController.text.isNotEmpty ? _reportStartDateController.text : 'Current'} to ${_reportEndDateController.text.isNotEmpty ? _reportEndDateController.text : 'Now'}

Total Positions: ${positions.length}
Unique Symbols: ${symbols.length}
Total P&L: \$${totalPnL.toStringAsFixed(2)}
Winning Trades: $winningTrades
Losing Trades: $losingTrades
${positions.isNotEmpty ? 'Win Rate: ${(winningTrades / positions.length * 100).toStringAsFixed(1)}%' : ''}

API Response Details:
Accounts: ${response.data.toString()}
Positions: ${positions.toString()}''';
        });
      } else {
        throw Exception('Failed to get account list: ${response.statusCode}');
      }
    } on DioException catch (e) {
      setState(() {
        _reportSuccess = false;
        _reportResponse = '''
Error getting position report:
${e.response?.data?.toString() ?? e.message}''';
      });
    } catch (e) {
      setState(() {
        _reportSuccess = false;
        _reportResponse = '''
Error getting position report:
${e.toString()}''';
      });
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showConnectionResultDialog(
      BuildContext context, bool success, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: Icon(
          success ? Icons.check_circle_outline : Icons.error_outline,
          color: success ? Colors.green : Theme.of(context).colorScheme.error,
          size: 48,
        ),
        title: Text(success ? 'Success' : 'Connection Error'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(message),
            if (!success) ...[
              const SizedBox(height: 16),
              const Text('Please check:'),
              const Text('• Your internet connection'),
              const Text('• Your account credentials'),
              const Text('• The selected environment (demo/live)'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Future<List<String>?> _showAccountSelectionDialog(
      List<TradingAccount> accounts) async {
    final selectedAccounts = <String>{};

    return showDialog<List<String>>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Select Accounts to Connect'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CheckboxListTile(
                  title: const Text('Select All'),
                  value: selectedAccounts.length == accounts.length,
                  onChanged: (bool? value) {
                    setState(() {
                      if (value == true) {
                        selectedAccounts.addAll(accounts.map((a) => a.id));
                      } else {
                        selectedAccounts.clear();
                      }
                    });
                  },
                ),
                const Divider(),
                ...accounts.map((account) => CheckboxListTile(
                      value: selectedAccounts.contains(account.id),
                      onChanged: (bool? value) {
                        setState(() {
                          if (value == true) {
                            selectedAccounts.add(account.id);
                          } else {
                            selectedAccounts.remove(account.id);
                          }
                        });
                      },
                      title: Text(account.name),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('Provider: ${account.provider}'),
                          if (account.credentials.isNotEmpty) ...[
                            const SizedBox(height: 4),
                            ...account.credentials.entries.map(
                              (e) => Text('${e.key}: ${e.value}'),
                            ),
                          ],
                        ],
                      ),
                      isThreeLine: account.credentials.isNotEmpty,
                    )),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            FilledButton(
              onPressed: selectedAccounts.isEmpty
                  ? null
                  : () => Navigator.pop(context, selectedAccounts.toList()),
              child: const Text('Connect Selected'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTradingAccountsList() {
    if (_settings.accounts.tradingAccounts.isEmpty) {
      return Container(
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).dividerColor.withOpacity(0.2),
          ),
        ),
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: Text('No trading accounts added yet'),
        ),
      );
    }

    // Group accounts by their main connection (using username and provider)
    final accountGroups = <String, List<TradingAccount>>{};
    for (final account in _settings.accounts.tradingAccounts) {
      final key = '${account.credentials['username']}_${account.provider}';
      accountGroups.putIfAbsent(key, () => []).add(account);
    }

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).dividerColor.withOpacity(0.2),
        ),
      ),
      child: Column(
        children: accountGroups.entries.map((entry) {
          final accounts = entry.value;
          final mainAccount = accounts.first;
          final isMultipleAccounts = accounts.length > 1;

          return ExpansionTile(
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      mainAccount.status == AccountStatus.CONNECTED
                          ? Icons.check_circle
                          : Icons.error_outline,
                      size: 16,
                      color: mainAccount.status == AccountStatus.CONNECTED
                          ? Colors.green
                          : Colors.orange,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      mainAccount.provider,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SizedBox(width: 28),
                    if (isMultipleAccounts)
                      Icon(
                        Icons.keyboard_arrow_down,
                        size: 16,
                        color: Theme.of(context).primaryColor,
                      ),
                    const SizedBox(width: 4),
                    Flexible(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color:
                              Theme.of(context).primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '${accounts.length} ${accounts.length == 1 ? 'Account' : 'Accounts'} Connected',
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Theme.of(context).primaryColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            trailing: Container(
              constraints: const BoxConstraints(maxWidth: 100),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  IconButton(
                    icon: const Icon(AppIcons.edit, size: 20),
                    visualDensity: VisualDensity.compact,
                    style: IconButton.styleFrom(
                      padding: const EdgeInsets.all(8),
                    ),
                    onPressed: () =>
                        _handleTradingAccountAction('edit', mainAccount),
                  ),
                  IconButton(
                    icon: const Icon(AppIcons.delete, size: 20),
                    visualDensity: VisualDensity.compact,
                    style: IconButton.styleFrom(
                      padding: const EdgeInsets.all(8),
                      foregroundColor: Colors.red,
                    ),
                    onPressed: () =>
                        _handleTradingAccountAction('delete', mainAccount),
                  ),
                ],
              ),
            ),
            children: isMultipleAccounts
                ? accounts
                    .skip(1)
                    .map(
                      (subAccount) => Container(
                        decoration: BoxDecoration(
                          color: Theme.of(context).cardColor.withOpacity(0.5),
                          border: Border(
                            top: BorderSide(
                              color: Theme.of(context)
                                  .dividerColor
                                  .withOpacity(0.2),
                            ),
                          ),
                        ),
                        child: ListTile(
                          leading: Icon(
                            subAccount.status == AccountStatus.CONNECTED
                                ? Icons.check_circle
                                : Icons.error_outline,
                            size: 16,
                            color: subAccount.status == AccountStatus.CONNECTED
                                ? Colors.green
                                : Colors.orange,
                          ),
                          title: Text(
                            subAccount.name,
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Account ID: ${subAccount.credentials['accountId'] ?? 'N/A'}',
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                              Text(
                                'Balance: \$${NumberFormat('#,##0.00').format(double.tryParse(subAccount.credentials['balance'] ?? '0') ?? 0)}',
                                style: TextStyle(
                                    color: Colors.green,
                                    fontWeight: FontWeight.w600),
                              ),
                              Text(
                                  'Last Synced: ${subAccount.lastSynced.toString().split('.')[0]}'),
                            ],
                          ),
                          trailing: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                icon: const Icon(AppIcons.edit),
                                onPressed: () => _handleTradingAccountAction(
                                    'edit', subAccount),
                              ),
                              IconButton(
                                icon: const Icon(AppIcons.delete),
                                color: Colors.red,
                                onPressed: () => _handleTradingAccountAction(
                                    'delete', subAccount),
                              ),
                            ],
                          ),
                        ),
                      ),
                    )
                    .toList()
                : [],
          );
        }).toList(),
      ),
    );
  }

  @override
  void dispose() {
    _accountIdController.dispose();
    _yearController.dispose();
    _monthController.dispose();
    _dayController.dispose();
    _reportStartDateController.dispose();
    _reportEndDateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Accounts'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Center(
              child: Padding(
                padding: EdgeInsets.only(bottom: 24),
                child: AppLogo(
                  size: 80,
                  showText: true,
                ),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Trading Accounts',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton.icon(
                  onPressed: () => _handleTradingAccountAction('add', null),
                  icon: const Icon(
                    AppIcons.add,
                    size: 20,
                    color: AppColors.primaryBlue,
                  ),
                  label: const Text(
                    'Add New',
                    style: TextStyle(
                      color: AppColors.primaryBlue,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildTradingAccountsList(),
            const SizedBox(height: 32),
            Text(
              'API Keys',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.dividerColor.withOpacity(0.2),
                ),
              ),
              child: Column(
                children: _settings.accounts.apiKeys.map((apiKey) {
                  final isLast = _settings.accounts.apiKeys.indexOf(apiKey) ==
                      _settings.accounts.apiKeys.length - 1;
                  return Column(
                    children: [
                      ListTile(
                        title: Text(apiKey.name),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Status: ${apiKey.status}'),
                            if (apiKey.expiryDate != null)
                              Text(
                                  'Expires: ${apiKey.expiryDate?.toString().split(' ')[0]}'),
                          ],
                        ),
                        trailing: PopupMenuButton(
                          icon: const Icon(Icons.more_vert),
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'edit',
                              child: Text('Edit'),
                            ),
                            const PopupMenuItem(
                              value: 'delete',
                              child: Text('Delete'),
                            ),
                          ],
                          onSelected: (value) =>
                              _handleApiKeyAction(value, apiKey),
                        ),
                      ),
                      if (!isLast)
                        Divider(
                          height: 1,
                          color: theme.dividerColor.withOpacity(0.2),
                        ),
                    ],
                  );
                }).toList(),
              ),
            ),
            const SizedBox(height: 32),
            Text(
              'Account Management',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.dividerColor.withOpacity(0.2),
                ),
              ),
              child: Column(
                children: [
                  SwitchListTile(
                    title: const Text('Auto-sync Trading Data'),
                    subtitle: const Text(
                        'Automatically sync trading data from connected accounts'),
                    value: _settings.accounts.autoSync,
                    onChanged: (value) async {
                      try {
                        await _settingsService.updateAccountSync(
                          value,
                          _settings.accounts.syncFrequency,
                        );
                        await _loadSettings();
                      } catch (e) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                              content: Text('Error updating sync settings')),
                        );
                      }
                    },
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Export Data'),
                    subtitle: const Text('Download your account data'),
                    trailing: const Icon(Icons.download),
                    onTap: () {
                      // TODO: Implement data export
                    },
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Delete Account'),
                    subtitle: const Text('Permanently delete your account'),
                    trailing: const Icon(
                      Icons.delete_outline,
                      color: Colors.red,
                    ),
                    onTap: () {
                      // TODO: Show delete account confirmation
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),
            Text(
              'Reset Demo Account',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.dividerColor.withOpacity(0.2),
                ),
              ),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextFormField(
                    controller: _accountIdController,
                    decoration: const InputDecoration(
                      labelText: 'Account ID',
                      hintText: 'Enter demo account ID',
                    ),
                    keyboardType: TextInputType.number,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Reset Trade Date',
                    style: theme.textTheme.titleSmall,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: _yearController,
                          decoration: const InputDecoration(
                            labelText: 'Year',
                            hintText: 'YYYY',
                          ),
                          keyboardType: TextInputType.number,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: TextFormField(
                          controller: _monthController,
                          decoration: const InputDecoration(
                            labelText: 'Month',
                            hintText: 'MM',
                          ),
                          keyboardType: TextInputType.number,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: TextFormField(
                          controller: _dayController,
                          decoration: const InputDecoration(
                            labelText: 'Day',
                            hintText: 'DD',
                          ),
                          keyboardType: TextInputType.number,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  Center(
                    child: ElevatedButton.icon(
                      onPressed: _isResetting ? null : () => _resetAccount(),
                      icon: _isResetting
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Icon(AppIcons.refresh),
                      label:
                          Text(_isResetting ? 'Resetting...' : 'Reset Account'),
                    ),
                  ),
                  if (_resetResponse != null) ...[
                    const SizedBox(height: 16),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: _resetSuccess
                            ? Colors.green.withOpacity(0.1)
                            : Colors.red.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: _resetSuccess ? Colors.green : Colors.red,
                          width: 1,
                        ),
                      ),
                      child: SelectableText(
                        _resetResponse!,
                        style: TextStyle(
                          color: _resetSuccess
                              ? Colors.green[900]
                              : Colors.red[900],
                          fontFamily: 'monospace',
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
            const SizedBox(height: 32),
            Text(
              'Get Reports',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.dividerColor.withOpacity(0.2),
                ),
              ),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Using Account: ${_settings.accounts.tradingAccounts.isNotEmpty ? _settings.accounts.tradingAccounts.first.name : 'No account connected'}',
                    style: theme.textTheme.titleSmall,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: _reportStartDateController,
                          decoration: const InputDecoration(
                            labelText: 'Start Date',
                            hintText: 'YYYY-MM-DD',
                          ),
                          onTap: () async {
                            final date = await showDatePicker(
                              context: context,
                              initialDate: DateTime.now()
                                  .subtract(const Duration(days: 30)),
                              firstDate: DateTime(2020),
                              lastDate: DateTime.now(),
                            );
                            if (date != null) {
                              _reportStartDateController.text =
                                  DateFormat('yyyy-MM-dd').format(date);
                            }
                          },
                          readOnly: true,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextFormField(
                          controller: _reportEndDateController,
                          decoration: const InputDecoration(
                            labelText: 'End Date',
                            hintText: 'YYYY-MM-DD',
                          ),
                          onTap: () async {
                            final date = await showDatePicker(
                              context: context,
                              initialDate: DateTime.now(),
                              firstDate: DateTime(2020),
                              lastDate: DateTime.now(),
                            );
                            if (date != null) {
                              _reportEndDateController.text =
                                  DateFormat('yyyy-MM-dd').format(date);
                            }
                          },
                          readOnly: true,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  Center(
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : () => _getPositionReport(),
                      icon: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Icon(AppIcons.analytics),
                      label: Text(
                          _isLoading ? 'Loading...' : 'Get Position Report'),
                    ),
                  ),
                  if (_reportResponse != null) ...[
                    const SizedBox(height: 16),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: _reportSuccess
                            ? Colors.green.withOpacity(0.1)
                            : Colors.red.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: _reportSuccess ? Colors.green : Colors.red,
                          width: 1,
                        ),
                      ),
                      child: SelectableText(
                        _reportResponse!,
                        style: TextStyle(
                          color: _reportSuccess
                              ? Colors.green[900]
                              : Colors.red[900],
                          fontFamily: 'monospace',
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ApiKeyDialog extends StatefulWidget {
  final ApiKey? apiKey;

  const _ApiKeyDialog({this.apiKey});

  @override
  State<_ApiKeyDialog> createState() => _ApiKeyDialogState();
}

class _ApiKeyDialogState extends State<_ApiKeyDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _keyController;
  late TextEditingController _secretController;
  late DateTime? _expiryDate;
  late ApiKeyStatus _status;

  @override
  void initState() {
    super.initState();
    final apiKey = widget.apiKey;
    _nameController = TextEditingController(text: apiKey?.name ?? '');
    _keyController = TextEditingController(text: apiKey?.key ?? '');
    _secretController = TextEditingController(text: apiKey?.secret ?? '');
    _expiryDate = apiKey?.expiryDate;
    _status = apiKey?.status ?? ApiKeyStatus.ACTIVE;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _keyController.dispose();
    _secretController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('${widget.apiKey == null ? 'Add' : 'Edit'} API Key'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Name',
                  hintText: 'e.g., Alpha Vantage',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _keyController,
                decoration: const InputDecoration(
                  labelText: 'API Key',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter an API key';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _secretController,
                decoration: const InputDecoration(
                  labelText: 'API Secret (Optional)',
                ),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<ApiKeyStatus>(
                value: _status,
                decoration: const InputDecoration(
                  labelText: 'Status',
                ),
                items: ApiKeyStatus.values.map((status) {
                  return DropdownMenuItem(
                    value: status,
                    child:
                        Text(status.toString().split('.').last.toUpperCase()),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() => _status = value);
                  }
                },
              ),
              const SizedBox(height: 16),
              ListTile(
                title: const Text('Expiry Date'),
                subtitle: Text(
                    _expiryDate?.toString().split(' ')[0] ?? 'No expiry date'),
                trailing: const Icon(Icons.calendar_today),
                onTap: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: _expiryDate ?? DateTime.now(),
                    firstDate: DateTime.now(),
                    lastDate:
                        DateTime.now().add(const Duration(days: 365 * 10)),
                  );
                  if (date != null) {
                    setState(() => _expiryDate = date);
                  }
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            if (_formKey.currentState?.validate() ?? false) {
              final apiKey = ApiKey(
                id: widget.apiKey?.id ??
                    DateTime.now().millisecondsSinceEpoch.toString(),
                name: _nameController.text,
                key: _keyController.text,
                secret: _secretController.text.isEmpty
                    ? null
                    : _secretController.text,
                status: _status,
                expiryDate: _expiryDate,
              );
              Navigator.pop(context, apiKey);
            }
          },
          child: const Text('Save'),
        ),
      ],
    );
  }
}

class _TradingAccountDialog extends ConsumerStatefulWidget {
  final TradingAccount? account;

  const _TradingAccountDialog({this.account});

  @override
  ConsumerState<_TradingAccountDialog> createState() =>
      _TradingAccountDialogState();
}

class _TradingAccountDialogState extends ConsumerState<_TradingAccountDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _providerController = TextEditingController();
  final _initialCapitalController = TextEditingController();
  final _usernameController = TextEditingController();
  final _tokenController = TextEditingController();
  final _checkTokenController = TextEditingController();
  final _userIdController = TextEditingController();
  late AccountStatus _status;
  String _environment = 'demo';
  bool _hasMarketData = true;
  late DateTime _expirationDate;
  bool _isTestingConnection = false;
  late core.SettingsService _settingsService;
  late core.TradeService _tradeService;
  bool _isManualEntry = true;
  String _selectedBrokerage = 'TRADOVATE'; // Default brokerage
  final List<String> _brokerages = ['TRADOVATE', 'TD Ameritrade', 'Fidelity'];
  final _newBrokerageController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _settingsService = ref.read(settingsServiceProvider);
    _tradeService = ref.read(tradeServiceProvider);
    _expirationDate = DateTime.now().add(const Duration(days: 365));
    _providerController.text = widget.account?.provider ?? 'Manual';
    _initialCapitalController.text =
        widget.account?.initialCapital.toString() ?? '10000.0';
    _status = widget.account?.status ?? AccountStatus.DISCONNECTED;

    if (widget.account != null) {
      _nameController.text = widget.account!.name;
      _isManualEntry = widget.account!.provider == 'Manual';
      _selectedBrokerage = _brokerages.contains(widget.account!.provider)
          ? widget.account!.provider
          : _brokerages[0];

      // Initialize all credentials if available
      final credentials = widget.account!.credentials;
      _usernameController.text = credentials['username'] ?? '';
      _tokenController.text = credentials['token'] ?? '';
      _checkTokenController.text = credentials['checkToken'] ?? '';
      _userIdController.text = credentials['userId'] ?? '';
      if (credentials['expiration'] != null) {
        _expirationDate =
            DateTime.tryParse(credentials['expiration']!) ?? _expirationDate;
      }
      if (credentials['environment'] != null) {
        _environment = credentials['environment']!;
      }
      if (credentials['hasMarketData'] != null) {
        _hasMarketData = credentials['hasMarketData']!.toLowerCase() == 'true';
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _providerController.dispose();
    _initialCapitalController.dispose();
    _usernameController.dispose();
    _tokenController.dispose();
    _checkTokenController.dispose();
    _userIdController.dispose();
    _newBrokerageController.dispose();
    super.dispose();
  }

  void _showAddBrokerageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New Brokerage'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _newBrokerageController,
              decoration: const InputDecoration(
                labelText: 'Brokerage Name',
                hintText: 'Enter brokerage name',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a brokerage name';
                }
                return null;
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (_newBrokerageController.text.isNotEmpty) {
                setState(() {
                  _brokerages.add(_newBrokerageController.text);
                  _selectedBrokerage = _newBrokerageController.text;
                  _providerController.text = _newBrokerageController.text;
                });
                _newBrokerageController.clear();
                Navigator.pop(context);
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  Widget _buildBrokerageSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedBrokerage,
                decoration: const InputDecoration(
                  labelText: 'Select Brokerage',
                  border: OutlineInputBorder(),
                  filled: true,
                ),
                items: _brokerages.map((brokerage) {
                  return DropdownMenuItem(
                    value: brokerage,
                    child: Text(brokerage),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedBrokerage = value;
                      _providerController.text = value;
                    });
                  }
                },
              ),
            ),
            const SizedBox(width: 16),
            IconButton(
              onPressed: _showAddBrokerageDialog,
              icon: const Icon(Icons.add_circle_outline),
              tooltip: 'Add New Brokerage',
              style: IconButton.styleFrom(
                foregroundColor: Theme.of(context).primaryColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),
        // Show appropriate credential fields based on selected brokerage
        if (_selectedBrokerage == 'TRADOVATE')
          _buildTradovateCredentialsFields()
        else
          _buildGenericCredentialsFields(),
      ],
    );
  }

  Widget _buildTradovateCredentialsFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Tradovate Credentials',
              style: TextStyle(
                color: Theme.of(context).textTheme.bodySmall?.color,
                fontSize: 13,
              ),
            ),
            TextButton.icon(
              onPressed: _handleTradovateConnection,
              style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).primaryColor,
                padding: const EdgeInsets.symmetric(horizontal: 12),
              ),
              icon: _isTestingConnection
                  ? const SizedBox(
                      width: 12,
                      height: 12,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                      ),
                    )
                  : const Icon(Icons.link, size: 16),
              label: Text(
                _isTestingConnection ? 'Connecting...' : 'Connect',
                style: const TextStyle(fontSize: 13),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _usernameController,
          decoration: const InputDecoration(
            labelText: 'Username',
            hintText: 'Tradovate username',
            border: OutlineInputBorder(),
            filled: true,
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter Tradovate username';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _tokenController,
          decoration: const InputDecoration(
            labelText: 'Access Token',
            hintText: 'Tradovate access token',
            border: OutlineInputBorder(),
            filled: true,
          ),
          maxLines: 2,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter Tradovate access token';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _checkTokenController,
          decoration: const InputDecoration(
            labelText: 'Check Token',
            hintText: 'Tradovate check token',
            border: OutlineInputBorder(),
            filled: true,
          ),
          maxLines: 2,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter Tradovate check token';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _userIdController,
          decoration: const InputDecoration(
            labelText: 'User ID',
            hintText: 'Tradovate user ID',
            border: OutlineInputBorder(),
            filled: true,
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter Tradovate user ID';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        DropdownButtonFormField<String>(
          value: _environment,
          decoration: const InputDecoration(
            labelText: 'Environment',
            border: OutlineInputBorder(),
            filled: true,
          ),
          items: ['demo', 'live'].map((env) {
            return DropdownMenuItem(
              value: env,
              child: Text(env.toUpperCase()),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() => _environment = value);
            }
          },
        ),
        const SizedBox(height: 16),
        SwitchListTile(
          title: const Text('Has Market Data'),
          value: _hasMarketData,
          onChanged: (value) {
            setState(() => _hasMarketData = value);
          },
        ),
      ],
    );
  }

  Widget _buildGenericCredentialsFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$_selectedBrokerage Credentials',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _usernameController,
          decoration: const InputDecoration(
            labelText: 'Username/API Key',
            border: OutlineInputBorder(),
            filled: true,
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter username or API key';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _tokenController,
          decoration: const InputDecoration(
            labelText: 'Password/Secret',
            border: OutlineInputBorder(),
            filled: true,
          ),
          obscureText: true,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter password or secret';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            border: Border.all(
                color: Theme.of(context).dividerColor.withOpacity(0.2)),
            borderRadius: BorderRadius.circular(4),
          ),
          child: ListTile(
            dense: true,
            contentPadding: const EdgeInsets.symmetric(horizontal: 12),
            title: Text(
              'Expiration Date',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            subtitle: Text(
              DateFormat('yyyy-MM-dd').format(_expirationDate),
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            trailing: const Icon(Icons.calendar_today, size: 20),
            onTap: () async {
              final date = await showDatePicker(
                context: context,
                initialDate: _expirationDate,
                firstDate: DateTime.now(),
                lastDate: DateTime.now().add(const Duration(days: 365 * 10)),
              );
              if (date != null) {
                setState(() => _expirationDate = date);
              }
            },
          ),
        ),
      ],
    );
  }

  Future<void> _handleTradovateConnection() async {
    // Check mounted state at the beginning
    if (!mounted) return;

    // Validate required fields
    if (_tokenController.text.isEmpty ||
        _checkTokenController.text.isEmpty ||
        _usernameController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fill in all required Tradovate credentials'),
        ),
      );
      return;
    }

    try {
      setState(() => _isTestingConnection = true);

      // Create a base account for testing
      final baseAccount = TradingAccount(
        id: 'test',
        name: _nameController.text.isNotEmpty
            ? _nameController.text
            : 'New Account',
        provider: 'TRADOVATE',
        status: AccountStatus.PENDING,
        lastSynced: DateTime.now(),
        credentials: {
          'environment': _environment,
          'username': _usernameController.text,
          'token': _tokenController.text,
          'checkToken': _checkTokenController.text,
          'userId': _userIdController.text,
        },
        initialCapital: double.tryParse(_initialCapitalController.text) ?? 0,
        isActive: true,
      );

      // Test the connection and get available accounts
      final result = await _testConnection(baseAccount);

      if (!mounted) return;

      if (result.status == 'Success') {
        // Show account selection dialog
        final selectedAccounts = await _showTradovateAccountSelectionDialog(
          result.availableAccounts,
        );

        if (!mounted) return;

        if (selectedAccounts != null && selectedAccounts.isNotEmpty) {
          // Create a list of accounts to return
          final dio = Dio();
          final accounts =
              await Future.wait(selectedAccounts.map((selectedAccount) async {
            // Fetch account balance
            final balanceResponse = await dio.get(
              'https://demo.tradovateapi.com/v1/account/item',
              queryParameters: {'id': selectedAccount['id']},
              options: Options(
                headers: {
                  'Authorization': 'Bearer ${baseAccount.credentials['token']}',
                  'Content-Type': 'application/json',
                },
              ),
            );

            final accountDetails = balanceResponse.data;

            return baseAccount.copyWith(
              id: DateTime.now().microsecondsSinceEpoch.toString() +
                  '_${selectedAccount['id']}',
              name: selectedAccount['name'] as String,
              status: AccountStatus.CONNECTED,
              credentials: {
                ...baseAccount.credentials,
                'accountId': selectedAccount['id'].toString(),
                'accountName': selectedAccount['name'] as String,
                'active':
                    (selectedAccount['active'] as bool? ?? false).toString(),
                'tradingPermission':
                    (selectedAccount['tradingPermission'] as bool? ?? false)
                        .toString(),
                'balance': (accountDetails['cashBalance'] ?? 0.0).toString(),
                'equity': (accountDetails['netLiq'] ?? 0.0).toString(),
                'marginUsed':
                    (accountDetails['initialMargin'] ?? 0.0).toString(),
                'marginAvailable':
                    (accountDetails['marginAvailable'] ?? 0.0).toString(),
              },
            );
          }));

          // Add the accounts directly
          for (final account in accounts) {
            await _settingsService.addTradingAccount(account.toCore());
          }

          // Refresh the providers
          await ref.refresh(settingsServiceProvider);
          await ref.refresh(tradeServiceProvider);

          // Close the dialogs and show success message
          if (mounted) {
            // Pop both dialogs
            Navigator.of(context).pop(); // Close account selection dialog
            Navigator.of(context).pop(
                accounts); // Close trading account dialog and return accounts for parent to handle
          }
        }
      } else {
        // Show error dialog
        if (!mounted) return;
        await showDialog(
          context: context,
          builder: (context) => AlertDialog(
            titlePadding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
            contentPadding: const EdgeInsets.fromLTRB(24, 16, 24, 24),
            title: Row(
              children: [
                Icon(
                  Icons.error_outline,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Connection Error',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Unable to connect to your Tradovate account. Please check:',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.error,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 12),
                const Text('• Your username is correct'),
                const Text('• Your access token is valid and not expired'),
                const Text('• Your check token is valid'),
                const Text(
                    '• You have selected the correct environment (demo/live)'),
                if (result.availableData.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  const Text(
                    'Technical Details:',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 4),
                  ...result.availableData.map(
                    (data) => Padding(
                      padding: const EdgeInsets.only(left: 16, top: 4),
                      child: Text('• $data'),
                    ),
                  ),
                ],
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;
      await showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Connection Error'),
          content: const Text(
              'Unable to connect to Tradovate. Please check your internet connection and try again.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK'),
            ),
          ],
        ),
      );
    } finally {
      if (mounted) {
        setState(() => _isTestingConnection = false);
      }
    }
  }

  Future<List<Map<String, dynamic>>?> _showTradovateAccountSelectionDialog(
    List<Map<String, dynamic>> accounts,
  ) async {
    final selectedAccounts = <Map<String, dynamic>>{};

    return showDialog<List<Map<String, dynamic>>>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Dialog(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: 600,
              maxHeight: MediaQuery.of(context).size.height * 0.8,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    'Select Trading Accounts',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                ),
                const Divider(height: 1),
                Flexible(
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CheckboxListTile(
                          title: const Text('Select All'),
                          value: selectedAccounts.length == accounts.length,
                          onChanged: (bool? value) {
                            setState(() {
                              if (value == true) {
                                selectedAccounts.addAll(accounts);
                              } else {
                                selectedAccounts.clear();
                              }
                            });
                          },
                        ),
                        const Divider(),
                        ...accounts.map((account) {
                          final isActive = account['active'] as bool? ?? false;
                          final hasPermission =
                              account['tradingPermission'] as bool? ?? false;
                          final isSelected = selectedAccounts.contains(account);

                          return CheckboxListTile(
                            value: isSelected,
                            onChanged: (bool? value) {
                              setState(() {
                                if (value == true) {
                                  selectedAccounts.add(account);
                                } else {
                                  selectedAccounts.remove(account);
                                }
                              });
                            },
                            title: Text(account['name'] as String),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Account ID: ${account['id']}'),
                                Row(
                                  children: [
                                    Icon(
                                      isActive
                                          ? Icons.check_circle
                                          : Icons.error_outline,
                                      size: 16,
                                      color:
                                          isActive ? Colors.green : Colors.red,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(isActive ? 'Active' : 'Inactive'),
                                  ],
                                ),
                              ],
                            ),
                            isThreeLine: true,
                          );
                        }).toList(),
                      ],
                    ),
                  ),
                ),
                const Divider(height: 1),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('Cancel'),
                      ),
                      const SizedBox(width: 8),
                      FilledButton(
                        onPressed: selectedAccounts.isEmpty
                            ? null
                            : () => Navigator.pop(
                                context, selectedAccounts.toList()),
                        child: const Text('Connect Selected'),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<TestConnectionResult> _testConnection(TradingAccount account) async {
    try {
      final dio = Dio();
      try {
        final response = await dio.get(
          'https://demo.tradovateapi.com/v1/account/list',
          options: Options(
            headers: {
              'Authorization': 'Bearer ${account.credentials['token']}',
              'Content-Type': 'application/json',
            },
          ),
        );

        if (response.statusCode != 200) {
          return TestConnectionResult(
            status: 'Failed',
            accountType:
                '${account.credentials['environment']?.toUpperCase()} Account',
            availableData: [
              'Server returned an error response',
              'Status code: ${response.statusCode}',
              if (response.data != null) 'Details: ${response.data.toString()}'
            ],
            availableAccounts: [],
          );
        }

        final accounts = response.data as List<dynamic>;
        final accountsList =
            accounts.map((acc) => acc as Map<String, dynamic>).toList();

        if (accounts.isEmpty) {
          return TestConnectionResult(
            status: 'Failed',
            accountType:
                '${account.credentials['environment']?.toUpperCase()} Account',
            availableData: ['No trading accounts found for this user'],
            availableAccounts: [],
          );
        }

        return TestConnectionResult(
          status: 'Success',
          accountType:
              '${account.credentials['environment']?.toUpperCase()} Account',
          availableData: [
            'Successfully connected to Tradovate',
            'Found ${accounts.length} trading account(s)',
            ...accounts.map((acc) => 'Account: ${acc['name']} (${acc['id']})'),
          ],
          availableAccounts: accountsList,
        );
      } on DioException catch (e) {
        String errorMessage = 'Unable to connect to Tradovate';
        if (e.response?.statusCode == 401) {
          errorMessage = 'Invalid credentials';
        } else if (e.response?.statusCode == 403) {
          errorMessage = 'Access denied';
        } else if (e.type == DioExceptionType.connectionTimeout) {
          errorMessage = 'Connection timed out';
        }

        return TestConnectionResult(
          status: 'Failed',
          accountType:
              '${account.credentials['environment']?.toUpperCase()} Account',
          availableData: [
            errorMessage,
            if (e.response?.data != null)
              'Details: ${e.response?.data.toString()}'
          ],
          availableAccounts: [],
        );
      }
    } catch (e) {
      return TestConnectionResult(
        status: 'Failed',
        accountType:
            '${account.credentials['environment']?.toUpperCase()} Account',
        availableData: [
          'An unexpected error occurred while connecting to Tradovate'
        ],
        availableAccounts: [],
      );
    }
  }

  void _showConnectionResultDialog(
      BuildContext context, bool success, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: Icon(
          success ? Icons.check_circle_outline : Icons.error_outline,
          color: success ? Colors.green : Theme.of(context).colorScheme.error,
          size: 48,
        ),
        title: Text(success ? 'Success' : 'Connection Error'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(message),
            if (!success) ...[
              const SizedBox(height: 16),
              const Text('Please check:'),
              const Text('• Your internet connection'),
              const Text('• Your account credentials'),
              const Text('• The selected environment (demo/live)'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Dialog(
      backgroundColor: theme.scaffoldBackgroundColor,
      insetPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
      child: Container(
        width: 1200,
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.95,
          maxHeight: MediaQuery.of(context).size.height * 0.95,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 16, 16, 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    widget.account == null
                        ? 'Add Trading Account'
                        : 'Edit Trading Account',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    visualDensity: VisualDensity.compact,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
            ),
            const Divider(height: 1),
            Expanded(
              child: SingleChildScrollView(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: theme.cardColor,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: theme.dividerColor.withOpacity(0.2),
                          ),
                        ),
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Entry Type',
                              style: theme.textTheme.titleSmall,
                            ),
                            const SizedBox(height: 12),
                            SegmentedButton<bool>(
                              segments: const [
                                ButtonSegment<bool>(
                                  value: true,
                                  label: Text('Manual Entry'),
                                  icon: Icon(Icons.edit_note),
                                ),
                                ButtonSegment<bool>(
                                  value: false,
                                  label: Text('Trading Account Connection'),
                                  icon: Icon(Icons.link),
                                ),
                              ],
                              selected: {_isManualEntry},
                              onSelectionChanged: (Set<bool> newSelection) {
                                setState(() {
                                  _isManualEntry = newSelection.first;
                                  if (_isManualEntry) {
                                    _providerController.text = 'Manual';
                                  } else {
                                    _providerController.text = 'TRADOVATE';
                                  }
                                });
                              },
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),
                      Text(
                        'Basic Information',
                        style: theme.textTheme.titleSmall?.copyWith(
                          color: theme.textTheme.bodySmall?.color,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _nameController,
                              decoration: const InputDecoration(
                                labelText: 'Account Name',
                                border: OutlineInputBorder(),
                                filled: true,
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter an account name';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 24),
                          Expanded(
                            child: TextFormField(
                              controller: _initialCapitalController,
                              decoration: const InputDecoration(
                                labelText: 'Initial Capital',
                                prefixText: '\$',
                                border: OutlineInputBorder(),
                                filled: true,
                              ),
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter initial capital';
                                }
                                final capital = double.tryParse(value);
                                if (capital == null || capital <= 0) {
                                  return 'Please enter a valid positive number';
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),
                      if (!_isManualEntry) ...[
                        const SizedBox(height: 32),
                        Text(
                          'Trading Account Connection',
                          style: theme.textTheme.titleSmall?.copyWith(
                            color: theme.textTheme.bodySmall?.color,
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildBrokerageSelection(),
                      ],
                    ],
                  ),
                ),
              ),
            ),
            const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () {
                      if (_formKey.currentState!.validate()) {
                        final Map<String, String> credentials = {};

                        if (!_isManualEntry) {
                          credentials.addAll({
                            'environment': _environment,
                            'username': _usernameController.text,
                            'token': _tokenController.text,
                            'checkToken': _checkTokenController.text,
                            'userId': _userIdController.text,
                            'expiration': _expirationDate.toIso8601String(),
                            'hasLive': 'false',
                            'hasFunded': 'false',
                            'hasMarketData': _hasMarketData.toString(),
                            'outdatedTaC': 'false',
                            'outdatedSentimentPolicy': 'true',
                            'requiredNonProCertification': 'false',
                            'userStatus': 'Active'
                          });
                        }

                        final account = TradingAccount(
                          id: widget.account?.id ??
                              DateTime.now().toIso8601String(),
                          name: _nameController.text,
                          provider: _providerController.text,
                          status: _isManualEntry
                              ? AccountStatus.CONNECTED
                              : _status,
                          lastSynced:
                              widget.account?.lastSynced ?? DateTime.now(),
                          credentials: credentials,
                          initialCapital:
                              double.parse(_initialCapitalController.text),
                        );
                        Navigator.pop(context, account);
                      }
                    },
                    child: const Text('Save'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
