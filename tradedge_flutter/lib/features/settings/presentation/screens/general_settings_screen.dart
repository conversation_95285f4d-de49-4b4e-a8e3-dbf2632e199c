import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/providers/auth_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart'
    show Settings, DisplaySettings, PnLDisplayMode;

class GeneralSettingsScreen extends ConsumerStatefulWidget {
  const GeneralSettingsScreen({super.key});

  @override
  ConsumerState<GeneralSettingsScreen> createState() =>
      _GeneralSettingsScreenState();
}

class _GeneralSettingsScreenState extends ConsumerState<GeneralSettingsScreen> {
  bool _isLoading = true;
  Settings? _settings;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final userId = ref.read(currentUserIdProvider);
      final settingsService = ref.read(settingsServiceProvider);

      try {
        final settings = await settingsService.getSettings();
        setState(() {
          _settings = settings;
          _isLoading = false;
        });
        return;
      } catch (e) {
        await settingsService.initialize(userId);
        final settings = await settingsService.getSettings();
        setState(() {
          _settings = settings;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _updateDisplaySettings(DisplaySettings newDisplay) async {
    try {
      final settingsService = ref.read(settingsServiceProvider);
      final updatedSettings =
          await settingsService.updateDisplaySettings(newDisplay);
      setState(() => _settings = updatedSettings);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to update settings'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('General'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final displaySettings = _settings?.display;
    if (displaySettings == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('General'),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text('Settings not available'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadSettings,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('General'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Appearance',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.dividerColor.withOpacity(0.2),
                ),
              ),
              child: Column(
                children: [
                  ListTile(
                    title: const Text('Theme'),
                    subtitle: Text(displaySettings.theme == 'system'
                        ? 'System Default'
                        : displaySettings.theme.substring(0, 1).toUpperCase() +
                            displaySettings.theme.substring(1)),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () async {
                      final newTheme = await showDialog<String>(
                        context: context,
                        builder: (context) => SimpleDialog(
                          title: const Text('Select Theme'),
                          children: [
                            SimpleDialogOption(
                              onPressed: () => Navigator.pop(context, 'system'),
                              child: const Text('System Default'),
                            ),
                            SimpleDialogOption(
                              onPressed: () => Navigator.pop(context, 'light'),
                              child: const Text('Light'),
                            ),
                            SimpleDialogOption(
                              onPressed: () => Navigator.pop(context, 'dark'),
                              child: const Text('Dark'),
                            ),
                          ],
                        ),
                      );

                      if (newTheme != null) {
                        final newDisplay = displaySettings.copyWith(
                          theme: newTheme,
                        );
                        await _updateDisplaySettings(newDisplay);
                      }
                    },
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Compact Mode'),
                    subtitle: const Text('Show more content in less space'),
                    trailing: Switch.adaptive(
                      value: displaySettings.compactMode,
                      onChanged: (value) async {
                        final newDisplay = displaySettings.copyWith(
                          compactMode: value,
                        );
                        await _updateDisplaySettings(newDisplay);
                      },
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),
            Text(
              'Trading Display & Analysis',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.dividerColor.withOpacity(0.2),
                ),
              ),
              child: Column(
                children: [
                  ListTile(
                    title: const Text('P&L Display Mode'),
                    subtitle: Text(
                        _getPnLDisplayModeText(displaySettings.pnlDisplayMode)),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () async {
                      final newMode = await showDialog<PnLDisplayMode>(
                        context: context,
                        builder: (context) => SimpleDialog(
                          title: const Text('P&L Display Mode'),
                          children: [
                            SimpleDialogOption(
                              onPressed: () => Navigator.pop(
                                  context, PnLDisplayMode.currency),
                              child: const Text('Currency (\$)'),
                            ),
                            SimpleDialogOption(
                              onPressed: () => Navigator.pop(
                                  context, PnLDisplayMode.percentage),
                              child: const Text('Percentage (%)'),
                            ),
                            SimpleDialogOption(
                              onPressed: () => Navigator.pop(
                                  context, PnLDisplayMode.rMultiple),
                              child: const Text('R-Multiple (R)'),
                            ),
                          ],
                        ),
                      );

                      if (newMode != null) {
                        final newDisplay = displaySettings.copyWith(
                          pnlDisplayMode: newMode,
                        );
                        await _updateDisplaySettings(newDisplay);
                      }
                    },
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Show Running P&L'),
                    subtitle:
                        const Text('Display running profit/loss in trade list'),
                    trailing: Switch.adaptive(
                      value: displaySettings.showRunningPnL,
                      onChanged: (value) async {
                        final newDisplay = displaySettings.copyWith(
                          showRunningPnL: value,
                        );
                        await _updateDisplaySettings(newDisplay);
                      },
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Show Equity Curve'),
                    subtitle: const Text('Display equity curve on dashboard'),
                    trailing: Switch.adaptive(
                      value: displaySettings.showEquityCurve,
                      onChanged: (value) async {
                        final newDisplay = displaySettings.copyWith(
                          showEquityCurve: value,
                        );
                        await _updateDisplaySettings(newDisplay);
                      },
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Default Account View'),
                    subtitle: Text(displaySettings.defaultAccountView == 'all'
                        ? 'All Accounts'
                        : 'Last Selected Account'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () async {
                      final newView = await showDialog<String>(
                        context: context,
                        builder: (context) => SimpleDialog(
                          title: const Text('Default Account View'),
                          children: [
                            SimpleDialogOption(
                              onPressed: () => Navigator.pop(context, 'all'),
                              child: const Text('All Accounts'),
                            ),
                            SimpleDialogOption(
                              onPressed: () => Navigator.pop(context, 'last'),
                              child: const Text('Last Selected Account'),
                            ),
                          ],
                        ),
                      );

                      if (newView != null) {
                        final newDisplay = displaySettings.copyWith(
                          defaultAccountView: newView,
                        );
                        await _updateDisplaySettings(newDisplay);
                      }
                    },
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Default Time Period'),
                    subtitle: Text(
                        _getTimePeriodText(displaySettings.defaultTimePeriod)),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () async {
                      final newPeriod = await showDialog<String>(
                        context: context,
                        builder: (context) => SimpleDialog(
                          title: const Text('Default Time Period'),
                          children: [
                            SimpleDialogOption(
                              onPressed: () => Navigator.pop(context, 'today'),
                              child: const Text('Today'),
                            ),
                            SimpleDialogOption(
                              onPressed: () => Navigator.pop(context, 'week'),
                              child: const Text('This Week'),
                            ),
                            SimpleDialogOption(
                              onPressed: () => Navigator.pop(context, 'month'),
                              child: const Text('This Month'),
                            ),
                            SimpleDialogOption(
                              onPressed: () => Navigator.pop(context, 'year'),
                              child: const Text('This Year'),
                            ),
                            SimpleDialogOption(
                              onPressed: () => Navigator.pop(context, 'all'),
                              child: const Text('All Time'),
                            ),
                          ],
                        ),
                      );

                      if (newPeriod != null) {
                        final newDisplay = displaySettings.copyWith(
                          defaultTimePeriod: newPeriod,
                        );
                        await _updateDisplaySettings(newDisplay);
                      }
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),
            Text(
              'Date & Time',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.dividerColor.withOpacity(0.2),
                ),
              ),
              child: Column(
                children: [
                  ListTile(
                    title: const Text('Date Format'),
                    subtitle:
                        Text(_getDateFormatExample(displaySettings.dateFormat)),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () async {
                      final newFormat = await showDialog<String>(
                        context: context,
                        builder: (context) => SimpleDialog(
                          title: const Text('Select Date Format'),
                          children: [
                            SimpleDialogOption(
                              onPressed: () =>
                                  Navigator.pop(context, 'yyyy-MM-dd'),
                              child: const Text('YYYY-MM-DD (2024-01-31)'),
                            ),
                            SimpleDialogOption(
                              onPressed: () =>
                                  Navigator.pop(context, 'MM/dd/yyyy'),
                              child: const Text('MM/DD/YYYY (01/31/2024)'),
                            ),
                            SimpleDialogOption(
                              onPressed: () =>
                                  Navigator.pop(context, 'dd/MM/yyyy'),
                              child: const Text('DD/MM/YYYY (31/01/2024)'),
                            ),
                          ],
                        ),
                      );

                      if (newFormat != null) {
                        final newDisplay = displaySettings.copyWith(
                          dateFormat: newFormat,
                        );
                        await _updateDisplaySettings(newDisplay);
                      }
                    },
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Time Format'),
                    subtitle:
                        Text(_getTimeFormatExample(displaySettings.timeFormat)),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () async {
                      final newFormat = await showDialog<String>(
                        context: context,
                        builder: (context) => SimpleDialog(
                          title: const Text('Select Time Format'),
                          children: [
                            SimpleDialogOption(
                              onPressed: () =>
                                  Navigator.pop(context, 'HH:mm:ss'),
                              child: const Text('24-hour (13:45:30)'),
                            ),
                            SimpleDialogOption(
                              onPressed: () =>
                                  Navigator.pop(context, 'hh:mm:ss a'),
                              child: const Text('12-hour (1:45:30 PM)'),
                            ),
                            SimpleDialogOption(
                              onPressed: () => Navigator.pop(context, 'HH:mm'),
                              child: const Text('24-hour, no seconds (13:45)'),
                            ),
                            SimpleDialogOption(
                              onPressed: () =>
                                  Navigator.pop(context, 'hh:mm a'),
                              child:
                                  const Text('12-hour, no seconds (1:45 PM)'),
                            ),
                          ],
                        ),
                      );

                      if (newFormat != null) {
                        final newDisplay = displaySettings.copyWith(
                          timeFormat: newFormat,
                        );
                        await _updateDisplaySettings(newDisplay);
                      }
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getPnLDisplayModeText(PnLDisplayMode mode) {
    switch (mode) {
      case PnLDisplayMode.currency:
        return 'Currency (\$)';
      case PnLDisplayMode.percentage:
        return 'Percentage (%)';
      case PnLDisplayMode.rMultiple:
        return 'R-Multiple (R)';
      default:
        return mode.name;
    }
  }

  String _getDateFormatExample(String format) {
    switch (format) {
      case 'yyyy-MM-dd':
        return 'YYYY-MM-DD (2024-01-31)';
      case 'MM/dd/yyyy':
        return 'MM/DD/YYYY (01/31/2024)';
      case 'dd/MM/yyyy':
        return 'DD/MM/YYYY (31/01/2024)';
      default:
        return format;
    }
  }

  String _getTimeFormatExample(String format) {
    switch (format) {
      case 'HH:mm:ss':
        return '24-hour (13:45:30)';
      case 'hh:mm:ss a':
        return '12-hour (1:45:30 PM)';
      case 'HH:mm':
        return '24-hour, no seconds (13:45)';
      case 'hh:mm a':
        return '12-hour, no seconds (1:45 PM)';
      default:
        return format;
    }
  }

  String _getTimePeriodText(String period) {
    switch (period) {
      case 'today':
        return 'Today';
      case 'week':
        return 'This Week';
      case 'month':
        return 'This Month';
      case 'year':
        return 'This Year';
      case 'all':
        return 'All Time';
      default:
        return period;
    }
  }
}
