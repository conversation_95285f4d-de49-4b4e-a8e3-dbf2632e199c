import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/providers/auth_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/trade_data_provider.dart';
import 'package:csv/csv.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:share_plus/share_plus.dart';
import 'package:cross_file/cross_file.dart';
// Conditionally import dart:html for web
import 'web_download.dart' if (dart.library.html) 'dart:html' as html;

class DataManagementScreen extends ConsumerWidget {
  const DataManagementScreen({super.key});

  DateTime _parseDate(String dateStr) {
    // Parse date in MM/dd/yyyy HH:mm:ss format
    final dateFormat = DateFormat('MM/dd/yyyy HH:mm:ss');
    try {
      return dateFormat.parse(dateStr.trim());
    } catch (e) {
      // Fallback to just MM/dd/yyyy if time is not included
      final shortDateFormat = DateFormat('MM/dd/yyyy');
      return shortDateFormat.parse(dateStr.trim());
    }
  }

  Future<void> _clearAllData(BuildContext context, WidgetRef ref) async {
    final tradeService = ref.read(tradeServiceProvider);
    final userId = ref.read(currentUserIdProvider);

    // Store ScaffoldMessenger reference
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      // Clear any existing SnackBars
      scaffoldMessenger.clearSnackBars();

      // Show loading indicator
      if (context.mounted) {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Clearing data...'),
            duration: Duration(seconds: 1),
          ),
        );
      }

      // Delete all trades for the user
      final trades = await tradeService.getTrades(userId);
      for (final trade in trades) {
        await tradeService.deleteTrade(userId, trade.id!);
      }

      // Notify that data has changed
      ref.read(tradeDataProvider.notifier).notifyDataChanged();

      // Show success message if context is still mounted
      if (context.mounted) {
        scaffoldMessenger.clearSnackBars();
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Successfully cleared ${trades.length} trades'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      print('Error clearing data: $e');
      if (context.mounted) {
        scaffoldMessenger.clearSnackBars();
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Error clearing data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadSampleData(BuildContext context, WidgetRef ref) async {
    final tradeService = ref.read(tradeServiceProvider);
    final userId = ref.read(currentUserIdProvider);

    try {
      // Show loading indicator
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
                SizedBox(width: 16),
                Text('Loading sample data...'),
              ],
            ),
            duration: Duration(seconds: 1),
          ),
        );
      }

      // Load sample CSV data
      final String rawCsv =
          await rootBundle.loadString('samples/Performance (1).csv');
      final List<List<dynamic>> csvData =
          const CsvToListConverter().convert(rawCsv);

      // Skip header row
      final trades = csvData
          .skip(1)
          .map((row) {
            try {
              final buyDate = _parseDate(row[10].toString());
              final sellDate = _parseDate(row[11].toString());

              // Parse PnL from the CSV (remove $ and , from the string)
              final pnlStr =
                  row[9].toString().replaceAll('\$', '').replaceAll(',', '');
              final pnl = double.parse(pnlStr);

              return {
                'id': DateTime.now().millisecondsSinceEpoch.toString(),
                'symbol': row[0].toString(),
                'type': 'LONG',
                'status': 'CLOSED',
                'entryPrice': double.parse(row[7].toString()),
                'exitPrice': double.parse(row[8].toString()),
                'quantity': double.parse(row[6].toString()),
                'entryDate': buyDate.toIso8601String(),
                'exitDate': sellDate.toIso8601String(),
                'accountId': userId,
                'userId': userId,
                'updatedAt': DateTime.now().toIso8601String(),
                'fees': 0.0,
                'tags': [],
                'predefinedPnL': pnl, // Use the PnL from the CSV file
              };
            } catch (e) {
              print('Error parsing row: $row');
              print('Error details: $e');
              return null;
            }
          })
          .where((trade) => trade != null)
          .cast<Map<String, dynamic>>()
          .toList();

      if (trades.isEmpty) {
        throw Exception('No valid trades found in the CSV file');
      }

      // Save trades to database
      for (final trade in trades) {
        await tradeService.createTrade(userId, trade);
      }

      // Notify that data has changed
      ref.read(tradeDataProvider.notifier).notifyDataChanged();
      ref.refresh(tradeServiceProvider);

      if (context.mounted) {
        final scaffoldMessenger = ScaffoldMessenger.of(context);
        scaffoldMessenger.clearSnackBars();
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 16),
                Text('${trades.length} trades imported successfully'),
              ],
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        final scaffoldMessenger = ScaffoldMessenger.of(context);
        scaffoldMessenger.clearSnackBars();
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error_outline, color: Colors.white),
                SizedBox(width: 16),
                Expanded(child: Text('Import failed: ${e.toString()}')),
              ],
            ),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 5),
          ),
        );
      }
    }
  }

  Future<void> _downloadSampleFile(BuildContext context) async {
    try {
      final String rawCsv =
          await rootBundle.loadString('samples/Performance (1).csv');

      if (kIsWeb) {
        // Web platform: Use Blob and anchor element
        final blob = html.Blob([rawCsv], 'text/csv');
        final url = html.Url.createObjectUrlFromBlob(blob);
        final anchor = html.AnchorElement(href: url)
          ..setAttribute('download', 'sample_trades.csv')
          ..click();
        html.Url.revokeObjectUrl(url);
      } else {
        // Mobile platform: Save to temporary directory and share
        final directory = await getTemporaryDirectory();
        final file = File('${directory.path}/sample_trades.csv');
        await file.writeAsString(rawCsv);
        await Share.shareXFiles(
          [XFile(file.path)],
          subject: 'Sample Trades Data Template',
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error downloading sample file: $e')),
        );
      }
    }
  }

  void _showClearDataDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      barrierDismissible: false, // Prevent dismissing by tapping outside
      builder: (dialogContext) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text(
          'Are you sure you want to delete all trades data? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              // Close the dialog first
              Navigator.pop(dialogContext);
              // Then clear the data
              await _clearAllData(context, ref);
            },
            style: TextButton.styleFrom(
              foregroundColor: AppColors.dangerRed,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showLoadSampleDataDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Load Sample Data'),
        content: const Text(
          'This will load sample trading data. Do you want to continue?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(dialogContext);
              _loadSampleData(context, ref);
            },
            style: TextButton.styleFrom(
              foregroundColor: AppColors.primaryBlue,
            ),
            child: const Text('Load'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Data Management'),
        centerTitle: false,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                'Data Operations',
                style: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.5,
                ),
              ),
            ),
            Card(
              margin: const EdgeInsets.symmetric(horizontal: 16.0),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(color: AppColors.border),
              ),
              child: Column(
                children: [
                  ListTile(
                    leading:
                        Icon(Icons.delete_forever, color: AppColors.dangerRed),
                    title: Text(
                      'Clear All Data',
                      style: TextStyle(color: AppColors.textPrimary),
                    ),
                    subtitle: Text(
                      'This will delete all trades data',
                      style: TextStyle(color: AppColors.textSecondary),
                    ),
                    onTap: () => _showClearDataDialog(context, ref),
                  ),
                  Divider(color: AppColors.border),
                  ListTile(
                    leading:
                        Icon(Icons.file_download, color: AppColors.primaryBlue),
                    title: Text(
                      'Load Sample Data',
                      style: TextStyle(color: AppColors.textPrimary),
                    ),
                    subtitle: Text(
                      'This will load sample trading data',
                      style: TextStyle(color: AppColors.textSecondary),
                    ),
                    onTap: () => _showLoadSampleDataDialog(context, ref),
                  ),
                  Divider(color: AppColors.border),
                  ListTile(
                    leading: Icon(Icons.download_rounded,
                        color: AppColors.primaryBlue),
                    title: Text(
                      'Download Sample CSV',
                      style: TextStyle(color: AppColors.textPrimary),
                    ),
                    subtitle: Text(
                      'Download the sample data template',
                      style: TextStyle(color: AppColors.textSecondary),
                    ),
                    onTap: () => _downloadSampleFile(context),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                'Data Statistics',
                style: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.5,
                ),
              ),
            ),
            Card(
              margin: const EdgeInsets.symmetric(horizontal: 16.0),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(color: AppColors.border),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Consumer(
                  builder: (context, ref, _) {
                    final userId = ref.watch(currentUserIdProvider);
                    final tradeService = ref.watch(tradeServiceProvider);

                    return FutureBuilder(
                      future: tradeService.getTrades(userId),
                      builder: (context, snapshot) {
                        if (snapshot.connectionState ==
                            ConnectionState.waiting) {
                          return const Center(
                              child: CircularProgressIndicator());
                        }

                        final trades = snapshot.data ?? [];
                        return Column(
                          children: [
                            _buildStatItem(
                              'Total Trades',
                              trades.length.toString(),
                              Icons.analytics_outlined,
                            ),
                            const SizedBox(height: 16),
                            _buildStatItem(
                              'Last Updated',
                              trades.isEmpty ? 'Never' : 'Recently',
                              Icons.update,
                            ),
                          ],
                        );
                      },
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: AppColors.textSecondary, size: 20),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: TextStyle(
                  color: AppColors.textPrimary,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
