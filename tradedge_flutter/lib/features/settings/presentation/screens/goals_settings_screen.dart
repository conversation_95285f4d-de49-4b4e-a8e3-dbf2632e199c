import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/providers/auth_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart'
    show Settings, GoalSettings;

class GoalsSettingsScreen extends ConsumerStatefulWidget {
  const GoalsSettingsScreen({super.key});

  @override
  ConsumerState<GoalsSettingsScreen> createState() =>
      _GoalsSettingsScreenState();
}

class _GoalsSettingsScreenState extends ConsumerState<GoalsSettingsScreen> {
  bool _isLoading = true;
  Settings? _settings;
  Map<String, bool> _activeMetrics = {
    'targetPnL': true,
    'winRate': true,
    'profitFactor': true,
    'averageRR': true,
    'maxDrawdown': true,
    'riskPerTrade': true,
    'maxOpenTrades': true,
    'tradesPerWeek': true,
    'journalEntries': true,
    'planAdherence': true,
  };

  Map<String, double> _metricGoals = {
    'targetPnL': 1000,
    'winRate': 60,
    'profitFactor': 2,
    'averageRR': 2,
    'maxDrawdown': 10,
    'riskPerTrade': 1,
    'maxOpenTrades': 3,
    'tradesPerWeek': 10,
    'journalEntries': 5,
    'planAdherence': 90,
  };

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final userId = ref.read(currentUserIdProvider);
      final settingsService = ref.read(settingsServiceProvider);

      try {
        final settings = await settingsService.getSettings();
        setState(() {
          _settings = settings;
          // Load saved metrics from settings
          if (settings.goals != null) {
            _activeMetrics =
                Map<String, bool>.from(settings.goals.activeMetrics);
            _metricGoals = Map<String, double>.from(settings.goals.metricGoals);
          }
          _isLoading = false;
        });
        print('Loaded existing settings: ${settings.toJson()}');
      } catch (e) {
        print('Settings not initialized, initializing now...');
        await settingsService.initialize(userId);
        final settings = await settingsService.getSettings();

        // Initialize with default values if no goals exist
        if (settings.goals == null || settings.goals.activeMetrics.isEmpty) {
          await settingsService.updateGoalSettings(
            GoalSettings(
              activeMetrics: _activeMetrics,
              metricGoals: _metricGoals,
            ),
          );
        }

        setState(() {
          _settings = settings;
          _isLoading = false;
        });
        print('Initialized new settings: ${settings.toJson()}');
      }
    } catch (e) {
      print('Error loading settings: $e');
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _updateMetricGoal(String metricId, double value) async {
    try {
      setState(() {
        _metricGoals[metricId] = value;
      });

      final settingsService = ref.read(settingsServiceProvider);
      await settingsService.updateGoalSettings(
        GoalSettings(
          activeMetrics: _activeMetrics,
          metricGoals: _metricGoals,
        ),
      );
    } catch (e) {
      print('Error updating metric goal: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save goal: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _toggleMetric(String metricId) async {
    try {
      setState(() {
        _activeMetrics[metricId] = !_activeMetrics[metricId]!;
      });

      final settingsService = ref.read(settingsServiceProvider);
      await settingsService.updateGoalSettings(
        GoalSettings(
          activeMetrics: _activeMetrics,
          metricGoals: _metricGoals,
        ),
      );
    } catch (e) {
      print('Error toggling metric: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save metric toggle: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _resetCategory(String category) async {
    // TODO: Reset metrics in category to default values
  }

  Future<void> _showGoalValueEditor(_MetricItem metric) async {
    final controller =
        TextEditingController(text: metric.defaultGoal.toString());
    final formKey = GlobalKey<FormState>();

    final result = await showDialog<double>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit ${metric.name} Goal'),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(metric.description),
              const SizedBox(height: 16),
              TextFormField(
                controller: controller,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                decoration: InputDecoration(
                  labelText: 'Goal Value',
                  suffixText: metric.unit,
                  border: const OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a value';
                  }
                  final number = double.tryParse(value);
                  if (number == null) {
                    return 'Please enter a valid number';
                  }
                  if (number < metric.min) {
                    return 'Value must be at least ${metric.min}${metric.unit}';
                  }
                  if (number > metric.max) {
                    return 'Value must be at most ${metric.max}${metric.unit}';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              if (formKey.currentState?.validate() ?? false) {
                final value = double.parse(controller.text);
                Navigator.of(context).pop(value);
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );

    if (result != null) {
      await _updateMetricGoal(metric.id, result);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_settings == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Goals'),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text('Settings not available'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadSettings,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Goals'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Goal Categories',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            _buildGoalCategory(
              theme,
              title: 'Performance',
              color: const Color(0xFF22C55E),
              icon: Icons.trending_up,
              description:
                  'Track your profit-related goals including win rate and profit factor',
              metrics: [
                _MetricItem(
                  id: 'targetPnL',
                  name: 'Target P&L',
                  description: 'Target profit and loss goal',
                  defaultValue: _activeMetrics['targetPnL'] ?? true,
                  defaultGoal: _metricGoals['targetPnL'] ?? 1000.0,
                  unit: '\$',
                  min: 100,
                  max: 100000,
                  step: 100,
                ),
                _MetricItem(
                  id: 'winRate',
                  name: 'Win Rate',
                  description: 'Percentage of winning trades',
                  defaultValue: _activeMetrics['winRate']!,
                  defaultGoal: _metricGoals['winRate']!,
                  unit: '%',
                  min: 0,
                  max: 100,
                ),
                _MetricItem(
                  id: 'profitFactor',
                  name: 'Profit Factor',
                  description: 'Ratio of gross profit to gross loss',
                  defaultValue: _activeMetrics['profitFactor']!,
                  defaultGoal: _metricGoals['profitFactor']!,
                  unit: '',
                  min: 0,
                  max: 10,
                  step: 0.1,
                ),
                _MetricItem(
                  id: 'averageRR',
                  name: 'Average R:R',
                  description: 'Average reward to risk ratio',
                  defaultValue: _activeMetrics['averageRR']!,
                  defaultGoal: _metricGoals['averageRR']!,
                  unit: '',
                  min: 0,
                  max: 10,
                  step: 0.1,
                ),
              ],
            ),
            const SizedBox(height: 24),
            _buildGoalCategory(
              theme,
              title: 'Risk Management',
              color: const Color(0xFFB7C1CF),
              icon: Icons.shield_outlined,
              description:
                  'Monitor risk management metrics and protective measures',
              metrics: [
                _MetricItem(
                  id: 'maxDrawdown',
                  name: 'Max Drawdown',
                  description: 'Maximum peak-to-trough decline',
                  defaultValue: _activeMetrics['maxDrawdown']!,
                  defaultGoal: _metricGoals['maxDrawdown']!,
                  unit: '%',
                  min: 0,
                  max: 100,
                ),
                _MetricItem(
                  id: 'riskPerTrade',
                  name: 'Risk per Trade',
                  description: 'Maximum risk per trade',
                  defaultValue: _activeMetrics['riskPerTrade']!,
                  defaultGoal: _metricGoals['riskPerTrade']!,
                  unit: '%',
                  min: 0,
                  max: 10,
                  step: 0.1,
                ),
                _MetricItem(
                  id: 'maxOpenTrades',
                  name: 'Max Open Trades',
                  description: 'Maximum number of open trades',
                  defaultValue: _activeMetrics['maxOpenTrades']!,
                  defaultGoal: _metricGoals['maxOpenTrades']!,
                  unit: '',
                  min: 1,
                  max: 10,
                ),
              ],
            ),
            const SizedBox(height: 24),
            _buildGoalCategory(
              theme,
              title: 'Consistency',
              color: const Color(0xFF3B82F6),
              icon: Icons.bolt_outlined,
              description: 'Track trading consistency and focus metrics',
              metrics: [
                _MetricItem(
                  id: 'tradesPerWeek',
                  name: 'Trades per Week',
                  description: 'Target number of trades per week',
                  defaultValue: _activeMetrics['tradesPerWeek']!,
                  defaultGoal: _metricGoals['tradesPerWeek']!,
                  unit: '',
                  min: 0,
                  max: 100,
                ),
                _MetricItem(
                  id: 'journalEntries',
                  name: 'Journal Entries',
                  description: 'Target number of journal entries per week',
                  defaultValue: _activeMetrics['journalEntries']!,
                  defaultGoal: _metricGoals['journalEntries']!,
                  unit: '',
                  min: 0,
                  max: 50,
                ),
                _MetricItem(
                  id: 'planAdherence',
                  name: 'Plan Adherence',
                  description: 'Percentage of trades following the plan',
                  defaultValue: _activeMetrics['planAdherence']!,
                  defaultGoal: _metricGoals['planAdherence']!,
                  unit: '%',
                  min: 0,
                  max: 100,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalCategory(
    ThemeData theme, {
    required String title,
    required Color color,
    required IconData icon,
    required String description,
    required List<_MetricItem> metrics,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.dividerColor.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: color.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          icon,
                          color: color,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              title,
                              style: theme.textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            Text(
                              description,
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.textTheme.bodySmall?.color
                                    ?.withOpacity(0.8),
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 2,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _resetCategory(title.toLowerCase()),
                  icon: const Icon(Icons.refresh, size: 16),
                  label: const Text('Reset'),
                  style: TextButton.styleFrom(
                    foregroundColor: theme.textTheme.bodyMedium?.color,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: metrics.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final metric = metrics[index];
              return ListTile(
                title: Text(metric.name),
                subtitle: Text(metric.description),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    InkWell(
                      onTap: () => _showGoalValueEditor(metric),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: theme.cardColor.withOpacity(0.5),
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(
                            color: theme.dividerColor.withOpacity(0.2),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              '${metric.defaultGoal}${metric.unit}',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Icon(
                              Icons.edit_outlined,
                              size: 16,
                              color: theme.textTheme.bodyMedium?.color
                                  ?.withOpacity(0.8),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Switch.adaptive(
                      value: metric.defaultValue,
                      onChanged: (value) => _toggleMetric(metric.id),
                      activeColor: color,
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

class _MetricItem {
  final String id;
  final String name;
  final String description;
  final bool defaultValue;
  final double defaultGoal;
  final String unit;
  final double min;
  final double max;
  final double? step;

  const _MetricItem({
    required this.id,
    required this.name,
    required this.description,
    required this.defaultValue,
    required this.defaultGoal,
    required this.unit,
    required this.min,
    required this.max,
    this.step,
  });
}
