import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/providers/auth_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart' show Settings;

class DataManagementSettingsScreen extends ConsumerStatefulWidget {
  const DataManagementSettingsScreen({super.key});

  @override
  ConsumerState<DataManagementSettingsScreen> createState() =>
      _DataManagementSettingsScreenState();
}

class _DataManagementSettingsScreenState
    extends ConsumerState<DataManagementSettingsScreen> {
  bool _isLoading = true;
  Settings? _settings;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final userId = ref.read(currentUserIdProvider);
      final settingsService = ref.read(settingsServiceProvider);

      // Try to get settings first
      try {
        final settings = await settingsService.getSettings();
        setState(() {
          _settings = settings;
          _isLoading = false;
        });
        print('Loaded existing settings: ${settings.toJson()}');
        return;
      } catch (e) {
        print('Settings not initialized, initializing now: $e');
        // Initialize only if getting settings failed
        await settingsService.initialize(userId);
        final settings = await settingsService.getSettings();
        print('Initialized new settings: ${settings.toJson()}');
        setState(() {
          _settings = settings;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading settings: $e');
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _updateAutoBackup(bool enabled, [int? frequency]) async {
    try {
      final settingsService = ref.read(settingsServiceProvider);
      final updatedSettings = await settingsService.updateAutoBackup(enabled);
      setState(() => _settings = updatedSettings);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to update backup settings'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_settings == null) {
      return const Scaffold(
        body: Center(
          child: Text('Failed to load settings'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Data Management'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Backup Settings',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.dividerColor.withOpacity(0.2),
                ),
              ),
              child: Column(
                children: [
                  ListTile(
                    title: const Text('Auto Backup'),
                    subtitle: const Text('Automatically backup your data'),
                    trailing: Switch.adaptive(
                      value: _settings!.autoBackup,
                      onChanged: (value) => _updateAutoBackup(value),
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Backup Frequency'),
                    subtitle: Text('Every ${_settings!.backupFrequency} days'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () async {
                      final frequency = await showDialog<int>(
                        context: context,
                        builder: (context) => _BackupFrequencyDialog(
                          initialValue: _settings!.backupFrequency,
                        ),
                      );
                      if (frequency != null) {
                        await _updateAutoBackup(
                            _settings!.autoBackup, frequency);
                      }
                    },
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Last Backup'),
                    subtitle: Text(_settings!.updatedAt.toString()),
                    trailing: TextButton(
                      onPressed: () {
                        // TODO: Implement manual backup
                      },
                      child: const Text('Backup Now'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _BackupFrequencyDialog extends StatefulWidget {
  final int initialValue;

  const _BackupFrequencyDialog({required this.initialValue});

  @override
  State<_BackupFrequencyDialog> createState() => _BackupFrequencyDialogState();
}

class _BackupFrequencyDialogState extends State<_BackupFrequencyDialog> {
  late final TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue.toString());
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Backup Frequency'),
      content: TextField(
        controller: _controller,
        keyboardType: TextInputType.number,
        decoration: const InputDecoration(
          labelText: 'Days between backups',
          hintText: 'Enter number of days',
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            final value = int.tryParse(_controller.text);
            if (value != null && value > 0) {
              Navigator.of(context).pop(value);
            }
          },
          child: const Text('Save'),
        ),
      ],
    );
  }
}
