import 'package:trade_reviewer_core/trade_reviewer_core.dart' as core;

enum AccountStatus {
  CONNECTED,
  DISCONNECTED,
  PENDING,
  ERROR,
}

enum ApiKeyStatus {
  ACTIVE,
  INACTIVE,
  EXPIRED,
}

class TradingAccount {
  final String id;
  final String name;
  final String provider;
  final AccountStatus status;
  final DateTime lastSynced;
  final Map<String, String> credentials;
  final double initialCapital;
  final bool isActive;

  TradingAccount({
    required this.id,
    required this.name,
    required this.provider,
    required this.status,
    required this.lastSynced,
    required this.credentials,
    required this.initialCapital,
    this.isActive = true,
  });

  TradingAccount copyWith({
    String? id,
    String? name,
    String? provider,
    AccountStatus? status,
    DateTime? lastSynced,
    Map<String, String>? credentials,
    double? initialCapital,
    bool? isActive,
  }) {
    return TradingAccount(
      id: id ?? this.id,
      name: name ?? this.name,
      provider: provider ?? this.provider,
      status: status ?? this.status,
      lastSynced: lastSynced ?? this.lastSynced,
      credentials: credentials ?? this.credentials,
      initialCapital: initialCapital ?? this.initialCapital,
      isActive: isActive ?? this.isActive,
    );
  }

  factory TradingAccount.fromCore(core.TradingAccount account) {
    return TradingAccount(
      id: account.id,
      name: account.name,
      provider: account.provider,
      status: AccountStatus.values.firstWhere(
        (s) =>
            s.toString().split('.').last ==
            account.status.toString().split('.').last,
      ),
      lastSynced: account.lastSynced,
      credentials: account.credentials,
      initialCapital: account.initialCapital,
      isActive: account.isActive,
    );
  }

  core.TradingAccount toCore() {
    return core.TradingAccount(
      id: id,
      name: name,
      provider: provider,
      status: core.AccountStatus.values.firstWhere(
        (s) =>
            s.toString().split('.').last == status.toString().split('.').last,
      ),
      lastSynced: lastSynced,
      credentials: credentials,
      initialCapital: initialCapital,
      isActive: isActive,
    );
  }
}

class ApiKey {
  final String id;
  final String name;
  final String key;
  final String? secret;
  final ApiKeyStatus status;
  final DateTime? expiryDate;

  ApiKey({
    required this.id,
    required this.name,
    required this.key,
    this.secret,
    required this.status,
    this.expiryDate,
  });

  factory ApiKey.fromCore(core.ApiKey apiKey) {
    return ApiKey(
      id: apiKey.id,
      name: apiKey.name,
      key: apiKey.key,
      secret: apiKey.secret,
      status: ApiKeyStatus.values.firstWhere(
        (s) =>
            s.toString().split('.').last ==
            apiKey.status.toString().split('.').last,
      ),
      expiryDate: apiKey.expiryDate,
    );
  }

  core.ApiKey toCore() {
    return core.ApiKey(
      id: id,
      name: name,
      key: key,
      secret: secret,
      status: core.ApiKeyStatus.values.firstWhere(
        (s) =>
            s.toString().split('.').last == status.toString().split('.').last,
      ),
      expiryDate: expiryDate,
    );
  }
}

class AccountSettings {
  final List<TradingAccount> tradingAccounts;
  final List<ApiKey> apiKeys;
  final bool autoSync;
  final int syncFrequency;

  AccountSettings({
    required this.tradingAccounts,
    required this.apiKeys,
    required this.autoSync,
    required this.syncFrequency,
  });

  factory AccountSettings.fromCore(core.AccountSettings settings) {
    return AccountSettings(
      tradingAccounts: settings.tradingAccounts
          .map((a) => TradingAccount.fromCore(a))
          .toList(),
      apiKeys: settings.apiKeys.map((k) => ApiKey.fromCore(k)).toList(),
      autoSync: settings.autoSync,
      syncFrequency: settings.syncFrequency,
    );
  }

  core.AccountSettings toCore() {
    return core.AccountSettings(
      tradingAccounts: tradingAccounts.map((a) => a.toCore()).toList(),
      apiKeys: apiKeys.map((k) => k.toCore()).toList(),
      autoSync: autoSync,
      syncFrequency: syncFrequency,
    );
  }
}
