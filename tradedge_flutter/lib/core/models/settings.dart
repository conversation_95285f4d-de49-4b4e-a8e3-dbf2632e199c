import 'package:trade_reviewer_core/trade_reviewer_core.dart' as core;
import 'account_settings.dart';

class Settings {
  final String userId;
  final core.TradeDefaults tradeDefaults;
  final core.DisplaySettings display;
  final core.BacktestSettings backtest;
  final core.GoalSettings goals;
  final AccountSettings accounts;
  final core.NotificationSettings notifications;
  final bool autoSync;
  final bool autoBackup;
  final int backupFrequency;
  final DateTime updatedAt;

  Settings({
    required this.userId,
    required this.tradeDefaults,
    required this.display,
    required this.backtest,
    required this.goals,
    required this.accounts,
    required this.notifications,
    required this.autoSync,
    required this.autoBackup,
    required this.backupFrequency,
    required this.updatedAt,
  });

  factory Settings.fromCore(core.Settings settings) {
    return Settings(
      userId: settings.userId,
      tradeDefaults: settings.tradeDefaults,
      display: settings.display,
      backtest: settings.backtest,
      goals: settings.goals,
      accounts: AccountSettings.fromCore(settings.accounts),
      notifications: settings.notifications,
      autoSync: settings.autoSync,
      autoBackup: settings.autoBackup,
      backupFrequency: settings.backupFrequency,
      updatedAt: settings.updatedAt,
    );
  }

  core.Settings toCore() {
    return core.Settings(
      userId: userId,
      tradeDefaults: tradeDefaults,
      display: display,
      backtest: backtest,
      goals: goals,
      accounts: accounts.toCore(),
      notifications: notifications,
      autoSync: autoSync,
      autoBackup: autoBackup,
      backupFrequency: backupFrequency,
      updatedAt: updatedAt,
    );
  }
}
