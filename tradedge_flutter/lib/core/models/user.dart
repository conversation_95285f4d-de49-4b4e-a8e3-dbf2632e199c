import 'package:flutter/foundation.dart';

class UserPreferences {
  final String language;
  final String timezone;
  final bool darkMode;
  final String defaultCurrency;
  final bool showPnLInPercent;
  final bool autoSyncEnabled;
  final bool autoLogoutEnabled;
  final bool twoFactorEnabled;
  // Privacy settings
  final bool profilePublic;
  final bool showTradeHistory;
  final bool showProfitLoss;
  final bool showPortfolioValue;
  final bool allowDataCollection;
  final bool allowCrashReports;
  final bool allowAnalytics;

  const UserPreferences({
    this.language = 'en',
    this.timezone = 'UTC',
    this.darkMode = false,
    this.defaultCurrency = 'USD',
    this.showPnLInPercent = true,
    this.autoSyncEnabled = true,
    this.autoLogoutEnabled = true,
    this.twoFactorEnabled = false,
    // Privacy settings
    this.profilePublic = false,
    this.showTradeHistory = false,
    this.showProfitLoss = false,
    this.showPortfolioValue = false,
    this.allowDataCollection = true,
    this.allowCrashReports = true,
    this.allowAnalytics = true,
  });

  UserPreferences copyWith({
    String? language,
    String? timezone,
    bool? darkMode,
    String? defaultCurrency,
    bool? showPnLInPercent,
    bool? autoSyncEnabled,
    bool? autoLogoutEnabled,
    bool? twoFactorEnabled,
    // Privacy settings
    bool? profilePublic,
    bool? showTradeHistory,
    bool? showProfitLoss,
    bool? showPortfolioValue,
    bool? allowDataCollection,
    bool? allowCrashReports,
    bool? allowAnalytics,
  }) {
    return UserPreferences(
      language: language ?? this.language,
      timezone: timezone ?? this.timezone,
      darkMode: darkMode ?? this.darkMode,
      defaultCurrency: defaultCurrency ?? this.defaultCurrency,
      showPnLInPercent: showPnLInPercent ?? this.showPnLInPercent,
      autoSyncEnabled: autoSyncEnabled ?? this.autoSyncEnabled,
      autoLogoutEnabled: autoLogoutEnabled ?? this.autoLogoutEnabled,
      twoFactorEnabled: twoFactorEnabled ?? this.twoFactorEnabled,
      // Privacy settings
      profilePublic: profilePublic ?? this.profilePublic,
      showTradeHistory: showTradeHistory ?? this.showTradeHistory,
      showProfitLoss: showProfitLoss ?? this.showProfitLoss,
      showPortfolioValue: showPortfolioValue ?? this.showPortfolioValue,
      allowDataCollection: allowDataCollection ?? this.allowDataCollection,
      allowCrashReports: allowCrashReports ?? this.allowCrashReports,
      allowAnalytics: allowAnalytics ?? this.allowAnalytics,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'language': language,
      'timezone': timezone,
      'darkMode': darkMode,
      'defaultCurrency': defaultCurrency,
      'showPnLInPercent': showPnLInPercent,
      'autoSyncEnabled': autoSyncEnabled,
      'autoLogoutEnabled': autoLogoutEnabled,
      'twoFactorEnabled': twoFactorEnabled,
      // Privacy settings
      'profilePublic': profilePublic,
      'showTradeHistory': showTradeHistory,
      'showProfitLoss': showProfitLoss,
      'showPortfolioValue': showPortfolioValue,
      'allowDataCollection': allowDataCollection,
      'allowCrashReports': allowCrashReports,
      'allowAnalytics': allowAnalytics,
    };
  }

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      language: json['language'] as String? ?? 'en',
      timezone: json['timezone'] as String? ?? 'UTC',
      darkMode: json['darkMode'] as bool? ?? false,
      defaultCurrency: json['defaultCurrency'] as String? ?? 'USD',
      showPnLInPercent: json['showPnLInPercent'] as bool? ?? true,
      autoSyncEnabled: json['autoSyncEnabled'] as bool? ?? true,
      autoLogoutEnabled: json['autoLogoutEnabled'] as bool? ?? true,
      twoFactorEnabled: json['twoFactorEnabled'] as bool? ?? false,
      // Privacy settings
      profilePublic: json['profilePublic'] as bool? ?? false,
      showTradeHistory: json['showTradeHistory'] as bool? ?? false,
      showProfitLoss: json['showProfitLoss'] as bool? ?? false,
      showPortfolioValue: json['showPortfolioValue'] as bool? ?? false,
      allowDataCollection: json['allowDataCollection'] as bool? ?? true,
      allowCrashReports: json['allowCrashReports'] as bool? ?? true,
      allowAnalytics: json['allowAnalytics'] as bool? ?? true,
    );
  }
}

@immutable
class User {
  final String id;
  final String email;
  final String name;
  final UserPreferences preferences;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isEmailVerified;
  final String? profileImageUrl;
  final String? phone;

  const User({
    required this.id,
    required this.email,
    required this.name,
    required this.preferences,
    required this.createdAt,
    required this.updatedAt,
    this.isEmailVerified = false,
    this.profileImageUrl,
    this.phone,
  });

  User copyWith({
    String? id,
    String? email,
    String? name,
    UserPreferences? preferences,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isEmailVerified,
    String? profileImageUrl,
    String? phone,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      preferences: preferences ?? this.preferences,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      phone: phone ?? this.phone,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'preferences': preferences.toJson(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isEmailVerified': isEmailVerified,
      'profileImageUrl': profileImageUrl,
      'phone': phone,
    };
  }

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as String,
      email: json['email'] as String,
      name: json['name'] as String,
      preferences:
          UserPreferences.fromJson(json['preferences'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isEmailVerified: json['isEmailVerified'] as bool? ?? false,
      profileImageUrl: json['profileImageUrl'] as String?,
      phone: json['phone'] as String?,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is User &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          email == other.email &&
          name == other.name &&
          preferences == other.preferences &&
          createdAt == other.createdAt &&
          updatedAt == other.updatedAt &&
          isEmailVerified == other.isEmailVerified &&
          profileImageUrl == other.profileImageUrl &&
          phone == other.phone;

  @override
  int get hashCode =>
      id.hashCode ^
      email.hashCode ^
      name.hashCode ^
      preferences.hashCode ^
      createdAt.hashCode ^
      updatedAt.hashCode ^
      isEmailVerified.hashCode ^
      profileImageUrl.hashCode ^
      phone.hashCode;

  @override
  String toString() {
    return 'User{id: $id, email: $email, name: $name, preferences: $preferences, '
        'createdAt: $createdAt, updatedAt: $updatedAt, isEmailVerified: $isEmailVerified, '
        'profileImageUrl: $profileImageUrl, phone: $phone}';
  }
}
