import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart';
import 'package:trade_reviewer_core/services/notification_scheduler_service.dart';
import 'service_provider.dart';
import 'auth_provider.dart';

final notificationServiceProvider = Provider<NotificationsService>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  final userId = ref.watch(currentUserIdProvider);
  final service = NotificationsService(storageService);

  // Initialize the service when created
  ref.onDispose(() async {
    service.dispose();
  });

  // Initialize immediately
  Future(() async {
    await service.initialize(userId);
  });

  return service;
});

final notificationDeliveryServiceProvider =
    Provider<NotificationDeliveryService>((ref) {
  final service = NotificationDeliveryService(
    emailHost: const String.fromEnvironment('EMAIL_HOST',
        defaultValue: 'smtp.gmail.com'),
    emailUsername: const String.fromEnvironment('EMAIL_USERNAME'),
    emailPassword: const String.fromEnvironment('EMAIL_PASSWORD'),
  );

  // Initialize the delivery service
  Future(() async {
    await service.initialize();
  });

  return service;
});

final notificationSchedulerProvider =
    Provider<NotificationSchedulerService>((ref) {
  final deliveryService = ref.watch(notificationDeliveryServiceProvider);
  return NotificationSchedulerService(deliveryService);
});

final activeNotificationsProvider = StreamProvider<List<Notification>>((ref) {
  final service = ref.watch(notificationServiceProvider);
  final userId = ref.watch(currentUserIdProvider);

  // Initialize and get the stream
  return service.getActiveNotificationsStream();
});

final unreadNotificationsCountProvider = Provider<int>((ref) {
  final notifications = ref.watch(activeNotificationsProvider);
  return notifications.when(
    data: (notifications) => notifications.where((n) => !n.read).length,
    loading: () => 0,
    error: (_, __) => 0,
  );
});
