import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart';
import 'package:trade_reviewer_core/services/notification_scheduler_service.dart';
import 'service_provider.dart';
import 'auth_provider.dart';

final notificationServiceProvider = Provider<NotificationsService>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  final service = NotificationsService(storageService);

  // Initialize the service when created
  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

final notificationDeliveryServiceProvider =
    Provider<NotificationDeliveryService>((ref) {
  final service = NotificationDeliveryService(
    emailHost: const String.fromEnvironment('EMAIL_HOST',
        defaultValue: 'smtp.gmail.com'),
    emailUsername: const String.fromEnvironment('EMAIL_USERNAME'),
    emailPassword: const String.fromEnvironment('EMAIL_PASSWORD'),
  );

  return service;
});

final notificationSchedulerProvider =
    Provider<NotificationSchedulerService>((ref) {
  final deliveryService = ref.watch(notificationDeliveryServiceProvider);
  return NotificationSchedulerService(deliveryService);
});

final activeNotificationsProvider = StreamProvider<List<Notification>>((ref) {
  final service = ref.watch(notificationServiceProvider);

  // Initialize and get the stream
  return service.getActiveNotificationsStream();
});

final unreadNotificationsCountProvider = Provider<int>((ref) {
  final notifications = ref.watch(activeNotificationsProvider);
  return notifications.when(
    data: (notifications) => notifications.where((n) => !n.read).length,
    loading: () => 0,
    error: (_, __) => 0,
  );
});
