import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart'
    show StorageService;

class TimePeriodState {
  final String selectedTimeframe;
  final DateTime? startDate;
  final DateTime? endDate;

  const TimePeriodState({
    required this.selectedTimeframe,
    this.startDate,
    this.endDate,
  });

  TimePeriodState copyWith({
    String? selectedTimeframe,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    return TimePeriodState(
      selectedTimeframe: selectedTimeframe ?? this.selectedTimeframe,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
    );
  }
}

class TimePeriodNotifier extends StateNotifier<TimePeriodState> {
  final StorageService _storageService;

  TimePeriodNotifier(this._storageService)
      : super(const TimePeriodState(selectedTimeframe: 'Today')) {
    _loadSavedTimePeriod();
  }

  Future<void> _loadSavedTimePeriod() async {
    try {
      final timeframe = await _storageService.get<String>('selected_timeframe');
      final startDateStr =
          await _storageService.get<String>('custom_start_date');
      final endDateStr = await _storageService.get<String>('custom_end_date');

      if (timeframe != null) {
        DateTime? startDate;
        DateTime? endDate;

        if (timeframe == 'Custom' &&
            startDateStr != null &&
            endDateStr != null) {
          startDate = DateTime.parse(startDateStr);
          endDate = DateTime.parse(endDateStr);
        }

        state = TimePeriodState(
          selectedTimeframe: timeframe,
          startDate: startDate,
          endDate: endDate,
        );
      }
    } catch (e) {
      print('Error loading time period: $e');
    }
  }

  Future<void> updateTimePeriod({
    required String timeframe,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // Save to storage
      await _storageService.set<String>('selected_timeframe', timeframe);
      if (timeframe == 'Custom' && startDate != null && endDate != null) {
        await _storageService.set<String>(
            'custom_start_date', startDate.toIso8601String());
        await _storageService.set<String>(
            'custom_end_date', endDate.toIso8601String());
      }

      // Update state
      state = TimePeriodState(
        selectedTimeframe: timeframe,
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      print('Error saving time period: $e');
    }
  }

  DateTime? getStartDate() {
    final now = DateTime.now();

    switch (state.selectedTimeframe) {
      case 'Today':
        return DateTime(now.year, now.month, now.day);
      case 'Yesterday':
        final yesterday = now.subtract(const Duration(days: 1));
        return DateTime(yesterday.year, yesterday.month, yesterday.day);
      case '7D':
        return now.subtract(const Duration(days: 7));
      case '30D':
        return now.subtract(const Duration(days: 30));
      case '90D':
        return now.subtract(const Duration(days: 90));
      case 'YTD':
        return DateTime(now.year, 1, 1);
      case 'Custom':
        return state.startDate;
      default:
        return null;
    }
  }

  DateTime? getEndDate() {
    final now = DateTime.now();

    switch (state.selectedTimeframe) {
      case 'Today':
        return DateTime(now.year, now.month, now.day, 23, 59, 59);
      case 'Yesterday':
        final yesterday = now.subtract(const Duration(days: 1));
        return DateTime(
            yesterday.year, yesterday.month, yesterday.day, 23, 59, 59);
      case '7D':
      case '30D':
      case '90D':
      case 'YTD':
        return DateTime(now.year, now.month, now.day, 23, 59, 59);
      case 'Custom':
        if (state.endDate != null) {
          return DateTime(
            state.endDate!.year,
            state.endDate!.month,
            state.endDate!.day,
            23,
            59,
            59,
          );
        }
        return state.endDate;
      default:
        return null;
    }
  }
}

final timePeriodProvider =
    StateNotifierProvider<TimePeriodNotifier, TimePeriodState>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  return TimePeriodNotifier(storageService);
});
