import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart'
    show StorageService;

class AccountFilterState {
  final String selectedAccountId;

  const AccountFilterState({
    this.selectedAccountId = 'all',
  });

  AccountFilterState copyWith({
    String? selectedAccountId,
  }) {
    return AccountFilterState(
      selectedAccountId: selectedAccountId ?? this.selectedAccountId,
    );
  }
}

class AccountFilterNotifier extends StateNotifier<AccountFilterState> {
  final StorageService _storageService;
  static const String _storageKey = 'selected_account_id';

  AccountFilterNotifier(this._storageService)
      : super(const AccountFilterState()) {
    _loadSavedAccount();
  }

  Future<void> _loadSavedAccount() async {
    try {
      final savedAccountId = await _storageService.get<String>(_storageKey);
      state = state.copyWith(selectedAccountId: savedAccountId ?? 'all');
    } catch (e) {
      print('Error loading saved account: $e');
      state = state.copyWith(selectedAccountId: 'all');
    }
  }

  Future<void> updateSelectedAccount(String? accountId) async {
    final normalizedAccountId = accountId ?? 'all';
    state = state.copyWith(selectedAccountId: normalizedAccountId);
    try {
      await _storageService.set<String>(_storageKey, normalizedAccountId);
    } catch (e) {
      print('Error saving selected account: $e');
    }
  }
}

final accountFilterProvider =
    StateNotifierProvider<AccountFilterNotifier, AccountFilterState>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  return AccountFilterNotifier(storageService);
});
