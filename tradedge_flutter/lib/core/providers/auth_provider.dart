import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'service_provider.dart';
import 'notification_provider.dart';

final currentUserIdProvider = StateProvider<String>((ref) {
  // Default user ID for development
  return 'default_user';
});

final authStateProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier(ref);
});

class AuthNotifier extends StateNotifier<AuthState> {
  final Ref _ref;

  AuthNotifier(this._ref) : super(const AuthState.initial()) {
    _initializeAuth();
  }

  Future<void> _initializeAuth() async {
    try {
      state = const AuthState.loading();

      // Initialize services with default user
      final userId = _ref.read(currentUserIdProvider);
      print('AuthNotifier - Initializing with user ID: $userId');

      // Initialize services one by one with proper error handling
      try {
        print('AuthNotifier - Initializing settings service...');
        final settingsService = _ref.read(settingsServiceProvider);
        await settingsService.initialize(userId);
        print('AuthNotifier - Settings service initialized');

        print('AuthNotifier - Initializing profile service...');
        final profileService = _ref.read(profileServiceProvider);
        await profileService.initialize(userId);
        print('AuthNotifier - Profile service initialized');

        print('AuthNotifier - Initializing notification service...');
        final notificationService = _ref.read(notificationServiceProvider);
        await notificationService.initialize(userId);
        print('AuthNotifier - Notification service initialized');

        print('AuthNotifier - All services initialized successfully');
        state = AuthState.authenticated(userId);
      } catch (e, stackTrace) {
        print('AuthNotifier - Error initializing services: $e');
        print('Stack trace: $stackTrace');

        // Still authenticate user even if some services fail
        print('AuthNotifier - Proceeding with partial initialization');
        state = AuthState.authenticated(userId);
      }
    } catch (e, stackTrace) {
      print('AuthNotifier - Fatal error: $e');
      print('Stack trace: $stackTrace');
      state = AuthState.error('Fatal error: $e');
    }
  }
}

class AuthState {
  final String? userId;
  final bool isLoading;
  final String? error;

  const AuthState._({
    this.userId,
    this.isLoading = false,
    this.error,
  });

  const AuthState.initial() : this._();

  const AuthState.loading() : this._(isLoading: true);

  const AuthState.authenticated(String uid) : this._(userId: uid);

  const AuthState.error(String message) : this._(error: message);
}
