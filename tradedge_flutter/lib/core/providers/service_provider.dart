import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart' as core;
import '../services/storage_service.dart' as flutter_storage;

// This provider must be overridden in main.dart with an actual SharedPreferences instance
final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
  throw UnimplementedError('SharedPreferences must be initialized before use');
});

// Storage service that wraps SharedPreferences
final storageServiceProvider = Provider<core.StorageService>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  return flutter_storage.FlutterStorageService(prefs);
});

// Core services that depend on StorageService
final profileServiceProvider = Provider<core.ProfileService>((ref) {
  final storage = ref.watch(storageServiceProvider);
  return core.ProfileService(storage);
});

final settingsServiceProvider = Provider<core.SettingsService>((ref) {
  final storage = ref.watch(storageServiceProvider);
  return core.SettingsService(storage);
});

final tradeServiceProvider = Provider<core.TradeService>((ref) {
  final storage = ref.watch(storageServiceProvider);
  return core.TradeService(storage);
});
