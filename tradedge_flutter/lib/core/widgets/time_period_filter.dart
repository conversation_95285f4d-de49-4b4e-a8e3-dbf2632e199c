import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/providers/time_period_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/account_filter_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_flutter/core/models/account_settings.dart';
import 'package:trade_reviewer_flutter/core/models/settings.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart' as core;
import 'package:intl/intl.dart';

class TimePeriodFilter extends ConsumerWidget {
  const TimePeriodFilter({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.primaryBlue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.calendar_today,
            size: 16,
            color: AppColors.primaryBlue,
          ),
        ),
        const SizedBox(width: 12),
        Text(
          'Time Period:',
          style: TextStyle(
            color: AppColors.textSecondary,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            _getDateRangeText(ref),
            style: TextStyle(
              color: AppColors.textPrimary,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        IconButton(
          icon: const Icon(Icons.filter_list, size: 20),
          onPressed: () => _showFilterBottomSheet(context, ref),
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          color: AppColors.textSecondary,
        ),
      ],
    );
  }

  String _getDateRangeText(WidgetRef ref) {
    final timePeriod = ref.watch(timePeriodProvider);
    final accountFilter = ref.watch(accountFilterProvider);

    String dateText = timePeriod.selectedTimeframe != 'Custom'
        ? timePeriod.selectedTimeframe
        : timePeriod.startDate == null || timePeriod.endDate == null
            ? 'Select Date Range'
            : '${DateFormat('M/d/y').format(timePeriod.startDate!)} - ${DateFormat('M/d/y').format(timePeriod.endDate!)}';

    return dateText;
  }

  void _showFilterBottomSheet(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final timePeriodNotifier = ref.read(timePeriodProvider.notifier);
    final timePeriod = ref.read(timePeriodProvider);
    final accountFilterNotifier = ref.read(accountFilterProvider.notifier);
    final accountFilter = ref.read(accountFilterProvider);
    final settingsService = ref.watch(settingsServiceProvider);

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.85,
          ),
          decoration: BoxDecoration(
            color: theme.scaffoldBackgroundColor,
            borderRadius: const BorderRadius.vertical(
              top: Radius.circular(20),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Container(
                margin: const EdgeInsets.only(top: 8),
                width: 40,
                height: 4,
                alignment: Alignment.center,
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Filter',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
              ),
              const Divider(height: 1),
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: FutureBuilder<core.Settings>(
                    future: settingsService.getSettings(),
                    builder: (context, snapshot) {
                      final accounts = snapshot.data != null
                          ? Settings.fromCore(snapshot.data!)
                              .accounts
                              .tradingAccounts
                          : [];

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          if (accounts.isNotEmpty) ...[
                            Text(
                              'Account',
                              style: theme.textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Container(
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: theme.dividerColor.withOpacity(0.2),
                                ),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: DropdownButtonHideUnderline(
                                child: DropdownButtonFormField<String?>(
                                  value: accountFilter.selectedAccountId,
                                  decoration: const InputDecoration(
                                    contentPadding:
                                        EdgeInsets.symmetric(horizontal: 16),
                                    border: InputBorder.none,
                                  ),
                                  items: [
                                    const DropdownMenuItem(
                                      value: 'all',
                                      child: Text('All Accounts'),
                                    ),
                                    ...accounts
                                        .map((account) => DropdownMenuItem(
                                              value: account.id,
                                              child: Text(account.name),
                                            )),
                                  ],
                                  onChanged: (value) {
                                    accountFilterNotifier
                                        .updateSelectedAccount(value);
                                  },
                                ),
                              ),
                            ),
                            const SizedBox(height: 24),
                          ],
                          Text(
                            'Time Period',
                            style: theme.textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: [
                              _buildTimeframeChip('Today', ref),
                              _buildTimeframeChip('Yesterday', ref),
                              _buildTimeframeChip('7D', ref),
                              _buildTimeframeChip('30D', ref),
                              _buildTimeframeChip('90D', ref),
                              _buildTimeframeChip('YTD', ref),
                              _buildTimeframeChip('All', ref),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Custom Range',
                            style: theme.textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: OutlinedButton.icon(
                                  onPressed: () =>
                                      _selectDate(context, ref, true),
                                  icon: const Icon(Icons.calendar_today),
                                  label: Text(
                                    timePeriod.startDate == null
                                        ? 'Start Date'
                                        : DateFormat('M/d/y')
                                            .format(timePeriod.startDate!),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: OutlinedButton.icon(
                                  onPressed: () =>
                                      _selectDate(context, ref, false),
                                  icon: const Icon(Icons.calendar_today),
                                  label: Text(
                                    timePeriod.endDate == null
                                        ? 'End Date'
                                        : DateFormat('M/d/y')
                                            .format(timePeriod.endDate!),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          timePeriodNotifier.updateTimePeriod(
                            timeframe: 'Today',
                            startDate: null,
                            endDate: null,
                          );
                          accountFilterNotifier.updateSelectedAccount(null);
                          Navigator.pop(context);
                        },
                        child: const Text('Reset'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: FilledButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        child: const Text('Apply'),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTimeframeChip(String label, WidgetRef ref) {
    final timePeriod = ref.watch(timePeriodProvider);
    final isSelected = timePeriod.selectedTimeframe == label;
    final notifier = ref.read(timePeriodProvider.notifier);

    return FilterChip(
      selected: isSelected,
      label: Text(label),
      onSelected: (selected) {
        if (selected) {
          ref.read(timePeriodProvider.notifier).updateTimePeriod(
                timeframe: label,
                startDate: notifier.getStartDate(),
                endDate: notifier.getEndDate(),
              );
          Navigator.pop(ref.context);
        }
      },
      backgroundColor: Colors.transparent,
      selectedColor: AppColors.primaryBlue.withOpacity(0.1),
      side: BorderSide(
        color:
            isSelected ? AppColors.primaryBlue : Colors.grey.withOpacity(0.3),
      ),
      labelStyle: TextStyle(
        color: isSelected ? AppColors.primaryBlue : Colors.grey,
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 8),
      showCheckmark: false,
      avatar: isSelected
          ? Icon(Icons.check, size: 18, color: AppColors.primaryBlue)
          : null,
    );
  }

  Future<void> _selectDate(
      BuildContext context, WidgetRef ref, bool isStart) async {
    final timePeriod = ref.read(timePeriodProvider);
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStart
          ? (timePeriod.startDate ?? DateTime.now())
          : (timePeriod.endDate ?? DateTime.now()),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      ref.read(timePeriodProvider.notifier).updateTimePeriod(
            timeframe: 'Custom',
            startDate: isStart ? picked : timePeriod.startDate,
            endDate: isStart ? timePeriod.endDate : picked,
          );
    }
  }
}
