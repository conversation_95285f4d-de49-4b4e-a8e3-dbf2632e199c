import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:badges/badges.dart' as badges;
import '../providers/notification_provider.dart';
import 'notification_popup.dart';

class NotificationBell extends ConsumerWidget {
  const NotificationBell({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final unreadCount = ref.watch(unreadNotificationsCountProvider);

    return GestureDetector(
      onTap: () => _showNotificationsPopup(context),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: badges.Badge(
          position: badges.BadgePosition.topEnd(top: -8, end: -4),
          showBadge: unreadCount > 0,
          badgeContent: Text(
            unreadCount.toString(),
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
          badgeStyle: const badges.BadgeStyle(
            badgeColor: Colors.red,
            padding: EdgeInsets.all(4),
          ),
          child: const Icon(Icons.notifications_outlined, size: 28),
        ),
      ),
    );
  }

  void _showNotificationsPopup(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const NotificationPopup(),
    );
  }
}
