import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart' hide Notification;
import 'package:trade_reviewer_core/models/notification.dart' as core;
import '../providers/notification_provider.dart';
import '../providers/auth_provider.dart';
import '../theme/app_colors.dart';

class NotificationPopup extends ConsumerWidget {
  const NotificationPopup({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notifications = ref.watch(activeNotificationsProvider);
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      backgroundColor: theme.scaffoldBackgroundColor,
      child: Container(
        width: 400,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          minHeight: 200,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 24, 8, 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.notifications_outlined,
                        color: theme.colorScheme.primary,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Recent',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      notifications.when(
                        data: (notifications) => notifications.isNotEmpty
                            ? TextButton(
                                onPressed: () async {
                                  try {
                                    final userId =
                                        ref.read(currentUserIdProvider);
                                    await ref
                                        .read(notificationServiceProvider)
                                        .markAllAsRead(userId);
                                    // Force refresh both providers
                                    ref.invalidate(activeNotificationsProvider);
                                    ref.invalidate(
                                        unreadNotificationsCountProvider);
                                  } catch (e) {
                                    if (context.mounted) {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        SnackBar(
                                          content: Text(
                                              'Error marking notifications as read: $e'),
                                          behavior: SnackBarBehavior.floating,
                                        ),
                                      );
                                    }
                                  }
                                },
                                child: Text(
                                  'Mark all read',
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    color: theme.colorScheme.primary,
                                  ),
                                ),
                              )
                            : const SizedBox.shrink(),
                        loading: () => const SizedBox.shrink(),
                        error: (_, __) => const SizedBox.shrink(),
                      ),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close),
                        color: theme.colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Expanded(
              child: notifications.when(
                data: (notifications) =>
                    _buildNotificationsList(notifications, ref, theme),
                loading: () => Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const CircularProgressIndicator(),
                      const SizedBox(height: 16),
                      Text(
                        'Loading notifications...',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.6),
                        ),
                      ),
                    ],
                  ),
                ),
                error: (error, stack) => Center(
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 48,
                          color: theme.colorScheme.error.withOpacity(0.8),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Error loading notifications',
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: theme.colorScheme.error,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          error.toString(),
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withOpacity(0.6),
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton(
                          onPressed: () {
                            ref.invalidate(activeNotificationsProvider);
                          },
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationsList(
      List<core.Notification> notifications, WidgetRef ref, ThemeData theme) {
    if (notifications.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.notifications_off_outlined,
                size: 48,
                color: theme.colorScheme.onSurface.withOpacity(0.4),
              ),
              const SizedBox(height: 16),
              Text(
                'No notifications',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'You\'re all caught up!',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.4),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.only(bottom: 16),
      itemCount: notifications.length,
      itemBuilder: (context, index) {
        final notification = notifications[index];
        return _NotificationItem(
          key: ValueKey(notification.id),
          notification: notification,
          onDismiss: () async {
            try {
              await ref
                  .read(notificationServiceProvider)
                  .dismiss(notification.id, notification.userId);
              // Force refresh both providers
              ref.invalidate(activeNotificationsProvider);
              ref.invalidate(unreadNotificationsCountProvider);
            } catch (e) {
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Error dismissing notification: $e'),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              }
            }
          },
        );
      },
    );
  }
}

class _NotificationItem extends ConsumerStatefulWidget {
  final core.Notification notification;
  final VoidCallback onDismiss;

  const _NotificationItem({
    super.key,
    required this.notification,
    required this.onDismiss,
  });

  @override
  ConsumerState<_NotificationItem> createState() => _NotificationItemState();
}

class _NotificationItemState extends ConsumerState<_NotificationItem> {
  bool _isDismissed = false;

  @override
  Widget build(BuildContext context) {
    if (_isDismissed) return const SizedBox.shrink();

    final theme = Theme.of(context);
    final isRead = widget.notification.read;

    return Dismissible(
      key: ValueKey(widget.notification.id),
      direction: DismissDirection.endToStart,
      onDismissed: (_) {
        setState(() => _isDismissed = true);
        widget.onDismiss();
      },
      background: Container(
        color: Colors.red.withOpacity(0.8),
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 24),
        child: const Icon(Icons.delete_outline, color: Colors.white),
      ),
      child: InkWell(
        onTap: () async {
          // Mark as read if not already read
          if (!isRead) {
            await ref
                .read(notificationServiceProvider)
                .markAsRead(widget.notification.id, widget.notification.userId);
          }

          // Close the notification popup
          if (context.mounted) {
            Navigator.of(context).pop();
          }

          // Handle notification action based on type
          switch (widget.notification.type) {
            case core.NotificationType.tradeAlert:
              // For trade alerts, go to trades list
              context.go('/trades');
              break;
            case core.NotificationType.priceAlert:
              // For price alerts, go to trades list
              context.go('/trades');
              break;
            case core.NotificationType.goalAchieved:
              // For goal achievements, go to goals calendar
              context.go('/goals/calendar');
              break;
            case core.NotificationType.riskWarning:
              // For risk warnings, go to trades list
              context.go('/trades');
              break;
            case core.NotificationType.systemUpdate:
              // For system updates, show bottom sheet with update details
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                backgroundColor: theme.scaffoldBackgroundColor,
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
                ),
                builder: (context) => DraggableScrollableSheet(
                  initialChildSize: 0.5,
                  minChildSize: 0.3,
                  maxChildSize: 0.9,
                  expand: false,
                  builder: (context, scrollController) => SingleChildScrollView(
                    controller: scrollController,
                    child: Padding(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.notification.title,
                            style: theme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            widget.notification.message,
                            style: theme.textTheme.bodyLarge,
                          ),
                          if (widget.notification.data != null &&
                              widget.notification.data!
                                  .containsKey('action')) ...[
                            const SizedBox(height: 24),
                            ElevatedButton(
                              onPressed: () {
                                Navigator.pop(context);
                                final action = widget
                                    .notification.data!['action'] as String;
                                if (action == 'update') {
                                  context.go('/settings/general');
                                }
                              },
                              child: const Text('View Details'),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ),
              );
              break;
            case core.NotificationType.backupReminder:
              // For backup reminders, show backup dialog
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Backup Reminder'),
                  content: Text(widget.notification.message),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Later'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        context.go('/settings/data');
                      },
                      child: const Text('Backup Now'),
                    ),
                  ],
                ),
              );
              break;
            case core.NotificationType.journalReminder:
              // For journal reminders, go directly to journal
              context.go('/journal');
              break;
            case core.NotificationType.tradeStats:
              // For trade stats, show stats bottom sheet
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                backgroundColor: theme.scaffoldBackgroundColor,
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
                ),
                builder: (context) => DraggableScrollableSheet(
                  initialChildSize: 0.5,
                  minChildSize: 0.3,
                  maxChildSize: 0.9,
                  expand: false,
                  builder: (context, scrollController) => SingleChildScrollView(
                    controller: scrollController,
                    child: Padding(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Trading Statistics',
                            style: theme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            widget.notification.message,
                            style: theme.textTheme.bodyLarge,
                          ),
                          const SizedBox(height: 24),
                          ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                              context.go('/analytics');
                            },
                            child: const Text('View Full Analytics'),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
              break;
          }
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          decoration: BoxDecoration(
            color: isRead ? null : theme.colorScheme.primary.withOpacity(0.08),
            border: Border(
              bottom: BorderSide(
                color: theme.dividerColor.withOpacity(0.1),
              ),
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildPriorityIndicator(widget.notification.priority),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.notification.title,
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight:
                            isRead ? FontWeight.normal : FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.notification.message,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.8),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _formatTimestamp(widget.notification.createdAt),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.5),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPriorityIndicator(core.NotificationPriority priority) {
    Color color;
    switch (priority) {
      case core.NotificationPriority.high:
        color = Colors.red;
        break;
      case core.NotificationPriority.medium:
        color = Colors.orange;
        break;
      case core.NotificationPriority.low:
        color = Colors.green;
        break;
      case core.NotificationPriority.urgent:
        color = Colors.purple;
        break;
    }

    return Container(
      width: 8,
      height: 8,
      margin: const EdgeInsets.only(top: 6),
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
