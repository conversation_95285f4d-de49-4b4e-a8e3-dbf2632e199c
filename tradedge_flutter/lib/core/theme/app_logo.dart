import 'package:flutter/material.dart';
import 'dart:math';

/// Custom logo widget for the app
class AppLogo extends StatelessWidget {
  final double size;
  final bool showText;

  const AppLogo({
    super.key,
    this.size = 48.0,
    this.showText = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      width: size + (showText ? 10 : 0),
      height: showText ? size * 1.3 : size + 10,
      padding: const EdgeInsets.only(top: 2),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: size,
            height: size,
            child: CustomPaint(
              painter: _LogoPainter(),
            ),
          ),
          if (showText) ...[
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'TRADE',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontSize: size * 0.14,
                    fontWeight: FontWeight.w800,
                    letterSpacing: 0.8,
                    color: const Color(0xFF2C3E50),
                  ),
                ),
                Text(
                  'EDGE',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontSize: size * 0.14,
                    fontWeight: FontWeight.w400,
                    letterSpacing: 0.8,
                    color: const Color(0xFF3498DB),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}

class _LogoPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.35;

    // Define colors
    final blueColor = Color(0xFF3498DB).withOpacity(0.95);
    final greenColor = Color(0xFF2ECC71).withOpacity(0.95);
    final silverColor = Color(0xFFB4B4B4);
    final silverFillColor = Color(0xFFD8D8D8);
    final silverDarkColor = Color(0xFF9E9E9E);

    // Paint for the outer rings with gradient
    final greenRingPaint = Paint()
      ..shader = LinearGradient(
        colors: [
          greenColor,
          greenColor.withOpacity(0.9),
        ],
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
      ).createShader(Rect.fromCircle(center: center, radius: radius))
      ..style = PaintingStyle.stroke
      ..strokeWidth = size.width * 0.09
      ..strokeCap = StrokeCap.round;

    final blueRingPaint = Paint()
      ..shader = LinearGradient(
        colors: [
          blueColor,
          blueColor.withOpacity(0.9),
        ],
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
      ).createShader(Rect.fromCircle(center: center, radius: radius))
      ..style = PaintingStyle.stroke
      ..strokeWidth = size.width * 0.09
      ..strokeCap = StrokeCap.round;

    // Draw complete outer rings (green and blue)
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -0.5 * pi,
      pi, // Green part (half circle)
      false,
      greenRingPaint,
    );

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      0.5 * pi,
      pi, // Blue part (other half circle)
      false,
      blueRingPaint,
    );

    // Draw bold shield
    final shieldPath = Path();
    final shieldWidth = radius * 1.5; // Slightly smaller
    final shieldHeight = radius * 1.6;
    final shieldTop = center.dy - shieldHeight * 0.48;
    final shieldBottom = center.dy + shieldHeight * 0.42;
    final shieldLeft = center.dx - shieldWidth * 0.42;
    final shieldRight = center.dx + shieldWidth * 0.42;

    // Shield outer shape
    shieldPath.moveTo(center.dx, shieldTop);
    shieldPath.lineTo(shieldRight, shieldTop + shieldHeight * 0.3);
    shieldPath.quadraticBezierTo(
      shieldRight,
      shieldBottom - shieldHeight * 0.2,
      center.dx,
      shieldBottom,
    );
    shieldPath.quadraticBezierTo(
      shieldLeft,
      shieldBottom - shieldHeight * 0.2,
      shieldLeft,
      shieldTop + shieldHeight * 0.3,
    );
    shieldPath.lineTo(center.dx, shieldTop);
    shieldPath.close();

    // Paint for the shield fill
    final shieldFillPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          silverFillColor,
          silverColor,
          silverDarkColor,
        ],
        stops: const [0.2, 0.5, 0.9],
      ).createShader(Rect.fromLTRB(
        center.dx - shieldWidth,
        shieldTop,
        center.dx + shieldWidth,
        shieldBottom,
      ))
      ..style = PaintingStyle.fill;

    // Paint for the shield outline
    final shieldOutlinePaint = Paint()
      ..color = silverDarkColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = size.width * 0.045
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    // Draw shield with fill and outline
    canvas.drawPath(shieldPath, shieldFillPaint);
    canvas.drawPath(shieldPath, shieldOutlinePaint);

    // Add subtle highlight at the top
    final highlightPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.center,
        colors: [
          Colors.white.withOpacity(0.4),
          Colors.white.withOpacity(0.0),
        ],
      ).createShader(Rect.fromLTRB(
        shieldLeft,
        shieldTop,
        shieldRight,
        center.dy,
      ))
      ..style = PaintingStyle.fill;

    final highlightPath = Path()
      ..moveTo(center.dx, shieldTop)
      ..lineTo(shieldRight, shieldTop + shieldHeight * 0.3)
      ..lineTo(shieldLeft, shieldTop + shieldHeight * 0.3)
      ..close();

    canvas.drawPath(highlightPath, highlightPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
