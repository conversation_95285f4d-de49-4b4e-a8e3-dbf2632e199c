import 'package:flutter/material.dart';

class AppColors {
  // Theme Colors
  static const Color background = Color(0xFF0F172A); // Dark blue background
  static const Color backgroundAlt = Color(0xFF1E293B); // Slightly lighter blue
  static const Color backgroundLight =
      Color(0xFF334155); // Light blue for hover

  // Primary Colors
  static const Color primaryBlue = Color(0xFF3B82F6);
  static const Color primaryBlueDark = Color(0xFF2563EB);
  static const Color primaryBlueLight = Color(0xFF60A5FA);

  // Secondary Colors - Slate
  static const Color slate50 = Color(0xFFF8FAFC);
  static const Color slate100 = Color(0xFFF1F5F9);
  static const Color slate200 = Color(0xFFE2E8F0);
  static const Color slate300 = Color(0xFFCBD5E1);
  static const Color slate400 = Color(0xFF94A3B8);
  static const Color slate500 = Color(0xFF64748B);
  static const Color slate600 = Color(0xFF475569);
  static const Color slate700 = Color(0xFF334155);
  static const Color slate800 = Color(0xFF1E293B);
  static const Color slate900 = Color(0xFF0F172A);

  // Status Colors
  static const Color successGreen = Color(0xFF22C55E);
  static const Color successGreenDark = Color(0xFF16A34A);
  static const Color successGreenLight = Color(0xFF4ADE80);

  static const Color dangerRed = Color(0xFFEF4444);
  static const Color dangerRedDark = Color(0xFFDC2626);
  static const Color dangerRedLight = Color(0xFFFCA5A5);

  static const Color warningYellow = Color(0xFFF59E0B);
  static const Color warningYellowDark = Color(0xFFD97706);
  static const Color warningYellowLight = Color(0xFFFBBF24);

  // Text Colors
  static const Color textPrimary = Color(0xFFFFFFFF); // White for dark theme
  static const Color textSecondary = Color(0xFF94A3B8); // Slate-400
  static const Color textTertiary = Color(0xFF64748B); // Slate-500

  // Border Colors
  static const Color border = Color(0xFF1E293B); // Slate-800
  static const Color borderLight = Color(0xFF334155); // Slate-700

  // Chart Colors
  static const List<Color> chartColors = [
    primaryBlue,
    successGreen,
    warningYellow,
    dangerRed,
    slate500,
    primaryBlueLight,
    successGreenLight,
    warningYellowLight,
  ];

  // Profit/Loss Colors
  static Color getPnLColor(double value) {
    if (value > 0) return successGreen;
    if (value < 0) return dangerRed;
    return textSecondary;
  }

  // Gradients
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      primaryBlue,
      primaryBlueDark,
    ],
  );

  static const LinearGradient successGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      successGreen,
      successGreenDark,
    ],
  );

  static const LinearGradient dangerGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      dangerRed,
      dangerRedDark,
    ],
  );
}
