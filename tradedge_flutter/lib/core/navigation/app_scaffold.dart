import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';

class AppScaffold extends StatelessWidget {
  final Widget child;

  const AppScaffold({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: child,
      bottomNavigationBar: NavigationBarTheme(
        data: NavigationBarThemeData(
          labelTextStyle: MaterialStateProperty.resolveWith((states) {
            if (states.contains(MaterialState.selected)) {
              return TextStyle(
                color: AppColors.primaryBlue,
                fontWeight: FontWeight.w600,
                fontSize: 13,
              );
            }
            return TextStyle(
              color: Colors.grey[600],
              fontSize: 13,
            );
          }),
        ),
        child: NavigationBar(
          selectedIndex: _calculateSelectedIndex(context),
          onDestinationSelected: (index) => _onItemTapped(index, context),
          destinations: [
            NavigationDestination(
              icon: Icon(Icons.analytics_outlined, color: Colors.grey[600]),
              selectedIcon: Icon(
                Icons.analytics,
                size: 28,
                color: AppColors.primaryBlue,
              ),
              label: 'Trades',
            ),
            NavigationDestination(
              icon: Icon(Icons.insights_outlined, color: Colors.grey[600]),
              selectedIcon: Icon(
                Icons.insights,
                size: 28,
                color: AppColors.primaryBlue,
              ),
              label: 'Analytics',
            ),
            NavigationDestination(
              icon: Icon(Icons.settings_outlined, color: Colors.grey[600]),
              selectedIcon: Icon(
                Icons.settings,
                size: 28,
                color: AppColors.primaryBlue,
              ),
              label: 'Settings',
            ),
          ],
          labelBehavior: NavigationDestinationLabelBehavior.alwaysShow,
          backgroundColor: theme.scaffoldBackgroundColor,
          elevation: 0,
          height: 65,
          indicatorColor: AppColors.primaryBlue.withOpacity(0.12),
          indicatorShape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          shadowColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          animationDuration: const Duration(milliseconds: 500),
        ),
      ),
    );
  }

  int _calculateSelectedIndex(BuildContext context) {
    final location = GoRouterState.of(context).uri.path;
    if (location.startsWith('/trades')) {
      return 0;
    }
    if (location.startsWith('/analytics')) {
      return 1;
    }
    if (location.startsWith('/settings')) {
      return 2;
    }
    return 0;
  }

  void _onItemTapped(int index, BuildContext context) {
    switch (index) {
      case 0:
        context.go('/trades');
        break;
      case 1:
        context.go('/analytics');
        break;
      case 2:
        context.go('/settings');
        break;
    }
  }
}
