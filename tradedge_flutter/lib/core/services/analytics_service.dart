import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/models/trade.dart';
import 'dart:math' as math;
import 'package:intl/intl.dart';

final analyticsServiceProvider = Provider<AnalyticsService>((ref) {
  return AnalyticsService();
});

class AnalyticsResult {
  final Summary summary;
  final Risk risk;
  final Volatility volatility;
  final Compliance compliance;
  final GaugeProgress gaugeProgress;

  AnalyticsResult({
    required this.summary,
    required this.risk,
    required this.volatility,
    required this.compliance,
    required this.gaugeProgress,
  });
}

class Summary {
  final double winRate;
  final double profitFactor;
  final double expectancy;
  final double avgTrade;
  final double avgWin;
  final double avgLoss;
  final double netPnL;
  final double grossProfit;
  final double grossLoss;
  final int totalTrades;
  final int winningTrades;
  final int losingTrades;
  final int profitableDays;
  final int tradingDays;
  final int maxWinStreak;
  final int maxLossStreak;
  final double consistency;

  Summary({
    required this.winRate,
    required this.profitFactor,
    required this.expectancy,
    required this.avgTrade,
    required this.avgWin,
    required this.avgLoss,
    required this.netPnL,
    required this.grossProfit,
    required this.grossLoss,
    required this.totalTrades,
    required this.winningTrades,
    required this.losingTrades,
    required this.profitableDays,
    required this.tradingDays,
    required this.maxWinStreak,
    required this.maxLossStreak,
    required this.consistency,
  });
}

class Risk {
  final double maxDrawdown;
  final double peakEquity;
  final double valleyEquity;
  final double avgRisk;
  final double maxRisk;
  final double minRisk;
  final double riskRewardRatio;
  final double avgWin;
  final double avgLoss;
  final double calmarRatio;
  final double recoveryRate;
  final double bounceBack;
  final double avgExposure;

  Risk({
    required this.maxDrawdown,
    required this.peakEquity,
    required this.valleyEquity,
    required this.avgRisk,
    required this.maxRisk,
    required this.minRisk,
    required this.riskRewardRatio,
    required this.avgWin,
    required this.avgLoss,
    required this.calmarRatio,
    required this.recoveryRate,
    required this.bounceBack,
    required this.avgExposure,
  });
}

class Volatility {
  final double sharpeRatio;
  final double stdDev;
  final double averageReturn;
  final double annualizedReturn;
  final double dailyVolatility;
  final double stability;

  Volatility({
    required this.sharpeRatio,
    required this.stdDev,
    required this.averageReturn,
    required this.annualizedReturn,
    required this.dailyVolatility,
    required this.stability,
  });
}

class Compliance {
  final double positionSizeCompliance;
  final double stopLossCompliance;
  final double riskRewardCompliance;
  final int stopLossViolations;
  final int riskRewardViolations;

  Compliance({
    required this.positionSizeCompliance,
    required this.stopLossCompliance,
    required this.riskRewardCompliance,
    required this.stopLossViolations,
    required this.riskRewardViolations,
  });
}

class GaugeProgress {
  final double overallProgress;
  final double profitProgress;
  final double guardProgress;
  final double focusProgress;

  GaugeProgress({
    required this.overallProgress,
    required this.profitProgress,
    required this.guardProgress,
    required this.focusProgress,
  });
}

class AnalyticsService {
  static const Map<String, double> defaultMetricGoals = {
    'targetPnL': 1000.0,
    'winRate': 0.60,
    'profitFactor': 1.5,
    'maxDrawdown': 0.10,
    'riskRewardRatio': 1.5,
  };

  static final _emptyResult = AnalyticsResult(
    summary: Summary(
      winRate: 0,
      profitFactor: 0,
      expectancy: 0,
      avgTrade: 0,
      avgWin: 0,
      avgLoss: 0,
      netPnL: 0,
      grossProfit: 0,
      grossLoss: 0,
      totalTrades: 0,
      winningTrades: 0,
      losingTrades: 0,
      profitableDays: 0,
      tradingDays: 0,
      maxWinStreak: 0,
      maxLossStreak: 0,
      consistency: 0,
    ),
    risk: Risk(
      maxDrawdown: 0,
      peakEquity: 0,
      valleyEquity: 0,
      avgRisk: 0,
      maxRisk: 0,
      minRisk: 0,
      riskRewardRatio: 0,
      avgWin: 0,
      avgLoss: 0,
      calmarRatio: 0,
      recoveryRate: 0,
      bounceBack: 0,
      avgExposure: 0,
    ),
    volatility: Volatility(
      sharpeRatio: 0,
      stdDev: 0,
      averageReturn: 0,
      annualizedReturn: 0,
      dailyVolatility: 0,
      stability: 0,
    ),
    compliance: Compliance(
      positionSizeCompliance: 0,
      stopLossCompliance: 0,
      riskRewardCompliance: 0,
      stopLossViolations: 0,
      riskRewardViolations: 0,
    ),
    gaugeProgress: GaugeProgress(
      overallProgress: 0,
      profitProgress: 0,
      guardProgress: 0,
      focusProgress: 0,
    ),
  );

  AnalyticsResult calculateMetrics(
    List<Trade> trades, [
    Map<String, double>? goals,
    String? accountId,
    double? accountInitialCapital,
  ]) {
    if (trades.isEmpty) {
      return _emptyResult;
    }

    final metricGoals = Map<String, double>.from(defaultMetricGoals);
    if (goals != null) {
      metricGoals.addAll(goals);
    }

    // Filter trades by account if specified
    if (accountId != null) {
      trades = trades.where((t) => t.accountId == accountId).toList();
      if (trades.isEmpty) {
        return _emptyResult;
      }
    }

    // Calculate basic metrics
    final winningTrades = trades.where((t) => t.calculatePnL() > 0).toList();
    final losingTrades = trades.where((t) => t.calculatePnL() < 0).toList();
    final grossProfit =
        winningTrades.fold(0.0, (sum, t) => sum + t.calculatePnL());
    final grossLoss =
        losingTrades.fold(0.0, (sum, t) => sum + t.calculatePnL());
    final netPnL = trades.fold(0.0, (sum, t) => sum + t.calculatePnL());

    // Calculate daily stats
    final dailyPnLMap = <String, double>{};
    for (final trade in trades) {
      final date =
          DateFormat('yyyy-MM-dd').format(trade.exitDate ?? DateTime.now());
      dailyPnLMap[date] = (dailyPnLMap[date] ?? 0.0) + trade.calculatePnL();
    }

    final profitableDays = dailyPnLMap.values.where((pnl) => pnl > 0).length;
    final tradingDays = dailyPnLMap.length;

    // Calculate streaks
    int currentWinStreak = 0;
    int currentLossStreak = 0;
    int maxWinStreak = 0;
    int maxLossStreak = 0;

    for (final trade in trades) {
      final pnl = trade.calculatePnL();
      if (pnl > 0) {
        currentWinStreak++;
        currentLossStreak = 0;
        maxWinStreak = math.max(maxWinStreak, currentWinStreak);
      } else if (pnl < 0) {
        currentLossStreak++;
        currentWinStreak = 0;
        maxLossStreak = math.max(maxLossStreak, currentLossStreak);
      } else {
        currentWinStreak = 0;
        currentLossStreak = 0;
      }
    }

    // Calculate drawdown
    double maxDrawdown = 0.0;
    double startingCapital =
        accountInitialCapital ?? 10000.0; // Default to 10000 if not provided
    double currentEquity = startingCapital;
    double peakEquity = startingCapital;
    double valleyEquity = startingCapital;

    // For percentage calculations, use max(startingCapital, 1) to avoid division by zero
    double calculationBase = math.max(startingCapital, 1.0);

    // Group trades by day to calculate daily drawdowns
    final dailyTrades = <String, List<Trade>>{};
    final dailyStartingCapital = <String, double>{};

    // Sort all trades by date first
    trades.sort((a, b) => a.entryDate.compareTo(b.entryDate));

    // Group trades by day and calculate starting capital for each day
    for (final trade in trades) {
      final date = DateFormat('yyyy-MM-dd').format(trade.entryDate);
      if (!dailyTrades.containsKey(date)) {
        dailyTrades[date] = [];
        // Starting capital for each day is the previous day's ending balance
        dailyStartingCapital[date] = currentEquity;
      }
      dailyTrades[date]!.add(trade);
    }

    // Calculate max drawdown for each day
    for (final date in dailyTrades.keys) {
      final dayTrades = dailyTrades[date]!;
      final dayStartingCapital = dailyStartingCapital[date]!;
      currentEquity = dayStartingCapital;
      double dailyMaxDrawdown = 0.0;

      for (final trade in dayTrades) {
        final pnl = trade.calculatePnL();
        currentEquity += pnl;

        // Calculate drawdown from peak equity
        if (currentEquity > peakEquity) {
          peakEquity = currentEquity;
        } else if (currentEquity < peakEquity) {
          final currentDrawdown =
              ((peakEquity - currentEquity) / calculationBase) * 100;
          dailyMaxDrawdown = math.max(dailyMaxDrawdown, currentDrawdown);
          valleyEquity = math.min(valleyEquity, currentEquity);
        }
      }

      // Update overall max drawdown if this day's drawdown is larger
      maxDrawdown = math.max(maxDrawdown, dailyMaxDrawdown);
    }

    // Calculate risk metrics
    final risks = trades
        .map((trade) => ({
              'risk': (trade.stopLoss != null
                  ? (trade.entryPrice - trade.stopLoss!).abs() * trade.quantity
                  : 0.0),
              'reward': (trade.takeProfit != null
                  ? (trade.takeProfit! - trade.entryPrice).abs() *
                      trade.quantity
                  : 0.0),
            }))
        .toList();

    final avgRisk = risks.isEmpty
        ? 0.0
        : risks.fold(0.0, (sum, r) => sum + r['risk']!) / risks.length;
    final maxRisk = risks.isEmpty
        ? 0.0
        : risks.fold(0.0, (max, r) => math.max(max, r['risk']!));
    final minRisk = risks.isEmpty
        ? 0.0
        : risks.fold(double.infinity, (min, r) => math.min(min, r['risk']!));
    final avgReward = risks.isEmpty
        ? 0.0
        : risks.fold(0.0, (sum, r) => sum + r['reward']!) / risks.length;

    // Calculate volatility metrics
    final returns = trades.isEmpty
        ? [0.0]
        : trades
            .map((t) =>
                t.calculatePnL() / math.max(t.entryPrice * t.quantity, 1.0))
            .toList();
    final avgReturn = returns.fold(0.0, (sum, r) => sum + r) / returns.length;
    final variance = returns.isEmpty
        ? 0.0
        : returns.fold(0.0, (sum, r) => sum + math.pow(r - avgReturn, 2)) /
            returns.length;
    final stdDev = math.sqrt(variance);
    final annualizedReturn = avgReturn * 252;
    final annualizedStdDev = stdDev * math.sqrt(252);
    final riskFreeRate = 0.02; // 2% annual risk-free rate

    // Calculate daily volatility
    final dailyReturns = dailyPnLMap.values.toList();
    final avgDailyReturn = dailyReturns.isEmpty
        ? 0.0
        : dailyReturns.fold(0.0, (sum, r) => sum + r) / dailyReturns.length;
    final dailyVariance = dailyReturns.isEmpty
        ? 0.0
        : dailyReturns.fold(
                0.0, (sum, r) => sum + math.pow(r - avgDailyReturn, 2)) /
            dailyReturns.length;
    final dailyVolatility = math.sqrt(dailyVariance);

    // Calculate compliance metrics
    final stopLossViolations = trades
        .where((t) =>
            t.stopLoss != null &&
            t.calculatePnL() < 0 &&
            t.calculatePnL().abs() > (t.stopLoss! * t.quantity))
        .length;

    final riskRewardViolations = trades
        .where((t) =>
            t.takeProfit != null &&
            t.stopLoss != null &&
            (t.takeProfit! - t.entryPrice).abs() /
                    (t.entryPrice - t.stopLoss!).abs() <
                2)
        .length;

    // Calculate recovery metrics
    double recoveryRate = 0.0;
    double bounceBack = 0.0;
    if (maxDrawdown > 0) {
      final timeToRecover = trades.indexWhere((t) {
        currentEquity += t.calculatePnL();
        return currentEquity >= startingCapital;
      });
      recoveryRate = timeToRecover > 0 ? 1 / timeToRecover : 0.0;
      bounceBack = currentEquity >= startingCapital
          ? ((currentEquity - startingCapital) / maxDrawdown).abs()
          : 0.0;
    }

    final summaryResult = Summary(
      winRate:
          trades.isEmpty ? 0 : (winningTrades.length / trades.length) * 100,
      profitFactor: grossLoss == 0
          ? (grossProfit > 0 ? double.infinity : 0.0)
          : grossProfit.abs() / grossLoss.abs(),
      expectancy: trades.isEmpty
          ? 0
          : ((winningTrades.isEmpty ? 0 : grossProfit / winningTrades.length) *
                  (winningTrades.length / trades.length)) -
              ((losingTrades.isEmpty
                      ? 0
                      : grossLoss.abs() / losingTrades.length) *
                  (losingTrades.length / trades.length)),
      avgTrade: trades.isEmpty ? 0 : netPnL / trades.length,
      avgWin: winningTrades.isEmpty ? 0 : grossProfit / winningTrades.length,
      avgLoss: losingTrades.isEmpty ? 0 : grossLoss.abs() / losingTrades.length,
      netPnL: netPnL,
      grossProfit: grossProfit,
      grossLoss: grossLoss.abs(),
      totalTrades: trades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      profitableDays: profitableDays,
      tradingDays: tradingDays,
      maxWinStreak: maxWinStreak,
      maxLossStreak: maxLossStreak,
      consistency: tradingDays == 0 ? 0 : profitableDays / tradingDays,
    );

    final riskResult = Risk(
      maxDrawdown: maxDrawdown,
      peakEquity: peakEquity,
      valleyEquity: valleyEquity,
      avgRisk: avgRisk,
      maxRisk: maxRisk,
      minRisk: minRisk,
      riskRewardRatio: avgRisk == 0 ? 0 : avgReward / avgRisk,
      avgWin: winningTrades.isEmpty ? 0 : grossProfit / winningTrades.length,
      avgLoss: losingTrades.isEmpty ? 0 : grossLoss / losingTrades.length,
      calmarRatio: maxDrawdown == 0
          ? 0
          : (annualizedReturn - riskFreeRate) / maxDrawdown,
      recoveryRate: recoveryRate,
      bounceBack: bounceBack,
      avgExposure: trades.isEmpty
          ? 0
          : trades.fold(
                  0.0,
                  (sum, t) =>
                      sum +
                      (t.exitDate?.difference(t.entryDate).inMinutes ?? 0)) /
              trades.length,
    );

    final volatilityResult = Volatility(
      sharpeRatio: annualizedStdDev == 0
          ? 0
          : (annualizedReturn - riskFreeRate) / annualizedStdDev,
      stdDev: stdDev,
      averageReturn: avgReturn,
      annualizedReturn: annualizedReturn,
      dailyVolatility: dailyVolatility,
      stability: dailyVolatility == 0
          ? 0
          : 1 - (dailyVolatility / math.max(avgDailyReturn.abs(), 1)).abs(),
    );

    final complianceResult = Compliance(
      positionSizeCompliance: trades.isEmpty
          ? 0
          : trades.where((t) => t.quantity <= 100000 / t.entryPrice).length /
              trades.length,
      stopLossCompliance:
          trades.isEmpty ? 0 : 1 - (stopLossViolations / trades.length),
      riskRewardCompliance:
          trades.isEmpty ? 0 : 1 - (riskRewardViolations / trades.length),
      stopLossViolations: stopLossViolations,
      riskRewardViolations: riskRewardViolations,
    );

    // Calculate gauge progress metrics
    final pnlProgress = summaryResult.netPnL.isNaN ||
            summaryResult.netPnL.isInfinite ||
            metricGoals['targetPnL'] == null ||
            metricGoals['targetPnL'] == 0
        ? 0.0
        : summaryResult.netPnL >= metricGoals['targetPnL']!
            ? 100.0
            : (summaryResult.netPnL / metricGoals['targetPnL']! * 100)
                .clamp(0.0, 100.0);

    final winRateProgress = summaryResult.winRate.isNaN ||
            summaryResult.winRate.isInfinite ||
            metricGoals['winRate'] == null ||
            metricGoals['winRate'] == 0
        ? 0.0
        : (summaryResult.winRate / metricGoals['winRate']! * 100)
            .clamp(0.0, 100.0);

    // Profit progress is average of PnL and win rate progress
    final profitProgress = (pnlProgress + winRateProgress) / 2;

    // Guard progress is inverse of drawdown percentage
    final guardProgress = riskResult.maxDrawdown.isNaN ||
            riskResult.maxDrawdown.isInfinite ||
            metricGoals['maxDrawdown'] == null ||
            metricGoals['maxDrawdown'] == 0
        ? 0.0
        : riskResult.maxDrawdown >= metricGoals['maxDrawdown']!
            ? 0.0
            : ((metricGoals['maxDrawdown']! - riskResult.maxDrawdown) /
                    metricGoals['maxDrawdown']! *
                    100)
                .clamp(0.0, 100.0);

    // Focus progress is based on profit factor
    final focusProgress = summaryResult.profitFactor.isNaN ||
            summaryResult.profitFactor.isInfinite ||
            metricGoals['profitFactor'] == null ||
            metricGoals['profitFactor'] == 0
        ? 0.0
        : summaryResult.profitFactor >= metricGoals['profitFactor']!
            ? 100.0
            : (summaryResult.profitFactor / metricGoals['profitFactor']! * 100)
                .clamp(0.0, 100.0);

    // Overall progress is average of all three
    final overallProgress =
        (profitProgress + guardProgress + focusProgress) / 3;

    final gaugeProgressResult = GaugeProgress(
      overallProgress: overallProgress,
      profitProgress: profitProgress,
      guardProgress: guardProgress,
      focusProgress: focusProgress,
    );

    return AnalyticsResult(
      summary: summaryResult,
      risk: riskResult,
      volatility: volatilityResult,
      compliance: complianceResult,
      gaugeProgress: gaugeProgressResult,
    );
  }
}
