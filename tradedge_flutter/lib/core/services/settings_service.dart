import 'package:shared_preferences/shared_preferences.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart' as core;
import 'storage_service.dart';

class SettingsService {
  final core.SettingsService _coreService;
  core.Settings? _cachedSettings;

  SettingsService(SharedPreferences prefs)
      : _coreService = core.SettingsService(FlutterStorageService(prefs));

  Future<core.Settings> initialize(String userId) async {
    try {
      _cachedSettings = await _coreService.initialize(userId);
      return _cachedSettings!;
    } catch (e) {
      print('Error initializing settings: $e');
      rethrow;
    }
  }

  Future<core.Settings> getSettings() async {
    try {
      _cachedSettings = await _coreService.getSettings();
      return _cachedSettings!;
    } catch (e) {
      print('Error getting settings: $e');
      rethrow;
    }
  }

  Future<core.Settings> updateDisplaySettings(
      core.DisplaySettings newDisplay) async {
    try {
      _cachedSettings = await _coreService.updateDisplaySettings(newDisplay);
      return _cachedSettings!;
    } catch (e) {
      print('Error updating display settings: $e');
      rethrow;
    }
  }

  Future<core.Settings> updateTradeDefaults(
      core.TradeDefaults newDefaults) async {
    try {
      _cachedSettings = await _coreService.updateTradeDefaults(newDefaults);
      return _cachedSettings!;
    } catch (e) {
      print('Error updating trade defaults: $e');
      rethrow;
    }
  }

  Future<core.Settings> updateBacktestSettings(
      core.BacktestSettings newBacktest) async {
    try {
      _cachedSettings = await _coreService.updateBacktestSettings(newBacktest);
      return _cachedSettings!;
    } catch (e) {
      print('Error updating backtest settings: $e');
      rethrow;
    }
  }

  Future<core.Settings> updateAutoBackup(bool enabled) async {
    try {
      _cachedSettings = await _coreService.updateAutoBackup(enabled);
      return _cachedSettings!;
    } catch (e) {
      print('Error updating auto backup: $e');
      rethrow;
    }
  }

  Future<core.Settings> updateGoalSettings(core.GoalSettings goals) async {
    try {
      _cachedSettings = await _coreService.updateGoalSettings(goals);
      return _cachedSettings!;
    } catch (e) {
      print('Error updating goal settings: $e');
      rethrow;
    }
  }

  Future<core.Settings> addTradingAccount(core.TradingAccount account) async {
    try {
      _cachedSettings = await _coreService.addTradingAccount(account);
      return _cachedSettings!;
    } catch (e) {
      print('Error adding trading account: $e');
      rethrow;
    }
  }

  Future<core.Settings> updateTradingAccount(
      core.TradingAccount account) async {
    try {
      _cachedSettings = await _coreService.updateTradingAccount(account);
      return _cachedSettings!;
    } catch (e) {
      print('Error updating trading account: $e');
      rethrow;
    }
  }

  Future<core.Settings> removeTradingAccount(String accountId) async {
    try {
      _cachedSettings = await _coreService.removeTradingAccount(accountId);
      return _cachedSettings!;
    } catch (e) {
      print('Error removing trading account: $e');
      rethrow;
    }
  }

  Future<core.Settings> addApiKey(core.ApiKey apiKey) async {
    try {
      _cachedSettings = await _coreService.addApiKey(apiKey);
      return _cachedSettings!;
    } catch (e) {
      print('Error adding API key: $e');
      rethrow;
    }
  }

  Future<core.Settings> updateApiKey(core.ApiKey apiKey) async {
    try {
      _cachedSettings = await _coreService.updateApiKey(apiKey);
      return _cachedSettings!;
    } catch (e) {
      print('Error updating API key: $e');
      rethrow;
    }
  }

  Future<core.Settings> removeApiKey(String apiKeyId) async {
    try {
      _cachedSettings = await _coreService.removeApiKey(apiKeyId);
      return _cachedSettings!;
    } catch (e) {
      print('Error removing API key: $e');
      rethrow;
    }
  }

  Future<core.Settings> updateAccountSync(
      bool autoSync, int syncFrequency) async {
    try {
      _cachedSettings =
          await _coreService.updateAccountSync(autoSync, syncFrequency);
      return _cachedSettings!;
    } catch (e) {
      print('Error updating account sync settings: $e');
      rethrow;
    }
  }
}
