import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart'
    show StorageService, User;
import 'package:trade_reviewer_core/trade_reviewer_core.dart' as core;

class FlutterStorageService implements StorageService {
  static const String _settingsKey = 'trade_reviewer_settings';
  static const String _profilesKey = 'trade_reviewer_profiles';
  static const String _tradesKey = 'trade_reviewer_trades';
  final SharedPreferences _prefs;

  FlutterStorageService(this._prefs);

  @override
  Future<bool> verifyStorageAccess() async {
    try {
      final testKey = 'storage_test_key';
      final testValue = 'test_value_${DateTime.now().millisecondsSinceEpoch}';

      // Try to write
      final writeSuccess = await _prefs.setString(testKey, testValue);
      if (!writeSuccess) {
        print('ERROR: Failed to write test value');
        return false;
      }

      // Try to read
      final readValue = _prefs.getString(testKey);
      if (readValue != testValue) {
        print('ERROR: Read value does not match written value');
        return false;
      }

      // Try to remove
      final removeSuccess = await _prefs.remove(testKey);
      if (!removeSuccess) {
        print('WARNING: Failed to remove test key');
      }

      return true;
    } catch (e) {
      print('Error verifying storage access: $e');
      return false;
    }
  }

  @override
  Future<Map<String, dynamic>?> getSettings() async {
    final jsonStr = _prefs.getString(_settingsKey);
    if (jsonStr == null) {
      return null;
    }
    return json.decode(jsonStr) as Map<String, dynamic>;
  }

  @override
  Future<void> saveSettings(dynamic settings) async {
    final jsonStr = json.encode(settings.toJson());
    await _prefs.setString(_settingsKey, jsonStr);
  }

  @override
  Future<void> clearSettings() async {
    await _prefs.remove(_settingsKey);
  }

  @override
  Future<T?> get<T>(String key) async {
    final value = _prefs.getString(key);
    if (value == null) return null;
    return json.decode(value) as T;
  }

  @override
  Future<bool> set<T>(String key, T value) async {
    final jsonStr = json.encode(value);
    return await _prefs.setString(key, jsonStr);
  }

  @override
  Future<void> remove(String key) async {
    await _prefs.remove(key);
  }

  @override
  Future<void> clear() async {
    try {
      await _prefs.clear();
    } catch (e) {
      print('Error clearing storage: $e');
      rethrow;
    }
  }

  @override
  Future<User?> getProfile(String userId) async {
    try {
      final profiles = await _getProfiles();
      return profiles.firstWhere((p) => p.id == userId);
    } catch (e) {
      print('Error getting profile: $e');
      return null;
    }
  }

  @override
  Future<void> saveProfile(User profile) async {
    try {
      final profiles = await _getProfiles();
      final index = profiles.indexWhere((p) => p.id == profile.id);
      if (index != -1) {
        profiles[index] = profile;
      } else {
        profiles.add(profile);
      }
      await _saveProfilesToStorage(profiles);
    } catch (e) {
      print('Error saving profile: $e');
      rethrow;
    }
  }

  @override
  Future<void> updateProfile(User profile) async {
    await saveProfile(profile);
  }

  @override
  Future<void> deleteProfile(String userId) async {
    try {
      final profiles = await _getProfiles();
      profiles.removeWhere((p) => p.id == userId);
      await _saveProfilesToStorage(profiles);
    } catch (e) {
      print('Error deleting profile: $e');
      rethrow;
    }
  }

  @override
  Future<List<core.Trade>> getTrades() async {
    try {
      print('\n=== Getting trades from storage ===');
      final String? tradesJson = _prefs.getString(_tradesKey);
      print('Raw trades JSON from storage: ${tradesJson ?? "null"}');

      if (tradesJson == null) {
        print('No trades found in storage');
        return [];
      }

      final List<dynamic> tradesList = json.decode(tradesJson);
      print('Decoded ${tradesList.length} trades from JSON');

      final trades = tradesList
          .map((json) => core.Trade.fromJson(json as Map<String, dynamic>))
          .toList();

      print('\nTrades loaded from storage:');
      for (var trade in trades) {
        print('\nTrade details:');
        print('  ID: ${(trade as dynamic).id}');
        print('  Symbol: ${(trade as dynamic).symbol}');
        print('  Type: ${(trade as dynamic).type}');
        print('  Status: ${(trade as dynamic).status}');
        print('  Entry Price: ${(trade as dynamic).entryPrice}');
        print('  Exit Price: ${(trade as dynamic).exitPrice}');
        print('  Quantity: ${(trade as dynamic).quantity}');
        print('  PnL: ${trade.calculatePnL()}');
      }

      return trades;
    } catch (e, stack) {
      print('Error getting trades: $e');
      print('Stack trace: $stack');
      return [];
    }
  }

  @override
  Future<void> saveTrade(core.Trade trade) async {
    try {
      print('\n=== Saving trade to storage ===');
      print('Trade details:');
      print('  ID: ${(trade as dynamic).id}');
      print('  Symbol: ${(trade as dynamic).symbol}');
      print('  Type: ${(trade as dynamic).type}');
      print('  Status: ${(trade as dynamic).status}');
      print('  Entry Price: ${(trade as dynamic).entryPrice}');
      print('  Exit Price: ${(trade as dynamic).exitPrice}');
      print('  Quantity: ${(trade as dynamic).quantity}');
      print('  PnL: ${trade.calculatePnL()}');

      final trades = await getTrades();
      trades.add(trade);
      await _saveTradesToStorage(trades);
      print('Trade saved successfully');
    } catch (e, stack) {
      print('Error saving trade: $e');
      print('Stack trace: $stack');
      rethrow;
    }
  }

  @override
  Future<void> updateTrade(core.Trade trade) async {
    try {
      final trades = await getTrades();
      final index =
          trades.indexWhere((t) => (t as dynamic).id == (trade as dynamic).id);
      if (index != -1) {
        trades[index] = trade;
        await _saveTradesToStorage(trades);
      }
    } catch (e) {
      print('Error updating trade: $e');
      rethrow;
    }
  }

  @override
  Future<void> deleteTrade(String tradeId) async {
    try {
      final trades = await getTrades();
      trades.removeWhere((t) => (t as dynamic).id == tradeId);
      await _saveTradesToStorage(trades);
    } catch (e) {
      print('Error deleting trade: $e');
      rethrow;
    }
  }

  Future<List<User>> _getProfiles() async {
    try {
      final String? profilesJson = _prefs.getString(_profilesKey);
      if (profilesJson == null) return [];

      final List<dynamic> profilesList = json.decode(profilesJson);
      return profilesList
          .map((json) => User.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      print('Error getting profiles: $e');
      return [];
    }
  }

  Future<void> _saveProfilesToStorage(List<User> profiles) async {
    try {
      final profilesJson =
          json.encode(profiles.map((p) => p.toJson()).toList());
      final success = await _prefs.setString(_profilesKey, profilesJson);
      if (!success) {
        throw Exception('Failed to save profiles to SharedPreferences');
      }
    } catch (e) {
      print('Error saving profiles to storage: $e');
      rethrow;
    }
  }

  Future<void> _saveTradesToStorage(List<core.Trade> trades) async {
    try {
      print('\n=== Saving trades to storage ===');
      print('Number of trades to save: ${trades.length}');

      final List<Map<String, dynamic>> tradesList = trades
          .map((trade) => (trade as dynamic).toJson() as Map<String, dynamic>)
          .toList();

      print('Trades converted to JSON format');
      final String tradesJson = json.encode(tradesList);
      print('Trades encoded to JSON string');

      await _prefs.setString(_tradesKey, tradesJson);
      print('Trades saved to storage successfully');

      // Verify the save
      final savedJson = _prefs.getString(_tradesKey);
      print('Verification - Trades in storage: ${savedJson != null}');
    } catch (e, stack) {
      print('Error saving trades to storage: $e');
      print('Stack trace: $stack');
      rethrow;
    }
  }
}
