import 'package:trade_reviewer_core/trade_reviewer_core.dart' as core;
import '../models/trade.dart';

class TradeService {
  final core.StorageService _storageService;

  TradeService(this._storageService);

  Future<List<Trade>> getTrades(String userId,
      {core.TradeFilter? filter}) async {
    final trades = await _storageService.getTrades();
    final userTrades = trades.where((t) => t.userId == userId).toList();

    if (filter == null) return userTrades.map(_convertToLocalTrade).toList();

    return userTrades
        .where((trade) {
          if (filter.symbol != null && trade.symbol != filter.symbol)
            return false;
          if (filter.type != null && trade.type != filter.type) return false;
          if (filter.status != null && trade.status != filter.status)
            return false;
          if (filter.accountId != null && trade.accountId != filter.accountId)
            return false;

          if (filter.startDate != null &&
              trade.entryDate.isBefore(filter.startDate!)) {
            return false;
          }
          if (filter.endDate != null &&
              trade.entryDate.isAfter(filter.endDate!)) {
            return false;
          }

          if (filter.tags != null && filter.tags!.isNotEmpty) {
            if (!filter.tags!.any((tag) => trade.tags.contains(tag)))
              return false;
          }

          return true;
        })
        .map(_convertToLocalTrade)
        .toList();
  }

  Future<Trade?> getTradeById(String userId, String tradeId) async {
    final trades = await _storageService.getTrades();
    try {
      final trade = trades.firstWhere(
        (t) => t.id == tradeId && t.userId == userId,
      );
      return _convertToLocalTrade(trade);
    } catch (e) {
      return null;
    }
  }

  Future<Trade> createTrade(String userId, Map<String, dynamic> trade) async {
    if (!trade.containsKey('accountId')) {
      throw ArgumentError('accountId is required');
    }

    final newTrade = core.Trade.fromJson({
      ...{
        'tags': [],
        'fees': 0.0,
      },
      ...trade,
      'id': _generateTradeId(),
      'userId': userId,
      'updatedAt': DateTime.now().toIso8601String(),
    });
    await _storageService.saveTrade(newTrade);
    return _convertToLocalTrade(newTrade);
  }

  Future<Trade> updateTrade(
      String userId, String tradeId, Map<String, dynamic> updates) async {
    final trades = await _storageService.getTrades();
    final trade = trades.firstWhere(
      (t) => t.id == tradeId && t.userId == userId,
      orElse: () => throw Exception('Trade not found'),
    );

    final updatedTrade = core.Trade.fromJson({
      ...trade.toJson(),
      ...updates,
      'userId': userId,
      'accountId': trade.accountId,
      'tags': updates['tags'] ?? trade.tags,
      'fees': updates['fees'] ?? trade.fees,
      'updatedAt': DateTime.now().toIso8601String(),
    });

    await _storageService.updateTrade(updatedTrade);
    return _convertToLocalTrade(updatedTrade);
  }

  Future<void> deleteTrade(String userId, String tradeId) async {
    final trades = await _storageService.getTrades();
    final trade = trades.firstWhere(
      (t) => t.id == tradeId && t.userId == userId,
      orElse: () => throw Exception('Trade not found'),
    );
    await _storageService.deleteTrade(trade.id);
  }

  Future<Trade> closeTrade(String userId, String tradeId, double exitPrice,
      {DateTime? exitDate}) async {
    final trades = await _storageService.getTrades();
    final trade = trades.firstWhere(
      (t) => t.id == tradeId && t.userId == userId,
      orElse: () => throw Exception('Trade not found'),
    );

    final updatedTrade = core.Trade.fromJson({
      ...trade.toJson(),
      'exitPrice': exitPrice,
      'exitDate': (exitDate ?? DateTime.now()).toIso8601String(),
      'status': 'CLOSED',
      'updatedAt': DateTime.now().toIso8601String(),
    });

    await _storageService.updateTrade(updatedTrade);
    return _convertToLocalTrade(updatedTrade);
  }

  Future<Trade> addTags(
      String userId, String tradeId, List<String> tags) async {
    final trades = await _storageService.getTrades();
    final trade = trades.firstWhere(
      (t) => t.id == tradeId && t.userId == userId,
      orElse: () => throw Exception('Trade not found'),
    );

    final uniqueTags = {...trade.tags, ...tags}.toList();
    final updatedTrade = core.Trade.fromJson({
      ...trade.toJson(),
      'tags': uniqueTags,
      'updatedAt': DateTime.now().toIso8601String(),
    });

    await _storageService.updateTrade(updatedTrade);
    return _convertToLocalTrade(updatedTrade);
  }

  Future<Trade> removeTags(
      String userId, String tradeId, List<String> tags) async {
    final trades = await _storageService.getTrades();
    final trade = trades.firstWhere(
      (t) => t.id == tradeId && t.userId == userId,
      orElse: () => throw Exception('Trade not found'),
    );

    final updatedTags = trade.tags.where((tag) => !tags.contains(tag)).toList();
    final updatedTrade = core.Trade.fromJson({
      ...trade.toJson(),
      'tags': updatedTags,
      'updatedAt': DateTime.now().toIso8601String(),
    });

    await _storageService.updateTrade(updatedTrade);
    return _convertToLocalTrade(updatedTrade);
  }

  String _generateTradeId() {
    return 'tr_${DateTime.now().millisecondsSinceEpoch}_${(1000000 + (DateTime.now().microsecondsSinceEpoch % 1000000)).toString().substring(1)}';
  }

  Trade _convertToLocalTrade(core.Trade trade) {
    return Trade(
      id: trade.id,
      symbol: trade.symbol,
      type:
          trade.type == core.TradeType.long ? TradeType.LONG : TradeType.SHORT,
      status: trade.status == core.TradeStatus.open
          ? TradeStatus.OPEN
          : TradeStatus.CLOSED,
      entryPrice: trade.entryPrice,
      exitPrice: trade.exitPrice,
      stopLoss: trade.stopLoss,
      takeProfit: trade.takeProfit,
      quantity: trade.quantity,
      fees: trade.fees,
      tags: trade.tags,
      notes: trade.notes,
      entryDate: trade.entryDate,
      exitDate: trade.exitDate,
      accountId: trade.accountId,
      userId: trade.userId,
      updatedAt: trade.updatedAt,
      predefinedPnL: null,
    );
  }
}
