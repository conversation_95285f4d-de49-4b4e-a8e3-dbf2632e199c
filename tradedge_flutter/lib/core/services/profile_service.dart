import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart' as core;

class ProfileService {
  static const String _profileKey = 'user_profile';
  static const String _profilesKey = 'user_profiles';
  final SharedPreferences _prefs;
  core.User? _cachedProfile;

  ProfileService(this._prefs);

  Future<core.User> initialize(String userId) async {
    try {
      print('ProfileService - Initializing profile for user: $userId');

      // Try to get existing profile first
      final existingProfile = await getProfile(userId);
      if (existingProfile != null) {
        print('ProfileService - Found existing profile');
        return existingProfile;
      }

      // Create default profile
      print('ProfileService - Creating default profile');
      final defaultProfile = core.User(
        id: userId,
        email: '',
        name: 'User',
        role: core.UserRole.user,
        preferences: const core.UserPreferences(
          notificationPreference: core.NotificationPreference.important,
          darkMode: false,
          defaultCurrency: 'USD',
          timezone: 'UTC',
          language: 'en',
          showPnLInPercent: true,
          autoSyncEnabled: true,
          profilePublic: false,
          showTradeHistory: false,
          showProfitLoss: false,
          showPortfolioValue: false,
          allowDataCollection: true,
          allowCrashReports: true,
          allowAnalytics: true,
        ),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Save the default profile
      await _saveProfile(defaultProfile);
      _cachedProfile = defaultProfile;
      return defaultProfile;
    } catch (e, stackTrace) {
      print('ProfileService - Error initializing profile: $e');
      print('Stack trace: $stackTrace');
      rethrow;
    }
  }

  Future<core.User?> getProfile(String userId) async {
    try {
      print('ProfileService - Getting profile for user: $userId');

      // Try to return cached profile first
      if (_cachedProfile?.id == userId) {
        print('ProfileService - Returning cached profile');
        return _cachedProfile;
      }

      // Try to get from profiles list
      final profiles = await _getProfiles();
      try {
        final profile = profiles.firstWhere((p) => p.id == userId);
        _cachedProfile = profile;
        return profile;
      } catch (e) {
        print('ProfileService - No profile found for user: $userId');
        return null;
      }
    } catch (e, stackTrace) {
      print('ProfileService - Error getting profile: $e');
      print('Stack trace: $stackTrace');
      return null;
    }
  }

  Future<core.User?> updateProfile(
      String userId, Map<String, dynamic> updates) async {
    try {
      print('ProfileService - Updating profile for user: $userId');
      print('Updates: $updates');

      // Get current profile or initialize
      var currentProfile = await getProfile(userId);
      if (currentProfile == null) {
        print('ProfileService - No profile found, initializing...');
        currentProfile = await initialize(userId);
      }

      // Create updated profile
      final updatedProfile = core.User(
        id: userId,
        email: updates['email'] as String? ?? currentProfile.email,
        name: updates['name'] as String? ?? currentProfile.name,
        role: currentProfile.role,
        preferences: updates['preferences'] as core.UserPreferences? ??
            currentProfile.preferences,
        createdAt: currentProfile.createdAt,
        updatedAt: DateTime.now(),
        lastLoginAt: currentProfile.lastLoginAt,
        isEmailVerified: updates['isEmailVerified'] as bool? ??
            currentProfile.isEmailVerified,
        profileImageUrl: updates['profileImageUrl'] as String? ??
            currentProfile.profileImageUrl,
      );

      print(
          'ProfileService - Updated profile data: ${updatedProfile.toJson()}');

      // Save the profile
      await _saveProfile(updatedProfile);

      // Verify the update
      final verifiedProfile = await getProfile(userId);
      if (verifiedProfile == null) {
        throw Exception('Profile verification failed - profile not found');
      }

      // Verify specific fields that were updated
      for (final key in updates.keys) {
        final updatedValue = updates[key];
        final verifiedValue = verifiedProfile.toJson()[key];
        if (updatedValue != null &&
            updatedValue.toString() != verifiedValue.toString()) {
          print('ProfileService - Field verification failed for $key:');
          print('Expected: $updatedValue');
          print('Got: $verifiedValue');
          throw Exception('Profile update verification failed for field: $key');
        }
      }

      print('ProfileService - Profile updated and verified successfully');
      return updatedProfile;
    } catch (e, stackTrace) {
      print('ProfileService - Error updating profile: $e');
      print('Stack trace: $stackTrace');
      rethrow;
    }
  }

  Future<void> _saveProfile(core.User profile) async {
    try {
      print('ProfileService - Saving profile: ${profile.id}');
      print('ProfileService - Profile data: ${profile.toJson()}');

      // Get current profiles
      final profiles = await _getProfiles();
      final index = profiles.indexWhere((p) => p.id == profile.id);

      if (index != -1) {
        profiles[index] = profile;
      } else {
        profiles.add(profile);
      }

      // Save to storage
      final profilesJson =
          json.encode(profiles.map((p) => p.toJson()).toList());
      final success = await _prefs.setString(_profilesKey, profilesJson);

      if (!success) {
        throw StateError('Failed to save profiles to SharedPreferences');
      }

      // Update cache
      _cachedProfile = profile;

      // Verify the save
      await _verifyProfile(profile);

      print('ProfileService - Profile saved successfully');
    } catch (e, stackTrace) {
      print('ProfileService - Error saving profile: $e');
      print('Stack trace: $stackTrace');
      rethrow;
    }
  }

  Future<void> _verifyProfile(core.User profile) async {
    try {
      print('ProfileService - Verifying profile save');

      // Get the profile from storage again
      final profiles = await _getProfiles();
      final savedProfile = profiles.firstWhere(
        (p) => p.id == profile.id,
        orElse: () => throw Exception('Profile not found after save'),
      );

      // Verify all fields match
      if (savedProfile.toJson().toString() != profile.toJson().toString()) {
        print('ProfileService - Profile verification failed:');
        print('Expected: ${profile.toJson()}');
        print('Got: ${savedProfile.toJson()}');
        throw Exception('Profile data mismatch after save');
      }

      print('ProfileService - Profile verification successful');
    } catch (e, stackTrace) {
      print('ProfileService - Error verifying profile: $e');
      print('Stack trace: $stackTrace');
      rethrow;
    }
  }

  Future<List<core.User>> _getProfiles() async {
    try {
      final String? profilesJson = _prefs.getString(_profilesKey);
      if (profilesJson == null) return [];

      final List<dynamic> profilesList = json.decode(profilesJson);
      return profilesList
          .map((json) => core.User.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      print('ProfileService - Error getting profiles: $e');
      return [];
    }
  }
}
