import 'package:flutter/material.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

class ProfilePicture extends StatefulWidget {
  final String? imageUrl;
  final double size;
  final VoidCallback? onTap;
  final bool showEditButton;

  const ProfilePicture({
    super.key,
    this.imageUrl,
    this.size = 80,
    this.onTap,
    this.showEditButton = false,
  });

  @override
  State<ProfilePicture> createState() => _ProfilePictureState();
}

class _ProfilePictureState extends State<ProfilePicture> {
  ImageProvider? imageProvider;
  bool isLoading = true;
  bool hasError = false;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  @override
  void didUpdateWidget(ProfilePicture oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.imageUrl != widget.imageUrl) {
      _loadImage();
    }
  }

  Future<void> _loadImage() async {
    if (!mounted) return;

    if (mounted) {
      setState(() {
        isLoading = true;
        hasError = false;
        imageProvider = null;
      });
    }

    if (widget.imageUrl == null || widget.imageUrl!.isEmpty) {
      print('ProfilePicture - No image URL provided');
      if (mounted) {
        setState(() {
          isLoading = false;
          hasError = false;
        });
      }
      return;
    }

    try {
      print('ProfilePicture - Loading image from: ${widget.imageUrl}');
      final appDir = await getApplicationDocumentsDirectory();
      final file = File('${appDir.path}/${widget.imageUrl}');

      print('ProfilePicture - Full path: ${file.path}');
      if (await file.exists()) {
        print('ProfilePicture - Image file exists');

        // Verify file is readable and is an image
        try {
          final bytes = await file.readAsBytes();
          if (bytes.isEmpty) {
            throw Exception('Image file is empty');
          }

          if (mounted) {
            setState(() {
              imageProvider = FileImage(file);
              isLoading = false;
              hasError = false;
            });
          }
        } catch (e) {
          print('ProfilePicture - Error reading image file: $e');
          if (mounted) {
            setState(() {
              isLoading = false;
              hasError = true;
            });
          }
        }
      } else {
        print('ProfilePicture - Image file does not exist at: ${file.path}');
        if (mounted) {
          setState(() {
            isLoading = false;
            hasError = true;
          });
        }
      }
    } catch (e) {
      print('ProfilePicture - Error loading image: $e');
      print('ProfilePicture - Stack trace: ${StackTrace.current}');
      if (mounted) {
        setState(() {
          isLoading = false;
          hasError = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        GestureDetector(
          onTap: widget.onTap,
          child: Container(
            width: widget.size,
            height: widget.size,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: AppColors.backgroundAlt,
              border: Border.all(
                color: AppColors.border,
                width: 1,
              ),
              image: imageProvider != null
                  ? DecorationImage(
                      image: imageProvider!,
                      fit: BoxFit.cover,
                    )
                  : null,
            ),
            child: isLoading
                ? const Center(
                    child: CircularProgressIndicator(),
                  )
                : imageProvider == null
                    ? Icon(
                        hasError ? Icons.error_outline : Icons.person_outline,
                        size: widget.size * 0.5,
                        color: hasError ? Colors.red : AppColors.textSecondary,
                      )
                    : null,
          ),
        ),
        if (widget.showEditButton)
          Positioned(
            right: 0,
            bottom: 0,
            child: Container(
              width: widget.size * 0.3,
              height: widget.size * 0.3,
              decoration: BoxDecoration(
                color: AppColors.primaryBlue,
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white,
                  width: 2,
                ),
              ),
              child: Icon(
                Icons.edit,
                size: widget.size * 0.15,
                color: Colors.white,
              ),
            ),
          ),
      ],
    );
  }
}
