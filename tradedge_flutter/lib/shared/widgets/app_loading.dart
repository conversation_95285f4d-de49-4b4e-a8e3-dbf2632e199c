import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/theme/app_theme.dart';

class AppLoading extends StatelessWidget {
  final String? message;
  final bool overlay;
  final Color? color;

  const AppLoading({
    Key? key,
    this.message,
    this.overlay = false,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final loadingWidget = Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              color ?? AppColors.primaryBlue,
            ),
          ),
          if (message != null) ...[
            const SizedBox(height: AppTheme.spacingMD),
            Text(
              message!,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );

    if (overlay) {
      return Container(
        color: Colors.black.withOpacity(0.5),
        child: loadingWidget,
      );
    }

    return loadingWidget;
  }
}

class AppLoadingShimmer extends StatelessWidget {
  final double height;
  final double? width;
  final double borderRadius;
  final EdgeInsets? margin;

  const AppLoadingShimmer({
    Key? key,
    this.height = 20,
    this.width,
    this.borderRadius = AppTheme.borderRadiusMD,
    this.margin,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: AppColors.backgroundSlate100,
      highlightColor: AppColors.backgroundSlate50,
      child: Container(
        height: height,
        width: width,
        margin: margin,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(borderRadius),
        ),
      ),
    );
  }
}

class AppLoadingCard extends StatelessWidget {
  final double height;
  final EdgeInsets? margin;
  final bool showHeader;

  const AppLoadingCard({
    Key? key,
    this.height = 100,
    this.margin,
    this.showHeader = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? const EdgeInsets.all(AppTheme.spacingSM),
      padding: const EdgeInsets.all(AppTheme.spacingMD),
      decoration: BoxDecoration(
        color: AppColors.backgroundSlate50,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showHeader) ...[
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: AppLoadingShimmer(
                    height: 24,
                    borderRadius: AppTheme.borderRadiusSM,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingMD),
                Expanded(
                  child: AppLoadingShimmer(
                    height: 24,
                    borderRadius: AppTheme.borderRadiusSM,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMD),
          ],
          AppLoadingShimmer(
            height: height,
            borderRadius: AppTheme.borderRadiusSM,
          ),
        ],
      ),
    );
  }
}

class AppLoadingList extends StatelessWidget {
  final int itemCount;
  final double itemHeight;
  final bool showHeader;
  final EdgeInsets? padding;

  const AppLoadingList({
    Key? key,
    this.itemCount = 3,
    this.itemHeight = 100,
    this.showHeader = true,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: padding,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: itemCount,
      itemBuilder: (context, index) => AppLoadingCard(
        height: itemHeight,
        showHeader: showHeader,
      ),
    );
  }
}
