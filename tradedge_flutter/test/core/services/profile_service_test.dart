import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:trade_reviewer_flutter/core/services/profile_service.dart';
import 'package:trade_reviewer_flutter/core/models/user.dart';

void main() {
  late ProfileService profileService;
  late SharedPreferences prefs;

  setUp(() async {
    SharedPreferences.setMockInitialValues({});
    prefs = await SharedPreferences.getInstance();
    profileService = ProfileService(prefs);
  });

  group('ProfileService', () {
    test('getProfile returns null when no profile exists', () async {
      final profile = await profileService.getProfile('test_user');
      expect(profile, isNull);
    });

    test('saveProfile creates new profile when none exists', () async {
      final updates = {
        'name': 'Test User',
        'email': '<EMAIL>',
        'preferences': const UserPreferences(
          language: 'en',
          timezone: 'UTC',
        ),
      };

      final profile = await profileService.updateProfile('test_user', updates);

      expect(profile, isNotNull);
      expect(profile?.name, equals('Test User'));
      expect(profile?.email, equals('<EMAIL>'));
      expect(profile?.preferences.language, equals('en'));
      expect(profile?.preferences.timezone, equals('UTC'));
    });

    test('updateProfile updates existing profile', () async {
      // First create a profile
      final initialUpdates = {
        'name': 'Test User',
        'email': '<EMAIL>',
        'preferences': const UserPreferences(),
      };

      await profileService.updateProfile('test_user', initialUpdates);

      // Then update it
      final updates = {
        'name': 'Updated User',
        'email': '<EMAIL>',
        'preferences': const UserPreferences(
          language: 'es',
          timezone: 'GMT',
        ),
      };

      final profile = await profileService.updateProfile('test_user', updates);

      expect(profile, isNotNull);
      expect(profile?.name, equals('Updated User'));
      expect(profile?.email, equals('<EMAIL>'));
      expect(profile?.preferences.language, equals('es'));
      expect(profile?.preferences.timezone, equals('GMT'));
    });

    test('profile persists after app restart', () async {
      // Save profile
      final updates = {
        'name': 'Test User',
        'email': '<EMAIL>',
        'preferences': const UserPreferences(
          language: 'en',
          timezone: 'UTC',
        ),
      };

      await profileService.updateProfile('test_user', updates);

      // Create new instance of SharedPreferences and ProfileService to simulate app restart
      final newPrefs = await SharedPreferences.getInstance();
      final newProfileService = ProfileService(newPrefs);

      final profile = await newProfileService.getProfile('test_user');

      expect(profile, isNotNull);
      expect(profile?.name, equals('Test User'));
      expect(profile?.email, equals('<EMAIL>'));
      expect(profile?.preferences.language, equals('en'));
      expect(profile?.preferences.timezone, equals('UTC'));
    });

    test('clearProfile removes profile data', () async {
      // First save a profile
      final updates = {
        'name': 'Test User',
        'email': '<EMAIL>',
      };

      await profileService.updateProfile('test_user', updates);

      // Clear the profile
      await profileService.clearProfile();

      // Try to get the profile
      final profile = await profileService.getProfile('test_user');
      expect(profile, isNull);
    });

    test('invalid JSON data returns null profile', () async {
      // Manually set invalid JSON
      await prefs.setString('user_profile', 'invalid json');

      final profile = await profileService.getProfile('test_user');
      expect(profile, isNull);
    });
  });
}
