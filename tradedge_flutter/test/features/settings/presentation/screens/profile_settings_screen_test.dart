import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_flutter/core/services/profile_service.dart';
import 'package:trade_reviewer_flutter/core/models/user.dart';
import 'package:trade_reviewer_flutter/features/settings/presentation/screens/profile_settings_screen.dart';

void main() {
  late SharedPreferences prefs;
  late ProfileService profileService;

  setUp(() async {
    SharedPreferences.setMockInitialValues({});
    prefs = await SharedPreferences.getInstance();
    profileService = ProfileService(prefs);
  });

  Widget createTestWidget({required Widget child}) {
    return ProviderScope(
      overrides: [
        sharedPreferencesProvider.overrideWithValue(prefs),
      ],
      child: MaterialApp(
        scaffoldMessengerKey: GlobalKey<ScaffoldMessengerState>(),
        home: SizedBox(
          width: 1024,
          height: 768,
          child: child,
        ),
      ),
    );
  }

  testWidgets('ProfileSettingsScreen shows empty state initially',
      (WidgetTester tester) async {
    await tester.pumpWidget(
      createTestWidget(child: ProfileSettingsScreen(userId: 'test_user')),
    );

    // Initial render
    await tester.pumpAndSettle();

    expect(find.text('Profile'), findsOneWidget);
    expect(find.byType(TextFormField),
        findsNWidgets(3)); // Name, Email, Phone fields
    expect(find.text('Save Changes'), findsOneWidget);
  });

  testWidgets('ProfileSettingsScreen loads existing profile',
      (WidgetTester tester) async {
    // Create a test profile
    final updates = {
      'name': 'Test User',
      'email': '<EMAIL>',
      'preferences': const UserPreferences(
        language: 'en',
        timezone: 'UTC',
      ),
    };
    await profileService.updateProfile('test_user', updates);

    await tester.pumpWidget(
      createTestWidget(child: ProfileSettingsScreen(userId: 'test_user')),
    );

    // Wait for profile to load
    await tester.pumpAndSettle();

    expect(find.text('Test User'), findsOneWidget);
    expect(find.text('<EMAIL>'), findsOneWidget);
  });

  testWidgets('ProfileSettingsScreen saves profile changes',
      (WidgetTester tester) async {
    await tester.pumpWidget(
      createTestWidget(child: ProfileSettingsScreen(userId: 'test_user')),
    );

    // Wait for initial load
    await tester.pumpAndSettle();

    // Enter new profile data
    await tester.enterText(find.byType(TextFormField).at(0), 'New Test User');
    await tester.enterText(
        find.byType(TextFormField).at(1), '<EMAIL>');
    await tester.pump();

    // Tap save button
    await tester.tap(find.text('Save Changes'));
    await tester.pump();

    // Wait for save operation and UI updates
    await tester.pumpAndSettle();

    // Verify success message in SnackBar
    expect(find.byType(SnackBar), findsOneWidget);
    expect(find.text('Profile updated successfully'), findsOneWidget);

    // Verify data was saved
    final profile = await profileService.getProfile('test_user');
    expect(profile?.name, equals('New Test User'));
    expect(profile?.email, equals('<EMAIL>'));
  });

  testWidgets('ProfileSettingsScreen shows error on save failure',
      (WidgetTester tester) async {
    // Create a failing SharedPreferences mock
    final failingPrefs = await SharedPreferences.getInstance();
    await failingPrefs.setString('user_profile', 'invalid json');

    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          sharedPreferencesProvider.overrideWithValue(failingPrefs),
        ],
        child: MaterialApp(
          scaffoldMessengerKey: GlobalKey<ScaffoldMessengerState>(),
          home: SizedBox(
            width: 1024,
            height: 768,
            child: Scaffold(
              body: ProfileSettingsScreen(userId: 'test_user'),
            ),
          ),
        ),
      ),
    );

    // Wait for initial load
    await tester.pumpAndSettle();

    // Enter new profile data
    await tester.enterText(find.byType(TextFormField).at(0), 'New Test User');
    await tester.enterText(
        find.byType(TextFormField).at(1), '<EMAIL>');
    await tester.pump();

    // Find and scroll to the save button
    await tester.dragUntilVisible(
      find.text('Save Changes'),
      find.byType(SingleChildScrollView),
      const Offset(0, -100),
    );
    await tester.pumpAndSettle();

    // Tap save button
    await tester.tap(find.text('Save Changes'));
    await tester.pump();

    // Wait for save operation and UI updates
    await tester.pumpAndSettle();

    // Wait for error message to appear
    await tester.pump(const Duration(milliseconds: 100));

    // Verify error message in SnackBar
    expect(find.byType(SnackBar), findsOneWidget);
    expect(find.textContaining('Failed to save profile'), findsOneWidget);
  });

  testWidgets('ProfileSettingsScreen updates preferences',
      (WidgetTester tester) async {
    // Set up a larger screen size for the test
    await tester.binding.setSurfaceSize(const Size(1024, 768));

    await tester.pumpWidget(
      createTestWidget(child: ProfileSettingsScreen(userId: 'test_user')),
    );

    // Wait for initial load
    await tester.pumpAndSettle();

    // Find and scroll to the preferences section
    await tester.dragUntilVisible(
      find.text('Language'),
      find.byType(SingleChildScrollView),
      const Offset(0, -100),
    );
    await tester.pumpAndSettle();

    // Tap language selector
    await tester.tap(find.text('Language'));
    await tester.pumpAndSettle();

    // Select new language
    await tester.tap(find.text('English').last);
    await tester.pumpAndSettle();

    // Find and tap timezone selector
    await tester.dragUntilVisible(
      find.text('Time Zone'),
      find.byType(SingleChildScrollView),
      const Offset(0, -100),
    );
    await tester.tap(find.text('Time Zone'));
    await tester.pumpAndSettle();

    // Select newtimezone
    await tester.tap(find.text('UTC-8 (Pacific Time)').last);
    await tester.pumpAndSettle();

    // Save changes
    await tester.tap(find.text('Save Changes'));
    await tester.pumpAndSettle();

    // Verify success message in SnackBar
    expect(find.byType(SnackBar), findsOneWidget);
    expect(find.text('Profile updated successfully'), findsOneWidget);

    // Verify preferences were saved
    final profile = await profileService.getProfile('test_user');
    expect(profile?.preferences.language, equals('English'));
    expect(profile?.preferences.timezone, equals('UTC-8 (Pacific Time)'));

    // Reset the surface size
    await tester.binding.setSurfaceSize(null);
  });
}
