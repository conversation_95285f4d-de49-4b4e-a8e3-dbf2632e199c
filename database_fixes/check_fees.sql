-- Check what fees are stored in your NQ trades
SELECT 
    'NQ Trade Fees Check' as check_category,
    id,
    symbol,
    trade_type,
    entry_price,
    exit_price,
    quantity,
    fees,
    commission,
    pnl as current_pnl,
    -- Calculate what P&L should be with NO fees
    CASE 
        WHEN trade_type = 'LONG' THEN 
            (exit_price - entry_price) * quantity * 20
        ELSE 
            (entry_price - exit_price) * quantity * 20
    END as pnl_no_fees,
    -- Calculate what P&L should be WITH fees
    CASE 
        WHEN trade_type = 'LONG' THEN 
            (exit_price - entry_price) * quantity * 20 - COALESCE(fees, 0)
        ELSE 
            (entry_price - exit_price) * quantity * 20 - COALESCE(fees, 0)
    END as pnl_with_fees
FROM trades 
WHERE symbol ILIKE '%NQ%' 
  AND status = 'CLOSED' 
  AND exit_price IS NOT NULL
ORDER BY entry_date DESC
LIMIT 10;

-- Check if there are any default fees being applied
SELECT 
    'Fee Statistics' as category,
    COUNT(*) as total_trades,
    COUNT(CASE WHEN fees > 0 THEN 1 END) as trades_with_fees,
    AVG(fees) as avg_fees,
    MIN(fees) as min_fees,
    MAX(fees) as max_fees,
    COUNT(CASE WHEN fees = 1.77 THEN 1 END) as trades_with_177_fees
FROM trades 
WHERE symbol ILIKE '%NQ%' 
  AND status = 'CLOSED';

-- Show the specific trade from your example
SELECT 
    'Your Example Trade' as category,
    entry_price,
    exit_price,
    quantity,
    fees,
    commission,
    pnl,
    (exit_price - entry_price) * quantity * 20 as gross_pnl,
    (exit_price - entry_price) * quantity * 20 - COALESCE(fees, 0) as net_pnl_with_fees,
    (exit_price - entry_price) * quantity * 20 - 0 as net_pnl_no_fees
FROM trades 
WHERE entry_price = 23578.5 
  AND exit_price = 23583.25
  AND symbol ILIKE '%NQ%';
