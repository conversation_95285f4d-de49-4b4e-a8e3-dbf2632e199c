-- CENTRALIZED P&L CALCULATION - DATABASE VERSION
-- This function mirrors the logic in src/utils/pnlCalculator.js
-- Any changes here should be reflected in the JavaScript version and vice versa

CREATE OR REPLACE FUNCTION get_contract_multiplier(symbol TEXT)
RETURNS NUMERIC AS $$
BEGIN
    -- Handle null/empty symbol
    IF symbol IS NULL OR symbol = '' THEN
        RETURN 1;
    END IF;

    -- Convert to uppercase for comparison
    symbol := UPPER(symbol);

    -- Futures contract multipliers (must match src/utils/pnlCalculator.js)
    IF symbol LIKE '%NQ%' THEN
        RETURN 20;    -- Nasdaq-100 E-mini: $20 per point
    ELSIF symbol LIKE '%ES%' THEN
        RETURN 50;    -- S&P 500 E-mini: $50 per point
    ELSIF symbol LIKE '%YM%' THEN
        RETURN 5;     -- Dow E-mini: $5 per point
    ELSIF symbol LIKE '%RTY%' THEN
        RETURN 50;    -- Russell 2000 E-mini: $50 per point
    ELSIF symbol LIKE '%CL%' THEN
        RETURN 1000;  -- Crude Oil: $1000 per point
    ELSIF symbol LIKE '%GC%' THEN
        RETURN 100;   -- Gold: $100 per point
    ELSIF symbol LIKE '%SI%' THEN
        RETURN 5000;  -- Silver: $5000 per point
    ELSE
        RETURN 1;     -- Default for stocks
    END IF;
END;
$$ LANGUAGE plpgsql;

-- AUTHORITATIVE P&L CALCULATION TRIGGER
-- This is the SINGLE SOURCE OF TRUTH for P&L calculations
-- Mirrors the logic in src/utils/pnlCalculator.js
CREATE OR REPLACE FUNCTION calculate_trade_pnl()
RETURNS TRIGGER AS $$
DECLARE
    contract_multiplier NUMERIC;
    gross_pnl NUMERIC;
    total_investment NUMERIC;
BEGIN
    -- Only calculate P&L for closed trades with valid data
    IF NEW.exit_price IS NOT NULL AND NEW.status = 'CLOSED' AND
       NEW.entry_price > 0 AND NEW.exit_price > 0 AND NEW.quantity > 0 THEN

        -- Get contract multiplier for the symbol
        contract_multiplier := get_contract_multiplier(NEW.symbol);

        -- Calculate gross P&L based on trade type with contract multiplier
        IF UPPER(NEW.trade_type) = 'LONG' THEN
            gross_pnl := (NEW.exit_price - NEW.entry_price) * NEW.quantity * contract_multiplier;
        ELSIF UPPER(NEW.trade_type) = 'SHORT' THEN
            gross_pnl := (NEW.entry_price - NEW.exit_price) * NEW.quantity * contract_multiplier;
        ELSE
            -- Invalid trade type, set P&L to null
            NEW.pnl := NULL;
            NEW.pnl_percentage := NULL;
            RETURN NEW;
        END IF;

        -- Calculate net P&L (subtract fees)
        NEW.pnl := gross_pnl - COALESCE(NEW.fees, 0);

        -- Calculate P&L percentage
        total_investment := NEW.entry_price * NEW.quantity * contract_multiplier;
        IF total_investment > 0 THEN
            NEW.pnl_percentage := (NEW.pnl / total_investment) * 100;
        ELSE
            NEW.pnl_percentage := NULL;
        END IF;

    ELSE
        -- Open trade or invalid data - clear P&L fields
        NEW.pnl := NULL;
        NEW.pnl_percentage := NULL;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Ensure the trigger exists
DROP TRIGGER IF EXISTS calculate_pnl_trigger ON trades;
CREATE TRIGGER calculate_pnl_trigger 
    BEFORE INSERT OR UPDATE ON trades
    FOR EACH ROW 
    EXECUTE FUNCTION calculate_trade_pnl();

-- Update existing trades with incorrect P&L calculations for NQ futures
-- This will recalculate P&L for all closed NQ trades
UPDATE trades 
SET pnl = CASE 
    WHEN trade_type = 'LONG' THEN 
        (exit_price - entry_price) * quantity * get_contract_multiplier(symbol) - COALESCE(fees, 0)
    ELSE 
        (entry_price - exit_price) * quantity * get_contract_multiplier(symbol) - COALESCE(fees, 0)
END,
pnl_percentage = CASE 
    WHEN entry_price > 0 AND quantity > 0 THEN
        (CASE 
            WHEN trade_type = 'LONG' THEN 
                (exit_price - entry_price) * quantity * get_contract_multiplier(symbol) - COALESCE(fees, 0)
            ELSE 
                (entry_price - exit_price) * quantity * get_contract_multiplier(symbol) - COALESCE(fees, 0)
        END) / (entry_price * quantity * get_contract_multiplier(symbol)) * 100
    ELSE 0
END
WHERE status = 'CLOSED' 
  AND exit_price IS NOT NULL 
  AND (symbol ILIKE '%NQ%' OR symbol ILIKE '%ES%' OR symbol ILIKE '%YM%' OR symbol ILIKE '%RTY%' OR symbol ILIKE '%CL%' OR symbol ILIKE '%GC%' OR symbol ILIKE '%SI%')
  AND get_contract_multiplier(symbol) > 1;
