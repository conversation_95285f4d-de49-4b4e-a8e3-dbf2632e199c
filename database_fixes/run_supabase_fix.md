# 🚀 Run P&L Fix on Supabase Database

## Your Supabase Details
- **URL:** https://pqgctrqxihbmanzrrjqd.supabase.co
- **Project:** pqgctrqxihbmanzrrjqd

## 🔧 Method 1: Supabase Dashboard (Recommended)

### Step 1: Open Supabase SQL Editor
1. Go to: https://supabase.com/dashboard/project/pqgctrqxihbmanzrrjqd
2. Click on **"SQL Editor"** in the left sidebar
3. Click **"New Query"**

### Step 2: Run the P&L Fix Script
1. Copy the entire contents of `database_fixes/update_pnl_calculation.sql`
2. Paste it into the SQL Editor
3. Click **"Run"** button
4. Wait for completion (should take 10-30 seconds)

### Step 3: Verify the Fix
1. Create another new query
2. Copy the contents of `database_fixes/verify_pnl_fix.sql`
3. Paste and run it
4. Check the results to confirm the fix worked

## 🔧 Method 2: Using psql with Supabase

### Get Your Database Connection String
1. Go to: https://supabase.com/dashboard/project/pqgctrqxihbmanzrrjqd/settings/database
2. Copy the **Connection string** under "Connection parameters"
3. It should look like: `postgresql://postgres:[YOUR-PASSWORD]@db.pqgctrqxihbmanzrrjqd.supabase.co:5432/postgres`

### Run the Script
```bash
# Replace [YOUR-PASSWORD] with your actual database password
psql "postgresql://postgres:[YOUR-PASSWORD]@db.pqgctrqxihbmanzrrjqd.supabase.co:5432/postgres" -f database_fixes/update_pnl_calculation.sql
```

## 🔧 Method 3: JavaScript/Node.js Script

If you prefer to run it programmatically, I can create a Node.js script that uses your Supabase connection.

## ✅ What to Expect After Running

### Before (Current Wrong Values):
```
Avg Win/Loss: $3.84 / $11.77
Net P&L: $15.09
Average Trade: $1.89
```

### After (Correct Values):
```
Avg Win/Loss: $112.14 / $200.00
Net P&L: $585.00
Average Trade: $73.13
```

## 🔍 Verification Steps

After running the script:

1. **Check the verification results** - should show:
   - ✅ Functions exist
   - ✅ NQ multiplier = 20
   - ✅ Trades updated correctly

2. **Refresh your web app** (hard refresh: Cmd+Shift+R)

3. **Check your metrics** - should immediately show correct values

## 🐛 Troubleshooting

**If you get permission errors:**
- Make sure you're using the service role key (not anon key) for database modifications
- Or use the Supabase dashboard method instead

**If metrics still show wrong values:**
- Clear browser cache completely
- Check browser console for errors
- Verify the SQL script completed without errors

## 📋 Quick Checklist

- [ ] Open Supabase SQL Editor
- [ ] Run `update_pnl_calculation.sql`
- [ ] Run `verify_pnl_fix.sql` to confirm
- [ ] Refresh web application
- [ ] Verify metrics show correct values
- [ ] Test a few individual trades

## 🎯 Expected Timeline

- SQL script execution: 30 seconds
- Web app refresh: Immediate
- **Total fix time: Under 2 minutes**

Choose **Method 1 (Supabase Dashboard)** for the easiest approach!
