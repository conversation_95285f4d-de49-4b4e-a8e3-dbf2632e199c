#!/bin/bash

# P&L Fix Database Update Script
# This script will update your database with the corrected P&L calculations

echo "🔧 TRADEDGE P&L Fix Database Update"
echo "=================================="
echo ""

# Check if database connection details are provided
if [ -z "$1" ]; then
    echo "Usage: $0 <database_name> [host] [port] [username]"
    echo ""
    echo "Examples:"
    echo "  $0 tradedge_db"
    echo "  $0 tradedge_db localhost 5432 postgres"
    echo ""
    echo "Or set environment variables:"
    echo "  export PGDATABASE=tradedge_db"
    echo "  export PGHOST=localhost"
    echo "  export PGPORT=5432"
    echo "  export PGUSER=postgres"
    echo "  $0"
    exit 1
fi

# Set database connection parameters
DB_NAME=${1:-$PGDATABASE}
DB_HOST=${2:-${PGHOST:-localhost}}
DB_PORT=${3:-${PGPORT:-5432}}
DB_USER=${4:-${PGUSER:-postgres}}

echo "📊 Database Connection:"
echo "  Database: $DB_NAME"
echo "  Host: $DB_HOST"
echo "  Port: $DB_PORT"
echo "  User: $DB_USER"
echo ""

# Function to run SQL and check for errors
run_sql() {
    local sql_file=$1
    local description=$2
    
    echo "🔄 $description..."
    
    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "$sql_file"; then
        echo "✅ $description completed successfully"
    else
        echo "❌ $description failed"
        exit 1
    fi
    echo ""
}

# Function to run verification and show results
run_verification() {
    echo "🔍 Running verification checks..."
    echo ""
    
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "verify_pnl_fix.sql"
    
    echo ""
    echo "📋 Verification completed"
    echo ""
}

# Backup reminder
echo "⚠️  IMPORTANT: Make sure you have a database backup before proceeding!"
echo ""
read -p "Do you have a backup and want to continue? (y/N): " -n 1 -r
echo ""

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Operation cancelled. Please create a backup first."
    exit 1
fi

echo ""
echo "🚀 Starting P&L fix process..."
echo ""

# Step 1: Update P&L calculation functions and trigger
run_sql "update_pnl_calculation.sql" "Installing updated P&L calculation functions and trigger"

# Step 2: Run verification
run_verification

# Step 3: Show summary
echo "🎉 P&L Fix Process Complete!"
echo ""
echo "📈 Expected Results:"
echo "  • NQ trades should now show correct P&L with 20x multiplier"
echo "  • Your example: Entry $23,578.5 → Exit $23,583.25 = $95.00 profit"
echo "  • Metrics should now show: Avg Win $112.14, Net P&L $585.00, etc."
echo ""
echo "🔄 Next Steps:"
echo "  1. Refresh your web application"
echo "  2. Check that the metrics now show the correct values"
echo "  3. Verify a few individual trades show correct P&L"
echo ""
echo "🐛 If you still see incorrect values:"
echo "  1. Check the verification results above"
echo "  2. Clear browser cache and refresh"
echo "  3. Check browser console for any errors"
echo ""
echo "✅ Database update completed successfully!"
