-- Fix fees and recalculate P&L to match your correct data
-- This assumes your "correct" data has zero fees

-- First, let's check current fees
SELECT 'Current Fees Check' as step, symbol, fees, COUNT(*) as count
FROM trades 
WHERE symbol ILIKE '%NQ%' AND status = 'CLOSED'
GROUP BY symbol, fees
ORDER BY symbol, fees;

-- Fix: Remove the hardcoded $1.77 commission from CSV imports and recalculate P&L
-- This fixes the issue where web CSV import was adding $1.77 commission to every trade
UPDATE trades
SET
    fees = 0,
    commission = 0,
    pnl = CASE 
        WHEN trade_type = 'LONG' THEN 
            (exit_price - entry_price) * quantity * get_contract_multiplier(symbol)
        ELSE 
            (entry_price - exit_price) * quantity * get_contract_multiplier(symbol)
    END,
    pnl_percentage = CASE 
        WHEN entry_price > 0 AND quantity > 0 THEN
            (CASE 
                WHEN trade_type = 'LONG' THEN 
                    (exit_price - entry_price) * quantity * get_contract_multiplier(symbol)
                ELSE 
                    (entry_price - exit_price) * quantity * get_contract_multiplier(symbol)
            END) / (entry_price * quantity * get_contract_multiplier(symbol)) * 100
        ELSE 0
    END
WHERE status = 'CLOSED' 
  AND exit_price IS NOT NULL 
  AND symbol ILIKE '%NQ%';

-- Verify the fix with your specific example
SELECT 
    'Verification - Your Example' as check_name,
    entry_price,
    exit_price,
    quantity,
    fees,
    pnl,
    CASE 
        WHEN pnl = 95.00 THEN '✅ CORRECT ($95.00)'
        ELSE '❌ STILL WRONG'
    END as status
FROM trades 
WHERE entry_price = 23578.5 
  AND exit_price = 23583.25
  AND symbol ILIKE '%NQ%';

-- Show all updated NQ trades
SELECT 
    'Updated NQ Trades' as category,
    entry_price,
    exit_price,
    quantity,
    fees,
    pnl,
    (exit_price - entry_price) * quantity * 20 as expected_pnl
FROM trades 
WHERE symbol ILIKE '%NQ%' 
  AND status = 'CLOSED' 
  AND exit_price IS NOT NULL
ORDER BY entry_date DESC;
