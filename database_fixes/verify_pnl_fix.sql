-- Verification Script for P&L Fix
-- Run this to check if the P&L calculations have been corrected

-- 1. Check if the new functions exist
SELECT 
    'get_contract_multiplier function exists' as check_name,
    CASE WHEN EXISTS (
        SELECT 1 FROM pg_proc 
        WHERE proname = 'get_contract_multiplier'
    ) THEN '✅ EXISTS' ELSE '❌ MISSING' END as status;

SELECT 
    'calculate_trade_pnl function exists' as check_name,
    CASE WHEN EXISTS (
        SELECT 1 FROM pg_proc 
        WHERE proname = 'calculate_trade_pnl'
    ) THEN '✅ EXISTS' ELSE '❌ MISSING' END as status;

-- 2. Test contract multiplier function
SELECT 
    'Contract Multiplier Tests' as test_category,
    symbol,
    get_contract_multiplier(symbol) as multiplier,
    CASE 
        WHEN symbol = 'NQ' AND get_contract_multiplier(symbol) = 20 THEN '✅ CORRECT'
        WHEN symbol = 'ES' AND get_contract_multiplier(symbol) = 50 THEN '✅ CORRECT'
        WHEN symbol = 'YM' AND get_contract_multiplier(symbol) = 5 THEN '✅ CORRECT'
        WHEN symbol = 'RTY' AND get_contract_multiplier(symbol) = 50 THEN '✅ CORRECT'
        ELSE '❌ INCORRECT'
    END as status
FROM (VALUES ('NQ'), ('ES'), ('YM'), ('RTY'), ('AAPL')) AS test_symbols(symbol);

-- 3. Check sample NQ trades before and after fix
-- This shows what the P&L should be vs what it currently is
SELECT 
    'NQ Trade P&L Check' as check_category,
    id,
    symbol,
    trade_type,
    entry_price,
    exit_price,
    quantity,
    fees,
    pnl as current_pnl,
    CASE 
        WHEN trade_type = 'LONG' THEN 
            (exit_price - entry_price) * quantity * get_contract_multiplier(symbol) - COALESCE(fees, 0)
        ELSE 
            (entry_price - exit_price) * quantity * get_contract_multiplier(symbol) - COALESCE(fees, 0)
    END as correct_pnl,
    CASE 
        WHEN pnl = (CASE 
            WHEN trade_type = 'LONG' THEN 
                (exit_price - entry_price) * quantity * get_contract_multiplier(symbol) - COALESCE(fees, 0)
            ELSE 
                (entry_price - exit_price) * quantity * get_contract_multiplier(symbol) - COALESCE(fees, 0)
        END) THEN '✅ CORRECT' 
        ELSE '❌ NEEDS UPDATE' 
    END as status
FROM trades 
WHERE symbol ILIKE '%NQ%' 
  AND status = 'CLOSED' 
  AND exit_price IS NOT NULL
ORDER BY entry_date DESC
LIMIT 10;

-- 4. Summary of trades that need P&L recalculation
SELECT 
    'Trades Needing P&L Update' as summary_category,
    symbol,
    COUNT(*) as trade_count,
    COUNT(CASE 
        WHEN pnl != (CASE 
            WHEN trade_type = 'LONG' THEN 
                (exit_price - entry_price) * quantity * get_contract_multiplier(symbol) - COALESCE(fees, 0)
            ELSE 
                (entry_price - exit_price) * quantity * get_contract_multiplier(symbol) - COALESCE(fees, 0)
        END) THEN 1 
    END) as incorrect_pnl_count,
    ROUND(AVG(pnl), 2) as avg_current_pnl,
    ROUND(AVG(CASE 
        WHEN trade_type = 'LONG' THEN 
            (exit_price - entry_price) * quantity * get_contract_multiplier(symbol) - COALESCE(fees, 0)
        ELSE 
            (entry_price - exit_price) * quantity * get_contract_multiplier(symbol) - COALESCE(fees, 0)
    END), 2) as avg_correct_pnl
FROM trades 
WHERE status = 'CLOSED' 
  AND exit_price IS NOT NULL
  AND get_contract_multiplier(symbol) > 1  -- Only futures
GROUP BY symbol
ORDER BY symbol;

-- 5. Check if trigger is active
SELECT 
    'Trigger Status' as check_category,
    trigger_name,
    event_object_table,
    action_timing,
    event_manipulation,
    CASE WHEN trigger_name IS NOT NULL THEN '✅ ACTIVE' ELSE '❌ MISSING' END as status
FROM information_schema.triggers 
WHERE trigger_name = 'calculate_pnl_trigger'
  AND event_object_table = 'trades';

-- 6. Test calculation with your specific example
-- Entry: $23,578.5, Exit: $23,583.25, Size: 1
SELECT 
    'Your Example Test' as test_category,
    23578.5 as entry_price,
    23583.25 as exit_price,
    1 as quantity,
    'NQ' as symbol,
    get_contract_multiplier('NQ') as multiplier,
    (23583.25 - 23578.5) * 1 * get_contract_multiplier('NQ') as calculated_pnl,
    CASE 
        WHEN (23583.25 - 23578.5) * 1 * get_contract_multiplier('NQ') = 95.0 
        THEN '✅ CORRECT ($95.00)' 
        ELSE '❌ INCORRECT' 
    END as status;
