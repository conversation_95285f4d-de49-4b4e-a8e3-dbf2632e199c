<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trade Reviewer Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-100 p-6">
    <div class="container mx-auto">
        <!-- Key Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
            <!-- Total P&L Card -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-gray-500 text-sm font-medium">Total P&L</h3>
                <div class="flex items-center">
                    <div id="totalPnL" class="text-2xl font-bold text-gray-900">$0.00</div>
                </div>
            </div>

            <!-- Win Rate Card -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-gray-500 text-sm font-medium">Win Rate</h3>
                <div class="flex items-center">
                    <div id="winRate" class="text-2xl font-bold text-gray-900">0%</div>
                </div>
            </div>

            <!-- Total Trades Card -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-gray-500 text-sm font-medium">Total Trades</h3>
                <div class="flex items-center">
                    <div id="totalTrades" class="text-2xl font-bold text-gray-900">0</div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- Cumulative P&L Chart -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-gray-500 text-sm font-medium mb-4">Cumulative P&L</h3>
                <canvas id="pnlChart"></canvas>
            </div>

            <!-- Win/Loss by Instrument -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-gray-500 text-sm font-medium mb-4">Win/Loss by Instrument</h3>
                <canvas id="instrumentChart"></canvas>
            </div>
        </div>

        <!-- Trade Table Section -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-gray-500 text-sm font-medium">Recent Trades</h3>
                <div class="flex gap-4">
                    <input type="date" id="dateFilter" class="rounded border p-2">
                    <select id="instrumentFilter" class="rounded border p-2">
                        <option value="">All Instruments</option>
                    </select>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Instrument</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Entry</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Exit</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">P&L</th>
                        </tr>
                    </thead>
                    <tbody id="tradeTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- Trade rows will be inserted here dynamically -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="dashboard.js"></script>
</body>
</html> 