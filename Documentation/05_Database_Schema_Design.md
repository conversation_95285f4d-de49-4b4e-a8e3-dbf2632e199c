# 🗄️ TRADEDGE: Database Schema Design

**Version:** 1.0  
**Database:** PostgreSQL 14+  
**Last Updated:** January 2025  

## 📋 Overview

This document defines the complete database schema for TRADEDGE, including tables, relationships, indexes, and constraints. The schema is designed for scalability, performance, and data integrity across all application features.

---

## 🏗️ Schema Architecture

### **Design Principles**
- **Normalization**: 3NF compliance with strategic denormalization for performance
- **Scalability**: Partitioning strategies for large datasets
- **Performance**: Optimized indexes and query patterns
- **Integrity**: Comprehensive constraints and validation
- **Audit Trail**: Complete change tracking for critical data

### **Naming Conventions**
- **Tables**: `snake_case` (e.g., `user_profiles`)
- **Columns**: `snake_case` (e.g., `created_at`)
- **Indexes**: `idx_table_column` (e.g., `idx_trades_user_id`)
- **Foreign Keys**: `fk_table_column` (e.g., `fk_trades_user_id`)
- **Constraints**: `chk_table_condition` (e.g., `chk_trades_positive_price`)

---

## 👥 User Management Schema

### **users**
Core user account information.

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    avatar_url VARCHAR(500),
    email_verified BOOLEAN DEFAULT FALSE,
    email_verified_at TIMESTAMP WITH TIME ZONE,
    timezone VARCHAR(50) DEFAULT 'UTC',
    locale VARCHAR(10) DEFAULT 'en',
    currency VARCHAR(3) DEFAULT 'USD',
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'deleted')),
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_created_at ON users(created_at);
```

### **user_preferences**
User application preferences and settings.

```sql
CREATE TABLE user_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    theme VARCHAR(20) DEFAULT 'light' CHECK (theme IN ('light', 'dark', 'auto')),
    language VARCHAR(10) DEFAULT 'en',
    date_format VARCHAR(20) DEFAULT 'MM/DD/YYYY',
    time_format VARCHAR(10) DEFAULT '12h' CHECK (time_format IN ('12h', '24h')),
    number_format VARCHAR(20) DEFAULT 'US',
    notifications JSONB DEFAULT '{}',
    trading_settings JSONB DEFAULT '{}',
    dashboard_layout JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id)
);

-- Indexes
CREATE INDEX idx_user_preferences_user_id ON user_preferences(user_id);
```

### **user_sessions**
Active user sessions for security tracking.

```sql
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    refresh_token_hash VARCHAR(255) NOT NULL,
    device_info JSONB,
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX idx_user_sessions_refresh_token ON user_sessions(refresh_token_hash);
```

---

## 📈 Trading Data Schema

### **trades**
Core trading transactions and positions.

```sql
CREATE TABLE trades (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    symbol VARCHAR(20) NOT NULL,
    trade_type VARCHAR(10) NOT NULL CHECK (trade_type IN ('LONG', 'SHORT')),
    status VARCHAR(20) DEFAULT 'OPEN' CHECK (status IN ('OPEN', 'CLOSED', 'CANCELLED')),
    
    -- Price and Quantity
    entry_price DECIMAL(12,4) NOT NULL CHECK (entry_price > 0),
    exit_price DECIMAL(12,4) CHECK (exit_price > 0),
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    
    -- Risk Management
    stop_loss DECIMAL(12,4) CHECK (stop_loss > 0),
    take_profit DECIMAL(12,4) CHECK (take_profit > 0),
    
    -- Timing
    entry_date TIMESTAMP WITH TIME ZONE NOT NULL,
    exit_date TIMESTAMP WITH TIME ZONE,
    
    -- Financial Calculations
    fees DECIMAL(10,2) DEFAULT 0 CHECK (fees >= 0),
    pnl DECIMAL(12,2),
    pnl_percentage DECIMAL(8,4),
    
    -- Additional Information
    notes TEXT,
    strategy VARCHAR(100),
    market_conditions VARCHAR(100),
    
    -- Metadata
    broker VARCHAR(50),
    account_id VARCHAR(100),
    order_id VARCHAR(100),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT chk_trades_exit_logic CHECK (
        (status = 'CLOSED' AND exit_price IS NOT NULL AND exit_date IS NOT NULL) OR
        (status != 'CLOSED' AND exit_price IS NULL AND exit_date IS NULL)
    )
);

-- Indexes
CREATE INDEX idx_trades_user_id ON trades(user_id);
CREATE INDEX idx_trades_symbol ON trades(symbol);
CREATE INDEX idx_trades_status ON trades(status);
CREATE INDEX idx_trades_entry_date ON trades(entry_date);
CREATE INDEX idx_trades_user_entry_date ON trades(user_id, entry_date);
CREATE INDEX idx_trades_user_symbol ON trades(user_id, symbol);

-- Composite index for common queries
CREATE INDEX idx_trades_user_status_date ON trades(user_id, status, entry_date);
```

### **trade_tags**
Tagging system for trade categorization.

```sql
CREATE TABLE trade_tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    trade_id UUID NOT NULL REFERENCES trades(id) ON DELETE CASCADE,
    tag VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(trade_id, tag)
);

-- Indexes
CREATE INDEX idx_trade_tags_trade_id ON trade_tags(trade_id);
CREATE INDEX idx_trade_tags_tag ON trade_tags(tag);
```

### **trade_screenshots**
Screenshot attachments for trades.

```sql
CREATE TABLE trade_screenshots (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    trade_id UUID NOT NULL REFERENCES trades(id) ON DELETE CASCADE,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    description TEXT,
    upload_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT chk_screenshots_file_size CHECK (file_size > 0 AND file_size <= 10485760) -- 10MB max
);

-- Indexes
CREATE INDEX idx_trade_screenshots_trade_id ON trade_screenshots(trade_id);
```

---

## 🎯 Goals and Analytics Schema

### **trading_goals**
User-defined trading goals and targets.

```sql
CREATE TABLE trading_goals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    goal_type VARCHAR(50) NOT NULL CHECK (goal_type IN (
        'profit_target', 'win_rate', 'max_drawdown', 'trade_count', 
        'risk_reward', 'consistency', 'custom'
    )),
    target_value DECIMAL(12,4) NOT NULL,
    current_value DECIMAL(12,4) DEFAULT 0,
    unit VARCHAR(20) NOT NULL, -- 'dollars', 'percentage', 'count', etc.
    period_type VARCHAR(20) NOT NULL CHECK (period_type IN (
        'daily', 'weekly', 'monthly', 'quarterly', 'yearly', 'custom'
    )),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'failed', 'paused')),
    priority INTEGER DEFAULT 1 CHECK (priority BETWEEN 1 AND 5),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT chk_goals_date_range CHECK (end_date > start_date)
);

-- Indexes
CREATE INDEX idx_trading_goals_user_id ON trading_goals(user_id);
CREATE INDEX idx_trading_goals_status ON trading_goals(status);
CREATE INDEX idx_trading_goals_period ON trading_goals(start_date, end_date);
```

### **performance_snapshots**
Pre-calculated performance metrics for faster analytics.

```sql
CREATE TABLE performance_snapshots (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    snapshot_date DATE NOT NULL,
    period_type VARCHAR(20) NOT NULL CHECK (period_type IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly')),
    
    -- Core Metrics
    total_trades INTEGER DEFAULT 0,
    winning_trades INTEGER DEFAULT 0,
    losing_trades INTEGER DEFAULT 0,
    win_rate DECIMAL(5,2) DEFAULT 0,
    
    -- Financial Metrics
    total_pnl DECIMAL(12,2) DEFAULT 0,
    total_pnl_percentage DECIMAL(8,4) DEFAULT 0,
    average_win DECIMAL(10,2) DEFAULT 0,
    average_loss DECIMAL(10,2) DEFAULT 0,
    largest_win DECIMAL(10,2) DEFAULT 0,
    largest_loss DECIMAL(10,2) DEFAULT 0,
    
    -- Risk Metrics
    profit_factor DECIMAL(8,4) DEFAULT 0,
    sharpe_ratio DECIMAL(8,4) DEFAULT 0,
    max_drawdown DECIMAL(8,4) DEFAULT 0,
    expectancy DECIMAL(8,4) DEFAULT 0,
    
    -- Volume Metrics
    total_volume DECIMAL(15,2) DEFAULT 0,
    average_position_size DECIMAL(12,2) DEFAULT 0,
    
    -- Time Metrics
    average_hold_time INTERVAL,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, snapshot_date, period_type)
);

-- Indexes
CREATE INDEX idx_performance_snapshots_user_id ON performance_snapshots(user_id);
CREATE INDEX idx_performance_snapshots_date ON performance_snapshots(snapshot_date);
CREATE INDEX idx_performance_snapshots_user_date ON performance_snapshots(user_id, snapshot_date);
```

---

## 📊 Import/Export Schema

### **import_jobs**
Track CSV import operations and their status.

```sql
CREATE TABLE import_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    file_name VARCHAR(255) NOT NULL,
    file_size INTEGER NOT NULL,
    broker VARCHAR(50),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN (
        'pending', 'processing', 'completed', 'failed', 'cancelled'
    )),
    
    -- Results
    total_rows INTEGER DEFAULT 0,
    processed_rows INTEGER DEFAULT 0,
    imported_rows INTEGER DEFAULT 0,
    skipped_rows INTEGER DEFAULT 0,
    error_rows INTEGER DEFAULT 0,
    
    -- Configuration
    column_mapping JSONB,
    import_settings JSONB,
    
    -- Error Tracking
    error_details JSONB,
    
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_import_jobs_user_id ON import_jobs(user_id);
CREATE INDEX idx_import_jobs_status ON import_jobs(status);
CREATE INDEX idx_import_jobs_created_at ON import_jobs(created_at);
```

### **import_errors**
Detailed error information for failed import rows.

```sql
CREATE TABLE import_errors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    import_job_id UUID NOT NULL REFERENCES import_jobs(id) ON DELETE CASCADE,
    row_number INTEGER NOT NULL,
    raw_data JSONB NOT NULL,
    error_type VARCHAR(50) NOT NULL,
    error_message TEXT NOT NULL,
    field_errors JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_import_errors_job_id ON import_errors(import_job_id);
```

---

## 🔐 Security and Audit Schema

### **audit_logs**
Comprehensive audit trail for all critical operations.

```sql
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    action VARCHAR(50) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_resource ON audit_logs(resource_type, resource_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);

-- Partition by month for performance
CREATE TABLE audit_logs_y2025m01 PARTITION OF audit_logs
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
```

### **api_rate_limits**
Track API usage for rate limiting.

```sql
CREATE TABLE api_rate_limits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    ip_address INET,
    endpoint VARCHAR(200) NOT NULL,
    request_count INTEGER DEFAULT 1,
    window_start TIMESTAMP WITH TIME ZONE NOT NULL,
    window_end TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_api_rate_limits_user_id ON api_rate_limits(user_id);
CREATE INDEX idx_api_rate_limits_ip ON api_rate_limits(ip_address);
CREATE INDEX idx_api_rate_limits_window ON api_rate_limits(window_start, window_end);
```

---

## 🔧 Database Functions and Triggers

### **Update Timestamp Trigger**
```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply to relevant tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_trades_updated_at BEFORE UPDATE ON trades
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### **Calculate Trade P&L Function**
```sql
CREATE OR REPLACE FUNCTION calculate_trade_pnl()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.exit_price IS NOT NULL AND NEW.status = 'CLOSED' THEN
        -- Calculate P&L based on trade type
        IF NEW.trade_type = 'LONG' THEN
            NEW.pnl = (NEW.exit_price - NEW.entry_price) * NEW.quantity - NEW.fees;
        ELSE -- SHORT
            NEW.pnl = (NEW.entry_price - NEW.exit_price) * NEW.quantity - NEW.fees;
        END IF;
        
        -- Calculate P&L percentage
        NEW.pnl_percentage = (NEW.pnl / (NEW.entry_price * NEW.quantity)) * 100;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER calculate_pnl_trigger BEFORE INSERT OR UPDATE ON trades
    FOR EACH ROW EXECUTE FUNCTION calculate_trade_pnl();
```

---

## 📈 Performance Optimization

### **Partitioning Strategy**
```sql
-- Partition trades by year for better performance
CREATE TABLE trades_y2025 PARTITION OF trades
    FOR VALUES FROM ('2025-01-01') TO ('2026-01-01');

CREATE TABLE trades_y2024 PARTITION OF trades
    FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');
```

### **Materialized Views**
```sql
-- User performance summary view
CREATE MATERIALIZED VIEW user_performance_summary AS
SELECT 
    user_id,
    COUNT(*) as total_trades,
    COUNT(*) FILTER (WHERE pnl > 0) as winning_trades,
    COUNT(*) FILTER (WHERE pnl < 0) as losing_trades,
    ROUND(AVG(CASE WHEN pnl > 0 THEN pnl END), 2) as avg_win,
    ROUND(AVG(CASE WHEN pnl < 0 THEN pnl END), 2) as avg_loss,
    ROUND(SUM(pnl), 2) as total_pnl,
    ROUND((COUNT(*) FILTER (WHERE pnl > 0)::DECIMAL / COUNT(*)) * 100, 2) as win_rate
FROM trades 
WHERE status = 'CLOSED'
GROUP BY user_id;

CREATE UNIQUE INDEX idx_user_performance_summary_user_id 
    ON user_performance_summary(user_id);
```

---

## 🔄 Maintenance and Monitoring

### **Database Maintenance Scripts**
```sql
-- Clean up expired sessions
DELETE FROM user_sessions WHERE expires_at < NOW() - INTERVAL '7 days';

-- Archive old audit logs
INSERT INTO audit_logs_archive 
SELECT * FROM audit_logs WHERE created_at < NOW() - INTERVAL '1 year';

-- Refresh materialized views
REFRESH MATERIALIZED VIEW CONCURRENTLY user_performance_summary;
```

### **Monitoring Queries**
```sql
-- Check table sizes
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Check index usage
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;
```

This database schema provides a robust foundation for the TRADEDGE application with proper normalization, performance optimization, and scalability considerations.
