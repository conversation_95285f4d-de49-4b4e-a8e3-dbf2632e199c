{"name": "tradedge", "version": "1.0.0", "description": "Trade analysis tool for Tradovate", "main": "index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "jest", "test:watch": "jest --watch", "eject": "react-scripts eject", "dev": "react-scripts start"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@supabase/supabase-js": "^2.53.0", "axios": "^1.7.9", "chart.js": "^4.4.0", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-plugin-annotation": "^3.1.0", "cors": "^2.8.5", "csv-parse": "^5.6.0", "date-fns": "^2.30.0", "dotenv": "^16.4.7", "express": "^4.21.2", "framer-motion": "^11.15.0", "idb": "^7.1.1", "isomorphic-ws": "^5.0.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-datepicker": "^7.5.0", "react-dom": "^18.2.0", "react-grid-layout": "^1.4.2", "react-native-linear-gradient": "^2.8.3", "react-native-vector-icons": "^10.2.0", "react-router-dom": "^6.16.0", "react-scripts": "5.0.1", "sequelize-cli": "^6.6.2", "styled-components": "^6.1.13", "tailwindcss": "^3.3.3", "ws": "^8.18.0", "zustand": "^4.4.1"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/preset-env": "^7.22.20", "@babel/preset-react": "^7.22.15", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.15", "@tailwindcss/forms": "^0.5.6", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.0.0", "@types/react-native-vector-icons": "^6.4.18", "assert": "^2.1.0", "autoprefixer": "^10.4.16", "babel-jest": "^29.7.0", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "events": "^3.3.0", "fs-extra": "^11.2.0", "https-browserify": "^1.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "postcss": "^8.4.31", "process": "^0.11.10", "react-refresh": "^0.16.0", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "url": "^0.11.4", "util": "^0.12.5"}, "jest": {"testEnvironment": "node", "transform": {"^.+\\.jsx?$": "babel-jest"}, "testMatch": ["**/src/tests/TradeModel.test.js"], "moduleDirectories": ["node_modules", "src"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}