# Standardized P&L Calculation System

## Overview

This document describes the standardized P&L calculation system implemented to solve the profit calculation inconsistencies in the TRADEDGE application. The system ensures that **P&L is calculated only once** and consistently across the entire application.

## Problem Statement

Previously, P&L calculations were scattered across multiple files:
- `src/stores/supabaseTradeStore.js` (2 different places)
- `src/services/dataServiceSupabase.js`
- `src/components/PnLCalculator.jsx`
- `src/utils/tradeMetrics.js`
- Database PostgreSQL trigger
- Flutter models

This led to:
- **Inconsistent multipliers** (NQ was 5 in some places, 20 in others)
- **Maintenance nightmare** (changes needed in multiple places)
- **Calculation discrepancies** between different parts of the app
- **Debugging difficulties** when P&L values didn't match

## Solution Architecture

### Single Source of Truth: Database

The **database trigger** is now the **primary and authoritative** source for P&L calculations:

1. **Database calculates P&L** automatically when trades are inserted/updated
2. **Application uses database P&L** as the primary value
3. **Fallback calculations** only when database P&L is unavailable

### Centralized Utilities

#### 1. Core Calculation (`src/utils/pnlCalculator.js`)
- **Single authoritative P&L calculation function**
- **Contract multiplier definitions**
- **Standardized trade object handling**
- **Validation functions**

#### 2. Fallback System (`src/utils/pnlFallback.js`)
- **Fallback when database P&L unavailable**
- **Real-time unrealized P&L calculations**
- **Batch processing capabilities**
- **Validation against database values**

#### 3. Database Functions (`database_fixes/update_pnl_calculation.sql`)
- **PostgreSQL trigger for automatic P&L calculation**
- **Contract multiplier function in SQL**
- **Mirrors JavaScript logic exactly**

## Contract Multipliers (Standardized)

| Symbol | Multiplier | Description |
|--------|------------|-------------|
| NQ     | 20         | Nasdaq-100 E-mini: $20 per point |
| ES     | 50         | S&P 500 E-mini: $50 per point |
| YM     | 5          | Dow E-mini: $5 per point |
| RTY    | 50         | Russell 2000 E-mini: $50 per point |
| CL     | 1000       | Crude Oil: $1000 per point |
| GC     | 100        | Gold: $100 per point |
| SI     | 5000       | Silver: $5000 per point |
| Others | 1          | Stocks and unknown symbols |

## Data Flow

```
1. Trade Data Input
   ↓
2. Database Trigger Calculates P&L
   ↓
3. Application Reads Database P&L
   ↓
4. Fallback Calculation (if database P&L missing)
   ↓
5. Display to User
```

## Implementation Changes

### Files Modified

#### ✅ **Updated to Use Centralized System**
- `src/stores/supabaseTradeStore.js` - Uses fallback utility
- `src/services/dataServiceSupabase.js` - Relies on database calculation
- `src/components/PnLCalculator.jsx` - Uses centralized multipliers
- `src/utils/tradeMetrics.js` - Removed redundant calculations

#### ✅ **New Files Created**
- `src/utils/pnlCalculator.js` - Core calculation utility
- `src/utils/pnlFallback.js` - Fallback system
- `database_fixes/update_pnl_calculation.sql` - Database updates

#### ⚠️ **Deprecated Functions**
- Contract multiplier functions in individual files (marked deprecated)
- Inline P&L calculations (replaced with centralized calls)

### Database Changes Required

Run the SQL script to update your database:

```bash
psql -d your_database -f database_fixes/update_pnl_calculation.sql
```

This will:
1. Create `get_contract_multiplier()` function
2. Update `calculate_trade_pnl()` trigger function
3. Recalculate P&L for existing trades with correct multipliers

## Usage Examples

### Basic P&L Calculation
```javascript
import { calculatePnL, standardizeTradeObject } from '../utils/pnlCalculator';

const trade = standardizeTradeObject(rawTradeData);
const pnl = calculatePnL(trade);
```

### With Fallback System
```javascript
import { calculatePnLWithFallback } from '../utils/pnlFallback';

const result = calculatePnLWithFallback(trade, {
  preferDatabaseValue: true,
  validateCalculation: true
});

console.log(`P&L: ${result.pnl}, Source: ${result.source}`);
```

### Unrealized P&L
```javascript
import { calculateUnrealizedPnLWithFallback } from '../utils/pnlFallback';

const result = calculateUnrealizedPnLWithFallback(openTrade, currentMarketPrice);
console.log(`Unrealized P&L: ${result.unrealizedPnL}`);
```

## Validation and Testing

### Verification of Fix

**Before (Incorrect):**
- NQ Entry: $23,578.5, Exit: $23,583.25, Size: 1
- Wrong: (23583.25 - 23578.5) × 1 = **$4.75**

**After (Correct):**
- NQ Entry: $23,578.5, Exit: $23,583.25, Size: 1  
- Correct: (23583.25 - 23578.5) × 1 × 20 = **$95.00**

### Testing Checklist

- [ ] Database trigger calculates correct P&L for new trades
- [ ] Existing trades recalculated with correct multipliers
- [ ] Web application displays correct P&L values
- [ ] Fallback calculations match database calculations
- [ ] Contract multipliers consistent across all systems

## Maintenance Guidelines

### Adding New Contract Multipliers

1. **Update JavaScript constant** in `src/utils/pnlCalculator.js`
2. **Update SQL function** in database
3. **Test both calculations** produce same results

### Modifying P&L Logic

1. **Update core function** in `src/utils/pnlCalculator.js`
2. **Update database trigger** to match
3. **Run validation tests** to ensure consistency
4. **Update documentation**

### Debugging P&L Issues

1. **Check database P&L value** first
2. **Use fallback validation** to compare calculated vs database
3. **Check contract multiplier** for the symbol
4. **Verify trade data** (entry/exit prices, quantity, type)

## Migration Notes

### For Existing Data
- Run the SQL script to recalculate existing trades
- Backup database before running updates
- Verify sample calculations after migration

### For Development
- Remove any custom P&L calculation code
- Use centralized utilities for all new features
- Test against both database and calculated values

## Benefits

✅ **Single Source of Truth** - Database is authoritative  
✅ **Consistent Calculations** - Same logic everywhere  
✅ **Easy Maintenance** - Changes in one place  
✅ **Better Testing** - Centralized validation  
✅ **Correct Multipliers** - NQ now properly uses 20x  
✅ **Fallback Safety** - Graceful handling of missing data  
✅ **Performance** - Database calculations are faster  
✅ **Audit Trail** - Clear source of P&L values
