import 'package:flutter/material.dart';
import 'dart:math' as math;

class GoalGaugesCard extends StatelessWidget {
  final double overallProgress;
  final double profitProgress;
  final double guardProgress;
  final double focusProgress;

  const GoalGaugesCard({
    super.key,
    this.overallProgress = 75,
    this.profitProgress = 80,
    this.guardProgress = 65,
    this.focusProgress = 70,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.white, Color(0xFFF5F7FA)],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF1F2687).withOpacity(0.07),
            offset: const Offset(0, 8),
            blurRadius: 32,
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            'PERFORMANCE DASHBOARD',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Color(0xFF2D3748),
              letterSpacing: 0.5,
            ),
          ),
          const SizedBox(height: 24),
          SizedBox(
            height: 300,
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Background gradient circle
                Container(
                  width: 300,
                  height: 300,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: SweepGradient(
                      colors: [
                        const Color(0xFFFF6B6B).withOpacity(0.1),
                        const Color(0xFFFFD93D).withOpacity(0.1),
                        const Color(0xFF6BCB77).withOpacity(0.1),
                        const Color(0xFF4D96FF).withOpacity(0.1),
                      ],
                      stops: const [0.0, 0.33, 0.66, 1.0],
                      startAngle: math.pi * 1.5,
                      endAngle: math.pi * 3.5,
                    ),
                  ),
                ),
                // Dashed circle
                CustomPaint(
                  size: const Size(300, 300),
                  painter: DashedCirclePainter(),
                ),
                // Main gauge
                CustomPaint(
                  size: const Size(300, 300),
                  painter: GaugePainter(
                    progress: overallProgress,
                    color: const Color(0xFF2D3748),
                  ),
                ),
                // Center value
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '${overallProgress.toInt()}%',
                      style: const TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2D3748),
                      ),
                    ),
                    const Text(
                      'Overall',
                      style: TextStyle(
                        fontSize: 16,
                        color: Color(0xFF718096),
                      ),
                    ),
                  ],
                ),
                // Small gauges
                Positioned(
                  bottom: 40,
                  left: 0,
                  right: 0,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildSmallGauge(
                        'Profit',
                        profitProgress,
                        const Color(0xFFFFD700),
                      ),
                      _buildSmallGauge(
                        'Guard',
                        guardProgress,
                        const Color(0xFFC0C0C0),
                      ),
                      _buildSmallGauge(
                        'Focus',
                        focusProgress,
                        const Color(0xFF4299E1),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSmallGauge(String label, double value, Color color) {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.white,
            border: Border.all(color: color, width: 3),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                offset: const Offset(0, 4),
                blurRadius: 12,
              ),
            ],
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              CustomPaint(
                size: const Size(80, 80),
                painter: SmallGaugePainter(
                  progress: value,
                  color: color,
                ),
              ),
              Positioned(
                bottom: 15,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        offset: const Offset(0, 2),
                        blurRadius: 4,
                      ),
                    ],
                  ),
                  child: Text(
                    '${value.toInt()}%',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2D3748),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label.toUpperCase(),
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: color,
            letterSpacing: 0.5,
          ),
        ),
      ],
    );
  }
}

class DashedCirclePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFF2D3748).withOpacity(0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    const dashCount = 50;
    const dashLength = (2 * math.pi) / dashCount;

    for (var i = 0; i < dashCount; i++) {
      final startAngle = i * dashLength;
      final endAngle = startAngle + (dashLength / 2);
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        dashLength / 2,
        false,
        paint,
      );
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius * 0.94),
        startAngle,
        dashLength / 2,
        false,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class GaugePainter extends CustomPainter {
  final double progress;
  final Color color;

  GaugePainter({required this.progress, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4
      ..strokeCap = StrokeCap.round;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Draw hand
    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.rotate((progress / 100) * 2 * math.pi);
    canvas.drawLine(
      const Offset(0, 0),
      Offset(0, -radius + 20),
      paint..strokeWidth = 4,
    );
    canvas.restore();

    // Draw center circle
    canvas.drawCircle(
      center,
      6,
      Paint()..color = color,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class SmallGaugePainter extends CustomPainter {
  final double progress;
  final Color color;

  SmallGaugePainter({required this.progress, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3
      ..strokeCap = StrokeCap.round;

    final center = Offset(size.width / 2, size.height / 2);

    // Draw hand
    canvas.save();
    canvas.translate(center.dx, center.dy);
    canvas.rotate((progress / 100) * 2 * math.pi);
    canvas.drawLine(
      const Offset(0, 0),
      Offset(0, -size.height / 2 + 10),
      paint,
    );
    canvas.restore();

    // Draw dashed circle
    final dashPaint = Paint()
      ..color = color.withOpacity(0.4)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    const dashCount = 20;
    const dashLength = (2 * math.pi) / dashCount;

    for (var i = 0; i < dashCount; i++) {
      final startAngle = i * dashLength;
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: size.width / 2 - 5),
        startAngle,
        dashLength / 2,
        false,
        dashPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
