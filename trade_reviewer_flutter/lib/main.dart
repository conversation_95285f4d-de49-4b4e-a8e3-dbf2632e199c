import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:isar/isar.dart';
import 'package:path_provider/path_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/auth_provider.dart';
import 'package:trade_reviewer_flutter/core/config/router.dart';
import 'package:trade_reviewer_flutter/core/theme/app_theme.dart';
import 'package:trade_reviewer_flutter/core/models/settings.dart';

void main() async {
  try {
    // Ensure Flutter bindings are initialized
    WidgetsFlutterBinding.ensureInitialized();

    // Initialize SharedPreferences
    print('Initializing SharedPreferences...');
    final prefs = await SharedPreferences.getInstance();
    print('SharedPreferences initialized successfully');

    // Initialize Isar
    print('Initializing Isar...');
    final dir = await getApplicationDocumentsDirectory();
    await Isar.initializeIsarCore(download: true);
    final isar = await Isar.open(
      [SettingsSchema],
      directory: dir.path,
      inspector: true, // Enable inspector in debug mode
    );
    print('Isar initialized successfully');

    // Run the app with providers
    runApp(
      ProviderScope(
        overrides: [
          sharedPreferencesProvider.overrideWithValue(prefs),
          isarProvider.overrideWithValue(isar),
        ],
        child: const MyApp(),
      ),
    );
  } catch (e, stackTrace) {
    print('Error initializing app: $e');
    print('Stack trace: $stackTrace');
    rethrow;
  }
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(routerProvider);
    final authState = ref.watch(authStateProvider);

    return MaterialApp.router(
      title: 'TradeREVIEWER',
      theme: AppTheme.darkTheme,
      routerConfig: router,
      builder: (context, child) {
        if (authState.isLoading) {
          return MaterialApp(
            home: Scaffold(
              body: Center(
                child: CircularProgressIndicator(),
              ),
            ),
          );
        }

        if (authState.error != null) {
          return MaterialApp(
            home: Scaffold(
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text('Error: ${authState.error}'),
                    SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        ref.refresh(authStateProvider);
                      },
                      child: Text('Retry'),
                    ),
                  ],
                ),
              ),
            ),
          );
        }

        return child!;
      },
    );
  }
}
