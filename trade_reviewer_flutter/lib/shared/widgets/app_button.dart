import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/theme/app_theme.dart';

enum AppButtonVariant {
  primary,
  secondary,
  outline,
  text,
  success,
  danger,
}

enum AppButtonSize {
  small,
  medium,
  large,
}

class AppButton extends StatelessWidget {
  final String label;
  final VoidCallback? onPressed;
  final AppButtonVariant variant;
  final AppButtonSize size;
  final IconData? icon;
  final bool isLoading;
  final bool fullWidth;
  final EdgeInsets? padding;

  const AppButton({
    Key? key,
    required this.label,
    this.onPressed,
    this.variant = AppButtonVariant.primary,
    this.size = AppButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.fullWidth = false,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final backgroundColor = _getBackgroundColor();
    final foregroundColor = _getForegroundColor();
    final textStyle = _getTextStyle();
    final buttonPadding = padding ?? _getDefaultPadding();

    Widget buttonChild = Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (isLoading)
          Padding(
            padding: const EdgeInsets.only(right: AppTheme.spacingSM),
            child: SizedBox(
              width: _getLoadingSize(),
              height: _getLoadingSize(),
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(foregroundColor),
              ),
            ),
          )
        else if (icon != null)
          Padding(
            padding: const EdgeInsets.only(right: AppTheme.spacingSM),
            child: Icon(icon, size: _getIconSize()),
          ),
        Text(label, style: textStyle),
      ],
    );

    if (fullWidth) {
      buttonChild = Center(child: buttonChild);
    }

    return SizedBox(
      width: fullWidth ? double.infinity : null,
      child: MaterialButton(
        onPressed: isLoading ? null : onPressed,
        color: backgroundColor,
        disabledColor: backgroundColor.withOpacity(0.6),
        elevation: variant == AppButtonVariant.text ? 0 : AppTheme.elevationSM,
        highlightElevation:
            variant == AppButtonVariant.text ? 0 : AppTheme.elevationMD,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
          side: _getBorderSide(),
        ),
        padding: buttonPadding,
        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        child: buttonChild,
      ),
    );
  }

  Color _getBackgroundColor() {
    switch (variant) {
      case AppButtonVariant.primary:
        return AppColors.primaryBlue;
      case AppButtonVariant.secondary:
        return AppColors.secondarySlate;
      case AppButtonVariant.outline:
      case AppButtonVariant.text:
        return Colors.transparent;
      case AppButtonVariant.success:
        return AppColors.successGreen;
      case AppButtonVariant.danger:
        return AppColors.dangerRed;
    }
  }

  Color _getForegroundColor() {
    switch (variant) {
      case AppButtonVariant.outline:
        return AppColors.primaryBlue;
      case AppButtonVariant.text:
        return AppColors.primaryBlue;
      default:
        return Colors.white;
    }
  }

  TextStyle _getTextStyle() {
    final baseStyle = switch (size) {
      AppButtonSize.small => AppTextStyles.buttonSmall,
      AppButtonSize.medium => AppTextStyles.buttonMedium,
      AppButtonSize.large => AppTextStyles.buttonLarge,
    };

    return baseStyle.copyWith(color: _getForegroundColor());
  }

  BorderSide _getBorderSide() {
    if (variant == AppButtonVariant.outline) {
      return BorderSide(color: AppColors.primaryBlue);
    }
    return BorderSide.none;
  }

  EdgeInsets _getDefaultPadding() {
    switch (size) {
      case AppButtonSize.small:
        return const EdgeInsets.symmetric(
          horizontal: AppTheme.spacingSM,
          vertical: AppTheme.spacingXS,
        );
      case AppButtonSize.medium:
        return const EdgeInsets.symmetric(
          horizontal: AppTheme.spacingMD,
          vertical: AppTheme.spacingSM,
        );
      case AppButtonSize.large:
        return const EdgeInsets.symmetric(
          horizontal: AppTheme.spacingLG,
          vertical: AppTheme.spacingMD,
        );
    }
  }

  double _getIconSize() {
    switch (size) {
      case AppButtonSize.small:
        return 16;
      case AppButtonSize.medium:
        return 20;
      case AppButtonSize.large:
        return 24;
    }
  }

  double _getLoadingSize() {
    switch (size) {
      case AppButtonSize.small:
        return 14;
      case AppButtonSize.medium:
        return 18;
      case AppButtonSize.large:
        return 22;
    }
  }
}
