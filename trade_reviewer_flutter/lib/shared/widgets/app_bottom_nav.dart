import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/theme/app_text_styles.dart';
import 'package:trade_reviewer_flutter/features/settings/presentation/widgets/settings_bottom_sheet.dart';

class AppBottomNav extends StatelessWidget {
  const AppBottomNav({super.key});

  void _showSettingsBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const SettingsBottomSheet(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.backgroundAlt,
        border: Border(
          top: BorderSide(
            color: AppColors.border,
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: NavigationBar(
          height: 60,
          backgroundColor: AppColors.backgroundAlt,
          surfaceTintColor: Colors.transparent,
          indicatorColor: AppColors.primaryBlue.withOpacity(0.1),
          labelBehavior: NavigationDestinationLabelBehavior.alwaysShow,
          selectedIndex: _calculateSelectedIndex(GoRouterState.of(context)),
          onDestinationSelected: (index) {
            switch (index) {
              case 0:
                context.go('/dashboard');
                break;
              case 1:
                context.go('/trades');
                break;
              case 2:
                context.go('/journal');
                break;
              case 3:
                context.go('/analytics');
                break;
              case 4:
                _showSettingsBottomSheet(context);
                break;
            }
          },
          destinations: const [
            NavigationDestination(
              icon: Icon(Icons.dashboard_outlined,
                  color: AppColors.textSecondary),
              selectedIcon:
                  Icon(Icons.dashboard, color: const Color(0xFF60A5FA)),
              label: 'Dashboard',
            ),
            NavigationDestination(
              icon: Icon(Icons.show_chart, color: AppColors.textSecondary),
              selectedIcon:
                  Icon(Icons.show_chart, color: const Color(0xFF60A5FA)),
              label: 'Trades',
            ),
            NavigationDestination(
              icon: Icon(Icons.edit_note_outlined,
                  color: AppColors.textSecondary),
              selectedIcon:
                  Icon(Icons.edit_note, color: const Color(0xFF60A5FA)),
              label: 'Journal',
            ),
            NavigationDestination(
              icon: Icon(Icons.analytics_outlined,
                  color: AppColors.textSecondary),
              selectedIcon:
                  Icon(Icons.analytics, color: const Color(0xFF60A5FA)),
              label: 'Analytics',
            ),
            NavigationDestination(
              icon:
                  Icon(Icons.settings_outlined, color: AppColors.textSecondary),
              selectedIcon:
                  Icon(Icons.settings, color: const Color(0xFF60A5FA)),
              label: 'Settings',
            ),
          ],
        ),
      ),
    );
  }

  int _calculateSelectedIndex(GoRouterState state) {
    final location = state.matchedLocation;
    if (location.startsWith('/dashboard')) return 0;
    if (location.startsWith('/trades')) return 1;
    if (location.startsWith('/journal')) return 2;
    if (location.startsWith('/analytics')) return 3;
    return 0;
  }
}
