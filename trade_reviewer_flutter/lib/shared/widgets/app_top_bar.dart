import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/theme/app_text_styles.dart';
import 'package:trade_reviewer_flutter/core/theme/app_logo.dart';
import 'package:trade_reviewer_flutter/core/providers/auth_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_flutter/shared/widgets/profile_picture.dart';
import 'package:trade_reviewer_flutter/features/settings/presentation/widgets/settings_bottom_sheet.dart';

class AppTopBar extends ConsumerWidget implements PreferredSizeWidget {
  const AppTopBar({super.key});

  @override
  Size get preferredSize => const Size.fromHeight(64);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userId = ref.watch(currentUserIdProvider);
    final profileService = ref.watch(profileServiceProvider);

    return Container(
      decoration: BoxDecoration(
        color: AppColors.background,
        border: Border(
          bottom: BorderSide(
            color: AppColors.border,
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        bottom: false,
        child: Container(
          height: 64,
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Logo
              GestureDetector(
                onTap: () => context.go('/dashboard'),
                child: Row(
                  children: [
                    const AppLogo(size: 42, showText: false),
                    const SizedBox(width: 12),
                    ShaderMask(
                      shaderCallback: (bounds) => const LinearGradient(
                        colors: [
                          Color(0xFF3B82F6),
                          Color(0xFF60A5FA),
                          Color(0xFF93C5FD),
                        ],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ).createShader(bounds),
                      child: const Text(
                        'TRADEDGE',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w900,
                          color: Colors.white,
                          letterSpacing: -0.5,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Action buttons
              Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.notifications_outlined),
                    iconSize: 22,
                    color: AppColors.textSecondary,
                    splashRadius: 24,
                    onPressed: () {
                      // Handle notifications
                    },
                  ),
                  FutureBuilder(
                    future: profileService.getProfile(userId),
                    builder: (context, snapshot) {
                      print(
                          'AppTopBar - Profile data: ${snapshot.data?.toJson()}');
                      print(
                          'AppTopBar - Profile image URL: ${snapshot.data?.profileImageUrl}');
                      print(
                          'AppTopBar - Connection state: ${snapshot.connectionState}');
                      if (snapshot.hasError) {
                        print('AppTopBar - Error: ${snapshot.error}');
                      }

                      return GestureDetector(
                        onTap: () => context.push('/settings/profile'),
                        child: Container(
                          width: 36,
                          height: 36,
                          margin: const EdgeInsets.only(left: 8),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: AppColors.backgroundAlt,
                            border: Border.all(
                              color: AppColors.border,
                              width: 1,
                            ),
                          ),
                          clipBehavior: Clip.antiAlias,
                          child: ProfilePicture(
                            key: ValueKey(
                                snapshot.data?.profileImageUrl ?? 'no-image'),
                            imageUrl: snapshot.data?.profileImageUrl,
                            size: 36,
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
