import 'package:flutter/cupertino.dart';

/// Custom icons used throughout the app with iOS-style design
class AppIcons {
  static const IconData add = CupertinoIcons.add;
  static const IconData edit = CupertinoIcons.pencil;
  static const IconData delete = CupertinoIcons.delete;
  static const IconData checkCircle = CupertinoIcons.checkmark_circle_fill;
  static const IconData addCircle = CupertinoIcons.add_circled;
  static const IconData refresh = CupertinoIcons.refresh;
  static const IconData analytics = CupertinoIcons.graph_square;
  static const IconData calendar = CupertinoIcons.calendar;
  static const IconData download = CupertinoIcons.cloud_download;
  static const IconData settings = CupertinoIcons.settings;
  static const IconData moreVert = CupertinoIcons.ellipsis_vertical;
  static const IconData sync = CupertinoIcons.arrow_2_circlepath;
  static const IconData account = CupertinoIcons.person_circle;
  static const IconData trading = CupertinoIcons.chart_bar;
  static const IconData goals = CupertinoIcons.flag_fill;
  static const IconData reports = CupertinoIcons.doc_chart;
}
