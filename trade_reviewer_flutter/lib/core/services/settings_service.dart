import 'package:isar/isar.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart' as core;
import '../models/settings.dart';
import 'isar_settings_service.dart';
import '../errors/database_exception.dart';
import '../utils/database_result.dart';
import '../utils/database_logger.dart';

class SettingsService implements core.SettingsService {
  final IsarSettingsService _isarService;
  Settings? _cachedSettings;
  DateTime? _lastSyncTime;
  static const syncThreshold = Duration(minutes: 30);

  SettingsService(Isar isar) : _isarService = IsarSettingsService(isar);

  @override
  Future<core.Settings> initialize(String userId) async {
    try {
      // Try to load existing settings first
      final result = await _isarService.getSettings(userId);

      return result.fold(
        (settings) async {
          if (settings != null) {
            _cachedSettings = settings;
            return _cachedSettings!.toCore();
          }

          // Create new settings if none exist
          final newSettings = Settings.createDefault(userId);
          final saveResult = await _isarService.saveSettings(newSettings);

          return saveResult.fold(
            (_) {
              _cachedSettings = newSettings;
              return _cachedSettings!.toCore();
            },
            (error) => throw error,
          );
        },
        (error) => throw error,
      );
    } catch (e, stackTrace) {
      throw DatabaseException(
        'Failed to initialize settings',
        code: DatabaseErrorCodes.SETTINGS_INIT_ERROR,
        originalError: e,
        stackTrace: stackTrace,
      );
    }
  }

  @override
  Future<core.Settings> getSettings() async {
    if (_cachedSettings == null) {
      throw DatabaseException(
        'Settings not initialized',
        code: DatabaseErrorCodes.SETTINGS_NOT_INITIALIZED,
      );
    }
    return _cachedSettings!.toCore();
  }

  @override
  Future<core.Settings> updateTradeDefaults(
      core.TradeDefaults newDefaults) async {
    try {
      if (_cachedSettings == null) {
        throw DatabaseException(
          'Settings not initialized',
          code: DatabaseErrorCodes.SETTINGS_NOT_INITIALIZED,
        );
      }

      final updatedSettings = _cachedSettings!.copyWith(
        tradeDefaults: TradeDefaults.fromCore(newDefaults),
        updatedAt: DateTime.now(),
      );

      final result = await _isarService.saveSettings(updatedSettings);

      return result.fold(
        (_) {
          _cachedSettings = updatedSettings;
          return _cachedSettings!.toCore();
        },
        (error) => throw error,
      );
    } catch (e, stackTrace) {
      throw DatabaseException(
        'Failed to update trade defaults',
        code: DatabaseErrorCodes.SETTINGS_UPDATE_ERROR,
        originalError: e,
        stackTrace: stackTrace,
      );
    }
  }

  @override
  Future<core.Settings> updateDisplaySettings(
      core.DisplaySettings newDisplay) async {
    try {
      if (_cachedSettings == null) {
        throw DatabaseException(
          'Settings not initialized',
          code: DatabaseErrorCodes.SETTINGS_NOT_INITIALIZED,
        );
      }

      final updatedSettings = _cachedSettings!.copyWith(
        display: DisplaySettings.fromCore(newDisplay),
        updatedAt: DateTime.now(),
      );

      final result = await _isarService.saveSettings(updatedSettings);

      return result.fold(
        (_) {
          _cachedSettings = updatedSettings;
          return _cachedSettings!.toCore();
        },
        (error) => throw error,
      );
    } catch (e, stackTrace) {
      throw DatabaseException(
        'Failed to update display settings',
        code: DatabaseErrorCodes.SETTINGS_UPDATE_ERROR,
        originalError: e,
        stackTrace: stackTrace,
      );
    }
  }

  @override
  Future<core.Settings> addApiKey(core.ApiKey apiKey) async {
    try {
      if (_cachedSettings == null) {
        throw DatabaseException(
          'Settings not initialized',
          code: DatabaseErrorCodes.SETTINGS_NOT_INITIALIZED,
        );
      }

      final currentApiKeys =
          List<ApiKey>.from(_cachedSettings!.accounts.apiKeys);
      currentApiKeys.add(ApiKey.fromCore(apiKey));

      final updatedSettings = _cachedSettings!.copyWith(
        accounts: _cachedSettings!.accounts.copyWith(
          apiKeys: currentApiKeys,
        ),
        updatedAt: DateTime.now(),
      );

      final result = await _isarService.saveSettings(updatedSettings);
      return result.fold(
        (_) {
          _cachedSettings = updatedSettings;
          return _cachedSettings!.toCore();
        },
        (error) => throw error,
      );
    } catch (e, stackTrace) {
      throw DatabaseException(
        'Failed to add API key',
        code: DatabaseErrorCodes.SETTINGS_UPDATE_ERROR,
        originalError: e,
        stackTrace: stackTrace,
      );
    }
  }

  @override
  Future<core.Settings> addTradingAccount(core.TradingAccount account) async {
    try {
      if (_cachedSettings == null) {
        throw DatabaseException(
          'Settings not initialized',
          code: DatabaseErrorCodes.SETTINGS_NOT_INITIALIZED,
        );
      }

      final updatedAccounts =
          List<TradingAccount>.from(_cachedSettings!.accounts.tradingAccounts);
      updatedAccounts.add(TradingAccount.fromCore(account));

      final updatedSettings = _cachedSettings!.copyWith(
        accounts: _cachedSettings!.accounts.copyWith(
          tradingAccounts: updatedAccounts,
        ),
        updatedAt: DateTime.now(),
      );

      final result = await _isarService.saveSettings(updatedSettings);
      return result.fold(
        (_) {
          _cachedSettings = updatedSettings;
          return _cachedSettings!.toCore();
        },
        (error) => throw error,
      );
    } catch (e, stackTrace) {
      throw DatabaseException(
        'Failed to add trading account',
        code: DatabaseErrorCodes.SETTINGS_UPDATE_ERROR,
        originalError: e,
        stackTrace: stackTrace,
      );
    }
  }

  @override
  Future<core.Settings> removeApiKey(String apiKeyId) async {
    try {
      if (_cachedSettings == null) {
        throw DatabaseException(
          'Settings not initialized',
          code: DatabaseErrorCodes.SETTINGS_NOT_INITIALIZED,
        );
      }

      final currentApiKeys =
          List<ApiKey>.from(_cachedSettings!.accounts.apiKeys);
      currentApiKeys.removeWhere((k) => k.id == apiKeyId);

      final updatedSettings = _cachedSettings!.copyWith(
        accounts: _cachedSettings!.accounts.copyWith(
          apiKeys: currentApiKeys,
        ),
        updatedAt: DateTime.now(),
      );

      final result = await _isarService.saveSettings(updatedSettings);
      return result.fold(
        (_) {
          _cachedSettings = updatedSettings;
          return _cachedSettings!.toCore();
        },
        (error) => throw error,
      );
    } catch (e, stackTrace) {
      throw DatabaseException(
        'Failed to remove API key',
        code: DatabaseErrorCodes.SETTINGS_UPDATE_ERROR,
        originalError: e,
        stackTrace: stackTrace,
      );
    }
  }

  @override
  Future<core.Settings> removeTradingAccount(String accountId) async {
    try {
      if (_cachedSettings == null) {
        throw DatabaseException(
          'Settings not initialized',
          code: DatabaseErrorCodes.SETTINGS_NOT_INITIALIZED,
        );
      }

      final updatedAccounts =
          List<TradingAccount>.from(_cachedSettings!.accounts.tradingAccounts);
      updatedAccounts.removeWhere((a) => a.id == accountId);

      final updatedSettings = _cachedSettings!.copyWith(
        accounts: _cachedSettings!.accounts.copyWith(
          tradingAccounts: updatedAccounts,
        ),
        updatedAt: DateTime.now(),
      );

      final result = await _isarService.saveSettings(updatedSettings);
      return result.fold(
        (_) {
          _cachedSettings = updatedSettings;
          return _cachedSettings!.toCore();
        },
        (error) => throw error,
      );
    } catch (e, stackTrace) {
      throw DatabaseException(
        'Failed to remove trading account',
        code: DatabaseErrorCodes.SETTINGS_UPDATE_ERROR,
        originalError: e,
        stackTrace: stackTrace,
      );
    }
  }

  @override
  Future<core.Settings> updateApiKey(core.ApiKey apiKey) async {
    try {
      if (_cachedSettings == null) {
        throw DatabaseException(
          'Settings not initialized',
          code: DatabaseErrorCodes.SETTINGS_NOT_INITIALIZED,
        );
      }

      final currentApiKeys =
          List<ApiKey>.from(_cachedSettings!.accounts.apiKeys);
      final index = currentApiKeys.indexWhere((k) => k.id == apiKey.id);
      if (index == -1) {
        throw DatabaseException(
          'API key not found',
          code: DatabaseErrorCodes.SETTINGS_UPDATE_ERROR,
        );
      }

      currentApiKeys[index] = ApiKey.fromCore(apiKey);

      final updatedSettings = _cachedSettings!.copyWith(
        accounts: _cachedSettings!.accounts.copyWith(
          apiKeys: currentApiKeys,
        ),
        updatedAt: DateTime.now(),
      );

      final result = await _isarService.saveSettings(updatedSettings);
      return result.fold(
        (_) {
          _cachedSettings = updatedSettings;
          return _cachedSettings!.toCore();
        },
        (error) => throw error,
      );
    } catch (e, stackTrace) {
      throw DatabaseException(
        'Failed to update API key',
        code: DatabaseErrorCodes.SETTINGS_UPDATE_ERROR,
        originalError: e,
        stackTrace: stackTrace,
      );
    }
  }

  @override
  Future<core.Settings> updateTradingAccount(
      core.TradingAccount account) async {
    try {
      if (_cachedSettings == null) {
        throw DatabaseException(
          'Settings not initialized',
          code: DatabaseErrorCodes.SETTINGS_NOT_INITIALIZED,
        );
      }

      final updatedAccounts =
          List<TradingAccount>.from(_cachedSettings!.accounts.tradingAccounts);
      final index = updatedAccounts.indexWhere((a) => a.id == account.id);
      if (index == -1) {
        throw DatabaseException(
          'Trading account not found',
          code: DatabaseErrorCodes.SETTINGS_UPDATE_ERROR,
        );
      }

      updatedAccounts[index] = TradingAccount.fromCore(account);

      final updatedSettings = _cachedSettings!.copyWith(
        accounts: _cachedSettings!.accounts.copyWith(
          tradingAccounts: updatedAccounts,
        ),
        updatedAt: DateTime.now(),
      );

      final result = await _isarService.saveSettings(updatedSettings);
      return result.fold(
        (_) {
          _cachedSettings = updatedSettings;
          return _cachedSettings!.toCore();
        },
        (error) => throw error,
      );
    } catch (e, stackTrace) {
      throw DatabaseException(
        'Failed to update trading account',
        code: DatabaseErrorCodes.SETTINGS_UPDATE_ERROR,
        originalError: e,
        stackTrace: stackTrace,
      );
    }
  }

  @override
  Future<core.Settings> updateAccountSync(
      bool autoSync, int syncFrequency) async {
    try {
      if (_cachedSettings == null) {
        throw DatabaseException(
          'Settings not initialized',
          code: DatabaseErrorCodes.SETTINGS_NOT_INITIALIZED,
        );
      }

      final updatedSettings = _cachedSettings!.copyWith(
        accounts: _cachedSettings!.accounts.copyWith(
          autoSync: autoSync,
          syncFrequency: syncFrequency,
        ),
        updatedAt: DateTime.now(),
      );

      final result = await _isarService.saveSettings(updatedSettings);
      return result.fold(
        (_) {
          _cachedSettings = updatedSettings;
          return _cachedSettings!.toCore();
        },
        (error) => throw error,
      );
    } catch (e, stackTrace) {
      throw DatabaseException(
        'Failed to update account sync settings',
        code: DatabaseErrorCodes.SETTINGS_UPDATE_ERROR,
        originalError: e,
        stackTrace: stackTrace,
      );
    }
  }

  @override
  Future<core.Settings> updateBacktestSettings(
      core.BacktestSettings newBacktest) async {
    try {
      if (_cachedSettings == null) {
        throw DatabaseException(
          'Settings not initialized',
          code: DatabaseErrorCodes.SETTINGS_NOT_INITIALIZED,
        );
      }

      final updatedSettings = _cachedSettings!.copyWith(
        backtest: BacktestSettings.fromCore(newBacktest),
        updatedAt: DateTime.now(),
      );

      final result = await _isarService.saveSettings(updatedSettings);
      return result.fold(
        (_) {
          _cachedSettings = updatedSettings;
          return _cachedSettings!.toCore();
        },
        (error) => throw error,
      );
    } catch (e, stackTrace) {
      throw DatabaseException(
        'Failed to update backtest settings',
        code: DatabaseErrorCodes.SETTINGS_UPDATE_ERROR,
        originalError: e,
        stackTrace: stackTrace,
      );
    }
  }

  @override
  Future<core.Settings> updateGoalSettings(core.GoalSettings goals) async {
    try {
      if (_cachedSettings == null) {
        throw DatabaseException(
          'Settings not initialized',
          code: DatabaseErrorCodes.SETTINGS_NOT_INITIALIZED,
        );
      }

      final updatedSettings = _cachedSettings!.copyWith(
        goals: GoalSettings.fromCore(goals),
        updatedAt: DateTime.now(),
      );

      final result = await _isarService.saveSettings(updatedSettings);
      return result.fold(
        (_) {
          _cachedSettings = updatedSettings;
          return _cachedSettings!.toCore();
        },
        (error) => throw error,
      );
    } catch (e, stackTrace) {
      throw DatabaseException(
        'Failed to update goal settings',
        code: DatabaseErrorCodes.SETTINGS_UPDATE_ERROR,
        originalError: e,
        stackTrace: stackTrace,
      );
    }
  }

  @override
  Future<core.Settings> updateNotificationSettings(
      core.NotificationSettings newSettings) async {
    try {
      if (_cachedSettings == null) {
        throw DatabaseException(
          'Settings not initialized',
          code: DatabaseErrorCodes.SETTINGS_NOT_INITIALIZED,
        );
      }

      final updatedSettings = _cachedSettings!.copyWith(
        notifications: NotificationSettings.fromCore(newSettings),
        updatedAt: DateTime.now(),
      );

      final result = await _isarService.saveSettings(updatedSettings);
      return result.fold(
        (_) {
          _cachedSettings = updatedSettings;
          return _cachedSettings!.toCore();
        },
        (error) => throw error,
      );
    } catch (e, stackTrace) {
      throw DatabaseException(
        'Failed to update notification settings',
        code: DatabaseErrorCodes.SETTINGS_UPDATE_ERROR,
        originalError: e,
        stackTrace: stackTrace,
      );
    }
  }

  @override
  Future<core.Settings> updateAutoBackup(bool enabled) async {
    try {
      if (_cachedSettings == null) {
        throw DatabaseException(
          'Settings not initialized',
          code: DatabaseErrorCodes.SETTINGS_NOT_INITIALIZED,
        );
      }

      final updatedSettings = _cachedSettings!.copyWith(
        autoBackup: enabled,
        updatedAt: DateTime.now(),
      );

      final result = await _isarService.saveSettings(updatedSettings);
      return result.fold(
        (_) {
          _cachedSettings = updatedSettings;
          return _cachedSettings!.toCore();
        },
        (error) => throw error,
      );
    } catch (e, stackTrace) {
      throw DatabaseException(
        'Failed to update auto backup settings',
        code: DatabaseErrorCodes.SETTINGS_UPDATE_ERROR,
        originalError: e,
        stackTrace: stackTrace,
      );
    }
  }
}
