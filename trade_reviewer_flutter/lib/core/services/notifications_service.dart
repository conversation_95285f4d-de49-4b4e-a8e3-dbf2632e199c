import 'package:flutter/material.dart';

class NotificationsService {
  final List<Notification> _notifications = [];
  final StreamController<List<Notification>> _notificationsController =
      StreamController<List<Notification>>();

  Future<Notification?> dismiss(String notificationId, String userId) async {
    try {
      final index = _notifications.indexWhere(
        (n) => n.id == notificationId && n.userId == userId,
      );

      if (index == -1) return null;

      // Get the notification before removing it
      final notification = _notifications[index];

      // Remove the notification instead of just marking it as dismissed
      _notifications.removeAt(index);
      await _save();

      // Add to stream after successful save
      _notificationsController.add(List<Notification>.from(_notifications));
      return notification;
    } catch (e) {
      print('Error dismissing notification: $e');
      return null;
    }
  }

  Future<void> dismissAll(String userId) async {
    try {
      // Remove all notifications for the user instead of marking them as dismissed
      _notifications.removeWhere((n) => n.userId == userId);

      await _save();
      // Add to stream after successful save
      _notificationsController.add(List<Notification>.from(_notifications));
    } catch (e) {
      print('Error dismissing all notifications: $e');
    }
  }

  Future<void> _save() async {
    // Implementation of _save method
  }
}
