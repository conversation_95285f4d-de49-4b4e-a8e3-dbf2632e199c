import 'package:isar/isar.dart';
import '../models/settings.dart';
import '../errors/database_exception.dart';
import '../utils/database_result.dart';
import '../utils/database_logger.dart';

class IsarSettingsService {
  final Isar _isar;
  final ErrorPolicy _errorPolicy;

  IsarSettingsService(this._isar, {ErrorPolicy? errorPolicy})
      : _errorPolicy = errorPolicy ?? RetryErrorPolicy();

  Future<DatabaseResult<Settings?>> getSettings(String userId) async {
    return _errorPolicy.handle(
      () => _isar.settings.filter().userIdEqualTo(userId).findFirst(),
      'getSettings',
      errorCode: DatabaseErrorCodes.SETTINGS_FETCH_ERROR,
    );
  }

  Future<DatabaseResult<bool>> saveSettings(Settings settings) async {
    return _errorPolicy.handle(
      () async {
        await _isar.writeTxn(() async {
          await _isar.settings.put(settings);
        });
        return true;
      },
      'saveSettings',
      errorCode: DatabaseErrorCodes.SETTINGS_SAVE_ERROR,
    );
  }

  Future<DatabaseResult<bool>> deleteSettings(String userId) async {
    return _errorPolicy.handle(
      () async {
        final success = await _isar.writeTxn(() async {
          return await _isar.settings
              .filter()
              .userIdEqualTo(userId)
              .deleteFirst();
        });
        return success;
      },
      'deleteSettings',
      errorCode: DatabaseErrorCodes.SETTINGS_DELETE_ERROR,
    );
  }

  Future<DatabaseResult<bool>> updateSettings(Settings settings) async {
    return _errorPolicy.handle(
      () async {
        await _isar.writeTxn(() async {
          await _isar.settings.put(settings);
        });
        return true;
      },
      'updateSettings',
      errorCode: DatabaseErrorCodes.SETTINGS_UPDATE_ERROR,
    );
  }
}
