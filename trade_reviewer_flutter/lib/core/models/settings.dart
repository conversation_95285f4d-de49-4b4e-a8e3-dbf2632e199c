import 'package:isar/isar.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart' as core;

part 'settings.g.dart';

// Isar enum representations
enum ChartTimeframe { m1, m5, m15, m30, h1, h4, d1, w1, mn1 }

enum RiskCalculationType { percentage, fixed }

enum AccountStatus { active, inactive, syncing, error }

enum ApiKeyStatus { active, inactive, expired, revoked }

enum NotificationChannel { inApp, push, email, sms }

enum NotificationType {
  tradeAlert,
  priceAlert,
  goalAchieved,
  riskWarning,
  systemUpdate,
  backupReminder,
  journalReminder,
  tradeStats
}

enum ReminderFrequency { daily, weekly, monthly, never }

enum NotificationPriority { low, medium, high }

enum PnLDisplayMode { currency, percentage, both }

@collection
class Settings {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  final String userId;

  final TradeDefaults tradeDefaults;
  final DisplaySettings display;
  final BacktestSettings backtest;
  final AccountSettings accounts;
  final GoalSettings goals;
  final NotificationSettings notifications;

  final bool autoSync;
  final bool autoBackup;
  final int backupFrequency;
  final int updatedAtMillis;

  Settings({
    required this.userId,
    required this.tradeDefaults,
    required this.display,
    required this.backtest,
    required this.accounts,
    required this.goals,
    required this.notifications,
    required this.autoSync,
    required this.autoBackup,
    required this.backupFrequency,
    required DateTime updatedAt,
  }) : updatedAtMillis = updatedAt.millisecondsSinceEpoch;

  DateTime get updatedAt =>
      DateTime.fromMillisecondsSinceEpoch(updatedAtMillis);

  factory Settings.fromCore(core.Settings coreSettings) {
    return Settings(
      userId: coreSettings.userId,
      tradeDefaults: TradeDefaults.fromCore(coreSettings.tradeDefaults),
      display: DisplaySettings.fromCore(coreSettings.display),
      backtest: BacktestSettings.fromCore(coreSettings.backtest),
      accounts: AccountSettings.fromCore(coreSettings.accounts),
      goals: GoalSettings.fromCore(coreSettings.goals),
      notifications: NotificationSettings.fromCore(coreSettings.notifications),
      autoSync: coreSettings.autoSync,
      autoBackup: coreSettings.autoBackup,
      backupFrequency: coreSettings.backupFrequency,
      updatedAt: coreSettings.updatedAt,
    );
  }

  core.Settings toCore() {
    return core.Settings(
      userId: userId,
      tradeDefaults: tradeDefaults.toCore(),
      display: display.toCore(),
      backtest: backtest.toCore(),
      accounts: accounts.toCore(),
      goals: goals.toCore(),
      notifications: notifications.toCore(),
      autoSync: autoSync,
      autoBackup: autoBackup,
      backupFrequency: backupFrequency,
      updatedAt: updatedAt,
    );
  }

  Settings copyWith({
    String? userId,
    TradeDefaults? tradeDefaults,
    DisplaySettings? display,
    BacktestSettings? backtest,
    AccountSettings? accounts,
    GoalSettings? goals,
    NotificationSettings? notifications,
    bool? autoSync,
    bool? autoBackup,
    int? backupFrequency,
    DateTime? updatedAt,
  }) {
    final settings = Settings(
      userId: userId ?? this.userId,
      tradeDefaults: tradeDefaults ?? this.tradeDefaults,
      display: display ?? this.display,
      backtest: backtest ?? this.backtest,
      accounts: accounts ?? this.accounts,
      goals: goals ?? this.goals,
      notifications: notifications ?? this.notifications,
      autoSync: autoSync ?? this.autoSync,
      autoBackup: autoBackup ?? this.autoBackup,
      backupFrequency: backupFrequency ?? this.backupFrequency,
      updatedAt: updatedAt ?? this.updatedAt,
    );
    settings.id = this.id;
    return settings;
  }

  factory Settings.createDefault(String userId) {
    return Settings(
      userId: userId,
      tradeDefaults: TradeDefaults(
        defaultRisk: 1.0,
        defaultPosition: 100.0,
        defaultTimeframe: 4,
        riskCalculationType: 0,
        defaultCommission: 0.0,
        defaultFees: 0.0,
      ),
      display: DisplaySettings(
        theme: 'system',
        pnlDisplayMode: 0,
        showRunningPnL: true,
        showEquityCurve: true,
        showTradeStatistics: true,
        compactMode: false,
        dateFormat: 'yyyy-MM-dd',
        timeFormat: 'HH:mm:ss',
        timezone: 'UTC',
        goalNotificationsEnabled: true,
        weeklyReportsEnabled: true,
        monthlyReportsEnabled: true,
        tradeAlertsEnabled: true,
        marketNewsEnabled: true,
        priceAlertsEnabled: true,
        journalRemindersEnabled: true,
        emailNotificationsEnabled: true,
        pushNotificationsEnabled: true,
        smsNotificationsEnabled: false,
      ),
      backtest: BacktestSettings(
        initialCapital: 100000.0,
        useFixedPosition: false,
        maxRiskPerTrade: 2.0,
        maxOpenTrades: 5,
        includeFees: true,
        includeSlippage: true,
        slippageAmount: 0.1,
      ),
      accounts: AccountSettings(
        tradingAccounts: [],
        apiKeys: [],
        autoSync: true,
        syncFrequency: 7,
      ),
      goals: GoalSettings(
        activeMetrics: {},
        metricGoals: {},
      ),
      notifications: NotificationSettings(
        enabled: true,
        soundEnabled: true,
        hapticEnabled: true,
        channels: [0, 1, 2],
        enabledTypes: [0, 1, 2, 3, 4, 5, 6, 7],
        quietHoursStart: "21:00",
        quietHoursEnd: "09:00",
        journalReminderFrequency: 0,
        defaultPriority: 1,
      ),
      autoSync: true,
      autoBackup: true,
      backupFrequency: 7,
      updatedAt: DateTime.now(),
    );
  }
}

@embedded
class TradeDefaults {
  final double defaultRisk;
  final double defaultPosition;
  final int defaultTimeframe; // ChartTimeframe enum
  final int riskCalculationType; // RiskCalculationType enum
  final double defaultCommission;
  final double defaultFees;

  TradeDefaults({
    this.defaultRisk = 1.0,
    this.defaultPosition = 100.0,
    this.defaultTimeframe = 4, // h1
    this.riskCalculationType = 0, // percentage
    this.defaultCommission = 0.0,
    this.defaultFees = 0.0,
  });

  factory TradeDefaults.fromCore(core.TradeDefaults defaults) {
    return TradeDefaults(
      defaultRisk: defaults.defaultRisk,
      defaultPosition: defaults.defaultPosition,
      defaultTimeframe: defaults.defaultTimeframe.index,
      riskCalculationType: defaults.riskCalculationType.index,
      defaultCommission: defaults.defaultCommission,
      defaultFees: defaults.defaultFees,
    );
  }

  core.TradeDefaults toCore() {
    return core.TradeDefaults(
      defaultRisk: defaultRisk,
      defaultPosition: defaultPosition,
      defaultTimeframe: core.ChartTimeframe.values[defaultTimeframe],
      riskCalculationType: core.RiskCalculationType.values[riskCalculationType],
      defaultCommission: defaultCommission,
      defaultFees: defaultFees,
    );
  }
}

@embedded
class DisplaySettings {
  final String theme;
  final int pnlDisplayMode; // PnLDisplayMode enum
  final bool showRunningPnL;
  final bool showEquityCurve;
  final bool showTradeStatistics;
  final bool compactMode;
  final String dateFormat;
  final String timeFormat;
  final String timezone;
  final bool goalNotificationsEnabled;
  final bool weeklyReportsEnabled;
  final bool monthlyReportsEnabled;
  final bool tradeAlertsEnabled;
  final bool marketNewsEnabled;
  final bool priceAlertsEnabled;
  final bool journalRemindersEnabled;
  final bool emailNotificationsEnabled;
  final bool pushNotificationsEnabled;
  final bool smsNotificationsEnabled;

  DisplaySettings({
    this.theme = 'system',
    this.pnlDisplayMode = 0, // currency
    this.showRunningPnL = true,
    this.showEquityCurve = true,
    this.showTradeStatistics = true,
    this.compactMode = false,
    this.dateFormat = 'yyyy-MM-dd',
    this.timeFormat = 'HH:mm:ss',
    this.timezone = 'UTC',
    this.goalNotificationsEnabled = true,
    this.weeklyReportsEnabled = true,
    this.monthlyReportsEnabled = true,
    this.tradeAlertsEnabled = true,
    this.marketNewsEnabled = true,
    this.priceAlertsEnabled = true,
    this.journalRemindersEnabled = true,
    this.emailNotificationsEnabled = true,
    this.pushNotificationsEnabled = true,
    this.smsNotificationsEnabled = false,
  });

  factory DisplaySettings.fromCore(core.DisplaySettings settings) {
    return DisplaySettings(
      theme: settings.theme,
      pnlDisplayMode: settings.pnlDisplayMode.index,
      showRunningPnL: settings.showRunningPnL,
      showEquityCurve: settings.showEquityCurve,
      showTradeStatistics: settings.showTradeStatistics,
      compactMode: settings.compactMode,
      dateFormat: settings.dateFormat,
      timeFormat: settings.timeFormat,
      timezone: settings.timezone,
      goalNotificationsEnabled: settings.goalNotificationsEnabled,
      weeklyReportsEnabled: settings.weeklyReportsEnabled,
      monthlyReportsEnabled: settings.monthlyReportsEnabled,
      tradeAlertsEnabled: settings.tradeAlertsEnabled,
      marketNewsEnabled: settings.marketNewsEnabled,
      priceAlertsEnabled: settings.priceAlertsEnabled,
      journalRemindersEnabled: settings.journalRemindersEnabled,
      emailNotificationsEnabled: settings.emailNotificationsEnabled,
      pushNotificationsEnabled: settings.pushNotificationsEnabled,
      smsNotificationsEnabled: settings.smsNotificationsEnabled,
    );
  }

  core.DisplaySettings toCore() {
    return core.DisplaySettings(
      theme: theme,
      pnlDisplayMode: core.PnLDisplayMode.values[pnlDisplayMode],
      showRunningPnL: showRunningPnL,
      showEquityCurve: showEquityCurve,
      showTradeStatistics: showTradeStatistics,
      compactMode: compactMode,
      dateFormat: dateFormat,
      timeFormat: timeFormat,
      timezone: timezone,
      goalNotificationsEnabled: goalNotificationsEnabled,
      weeklyReportsEnabled: weeklyReportsEnabled,
      monthlyReportsEnabled: monthlyReportsEnabled,
      tradeAlertsEnabled: tradeAlertsEnabled,
      marketNewsEnabled: marketNewsEnabled,
      priceAlertsEnabled: priceAlertsEnabled,
      journalRemindersEnabled: journalRemindersEnabled,
      emailNotificationsEnabled: emailNotificationsEnabled,
      pushNotificationsEnabled: pushNotificationsEnabled,
      smsNotificationsEnabled: smsNotificationsEnabled,
    );
  }
}

@embedded
class BacktestSettings {
  final double initialCapital;
  final bool useFixedPosition;
  final double maxRiskPerTrade;
  final int maxOpenTrades;
  final bool includeFees;
  final bool includeSlippage;
  final double slippageAmount;

  BacktestSettings({
    this.initialCapital = 100000.0,
    this.useFixedPosition = false,
    this.maxRiskPerTrade = 2.0,
    this.maxOpenTrades = 5,
    this.includeFees = true,
    this.includeSlippage = true,
    this.slippageAmount = 0.1,
  });

  factory BacktestSettings.fromCore(core.BacktestSettings settings) {
    return BacktestSettings(
      initialCapital: settings.initialCapital,
      useFixedPosition: settings.useFixedPosition,
      maxRiskPerTrade: settings.maxRiskPerTrade,
      maxOpenTrades: settings.maxOpenTrades,
      includeFees: settings.includeFees,
      includeSlippage: settings.includeSlippage,
      slippageAmount: settings.slippageAmount,
    );
  }

  core.BacktestSettings toCore() {
    return core.BacktestSettings(
      initialCapital: initialCapital,
      useFixedPosition: useFixedPosition,
      maxRiskPerTrade: maxRiskPerTrade,
      maxOpenTrades: maxOpenTrades,
      includeFees: includeFees,
      includeSlippage: includeSlippage,
      slippageAmount: slippageAmount,
    );
  }
}

@embedded
class AccountSettings {
  final List<TradingAccount> tradingAccounts;
  final List<ApiKey> apiKeys;
  final bool autoSync;
  final int syncFrequency;

  AccountSettings({
    List<TradingAccount>? tradingAccounts,
    List<ApiKey>? apiKeys,
    bool? autoSync,
    int? syncFrequency,
  })  : tradingAccounts = tradingAccounts ?? [],
        apiKeys = apiKeys ?? [],
        autoSync = autoSync ?? true,
        syncFrequency = syncFrequency ?? 7;

  AccountSettings copyWith({
    List<TradingAccount>? tradingAccounts,
    List<ApiKey>? apiKeys,
    bool? autoSync,
    int? syncFrequency,
  }) {
    return AccountSettings(
      tradingAccounts: tradingAccounts ?? this.tradingAccounts,
      apiKeys: apiKeys ?? this.apiKeys,
      autoSync: autoSync ?? this.autoSync,
      syncFrequency: syncFrequency ?? this.syncFrequency,
    );
  }

  factory AccountSettings.fromCore(core.AccountSettings settings) {
    return AccountSettings(
      tradingAccounts: settings.tradingAccounts
          .map((a) => TradingAccount.fromCore(a))
          .toList(),
      apiKeys: settings.apiKeys.map((k) => ApiKey.fromCore(k)).toList(),
      autoSync: settings.autoSync,
      syncFrequency: settings.syncFrequency,
    );
  }

  core.AccountSettings toCore() {
    return core.AccountSettings(
      tradingAccounts: tradingAccounts.map((a) => a.toCore()).toList(),
      apiKeys: apiKeys.map((k) => k.toCore()).toList(),
      autoSync: autoSync,
      syncFrequency: syncFrequency,
    );
  }
}

@embedded
class TradingAccount {
  final String id;
  final String name;
  final String provider;
  @enumerated
  final int status; // Represents AccountStatus
  final int lastSyncedMillis;
  @ignore
  final Map<String, String> credentials;
  final double initialCapital;
  final bool isActive;

  TradingAccount({
    this.id = '',
    this.name = '',
    this.provider = '',
    this.status = 0,
    this.lastSyncedMillis = 0,
    this.credentials = const {},
    this.initialCapital = 0.0,
    this.isActive = true,
  });

  DateTime get lastSynced =>
      DateTime.fromMillisecondsSinceEpoch(lastSyncedMillis);

  factory TradingAccount.fromCore(core.TradingAccount account) {
    return TradingAccount(
      id: account.id,
      name: account.name,
      provider: account.provider,
      status: account.status.index,
      lastSyncedMillis: account.lastSynced.millisecondsSinceEpoch,
      credentials: Map<String, String>.from(account.credentials),
      initialCapital: account.initialCapital,
      isActive: account.isActive,
    );
  }

  core.TradingAccount toCore() {
    return core.TradingAccount(
      id: id,
      name: name,
      provider: provider,
      status: core.AccountStatus.values[status],
      lastSynced: lastSynced,
      credentials: credentials,
      initialCapital: initialCapital,
      isActive: isActive,
    );
  }
}

@embedded
class ApiKey {
  final String id;
  final String name;
  final String key;
  final String? secret;
  @enumerated
  final int status; // Represents ApiKeyStatus
  final int? expiryDateMillis;

  ApiKey({
    this.id = '',
    this.name = '',
    this.key = '',
    this.secret,
    this.status = 0,
    this.expiryDateMillis,
  });

  DateTime? get expiryDate => expiryDateMillis != null
      ? DateTime.fromMillisecondsSinceEpoch(expiryDateMillis!)
      : null;

  factory ApiKey.fromCore(core.ApiKey apiKey) {
    return ApiKey(
      id: apiKey.id,
      name: apiKey.name,
      key: apiKey.key,
      secret: apiKey.secret,
      status: apiKey.status.index,
      expiryDateMillis: apiKey.expiryDate?.millisecondsSinceEpoch,
    );
  }

  core.ApiKey toCore() {
    return core.ApiKey(
      id: id,
      name: name,
      key: key,
      secret: secret,
      status: core.ApiKeyStatus.values[status],
      expiryDate: expiryDate,
    );
  }
}

@embedded
class GoalSettings {
  @ignore
  final Map<String, bool> activeMetrics;
  @ignore
  final Map<String, double> metricGoals;

  GoalSettings({
    this.activeMetrics = const {},
    this.metricGoals = const {},
  });

  factory GoalSettings.fromCore(core.GoalSettings settings) {
    return GoalSettings(
      activeMetrics: Map<String, bool>.from(settings.activeMetrics),
      metricGoals: Map<String, double>.from(settings.metricGoals),
    );
  }

  core.GoalSettings toCore() {
    return core.GoalSettings(
      activeMetrics: activeMetrics,
      metricGoals: metricGoals,
    );
  }
}

@embedded
class NotificationSettings {
  final bool enabled;
  final bool soundEnabled;
  final bool hapticEnabled;
  final List<int> channels; // List of NotificationChannel enum indices
  final List<int> enabledTypes; // List of NotificationType enum indices
  final String quietHoursStart;
  final String quietHoursEnd;
  final int journalReminderFrequency; // ReminderFrequency enum
  final int defaultPriority; // NotificationPriority enum

  NotificationSettings({
    this.enabled = true,
    this.soundEnabled = true,
    this.hapticEnabled = true,
    this.channels = const [0, 1, 2], // inApp, push, email
    this.enabledTypes = const [0, 1, 2, 3, 4, 5, 6, 7], // all types
    this.quietHoursStart = "21:00",
    this.quietHoursEnd = "09:00",
    this.journalReminderFrequency = 0, // daily
    this.defaultPriority = 1, // medium
  });

  factory NotificationSettings.fromCore(core.NotificationSettings settings) {
    return NotificationSettings(
      enabled: settings.enabled,
      soundEnabled: settings.soundEnabled,
      hapticEnabled: settings.hapticEnabled,
      channels: settings.channels.map((c) => c.index).toList(),
      enabledTypes: settings.enabledTypes.map((t) => t.index).toList(),
      quietHoursStart: settings.quietHoursStart,
      quietHoursEnd: settings.quietHoursEnd,
      journalReminderFrequency: settings.journalReminderFrequency.index,
      defaultPriority: settings.defaultPriority.index,
    );
  }

  core.NotificationSettings toCore() {
    return core.NotificationSettings(
      enabled: enabled,
      soundEnabled: soundEnabled,
      hapticEnabled: hapticEnabled,
      channels:
          channels.map((i) => core.NotificationChannel.values[i]).toList(),
      enabledTypes:
          enabledTypes.map((i) => core.NotificationType.values[i]).toList(),
      quietHoursStart: quietHoursStart,
      quietHoursEnd: quietHoursEnd,
      journalReminderFrequency:
          core.ReminderFrequency.values[journalReminderFrequency],
      defaultPriority: core.NotificationPriority.values[defaultPriority],
    );
  }
}
