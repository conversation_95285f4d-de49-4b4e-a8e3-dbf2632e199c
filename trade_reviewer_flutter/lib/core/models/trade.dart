import 'package:trade_reviewer_core/trade_reviewer_core.dart' as core;
import '../utils/contract_multipliers.dart';

enum TradeType {
  LONG,
  SHORT,
}

enum TradeStatus {
  OPEN,
  CLOSED,
  CANCELLED,
}

class Trade {
  final String? id;
  final String symbol;
  final TradeType type;
  final TradeStatus status;
  final double entryPrice;
  final double? exitPrice;
  final double? stopLoss;
  final double? takeProfit;
  final double quantity;
  final double fees;
  final List<String> tags;
  final String? notes;
  final DateTime entryDate;
  final DateTime? exitDate;
  final String accountId;
  final String userId;
  final DateTime updatedAt;
  final double? predefinedPnL;

  Trade({
    this.id,
    required this.symbol,
    required this.type,
    required this.status,
    required this.entryPrice,
    this.exitPrice,
    this.stopLoss,
    this.takeProfit,
    required this.quantity,
    this.fees = 0.0,
    this.tags = const [],
    this.notes,
    required this.entryDate,
    this.exitDate,
    required this.accountId,
    required this.userId,
    required this.updatedAt,
    this.predefinedPnL,
  });

  factory Trade.fromCore(core.Trade trade) {
    return Trade(
      id: trade.id,
      symbol: trade.symbol,
      type:
          trade.type == core.TradeType.long ? TradeType.LONG : TradeType.SHORT,
      status: trade.status == core.TradeStatus.closed
          ? TradeStatus.CLOSED
          : TradeStatus.OPEN,
      entryPrice: trade.entryPrice,
      exitPrice: trade.exitPrice,
      stopLoss: trade.stopLoss,
      takeProfit: trade.takeProfit,
      quantity: trade.quantity,
      fees: trade.fees ?? 0.0,
      tags: trade.tags ?? [],
      notes: trade.notes,
      entryDate: trade.entryDate,
      exitDate: trade.exitDate,
      accountId: trade.accountId,
      userId: trade.userId,
      updatedAt: trade.updatedAt,
      predefinedPnL: (trade as dynamic).predefinedPnL,
    );
  }

  core.Trade toCore() {
    return core.Trade(
      id: id ?? '',
      symbol: symbol,
      type: type == TradeType.LONG ? core.TradeType.long : core.TradeType.short,
      status: status == TradeStatus.CLOSED
          ? core.TradeStatus.closed
          : core.TradeStatus.open,
      entryPrice: entryPrice,
      exitPrice: exitPrice,
      stopLoss: stopLoss,
      takeProfit: takeProfit,
      quantity: quantity,
      fees: fees,
      tags: tags,
      notes: notes,
      entryDate: entryDate,
      exitDate: exitDate,
      accountId: accountId,
      userId: userId,
      updatedAt: updatedAt,
      predefinedPnL: predefinedPnL,
    );
  }

  double calculatePnL() {
    if (predefinedPnL != null) return predefinedPnL!;
    if (exitPrice == null) return 0;
    final grossPnL = (exitPrice! - entryPrice) * quantity;
    return type == TradeType.LONG ? grossPnL - fees : -grossPnL - fees;
  }

  double calculateUnrealizedPnL(double currentPrice) {
    if (status == TradeStatus.CLOSED) return 0;
    final grossPnL = (currentPrice - entryPrice) * quantity;
    return type == TradeType.LONG ? grossPnL - fees : -grossPnL - fees;
  }

  double? calculateRRR() {
    if (stopLoss == null || takeProfit == null) return null;
    final risk = (entryPrice - stopLoss!).abs();
    if (risk == 0) return null; // Avoid division by zero
    final reward = (takeProfit! - entryPrice).abs();
    return reward / risk;
  }

  double calculateRisk() {
    if (stopLoss == null) return 0;
    final riskPerUnit = (entryPrice - stopLoss!).abs();
    return riskPerUnit * quantity;
  }

  double calculateReward() {
    if (takeProfit == null) return 0;
    final rewardPerUnit = (takeProfit! - entryPrice).abs();
    return rewardPerUnit * quantity;
  }
}
