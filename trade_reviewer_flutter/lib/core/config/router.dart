import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../features/analytics/presentation/analytics_screen.dart';
import '../../features/dashboard/presentation/dashboard_screen.dart';
import '../../features/journal/presentation/journal_screen.dart';
import '../../features/trades/presentation/screens/trades_screen.dart';
import '../../features/trades/presentation/screens/broker_import_screen.dart';
import '../../features/trades/presentation/screens/add_trade_screen.dart';
import '../../features/trades/presentation/screens/trade_details_screen.dart';
import '../../features/settings/presentation/screens/profile_settings_screen.dart';
import '../../features/settings/presentation/screens/general_settings_screen.dart';
import '../../features/settings/presentation/screens/accounts_settings_screen.dart';
import '../../features/settings/presentation/screens/notifications_settings_screen.dart';
import '../../features/settings/presentation/screens/security_settings_screen.dart';
import '../../features/settings/presentation/screens/privacy_settings_screen.dart';
import '../../features/settings/presentation/screens/data_management_screen.dart';
import '../../features/settings/presentation/screens/goals_settings_screen.dart';
import '../../features/goals/presentation/screens/goal_calendar_screen.dart';
import '../../features/auth/presentation/screens/login_screen.dart';
import '../../shared/widgets/app_bottom_nav.dart';
import '../../shared/widgets/app_top_bar.dart';
import '../theme/app_colors.dart';
import '../providers/auth_provider.dart';
import '../models/trade.dart';

part 'router.g.dart';

@riverpod
GoRouter router(RouterRef ref) {
  final rootNavigatorKey = GlobalKey<NavigatorState>();
  final shellNavigatorKey = GlobalKey<NavigatorState>();
  final userId = ref.watch(currentUserIdProvider);

  return GoRouter(
    navigatorKey: rootNavigatorKey,
    initialLocation: '/dashboard',
    routes: [
      // Auth screens
      GoRoute(
        parentNavigatorKey: rootNavigatorKey,
        path: '/login',
        builder: (context, state) => const LoginScreen(),
      ),
      ShellRoute(
        navigatorKey: shellNavigatorKey,
        builder: (context, state, child) {
          return Scaffold(
            backgroundColor: AppColors.background,
            appBar: const AppTopBar(),
            body: child,
            bottomNavigationBar: const AppBottomNav(),
          );
        },
        routes: [
          GoRoute(
            path: '/dashboard',
            pageBuilder: (context, state) => NoTransitionPage(
              child: const DashboardScreen(),
            ),
          ),
          GoRoute(
            path: '/trades',
            pageBuilder: (context, state) => NoTransitionPage(
              child: const TradesScreen(),
            ),
          ),
          GoRoute(
            path: '/journal',
            pageBuilder: (context, state) => NoTransitionPage(
              child: const JournalScreen(),
            ),
          ),
          GoRoute(
            path: '/analytics',
            pageBuilder: (context, state) => NoTransitionPage(
              child: const AnalyticsScreen(),
            ),
          ),
        ],
      ),
      // Settings screens
      GoRoute(
        parentNavigatorKey: rootNavigatorKey,
        path: '/settings/profile',
        builder: (context, state) => ProfileSettingsScreen(userId: userId),
      ),
      GoRoute(
        parentNavigatorKey: rootNavigatorKey,
        path: '/settings/general',
        builder: (context, state) => const GeneralSettingsScreen(),
      ),
      GoRoute(
        parentNavigatorKey: rootNavigatorKey,
        path: '/settings/accounts',
        builder: (context, state) => const AccountsSettingsScreen(),
      ),
      GoRoute(
        parentNavigatorKey: rootNavigatorKey,
        path: '/settings/notifications',
        builder: (context, state) => const NotificationsSettingsScreen(),
      ),
      GoRoute(
        parentNavigatorKey: rootNavigatorKey,
        path: '/settings/security',
        builder: (context, state) => const SecuritySettingsScreen(),
      ),
      GoRoute(
        parentNavigatorKey: rootNavigatorKey,
        path: '/settings/privacy',
        builder: (context, state) => const PrivacySettingsScreen(),
      ),
      GoRoute(
        parentNavigatorKey: rootNavigatorKey,
        path: '/settings/data',
        builder: (context, state) => const DataManagementScreen(),
      ),
      GoRoute(
        parentNavigatorKey: rootNavigatorKey,
        path: '/settings/goals',
        name: 'settings_goals',
        builder: (context, state) => const GoalsSettingsScreen(),
      ),
      // Goals screens
      GoRoute(
        parentNavigatorKey: rootNavigatorKey,
        path: '/goals/calendar',
        builder: (context, state) => const GoalCalendarScreen(),
      ),
      // Trades screens
      GoRoute(
        parentNavigatorKey: rootNavigatorKey,
        path: '/trades/import-broker',
        builder: (context, state) => const BrokerImportScreen(),
      ),
      GoRoute(
        parentNavigatorKey: rootNavigatorKey,
        path: '/trades/add',
        builder: (context, state) => const AddTradeScreen(),
      ),
      GoRoute(
        parentNavigatorKey: rootNavigatorKey,
        path: '/trades/:id',
        builder: (context, state) {
          final trade = state.extra as Trade;
          return TradeDetailsScreen(trade: trade);
        },
      ),
    ],
  );
}
