class DatabaseException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;
  final StackTrace? stackTrace;

  DatabaseException(
    this.message, {
    this.code,
    this.originalError,
    this.stackTrace,
  });

  @override
  String toString() {
    final buffer = StringBuffer('DatabaseException: $message');
    if (code != null) buffer.write(' (Code: $code)');
    if (originalError != null) buffer.write('\nOriginal error: $originalError');
    if (stackTrace != null) buffer.write('\n$stackTrace');
    return buffer.toString();
  }
}

class DatabaseErrorCodes {
  // Settings related errors
  static const String SETTINGS_FETCH_ERROR = 'SETTINGS_FETCH_ERROR';
  static const String SETTINGS_SAVE_ERROR = 'SETTINGS_SAVE_ERROR';
  static const String SETTINGS_DELETE_ERROR = 'SETTINGS_DELETE_ERROR';
  static const String SETTINGS_UPDATE_ERROR = 'SETTINGS_UPDATE_ERROR';
  static const String SETTINGS_INIT_ERROR = 'SETTINGS_INIT_ERROR';
  static const String SETTINGS_NOT_INITIALIZED = 'SETTINGS_NOT_INITIALIZED';

  // Trade related errors
  static const String TRADE_FETCH_ERROR = 'TRADE_FETCH_ERROR';
  static const String TRADE_SAVE_ERROR = 'TRADE_SAVE_ERROR';
  static const String TRADE_DELETE_ERROR = 'TRADE_DELETE_ERROR';
  static const String TRADE_UPDATE_ERROR = 'TRADE_UPDATE_ERROR';

  // Goal related errors
  static const String GOAL_FETCH_ERROR = 'GOAL_FETCH_ERROR';
  static const String GOAL_SAVE_ERROR = 'GOAL_SAVE_ERROR';
  static const String GOAL_DELETE_ERROR = 'GOAL_DELETE_ERROR';
  static const String GOAL_UPDATE_ERROR = 'GOAL_UPDATE_ERROR';

  // Database initialization errors
  static const String DB_INIT_ERROR = 'DB_INIT_ERROR';
  static const String DB_UPGRADE_ERROR = 'DB_UPGRADE_ERROR';
  static const String DB_CONNECTION_ERROR = 'DB_CONNECTION_ERROR';
}
