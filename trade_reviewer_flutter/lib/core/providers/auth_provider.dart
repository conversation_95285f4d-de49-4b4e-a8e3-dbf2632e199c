import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'service_provider.dart';

final currentUserIdProvider = StateProvider<String>((ref) {
  // Default user ID for development
  return 'default_user';
});

final authStateProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier(ref);
});

class AuthNotifier extends StateNotifier<AuthState> {
  final Ref _ref;

  AuthNotifier(this._ref) : super(const AuthState.initial()) {
    _initializeAuth();
  }

  Future<void> _initializeAuth() async {
    try {
      state = const AuthState.loading();

      // Initialize services with default user
      final userId = _ref.read(currentUserIdProvider);
      print('AuthNotifier - Initializing with user ID: $userId');

      final settingsService = _ref.read(settingsServiceProvider);
      final profileService = _ref.read(profileServiceProvider);

      // Initialize both services
      try {
        print('AuthNotifier - Initializing services...');
        await Future.wait([
          settingsService.initialize(userId),
          profileService.initialize(
              userId), // Changed to initialize instead of getProfile
        ]);
        print('AuthNotifier - Services initialized successfully');

        state = AuthState.authenticated(userId);
      } catch (e, stackTrace) {
        print('AuthNotifier - Error initializing services: $e');
        print('Stack trace: $stackTrace');

        // Try to recover by initializing services one at a time
        print('AuthNotifier - Attempting recovery...');
        try {
          await settingsService.initialize(userId);
          await profileService.initialize(userId);
          print('AuthNotifier - Recovery successful');
          state = AuthState.authenticated(userId);
        } catch (e2, stackTrace2) {
          print('AuthNotifier - Recovery failed: $e2');
          print('Stack trace: $stackTrace2');
          state = AuthState.error('Failed to initialize services: $e2');
        }
      }
    } catch (e, stackTrace) {
      print('AuthNotifier - Fatal error: $e');
      print('Stack trace: $stackTrace');
      state = AuthState.error('Fatal error: $e');
    }
  }
}

class AuthState {
  final String? userId;
  final bool isLoading;
  final String? error;

  const AuthState._({
    this.userId,
    this.isLoading = false,
    this.error,
  });

  const AuthState.initial() : this._();

  const AuthState.loading() : this._(isLoading: true);

  const AuthState.authenticated(String uid) : this._(userId: uid);

  const AuthState.error(String message) : this._(error: message);
}
