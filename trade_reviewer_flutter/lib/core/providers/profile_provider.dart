import 'package:flutter/foundation.dart';
import 'package:trade_reviewer_flutter/core/services/settings_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ProfileProvider extends ChangeNotifier {
  late final SettingsService settingsService;
  String? _userId;

  ProfileProvider() {
    _initialize();
  }

  String? get userId => _userId;

  Future<void> _initialize() async {
    final prefs = await SharedPreferences.getInstance();
    settingsService = SettingsService(prefs);
    _userId = prefs.getString('userId');
    if (_userId != null) {
      await settingsService.initialize(_userId!);
    }
    notifyListeners();
  }

  Future<void> setUserId(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('userId', userId);
    _userId = userId;
    await settingsService.initialize(userId);
    notifyListeners();
  }

  Future<void> clearUserId() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('userId');
    _userId = null;
    notifyListeners();
  }
}
