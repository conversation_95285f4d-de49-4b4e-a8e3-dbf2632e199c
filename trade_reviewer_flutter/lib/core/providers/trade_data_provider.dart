import 'package:flutter_riverpod/flutter_riverpod.dart';

/// A simple notifier to trigger UI updates when trade data changes
class TradeDataNotifier extends StateNotifier<int> {
  TradeDataNotifier() : super(0);

  void notifyDataChanged() {
    state++; // Increment the state to notify listeners
  }
}

/// Provider that can be watched to rebuild UI when trade data changes
final tradeDataProvider = StateNotifierProvider<TradeDataNotifier, int>((ref) {
  return TradeDataNotifier();
});
