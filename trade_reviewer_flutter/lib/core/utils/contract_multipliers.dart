/// Contract multipliers for futures symbols
class ContractMultipliers {
  static const Map<String, double> _multipliers = {
    'NQ': 20.0,   // Nasdaq-100 E-mini: $20 per point
    'ES': 50.0,   // S&P 500 E-mini: $50 per point
    'YM': 5.0,    // Dow E-mini: $5 per point
    'RTY': 50.0,  // Russell 2000 E-mini: $50 per point
    'CL': 1000.0, // Crude Oil: $1000 per point
    'GC': 100.0,  // Gold: $100 per point
    'SI': 5000.0, // Silver: $5000 per point
  };

  /// Get the contract multiplier for a given symbol
  /// Returns 1.0 for stocks or unknown symbols
  static double getMultiplier(String? symbol) {
    if (symbol == null || symbol.isEmpty) return 1.0;

    final symbolUpper = symbol.toUpperCase();

    // Check for futures symbols
    for (final entry in _multipliers.entries) {
      if (symbolUpper.contains(entry.key)) {
        return entry.value;
      }
    }

    // Default for stocks
    return 1.0;
  }

  /// Check if a symbol is a futures contract
  static bool isFutures(String? symbol) {
    if (symbol == null || symbol.isEmpty) return false;

    final symbolUpper = symbol.toUpperCase();
    return _multipliers.keys.any((key) => symbolUpper.contains(key));
  }
}
