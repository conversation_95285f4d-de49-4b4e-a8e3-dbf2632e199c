import 'package:flutter/foundation.dart';

class DatabaseLogger {
  static void logError(String operation, dynamic error, StackTrace stackTrace) {
    // In debug mode, print to console
    if (kDebugMode) {
      print('Database Error [$operation]: $error\n$stackTrace');
    }

    // TODO: Implement production logging strategy
    // This could integrate with services like Firebase Crashlytics,
    // Sentry, or other logging services
  }

  static void logWarning(String operation, String message) {
    if (kDebugMode) {
      print('Database Warning [$operation]: $message');
    }
    // TODO: Implement production logging strategy
  }

  static void logInfo(String operation, String message) {
    if (kDebugMode) {
      print('Database Info [$operation]: $message');
    }
    // TODO: Implement production logging strategy
  }
}
