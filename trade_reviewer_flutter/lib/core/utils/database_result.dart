import '../errors/database_exception.dart';
import 'database_logger.dart';

class DatabaseResult<T> {
  final T? data;
  final DatabaseException? error;
  final bool isSuccess;

  DatabaseResult.success(this.data)
      : error = null,
        isSuccess = true;

  DatabaseResult.error(this.error)
      : data = null,
        isSuccess = false;

  R fold<R>(
    R Function(T data) onSuccess,
    R Function(DatabaseException error) onError,
  ) {
    return isSuccess ? onSuccess(data!) : onError(error!);
  }

  DatabaseResult<R> map<R>(R Function(T data) transform) {
    return isSuccess
        ? DatabaseResult.success(transform(data!))
        : DatabaseResult.error(error!);
  }
}

abstract class ErrorPolicy {
  Future<DatabaseResult<T>> handle<T>(
    Future<T> Function() operation,
    String operationName, {
    String? errorCode,
  });
}

class RetryErrorPolicy implements ErrorPolicy {
  final int maxRetries;
  final Duration initialDelay;
  final double backoffFactor;

  RetryErrorPolicy({
    this.maxRetries = 3,
    this.initialDelay = const Duration(milliseconds: 100),
    this.backoffFactor = 2.0,
  });

  @override
  Future<DatabaseResult<T>> handle<T>(
    Future<T> Function() operation,
    String operationName, {
    String? errorCode,
  }) async {
    int attempts = 0;
    Duration currentDelay = initialDelay;

    while (attempts < maxRetries) {
      try {
        final result = await operation();
        if (attempts > 0) {
          DatabaseLogger.logInfo(
            operationName,
            'Succeeded after ${attempts + 1} attempts',
          );
        }
        return DatabaseResult.success(result);
      } catch (e, stackTrace) {
        attempts++;
        DatabaseLogger.logError(operationName, e, stackTrace);

        if (attempts == maxRetries) {
          return DatabaseResult.error(
            DatabaseException(
              'Operation failed after $maxRetries attempts',
              code: errorCode,
              originalError: e,
              stackTrace: stackTrace,
            ),
          );
        }

        await Future.delayed(currentDelay);
        currentDelay *= backoffFactor;
      }
    }

    // This should never be reached due to the return in the catch block
    throw StateError('Unexpected state in RetryErrorPolicy');
  }
}

class NoRetryErrorPolicy implements ErrorPolicy {
  @override
  Future<DatabaseResult<T>> handle<T>(
    Future<T> Function() operation,
    String operationName, {
    String? errorCode,
  }) async {
    try {
      final result = await operation();
      return DatabaseResult.success(result);
    } catch (e, stackTrace) {
      DatabaseLogger.logError(operationName, e, stackTrace);
      return DatabaseResult.error(
        DatabaseException(
          'Operation failed',
          code: errorCode,
          originalError: e,
          stackTrace: stackTrace,
        ),
      );
    }
  }
}
