import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/theme/app_text_styles.dart';
import 'package:trade_reviewer_flutter/core/models/trade.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/time_period_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/account_filter_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/trade_data_provider.dart';
import 'package:trade_reviewer_flutter/core/widgets/time_period_filter.dart';
import 'package:intl/intl.dart';

class TradesScreen extends ConsumerStatefulWidget {
  const TradesScreen({super.key});

  @override
  ConsumerState<TradesScreen> createState() => _TradesScreenState();
}

class _TradesScreenState extends ConsumerState<TradesScreen> {
  List<Trade> _trades = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadTrades();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Watch for trade data changes
    final dataVersion = ref.read(tradeDataProvider);
    if (dataVersion > 0) {
      _loadTrades();
    }
  }

  Future<void> _loadTrades() async {
    setState(() => _isLoading = true);
    try {
      final tradeService = ref.read(tradeServiceProvider);
      final settingsService = ref.read(settingsServiceProvider);
      final settings = await settingsService.getSettings();
      final userId = settings.userId;

      final trades = await tradeService.getTrades(userId);
      setState(() {
        _trades = trades.map((t) => Trade.fromCore(t)).toList();
        _trades.sort((a, b) => b.entryDate.compareTo(a.entryDate));
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading trades: $e');
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading trades: $e')),
        );
      }
    }
  }

  List<Trade> _getFilteredTrades() {
    var filtered = List<Trade>.from(_trades);

    // Apply time period filter
    final timePeriod = ref.watch(timePeriodProvider);
    final timePeriodNotifier = ref.read(timePeriodProvider.notifier);
    final startDate = timePeriodNotifier.getStartDate();
    final endDate = timePeriodNotifier.getEndDate();

    if (startDate != null && endDate != null) {
      filtered = filtered.where((trade) {
        return trade.entryDate.isAfter(startDate) &&
            trade.entryDate.isBefore(endDate.add(const Duration(days: 1)));
      }).toList();
    }

    // Apply account filter
    final accountFilter = ref.watch(accountFilterProvider);
    if (accountFilter.selectedAccountId != null) {
      print('Filtering by account: ${accountFilter.selectedAccountId}');
      filtered = filtered
          .where((trade) => trade.accountId == accountFilter.selectedAccountId)
          .toList();
      print('Filtered trades count: ${filtered.length}');
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    // Watch providers to rebuild when they change
    ref.watch(timePeriodProvider);
    ref.watch(accountFilterProvider);

    // Get filtered trades
    final filteredTrades = _getFilteredTrades();

    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Time Period Filter
          Padding(
            padding: const EdgeInsets.all(16),
            child: const TimePeriodFilter(),
          ),
          const SizedBox(height: 8),
          // Trades List
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : filteredTrades.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.search_off_rounded,
                              size: 48,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No trades found',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleMedium
                                  ?.copyWith(
                                    color: Colors.grey[600],
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Try adjusting your filters',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    color: Colors.grey[500],
                                  ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: filteredTrades.length,
                        itemBuilder: (context, index) {
                          final trade = filteredTrades[index];
                          return _buildTradeCard(trade);
                        },
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildTradeCard(Trade trade) {
    final isProfit = trade.calculatePnL() > 0;
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        title: Row(
          children: [
            Text(
              trade.symbol,
              style: AppTextStyles.h3,
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: trade.type == TradeType.LONG
                    ? AppColors.successGreen.withOpacity(0.1)
                    : AppColors.dangerRed.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                trade.type == TradeType.LONG ? 'Long' : 'Short',
                style: AppTextStyles.body2.copyWith(
                  color: trade.type == TradeType.LONG
                      ? AppColors.successGreen
                      : AppColors.dangerRed,
                ),
              ),
            ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              DateFormat('MMM d, h:mm a').format(trade.entryDate),
              style: AppTextStyles.body2.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'P&L: ${NumberFormat.currency(symbol: '\$').format(trade.calculatePnL())}',
              style: AppTextStyles.body1.copyWith(
                color: isProfit ? AppColors.successGreen : AppColors.dangerRed,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
