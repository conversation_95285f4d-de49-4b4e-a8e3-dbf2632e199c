import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:csv/csv.dart';
import 'package:file_picker/file_picker.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_flutter/core/models/trade.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart' as core;
import 'package:intl/intl.dart';
import 'package:uuid/uuid.dart';
import 'package:trade_reviewer_flutter/core/providers/trade_data_provider.dart';

class ImportCsvScreen extends ConsumerStatefulWidget {
  const ImportCsvScreen({super.key});

  @override
  ConsumerState<ImportCsvScreen> createState() => _ImportCsvScreenState();
}

class _ImportCsvScreenState extends ConsumerState<ImportCsvScreen> {
  File? _selectedFile;
  List<List<dynamic>>? _csvData;
  bool _isLoading = false;
  bool _isLoadingAccounts = true;
  String? _selectedAccount;
  List<core.TradingAccount> _accounts = [];
  String? _accountLoadError;
  late final _dateFormat = DateFormat("MM/dd/yyyy HH:mm:ss");
  Map<String, int> _columnMapping = {};

  final Map<String, List<String>> _fieldMappings = {
    'symbol': ['symbol'],
    'quantity': ['qty'],
    'buyPrice': ['buyPrice'],
    'sellPrice': ['sellPrice'],
    'pnl': ['pnl'],
    'buyFillId': ['buyFillId'],
    'sellFillId': ['sellFillId'],
    'entryDate': ['boughtTimestamp'],
    'exitDate': ['soldTimestamp'],
  };

  @override
  void initState() {
    super.initState();
    _loadAccounts();
  }

  Future<void> _loadAccounts() async {
    try {
      setState(() {
        _isLoadingAccounts = true;
        _accountLoadError = null;
      });

      final settingsService = ref.read(settingsServiceProvider);
      final settings = await settingsService.getSettings();

      if (!mounted) return;

      setState(() {
        _accounts = settings.accounts.tradingAccounts;
        if (_accounts.isNotEmpty) {
          _selectedAccount = _accounts.first.id;
        }
        _isLoadingAccounts = false;
      });
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _accountLoadError = 'Error loading accounts: $e';
        _isLoadingAccounts = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading accounts: $e')),
      );
    }
  }

  void _autoMapColumns(List<String> headers) {
    Map<String, int> mapping = {};

    // First try exact matches
    _fieldMappings.forEach((field, possibleNames) {
      for (var name in possibleNames) {
        final index =
            headers.indexWhere((h) => h.toLowerCase() == name.toLowerCase());
        if (index != -1) {
          mapping[field] = index;
          break;
        }
      }
    });

    // Then try contains matches for unmapped fields
    _fieldMappings.forEach((field, possibleNames) {
      if (!mapping.containsKey(field)) {
        for (var name in possibleNames) {
          final index = headers
              .indexWhere((h) => h.toLowerCase().contains(name.toLowerCase()));
          if (index != -1) {
            mapping[field] = index;
            break;
          }
        }
      }
    });

    // Add manual mapping for specific columns in Performance (1).csv
    if (!mapping.containsKey('pnl') && headers.contains('pnl')) {
      mapping['pnl'] = headers.indexOf('pnl');
    }

    setState(() {
      _columnMapping = mapping;
    });

    print('\nColumn mapping:');
    mapping.forEach((field, index) {
      print('$field -> ${headers[index]} (index: $index)');
    });
  }

  Future<void> _pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = File(result.files.first.path!);
        final contents = await file.readAsString();
        final csvData = const CsvToListConverter().convert(contents, eol: "\n");

        if (csvData.isNotEmpty) {
          _autoMapColumns(csvData[0].map((e) => e.toString()).toList());
        }

        setState(() {
          _selectedFile = file;
          _csvData = csvData;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error reading file: $e')),
        );
      }
    }
  }

  Future<void> _importTrades() async {
    if (_csvData == null || _selectedAccount == null) return;

    try {
      setState(() => _isLoading = true);

      final tradeService = ref.read(tradeServiceProvider);
      final settingsService = ref.read(settingsServiceProvider);
      final settings = await settingsService.getSettings();
      final userId = settings.userId;

      print('\n=== Starting import process ===');
      print('CSV Data rows: ${_csvData!.length}');
      print('Selected Account: $_selectedAccount');
      print('User ID: $userId');

      int successCount = 0;
      List<String> errors = [];

      // Skip header row
      for (var row in _csvData!.skip(1)) {
        try {
          // Parse prices and quantity
          double buyPrice =
              double.parse(row[_columnMapping['buyPrice'] ?? 7].toString());
          double sellPrice =
              double.parse(row[_columnMapping['sellPrice'] ?? 8].toString());
          double quantity =
              double.parse(row[_columnMapping['quantity'] ?? 6].toString());

          print('\nProcessing row:');
          print('Buy Price: $buyPrice');
          print('Sell Price: $sellPrice');
          print('Quantity: $quantity');

          // Parse PnL if available
          double? pnl;
          if (_columnMapping.containsKey('pnl')) {
            String pnlStr = row[_columnMapping['pnl']!].toString();
            // Remove currency symbol and handle parentheses for negative values
            pnlStr = pnlStr.replaceAll(RegExp(r'[\$,]'), '');
            if (pnlStr.startsWith('(') && pnlStr.endsWith(')')) {
              pnlStr = '-' + pnlStr.substring(1, pnlStr.length - 1);
            }
            if (pnlStr.isNotEmpty) {
              pnl = double.tryParse(pnlStr);
              print('PnL from CSV: $pnl');
            }
          }

          // Determine if it's a long or short trade based on entry/exit prices
          bool isLong = pnl != null ? pnl > 0 : buyPrice < sellPrice;
          print('Trade type: ${isLong ? "LONG" : "SHORT"}');

          final entryDate = _dateFormat
              .parse(row[_columnMapping['entryDate'] ?? 10].toString());
          final exitDate = _dateFormat
              .parse(row[_columnMapping['exitDate'] ?? 11].toString());

          final trade = {
            'id': const Uuid().v4(),
            'symbol': row[_columnMapping['symbol'] ?? 0].toString(),
            'type': isLong ? 'LONG' : 'SHORT',
            'status': 'CLOSED',
            'entryPrice': isLong ? buyPrice : sellPrice,
            'exitPrice': isLong ? sellPrice : buyPrice,
            'quantity': quantity,
            'fees': 0.0,
            'commission': 0.0,
            'entryDate': entryDate.toIso8601String(),
            'exitDate': exitDate.toIso8601String(),
            'accountId': _selectedAccount,
            'userId': userId,
            'tags': ['Imported'],
            'notes': pnl != null
                ? 'Imported from CSV (PnL: \$${pnl.toStringAsFixed(2)})'
                : 'Imported from CSV',
            'predefinedPnL': pnl,
            'updatedAt': DateTime.now().toIso8601String(),
          };

          print('\nCreating trade:');
          print('Symbol: ${trade['symbol']}');
          print('Type: ${trade['type']}');
          print('Status: ${trade['status']}');
          print('Entry Price: ${trade['entryPrice']}');
          print('Exit Price: ${trade['exitPrice']}');
          print('Quantity: ${trade['quantity']}');
          if (pnl != null) {
            print('PnL from CSV: $pnl');
          }

          await tradeService.createTrade(userId, trade);
          print('Trade created successfully');
          successCount++;
        } catch (e, stack) {
          print('Error importing row: $e');
          errors.add(e.toString());
        }
      }

      if (mounted) {
        if (successCount > 0) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Successfully imported $successCount trades${errors.isEmpty ? "." : " with some errors."}'),
              backgroundColor: Colors.green,
            ),
          );
        }

        if (errors.isNotEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${errors.length} errors occurred during import.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }

      setState(() => _isLoading = false);
      ref.read(tradeDataProvider.notifier).notifyDataChanged();
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error during import: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Import CSV'),
        actions: [
          if (!_isLoadingAccounts)
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _loadAccounts,
              tooltip: 'Refresh Accounts',
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Select Trading Account',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  if (_isLoadingAccounts)
                    const Center(
                      child: Padding(
                        padding: EdgeInsets.all(8.0),
                        child: CircularProgressIndicator(),
                      ),
                    )
                  else if (_accountLoadError != null)
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.errorContainer,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: theme.colorScheme.error,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _accountLoadError!,
                              style: TextStyle(
                                color: theme.colorScheme.error,
                              ),
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.refresh),
                            onPressed: _loadAccounts,
                            color: theme.colorScheme.error,
                          ),
                        ],
                      ),
                    )
                  else
                    DropdownButtonFormField<String>(
                      value: _selectedAccount,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: 'Select Account',
                      ),
                      items: _accounts.isEmpty
                          ? [
                              const DropdownMenuItem(
                                value: null,
                                child: Text('No accounts available'),
                              )
                            ]
                          : _accounts
                              .map((account) => DropdownMenuItem(
                                    value: account.id,
                                    child: Text(account.name),
                                  ))
                              .toList(),
                      onChanged: _accounts.isEmpty
                          ? null
                          : (value) {
                              setState(() => _selectedAccount = value);
                            },
                    ),
                  const SizedBox(height: 24),
                  if (_selectedFile == null)
                    Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.upload_file,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Select a CSV file to import',
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 24),
                          ElevatedButton.icon(
                            onPressed: _pickFile,
                            icon: const Icon(Icons.file_upload),
                            label: const Text('Choose File'),
                          ),
                        ],
                      ),
                    )
                  else
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Selected File',
                            style: theme.textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: theme.cardColor,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: theme.dividerColor.withOpacity(0.2),
                              ),
                            ),
                            child: Row(
                              children: [
                                const Icon(Icons.description),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    _selectedFile!.path.split('/').last,
                                    style: theme.textTheme.bodyMedium,
                                  ),
                                ),
                                IconButton(
                                  icon: const Icon(Icons.close),
                                  onPressed: () {
                                    setState(() {
                                      _selectedFile = null;
                                      _csvData = null;
                                      _columnMapping.clear();
                                    });
                                  },
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 24),
                          if (_csvData != null) ...[
                            Text(
                              'Found ${_csvData!.length - 1} trades',
                              style: theme.textTheme.titleMedium,
                            ),
                            const SizedBox(height: 24),
                            Text(
                              'Field Mapping',
                              style: theme.textTheme.titleMedium,
                            ),
                            const SizedBox(height: 8),
                            Expanded(
                              child: ListView(
                                children: _fieldMappings.keys.map((field) {
                                  final mappedIndex = _columnMapping[field];
                                  final mappedHeader = mappedIndex != null &&
                                          _csvData!.isNotEmpty
                                      ? _csvData![0][mappedIndex].toString()
                                      : 'Not mapped';
                                  return ListTile(
                                    title: Text(field),
                                    subtitle: Text(mappedHeader),
                                    trailing: Icon(
                                      mappedIndex != null
                                          ? Icons.check_circle
                                          : Icons.warning,
                                      color: mappedIndex != null
                                          ? Colors.green
                                          : Colors.orange,
                                    ),
                                  );
                                }).toList(),
                              ),
                            ),
                            const SizedBox(height: 16),
                            SizedBox(
                              width: double.infinity,
                              child: FilledButton.icon(
                                onPressed: _importTrades,
                                icon: const Icon(Icons.check),
                                label: const Text('Import Trades'),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                ],
              ),
            ),
    );
  }
}
