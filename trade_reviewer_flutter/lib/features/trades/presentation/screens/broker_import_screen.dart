import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/trade_data_provider.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';

class BrokerImportScreen extends ConsumerStatefulWidget {
  const BrokerImportScreen({super.key});

  @override
  ConsumerState<BrokerImportScreen> createState() => _BrokerImportScreenState();
}

class _BrokerImportScreenState extends ConsumerState<BrokerImportScreen> {
  final _formKey = GlobalKey<FormState>();
  String? _selectedBroker;
  bool _isLoading = false;
  DateTime? _startDate;
  DateTime? _endDate;

  // Sample brokers data - Replace with actual brokers from your backend
  final List<Map<String, dynamic>> _brokers = [
    {
      'id': 'td',
      'name': 'TD Ameritrade',
      'logo': 'assets/images/td_ameritrade.png',
      'status': 'Connected',
      'lastSync': '2024-01-15 10:30 AM',
    },
    {
      'id': 'etrade',
      'name': 'E*TRADE',
      'logo': 'assets/images/etrade.png',
      'status': 'Not Connected',
      'lastSync': null,
    },
    {
      'id': 'webull',
      'name': 'Webull',
      'logo': 'assets/images/webull.png',
      'status': 'Connected',
      'lastSync': '2024-01-14 3:45 PM',
    },
  ];

  Future<void> _selectDate(bool isStart) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        if (isStart) {
          _startDate = picked;
          if (_endDate == null || _endDate!.isBefore(_startDate!)) {
            _endDate = _startDate;
          }
        } else {
          _endDate = picked;
          if (_startDate == null || _startDate!.isAfter(_endDate!)) {
            _startDate = _endDate;
          }
        }
      });
    }
  }

  Future<void> _importTrades() async {
    if (_formKey.currentState?.validate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      try {
        // TODO: Implement broker import logic
        await Future.delayed(const Duration(seconds: 2)); // Simulate API call

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Trades imported successfully'),
              backgroundColor: Colors.green,
            ),
          );

          // Notify that data has changed
          ref.read(tradeDataProvider.notifier).notifyDataChanged();

          Navigator.pop(context);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error importing trades: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Import from Broker'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Form(
              key: _formKey,
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  // Broker Selection
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Select Broker',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ..._brokers.map(
                            (broker) => RadioListTile<String>(
                              title: Row(
                                children: [
                                  // TODO: Add broker logo
                                  // Image.asset(
                                  //   broker['logo'],
                                  //   width: 24,
                                  //   height: 24,
                                  // ),
                                  const SizedBox(width: 8),
                                  Text(broker['name']),
                                  const SizedBox(width: 8),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: broker['status'] == 'Connected'
                                          ? Colors.green.withOpacity(0.1)
                                          : Colors.grey.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: Text(
                                      broker['status'],
                                      style:
                                          theme.textTheme.bodySmall?.copyWith(
                                        color: broker['status'] == 'Connected'
                                            ? Colors.green
                                            : Colors.grey,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              subtitle: broker['lastSync'] != null
                                  ? Text(
                                      'Last synced: ${broker['lastSync']}',
                                      style: theme.textTheme.bodySmall,
                                    )
                                  : null,
                              value: broker['id'],
                              groupValue: _selectedBroker,
                              onChanged: broker['status'] == 'Connected'
                                  ? (value) {
                                      setState(() {
                                        _selectedBroker = value;
                                      });
                                    }
                                  : null,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Date Range
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Date Range',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: OutlinedButton.icon(
                                  onPressed: () => _selectDate(true),
                                  icon: const Icon(Icons.calendar_today),
                                  label: Text(
                                    _startDate == null
                                        ? 'Start Date'
                                        : '${_startDate!.month}/${_startDate!.day}/${_startDate!.year}',
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: OutlinedButton.icon(
                                  onPressed: () => _selectDate(false),
                                  icon: const Icon(Icons.calendar_today),
                                  label: Text(
                                    _endDate == null
                                        ? 'End Date'
                                        : '${_endDate!.month}/${_endDate!.day}/${_endDate!.year}',
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: _selectedBroker != null &&
                            _startDate != null &&
                            _endDate != null
                        ? _importTrades
                        : null,
                    child: const Text('Import Trades'),
                  ),
                ],
              ),
            ),
    );
  }
}
