import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_flutter/core/models/settings.dart';
import 'package:trade_reviewer_flutter/core/models/trade.dart';
import 'package:intl/intl.dart';

class AddTradeScreen extends ConsumerStatefulWidget {
  final Trade? tradeToEdit;

  const AddTradeScreen({
    super.key,
    this.tradeToEdit,
  });

  @override
  ConsumerState<AddTradeScreen> createState() => _AddTradeScreenState();
}

class _AddTradeScreenState extends ConsumerState<AddTradeScreen> {
  final _formKey = GlobalKey<FormState>();
  final _symbolController = TextEditingController();
  final _quantityController = TextEditingController();
  final _entryPriceController = TextEditingController();
  final _exitPriceController = TextEditingController();
  final _stopLossController = TextEditingController();
  final _takeProfitController = TextEditingController();
  final _notesController = TextEditingController();

  String _selectedType = 'Buy';
  String _selectedStrategy = 'Breakout';
  DateTime _selectedDate = DateTime.now();
  TimeOfDay _selectedTime = TimeOfDay.now();
  String? _selectedAccount;
  bool _isLoading = true;
  late Settings _settings;

  final List<String> _tradeTypes = ['Buy', 'Sell'];
  final List<String> _strategies = [
    'Breakout',
    'Trend Following',
    'Support/Resistance',
    'Scalping',
    'Swing',
    'Other'
  ];

  @override
  void initState() {
    super.initState();
    _loadSettings();
    if (widget.tradeToEdit != null) {
      _initializeWithTrade(widget.tradeToEdit!);
    }
  }

  void _initializeWithTrade(Trade trade) {
    _symbolController.text = trade.symbol;
    _quantityController.text = trade.quantity.toString();
    _entryPriceController.text = trade.entryPrice.toString();
    _exitPriceController.text = trade.exitPrice?.toString() ?? '';
    _stopLossController.text = trade.stopLoss?.toString() ?? '';
    _takeProfitController.text = trade.takeProfit?.toString() ?? '';
    _notesController.text = trade.notes ?? '';
    _selectedType = trade.type == TradeType.LONG ? 'Buy' : 'Sell';
    _selectedStrategy = _strategies.contains(trade.tags.firstOrNull ?? '')
        ? trade.tags.first
        : _strategies.first;
    _selectedDate = trade.entryDate;
    _selectedTime = TimeOfDay.fromDateTime(trade.entryDate);
    _selectedAccount = trade.accountId;
  }

  Future<void> _loadSettings() async {
    try {
      final settingsService = ref.read(settingsServiceProvider);
      final settings = await settingsService.getSettings();
      setState(() {
        _settings = Settings.fromCore(settings);
        _isLoading = false;
        if (_selectedAccount == null &&
            _settings.accounts.tradingAccounts.isNotEmpty) {
          _selectedAccount = _settings.accounts.tradingAccounts.first.id;
        }
      });
    } catch (e, stack) {
      print('Error loading settings: $e');
      print('Stack trace: $stack');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Error loading settings')),
      );
    }
  }

  String? _getAccountValidationMessage(String? accountId) {
    if (accountId == null || accountId.isEmpty) {
      return 'Please select an account';
    }
    final account =
        _settings.accounts.tradingAccounts.firstWhere((a) => a.id == accountId);
    if (account.status != AccountStatus.active.index) {
      return 'This account is not active. Please select an active account.';
    }
    return null;
  }

  @override
  void dispose() {
    _symbolController.dispose();
    _quantityController.dispose();
    _entryPriceController.dispose();
    _exitPriceController.dispose();
    _stopLossController.dispose();
    _takeProfitController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedTime,
    );
    if (picked != null && picked != _selectedTime) {
      setState(() {
        _selectedTime = picked;
      });
    }
  }

  void _submitForm() async {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();
      try {
        final tradeService = ref.read(tradeServiceProvider);
        final settingsService = ref.read(settingsServiceProvider);
        final settings = await settingsService.getSettings();
        final userId = settings.userId;

        final selectedDateTime = DateTime(
          _selectedDate.year,
          _selectedDate.month,
          _selectedDate.day,
          _selectedTime.hour,
          _selectedTime.minute,
        );

        final trade = {
          'symbol': _symbolController.text.toUpperCase(),
          'type': _selectedType == 'Buy' ? 'LONG' : 'SHORT',
          'status': 'CLOSED',
          'entryPrice': double.parse(_entryPriceController.text),
          'exitPrice': double.parse(_exitPriceController.text),
          'stopLoss': _stopLossController.text.isNotEmpty
              ? double.parse(_stopLossController.text)
              : double.parse(_entryPriceController.text) * 0.95,
          'takeProfit': _takeProfitController.text.isNotEmpty
              ? double.parse(_takeProfitController.text)
              : double.parse(_entryPriceController.text) * 1.05,
          'quantity': double.parse(_quantityController.text),
          'fees': 0.0,
          'tags': [_selectedStrategy],
          'notes': _notesController.text,
          'entryDate': selectedDateTime.toIso8601String(),
          'exitDate': selectedDateTime.toIso8601String(),
          'accountId': _selectedAccount!,
          'userId': userId,
          'updatedAt': DateTime.now().toIso8601String(),
        };

        if (widget.tradeToEdit != null) {
          await tradeService.updateTrade(
              userId, widget.tradeToEdit!.id!, trade);
        } else {
          await tradeService.createTrade(userId, trade);
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(widget.tradeToEdit != null
                  ? 'Trade updated successfully'
                  : 'Trade saved successfully'),
            ),
          );
          Navigator.pop(context, true);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error saving trade: $e')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    print(
        'Building AddTradeScreen with ${_settings.accounts.tradingAccounts.length} accounts');

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.tradeToEdit != null ? 'Edit Trade' : 'Add Trade'),
        actions: [
          TextButton(
            onPressed: () {
              if (_formKey.currentState?.validate() ?? false) {
                _submitForm();
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Account Selection
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Account',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    if (_settings.accounts.tradingAccounts.isEmpty)
                      const Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Text(
                          'No trading accounts found. Please add an account in Settings.',
                          style: TextStyle(color: Colors.red),
                        ),
                      )
                    else
                      DropdownButtonFormField<String>(
                        value: _selectedAccount,
                        decoration: const InputDecoration(
                          labelText: 'Trading Account',
                          border: InputBorder.none,
                        ),
                        items: _settings.accounts.tradingAccounts
                            .where(
                                (a) => a.status == AccountStatus.active.index)
                            .map((account) => DropdownMenuItem(
                                  value: account.id,
                                  child: Text(account.name),
                                ))
                            .toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedAccount = value;
                          });
                        },
                        validator: _getAccountValidationMessage,
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            // Symbol and Type Row
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: TextFormField(
                    controller: _symbolController,
                    decoration: InputDecoration(
                      labelText: 'Symbol',
                      hintText: 'Enter symbol',
                      prefixIcon: const Icon(Icons.trending_up),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    textCapitalization: TextCapitalization.characters,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Required';
                      }
                      return null;
                    },
                    onChanged: (value) {
                      setState(() {
                        _formKey.currentState?.validate();
                      });
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedType,
                    decoration: InputDecoration(
                      labelText: 'Type',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    items: _tradeTypes.map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Text(type),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => _selectedType = value);
                      }
                      _formKey.currentState?.validate();
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Strategy
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Strategy',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      value: _selectedStrategy,
                      isExpanded: true,
                      decoration: InputDecoration(
                        hintText: 'Select Strategy',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      items: _strategies.map((strategy) {
                        return DropdownMenuItem(
                          value: strategy,
                          child: Text(strategy),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() => _selectedStrategy = value);
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            // Date and Time Row
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () => _selectDate(context),
                    child: InputDecorator(
                      decoration: InputDecoration(
                        labelText: 'Date',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            DateFormat('yyyy-MM-dd').format(_selectedDate),
                          ),
                          const Icon(Icons.calendar_today, size: 20),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: InkWell(
                    onTap: () => _selectTime(context),
                    child: InputDecorator(
                      decoration: InputDecoration(
                        labelText: 'Time',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(_selectedTime.format(context)),
                          const Icon(Icons.access_time, size: 20),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Quantity and Entry Price Row
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _quantityController,
                    decoration: InputDecoration(
                      labelText: 'Quantity',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Required';
                      }
                      if (double.tryParse(value) == null) {
                        return 'Invalid number';
                      }
                      return null;
                    },
                    onChanged: (value) {
                      setState(() {
                        _formKey.currentState?.validate();
                      });
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextFormField(
                    controller: _entryPriceController,
                    decoration: InputDecoration(
                      labelText: 'Entry Price',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Required';
                      }
                      if (double.tryParse(value) == null) {
                        return 'Invalid number';
                      }
                      return null;
                    },
                    onChanged: (value) {
                      setState(() {
                        _formKey.currentState?.validate();
                      });
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Exit Price
            TextFormField(
              controller: _exitPriceController,
              decoration: InputDecoration(
                labelText: 'Exit Price',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Required';
                }
                if (double.tryParse(value) == null) {
                  return 'Invalid number';
                }
                return null;
              },
              onChanged: (value) {
                setState(() {
                  _formKey.currentState?.validate();
                });
              },
            ),
            const SizedBox(height: 16),
            // Stop Loss and Take Profit Row
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _stopLossController,
                    decoration: InputDecoration(
                      labelText: 'Stop Loss',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        if (double.tryParse(value) == null) {
                          return 'Invalid number';
                        }
                      }
                      return null;
                    },
                    onChanged: (value) {
                      setState(() {
                        _formKey.currentState?.validate();
                      });
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextFormField(
                    controller: _takeProfitController,
                    decoration: InputDecoration(
                      labelText: 'Take Profit',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        if (double.tryParse(value) == null) {
                          return 'Invalid number';
                        }
                      }
                      return null;
                    },
                    onChanged: (value) {
                      setState(() {
                        _formKey.currentState?.validate();
                      });
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Notes
            TextFormField(
              controller: _notesController,
              decoration: InputDecoration(
                labelText: 'Notes',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            // Save Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  if (_formKey.currentState?.validate() ?? false) {
                    _submitForm();
                  }
                },
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text('Save Trade'),
              ),
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}
