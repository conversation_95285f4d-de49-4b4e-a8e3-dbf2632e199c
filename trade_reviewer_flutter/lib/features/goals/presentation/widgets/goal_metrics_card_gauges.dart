import 'package:flutter/material.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/theme/app_text_styles.dart';
import 'package:trade_reviewer_flutter/features/goals/presentation/widgets/overlay_info_button.dart';
import 'dart:math' as math;

class GoalMetricsCardGauges extends StatelessWidget {
  final double profitProgress;
  final double guardProgress;
  final double focusProgress;
  final double winRate;
  final double profitFactor;

  const GoalMetricsCardGauges({
    super.key,
    required this.profitProgress,
    required this.guardProgress,
    required this.focusProgress,
    required this.winRate,
    required this.profitFactor,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            SmallGauge(
              progress: profitProgress / 100,
              color: const Color(0xFF22C55E),
              label: 'PROFIT',
              icon: Icons.monetization_on,
            ),
            SmallGauge(
              progress: guardProgress / 100,
              color: const Color(0xFFB7C1CF),
              label: 'GUARD',
              icon: Icons.shield,
            ),
            SmallGauge(
              progress: focusProgress / 100,
              color: const Color(0xFF3B82F6),
              label: 'FOCUS',
              icon: Icons.center_focus_strong,
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildMetricRow(
              'Win Rate',
              '${winRate.toStringAsFixed(1)}%',
              'Percentage of profitable trades\nover total trades.',
            ),
            _buildMetricRow(
              'Profit Factor',
              profitFactor.toStringAsFixed(2),
              'Ratio of gross profits to\ngross losses.',
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMetricRow(String label, String value, String description) {
    return Column(
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: AppTextStyles.h3.copyWith(
                color: AppColors.textSecondary,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 4),
            OverlayInfoButton(
              description: description,
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: AppTextStyles.h2.copyWith(
            color: AppColors.textPrimary,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}

class SmallGauge extends StatefulWidget {
  final double progress;
  final Color color;
  final String label;
  final IconData icon;

  const SmallGauge({
    Key? key,
    required this.progress,
    required this.color,
    required this.label,
    required this.icon,
  }) : super(key: key);

  @override
  State<SmallGauge> createState() => _SmallGaugeState();
}

class _SmallGaugeState extends State<SmallGauge>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );
    _progressAnimation = Tween<double>(
      begin: 0,
      end: widget.progress,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));
    _controller.forward();
  }

  @override
  void didUpdateWidget(SmallGauge oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.progress != widget.progress) {
      _progressAnimation = Tween<double>(
        begin: oldWidget.progress,
        end: widget.progress,
      ).animate(CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOutCubic,
      ));
      _controller.forward(from: 0);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              widget.label,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.white70,
                letterSpacing: 1.2,
              ),
            ),
            const SizedBox(width: 4),
            OverlayInfoButton(
              description: _getGaugeDescription(widget.label),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: const Color(0xFF0F172A),
            border: Border.all(
              color: widget.color.withOpacity(0.3),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: widget.color.withOpacity(0.2),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Stack(
            children: [
              // Outer circle with gradient border
              Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: const Color(0xFF0F172A),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      widget.color.withOpacity(0.2),
                      widget.color.withOpacity(0.1),
                    ],
                  ),
                ),
              ),
              // Background circle with dashed border
              Container(
                margin: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: const Color(0xFF0F172A),
                  border: Border.all(
                    color: widget.color.withOpacity(0.15),
                    width: 8,
                  ),
                ),
              ),
              // Animated progress arc
              Padding(
                padding: const EdgeInsets.all(6),
                child: AnimatedBuilder(
                  animation: _progressAnimation,
                  builder: (context, child) {
                    return CustomPaint(
                      painter: CircleGaugePainter(
                        progress: _progressAnimation.value,
                        color: widget.color,
                      ),
                      size: const Size.square(double.infinity),
                    );
                  },
                ),
              ),
              // Content
              Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      widget.icon,
                      color: widget.color,
                      size: 24,
                    ),
                    const SizedBox(height: 4),
                    AnimatedBuilder(
                      animation: _progressAnimation,
                      builder: (context, child) {
                        return Text(
                          '${(_progressAnimation.value * 100).toInt()}',
                          style: TextStyle(
                            color: widget.color,
                            fontSize: 22,
                            fontWeight: FontWeight.w700,
                            height: 1,
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _getGaugeDescription(String title) {
    switch (title) {
      case 'PROFIT':
        return 'Measures your trading profitability goals\n'
            'Combines win rate, profit factor, and average returns\n'
            'Higher values indicate better profit generation';
      case 'GUARD':
        return 'Evaluates risk management effectiveness\n'
            'Includes drawdown control and position sizing\n'
            'Higher values show better capital protection';
      case 'FOCUS':
        return 'Tracks trading discipline and consistency\n'
            'Monitors trade frequency and duration\n'
            'Higher values indicate better trading habits';
      default:
        return 'No description available';
    }
  }
}

class CircleGaugePainter extends CustomPainter {
  final double progress;
  final Color color;

  CircleGaugePainter({
    required this.progress,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;
    final startAngle = -math.pi / 2;
    final sweepAngle = 2 * math.pi * progress;

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 8
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle,
      sweepAngle,
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(CircleGaugePainter oldDelegate) =>
      oldDelegate.progress != progress || oldDelegate.color != color;
}
