import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:intl/intl.dart';
import 'package:trade_reviewer_flutter/features/journal/presentation/widgets/journal_dialog.dart';
import 'package:go_router/go_router.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/time_period_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/account_filter_provider.dart';
import 'package:trade_reviewer_flutter/core/models/trade.dart' as flutter;
import 'package:trade_reviewer_core/trade_reviewer_core.dart' as core;
import 'dart:convert';
import 'package:trade_reviewer_flutter/core/widgets/time_period_filter.dart';

class JournalScreen extends ConsumerStatefulWidget {
  const JournalScreen({super.key});

  @override
  ConsumerState<JournalScreen> createState() => _JournalScreenState();
}

class _JournalScreenState extends ConsumerState<JournalScreen> {
  List<flutter.Trade> _trades = [];
  List<flutter.Trade> _filteredTrades = [];
  bool _isLoading = true;
  String _selectedFilter = 'All';
  String _selectedMood = 'All';
  DateTime _selectedDate = DateTime.now();
  Map<String, String>? _dailyJournal;
  Map<String, bool> _expandedFields = {};

  final List<String> _moods = [
    'All',
    'Focused',
    'Confident',
    'Patient',
    'Impulsive',
    'Frustrated',
    'Anxious',
    'Tired',
    'FOMO',
  ];

  @override
  void initState() {
    super.initState();
    _loadTrades();
    _loadDailyJournal();
  }

  Future<void> _loadTrades() async {
    setState(() => _isLoading = true);
    try {
      final storageService = ref.read(storageServiceProvider);
      final trades = await storageService.getTrades();
      setState(() {
        _trades = trades.map((t) => flutter.Trade.fromCore(t)).toList();
        _trades.sort((a, b) => b.entryDate.compareTo(a.entryDate));
        _applyFilters();
      });
    } catch (e) {
      print('Error loading trades: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading trades: $e')),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadDailyJournal() async {
    try {
      final storageService = ref.read(storageServiceProvider);
      final dailyJournalKey = DateFormat('yyyy-MM-dd').format(_selectedDate);
      final journal = await storageService
          .get<Map<String, dynamic>>('daily_journal_$dailyJournalKey');
      setState(() {
        _dailyJournal =
            journal != null ? Map<String, String>.from(journal) : null;
      });
    } catch (e) {
      print('Error loading daily journal: $e');
    }
  }

  Future<void> _saveDailyJournal(Map<String, String> journalData,
      {bool showConfirmation = true}) async {
    try {
      final storageService = ref.read(storageServiceProvider);
      final dailyJournalKey = DateFormat('yyyy-MM-dd').format(_selectedDate);
      await storageService.set('daily_journal_$dailyJournalKey', journalData);
      setState(() {
        _dailyJournal = journalData;
      });
      if (mounted && showConfirmation) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Daily journal saved successfully')),
        );
      }
    } catch (e) {
      print('Error saving daily journal: $e');
      if (mounted && showConfirmation) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving daily journal: $e')),
        );
      }
    }
  }

  void _applyFilters() {
    var filtered = List<flutter.Trade>.from(_trades);

    // Apply status filter
    if (_selectedFilter != 'All') {
      filtered = filtered.where((trade) {
        if (_selectedFilter == 'Open') {
          return trade.status == flutter.TradeStatus.OPEN;
        } else if (_selectedFilter == 'Closed') {
          return trade.status == flutter.TradeStatus.CLOSED;
        }
        return true;
      }).toList();
    }

    // Apply timeframe filter using global time period
    final timePeriodNotifier = ref.read(timePeriodProvider.notifier);
    final startDate = timePeriodNotifier.getStartDate();
    final endDate = timePeriodNotifier.getEndDate();

    if (startDate != null && endDate != null) {
      filtered = filtered.where((trade) {
        return trade.entryDate.isAfter(startDate) &&
            trade.entryDate.isBefore(endDate.add(const Duration(days: 1)));
      }).toList();
    }

    // Apply account filter
    final accountFilter = ref.read(accountFilterProvider);
    if (accountFilter.selectedAccountId != null &&
        accountFilter.selectedAccountId != 'all') {
      filtered = filtered
          .where((trade) => trade.accountId == accountFilter.selectedAccountId)
          .toList();
    }

    // Apply mood filter
    if (_selectedMood != 'All') {
      filtered = filtered.where((trade) {
        if (trade.notes == null) return false;
        try {
          final journalData =
              Map<String, String>.from(json.decode(trade.notes!));
          return journalData['emotions']
                  ?.toLowerCase()
                  .contains(_selectedMood.toLowerCase()) ??
              false;
        } catch (e) {
          return false;
        }
      }).toList();
    }

    setState(() {
      _filteredTrades = filtered;
    });
  }

  Map<String, dynamic> _getJournalStats() {
    final closedTrades = _filteredTrades
        .where((t) => t.status == flutter.TradeStatus.CLOSED)
        .toList();
    final openTrades = _filteredTrades
        .where((t) => t.status == flutter.TradeStatus.OPEN)
        .toList();
    final profitableTrades =
        closedTrades.where((t) => t.calculatePnL() > 0).toList();

    double totalProfit =
        closedTrades.fold(0, (sum, trade) => sum + trade.calculatePnL());
    double avgProfitPercent =
        closedTrades.isEmpty ? 0 : totalProfit / closedTrades.length;
    double winRate = closedTrades.isEmpty
        ? 0
        : profitableTrades.length / closedTrades.length * 100;
    double avgRiskReward = closedTrades.isEmpty
        ? 0
        : closedTrades.fold(
                0.0, (sum, trade) => sum + (trade.calculateRRR() ?? 0)) /
            closedTrades.length;

    return {
      'totalProfit': totalProfit,
      'avgProfitPercent': avgProfitPercent,
      'openTrades': openTrades.length,
      'closedTrades': closedTrades.length,
      'winRate': winRate,
      'avgRiskReward': avgRiskReward,
      'profitableTrades': profitableTrades.length,
      'totalTrades': closedTrades.length,
    };
  }

  Future<void> _deleteTrade(flutter.Trade trade) async {
    try {
      final storageService = ref.read(storageServiceProvider);
      await storageService.deleteTrade(trade.id!);
      await _loadTrades();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Trade deleted successfully')),
        );
      }
    } catch (e) {
      print('Error deleting trade: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error deleting trade: $e')),
        );
      }
    }
  }

  Future<void> _updateTrade(
      flutter.Trade trade, Map<String, String> journalData) async {
    try {
      final updatedTrade = flutter.Trade(
        id: trade.id,
        symbol: trade.symbol,
        type: trade.type,
        status: trade.status,
        entryPrice: trade.entryPrice,
        exitPrice: trade.exitPrice,
        stopLoss: trade.stopLoss,
        takeProfit: trade.takeProfit,
        quantity: trade.quantity,
        fees: trade.fees,
        tags: trade.tags,
        notes: json.encode(journalData),
        entryDate: trade.entryDate,
        exitDate: trade.exitDate,
        accountId: trade.accountId,
        userId: trade.userId,
        updatedAt: DateTime.now(),
        predefinedPnL: trade.predefinedPnL,
      );

      final storageService = ref.read(storageServiceProvider);
      await storageService.updateTrade(updatedTrade.toCore());

      // Update local state immediately
      setState(() {
        final index = _trades.indexWhere((t) => t.id == trade.id);
        if (index != -1) {
          _trades[index] = updatedTrade;
          _applyFilters(); // Reapply filters to update filtered list
        }
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Trade journal updated successfully')),
        );
      }
    } catch (e) {
      print('Error updating trade: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating trade journal: $e')),
        );
      }
    }
  }

  void _showJournalDetails(flutter.Trade? trade) {
    if (trade == null) return; // Remove ability to create journal without trade

    Map<String, String>? existingJournal;
    if (trade.notes != null) {
      try {
        existingJournal = Map<String, String>.from(json.decode(trade.notes!));
      } catch (e) {
        // If notes is not in JSON format, use it as lessons
        existingJournal = {
          'setup': '',
          'execution': '',
          'management': '',
          'lessons': trade.notes!,
          'emotions': '',
          'market': '',
          'improvements': '',
        };
      }
    }

    showDialog(
      context: context,
      builder: (context) => JournalDialog(
        symbol: trade.symbol,
        date: DateFormat('yyyy-MM-dd HH:mm').format(trade.entryDate),
        pnl: trade.calculatePnL(),
        existingJournal: existingJournal,
        onSave: (journalData) async {
          await _updateTrade(trade, journalData);
          if (mounted) {
            Navigator.pop(context);
          }
        },
      ),
    );
  }

  Widget _buildJournalNotes(flutter.Trade trade) {
    if (trade.notes == null) return const SizedBox.shrink();

    try {
      final journalData = Map<String, String>.from(json.decode(trade.notes!));
      final List<Widget> sections = [];

      // Analysis section
      final List<MapEntry<String, String>> analysisFields = [
        if (journalData['setup']?.isNotEmpty == true)
          MapEntry('Setup', journalData['setup']!),
        if (journalData['execution']?.isNotEmpty == true)
          MapEntry('Execution', journalData['execution']!),
        if (journalData['management']?.isNotEmpty == true)
          MapEntry('Management', journalData['management']!),
      ];

      if (analysisFields.isNotEmpty) {
        sections.add(
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primaryBlue.withOpacity(0.03),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.primaryBlue.withOpacity(0.08),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.analytics_outlined,
                        size: 16, color: AppColors.primaryBlue),
                    const SizedBox(width: 4),
                    Text(
                      'Analysis',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColors.primaryBlue,
                          ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                ...analysisFields.map((entry) => Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          entry.key,
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: Theme.of(context)
                                        .textTheme
                                        .bodyLarge
                                        ?.color,
                                  ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          entry.value,
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Theme.of(context)
                                        .textTheme
                                        .bodyLarge
                                        ?.color
                                        ?.withOpacity(0.8),
                                  ),
                        ),
                        if (entry != analysisFields.last)
                          const SizedBox(height: 8),
                      ],
                    )),
              ],
            ),
          ),
        );
      }

      // Reflection section
      final List<MapEntry<String, String>> reflectionFields = [
        if (journalData['emotions']?.isNotEmpty == true)
          MapEntry('Emotions', journalData['emotions']!),
        if (journalData['lessons']?.isNotEmpty == true)
          MapEntry('Lessons', journalData['lessons']!),
        if (journalData['improvements']?.isNotEmpty == true)
          MapEntry('Improvements', journalData['improvements']!),
      ];

      if (reflectionFields.isNotEmpty) {
        if (sections.isNotEmpty) sections.add(const SizedBox(height: 8));
        sections.add(
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primaryBlue.withOpacity(0.03),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.primaryBlue.withOpacity(0.08),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.psychology_outlined,
                        size: 16, color: AppColors.primaryBlue),
                    const SizedBox(width: 4),
                    Text(
                      'Reflection',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColors.primaryBlue,
                          ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                ...reflectionFields.map((entry) => Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          entry.key,
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: Theme.of(context)
                                        .textTheme
                                        .bodyLarge
                                        ?.color,
                                  ),
                        ),
                        const SizedBox(height: 2),
                        if (entry.key == 'Emotions')
                          Wrap(
                            spacing: 4,
                            runSpacing: 4,
                            children: entry.value
                                .split(',')
                                .map((e) => e.trim())
                                .where((e) => e.isNotEmpty)
                                .map((emotion) => Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 6, vertical: 2),
                                      decoration: BoxDecoration(
                                        color: AppColors.primaryBlue
                                            .withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: Text(
                                        emotion,
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodySmall
                                            ?.copyWith(
                                              color: AppColors.primaryBlue,
                                              fontWeight: FontWeight.w500,
                                            ),
                                      ),
                                    ))
                                .toList(),
                          )
                        else
                          Text(
                            entry.value,
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Theme.of(context)
                                          .textTheme
                                          .bodyLarge
                                          ?.color
                                          ?.withOpacity(0.8),
                                    ),
                          ),
                        if (entry != reflectionFields.last)
                          const SizedBox(height: 8),
                      ],
                    )),
              ],
            ),
          ),
        );
      }

      // Market section
      if (journalData['market']?.isNotEmpty == true) {
        if (sections.isNotEmpty) sections.add(const SizedBox(height: 8));
        sections.add(
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primaryBlue.withOpacity(0.03),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.primaryBlue.withOpacity(0.08),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.show_chart,
                        size: 16, color: AppColors.primaryBlue),
                    const SizedBox(width: 4),
                    Text(
                      'Market',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColors.primaryBlue,
                          ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  journalData['market']!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context)
                            .textTheme
                            .bodyLarge
                            ?.color
                            ?.withOpacity(0.8),
                      ),
                ),
              ],
            ),
          ),
        );
      }

      if (sections.isEmpty) return const SizedBox.shrink();

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: sections,
      );
    } catch (e) {
      // If not in JSON format, show as plain text
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppColors.primaryBlue.withOpacity(0.03),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: AppColors.primaryBlue.withOpacity(0.08),
          ),
        ),
        child: Text(
          trade.notes!,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context)
                    .textTheme
                    .bodyLarge
                    ?.color
                    ?.withOpacity(0.8),
              ),
        ),
      );
    }
  }

  void _showDailyJournalDialog() {
    showDialog(
      context: context,
      builder: (context) => JournalDialog(
        symbol: DateFormat('yyyy-MM-dd').format(_selectedDate),
        date: DateFormat('EEEE, MMMM d, yyyy').format(_selectedDate),
        pnl: 0,
        existingJournal: _dailyJournal,
        onSave: (journalData) async {
          await _saveDailyJournal(journalData);
          if (mounted) {
            Navigator.pop(context);
          }
        },
      ),
    );
  }

  Widget _buildTextField({
    required String initialValue,
    required String hintText,
    required String fieldKey,
    int minLines = 2,
    int maxLines = 2,
    bool expanded = false,
  }) {
    return Stack(
      children: [
        TextFormField(
          initialValue: initialValue,
          minLines: expanded ? 4 : minLines,
          maxLines: expanded ? 6 : maxLines,
          style: Theme.of(context).textTheme.bodyMedium,
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: TextStyle(color: Colors.grey[400]),
            contentPadding: const EdgeInsets.all(12),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                  color: Theme.of(context).dividerColor.withOpacity(0.2)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                  color: Theme.of(context).dividerColor.withOpacity(0.2)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppColors.primaryBlue, width: 1.5),
            ),
            filled: true,
            fillColor: Theme.of(context).cardColor,
          ),
          onChanged: (value) {
            final updatedJournal =
                Map<String, String>.from(_dailyJournal ?? {});
            updatedJournal[fieldKey] = value;
            _saveDailyJournal(updatedJournal, showConfirmation: false);
          },
        ),
        if (!expanded && initialValue.length > 100)
          Positioned(
            right: 8,
            bottom: 8,
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _expandedFields[fieldKey] = true;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(
                    color: AppColors.primaryBlue.withOpacity(0.2),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'More',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.primaryBlue,
                            fontWeight: FontWeight.w500,
                          ),
                    ),
                    const SizedBox(width: 2),
                    Icon(
                      Icons.chevron_right,
                      size: 14,
                      color: AppColors.primaryBlue,
                    ),
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildDailyJournalCard() {
    final theme = Theme.of(context);
    final bool isToday = _selectedDate.year == DateTime.now().year &&
        _selectedDate.month == DateTime.now().month &&
        _selectedDate.day == DateTime.now().day;

    Widget _buildSectionHeader(String title, IconData icon,
        {VoidCallback? onEdit}) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, size: 16, color: AppColors.primaryBlue),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.primaryBlue,
                ),
              ),
            ],
          ),
          if (onEdit != null)
            IconButton(
              icon: Icon(Icons.edit_outlined,
                  size: 18, color: AppColors.primaryBlue),
              onPressed: onEdit,
              visualDensity: VisualDensity.compact,
              style: IconButton.styleFrom(
                padding: const EdgeInsets.all(8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
        ],
      );
    }

    Widget _buildViewSection(String title, String? content) {
      if (content == null || content.isEmpty) {
        return Text(
          'No notes yet',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.textTheme.bodyLarge?.color?.withOpacity(0.5),
            fontStyle: FontStyle.italic,
          ),
        );
      }
      return Text(
        content,
        style: theme.textTheme.bodyMedium?.copyWith(
          color: theme.textTheme.bodyLarge?.color?.withOpacity(0.8),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Compact date navigator
        Padding(
          padding: const EdgeInsets.only(left: 16),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: const Icon(Icons.chevron_left, size: 16),
                onPressed: () {
                  setState(() {
                    _selectedDate =
                        _selectedDate.subtract(const Duration(days: 1));
                    _loadDailyJournal();
                  });
                },
                visualDensity: VisualDensity.compact,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                style: IconButton.styleFrom(
                  foregroundColor: Colors.grey[600],
                ),
              ),
              const SizedBox(width: 4),
              Text(
                '${DateFormat('EEE').format(_selectedDate).toUpperCase()}, ${DateFormat('MMM').format(_selectedDate).toUpperCase()} ${DateFormat('d').format(_selectedDate)}',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                  letterSpacing: 0.5,
                ),
              ),
              const SizedBox(width: 4),
              IconButton(
                icon: const Icon(Icons.chevron_right, size: 16),
                onPressed: _selectedDate.isBefore(DateTime.now())
                    ? () {
                        setState(() {
                          _selectedDate =
                              _selectedDate.add(const Duration(days: 1));
                          _loadDailyJournal();
                        });
                      }
                    : null,
                visualDensity: VisualDensity.compact,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                style: IconButton.styleFrom(
                  foregroundColor: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),

        // Market Analysis Section
        Padding(
          padding: const EdgeInsets.only(left: 16, right: 0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionHeader('Market Analysis', Icons.show_chart),
              const SizedBox(height: 8),
              _buildTextField(
                initialValue: _dailyJournal?['market'] ?? '',
                hintText:
                    'Describe today\'s market conditions, key events, and overall sentiment...',
                fieldKey: 'market',
                expanded: _expandedFields['market'] ?? false,
              ),
              const SizedBox(height: 20),

              // Trading Plan Section
              _buildSectionHeader('Trading Plan', Icons.assignment_outlined),
              const SizedBox(height: 8),
              _buildTextField(
                initialValue: _dailyJournal?['setup'] ?? '',
                hintText:
                    'What are your trading objectives, strategies, and key levels to watch today?',
                fieldKey: 'setup',
                expanded: _expandedFields['setup'] ?? false,
              ),
              const SizedBox(height: 20),

              // Daily Reflection Section
              _buildSectionHeader(
                  'Daily Reflection', Icons.psychology_outlined),
              const SizedBox(height: 8),
              _buildTextField(
                initialValue: _dailyJournal?['lessons'] ?? '',
                hintText:
                    'What did you learn today? What went well and what could be improved?',
                fieldKey: 'lessons',
                expanded: _expandedFields['lessons'] ?? false,
              ),
              const SizedBox(height: 20),

              // Emotional State Section
              _buildSectionHeader('Emotional State', Icons.mood_outlined),
              const SizedBox(height: 8),
              Wrap(
                spacing: 4,
                runSpacing: 4,
                alignment: WrapAlignment.start,
                children: _moods.where((mood) => mood != 'All').map((mood) {
                  final isSelected =
                      _dailyJournal?['emotions']?.contains(mood) ?? false;
                  return FilterChip(
                    selected: isSelected,
                    label: Text(mood),
                    onSelected: (selected) {
                      final updatedJournal =
                          Map<String, String>.from(_dailyJournal ?? {});
                      final currentEmotions = updatedJournal['emotions']
                              ?.split(',')
                              .map((e) => e.trim())
                              .where((e) => e.isNotEmpty)
                              .toList() ??
                          [];

                      if (selected && !currentEmotions.contains(mood)) {
                        currentEmotions.add(mood);
                      } else if (!selected) {
                        currentEmotions.remove(mood);
                      }

                      updatedJournal['emotions'] = currentEmotions.join(', ');
                      _saveDailyJournal(updatedJournal,
                          showConfirmation: false);
                    },
                    selectedColor: AppColors.primaryBlue.withOpacity(0.15),
                    checkmarkColor: AppColors.primaryBlue,
                    backgroundColor: Colors.transparent,
                    labelStyle: TextStyle(
                      color:
                          isSelected ? AppColors.primaryBlue : Colors.grey[600],
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.w500,
                      fontSize: 11,
                    ),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                      side: BorderSide(
                        color: isSelected
                            ? AppColors.primaryBlue.withOpacity(0.5)
                            : theme.dividerColor.withOpacity(0.2),
                        width: 0.5,
                      ),
                    ),
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    visualDensity: VisualDensity.compact,
                  );
                }).toList(),
              ),
            ],
          ),
        ),
        const SizedBox(height: 32),
      ],
    );
  }

  Widget _buildTradeCard(flutter.Trade trade, ThemeData theme) {
    final isOpen = trade.status == flutter.TradeStatus.OPEN;
    final isProfit = !isOpen && trade.calculatePnL() > 0;
    final pnl = trade.calculatePnL();
    final rr = trade.calculateRRR();

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: theme.dividerColor.withOpacity(0.2),
        ),
      ),
      elevation: 0,
      child: InkWell(
        onTap: () => _showJournalDetails(trade),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with symbol, type, and date
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(12)),
                border: Border(
                  bottom:
                      BorderSide(color: theme.dividerColor.withOpacity(0.1)),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: (trade.type == flutter.TradeType.LONG
                                  ? Colors.green
                                  : Colors.red)
                              .withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          trade.symbol,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: trade.type == flutter.TradeType.LONG
                                ? Colors.green[700]
                                : Colors.red[700],
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: (trade.type == flutter.TradeType.LONG
                                  ? Colors.green
                                  : Colors.red)
                              .withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          trade.type == flutter.TradeType.LONG
                              ? 'Long'
                              : 'Short',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: trade.type == flutter.TradeType.LONG
                                ? Colors.green[700]
                                : Colors.red[700],
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                  Text(
                    DateFormat('MMM d, HH:mm').format(trade.entryDate),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.textTheme.bodyLarge?.color?.withOpacity(0.6),
                    ),
                  ),
                ],
              ),
            ),

            // Trade details
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Price and PnL info
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Entry/Exit prices
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.login,
                                  size: 16, color: Colors.grey[600]),
                              const SizedBox(width: 4),
                              Text(
                                '\$${trade.entryPrice}',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                          if (!isOpen && trade.exitPrice != null) ...[
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(Icons.logout,
                                    size: 16, color: Colors.grey[600]),
                                const SizedBox(width: 4),
                                Text(
                                  '\$${trade.exitPrice}',
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                      // PnL and R:R
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          if (!isOpen)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: (isProfit ? Colors.green : Colors.red)
                                    .withOpacity(0.1),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                isProfit
                                    ? '+\$${pnl.toStringAsFixed(2)}'
                                    : '-\$${pnl.abs().toStringAsFixed(2)}',
                                style: theme.textTheme.titleSmall?.copyWith(
                                  color: isProfit ? Colors.green : Colors.red,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          if (rr != null) ...[
                            const SizedBox(height: 4),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: AppColors.primaryBlue.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                'R:R ${rr.toStringAsFixed(2)}',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: AppColors.primaryBlue,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),

                  // Journal notes if available
                  if (trade.notes != null) ...[
                    const SizedBox(height: 12),
                    _buildJournalNotes(trade),
                  ],

                  // Tags
                  if (trade.tags.isNotEmpty) ...[
                    const SizedBox(height: 12),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: trade.tags
                          .map((tag) => Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: AppColors.primaryBlue.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  tag,
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: AppColors.primaryBlue,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ))
                          .toList(),
                    ),
                  ],

                  // Action buttons
                  const SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      IconButton(
                        icon: Icon(Icons.edit_outlined,
                            size: 20, color: AppColors.primaryBlue),
                        onPressed: () => _showJournalDetails(trade),
                        tooltip: 'Edit Journal',
                        constraints: const BoxConstraints(),
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        visualDensity: VisualDensity.compact,
                      ),
                      IconButton(
                        icon: Icon(Icons.delete_outline,
                            size: 20, color: Colors.red),
                        onPressed: () =>
                            _showDeleteConfirmation(context, trade),
                        tooltip: 'Delete Trade',
                        constraints: const BoxConstraints(),
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        visualDensity: VisualDensity.compact,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Listen to time period changes to reapply filters
    ref.listen(timePeriodProvider, (previous, next) {
      if (previous?.selectedTimeframe != next.selectedTimeframe ||
          previous?.startDate != next.startDate ||
          previous?.endDate != next.endDate) {
        _applyFilters();
      }
    });

    // Listen to account filter changes to reapply filters
    ref.listen(accountFilterProvider, (previous, next) {
      if (previous?.selectedAccountId != next.selectedAccountId) {
        _applyFilters();
      }
    });

    final theme = Theme.of(context);
    final stats = _getJournalStats();

    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: theme.scaffoldBackgroundColor,
          elevation: 0,
          titleSpacing: 16,
          title: const TimePeriodFilter(),
          centerTitle: false,
          toolbarHeight: 48,
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(92),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Stats Row
                Container(
                  height: 52,
                  margin:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    color: AppColors.primaryBlue.withOpacity(0.03),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppColors.primaryBlue.withOpacity(0.08),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildStatItem(
                        'Total P&L',
                        '\$${stats['totalProfit'].toStringAsFixed(2)}',
                        stats['totalProfit'] >= 0 ? Colors.green : Colors.red,
                      ),
                      Container(
                        height: 24,
                        width: 1,
                        color: AppColors.primaryBlue.withOpacity(0.08),
                      ),
                      _buildStatItem(
                        'Win Rate',
                        '${stats['winRate'].toStringAsFixed(1)}%',
                        AppColors.primaryBlue,
                      ),
                      Container(
                        height: 24,
                        width: 1,
                        color: AppColors.primaryBlue.withOpacity(0.08),
                      ),
                      _buildStatItem(
                        'Avg R:R',
                        stats['avgRiskReward'].toStringAsFixed(2),
                        AppColors.primaryBlue,
                      ),
                      Container(
                        height: 24,
                        width: 1,
                        color: AppColors.primaryBlue.withOpacity(0.08),
                      ),
                      _buildStatItem(
                        'Trades',
                        '${stats['profitableTrades']}/${stats['totalTrades']}',
                        AppColors.textSecondary,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                // Tab Bar
                Container(
                  height: 48,
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    color: AppColors.primaryBlue.withOpacity(0.03),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppColors.primaryBlue.withOpacity(0.08),
                    ),
                  ),
                  child: TabBar(
                    padding: const EdgeInsets.all(4),
                    indicator: BoxDecoration(
                      color: AppColors.primaryBlue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: AppColors.primaryBlue.withOpacity(0.2),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primaryBlue.withOpacity(0.05),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    labelColor: AppColors.primaryBlue,
                    unselectedLabelColor: AppColors.textSecondary,
                    labelStyle: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                    unselectedLabelStyle: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    tabs: [
                      Tab(
                        height: 40,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.edit_note,
                              size: 18,
                            ),
                            const SizedBox(width: 8),
                            const Text('Daily Journal'),
                          ],
                        ),
                      ),
                      Tab(
                        height: 40,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.analytics_outlined,
                              size: 18,
                            ),
                            const SizedBox(width: 8),
                            const Text('Trade Journal'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : TabBarView(
                children: [
                  // Daily Journal Tab
                  ListView(
                    padding: const EdgeInsets.all(16),
                    children: [
                      _buildDailyJournalCard(),
                    ],
                  ),
                  // Trade Journal Tab
                  _filteredTrades.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.search_off_rounded,
                                size: 48,
                                color: Colors.grey[400],
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'No trades found',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: Colors.grey[600],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Try adjusting your filters',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: Colors.grey[500],
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: _filteredTrades.length,
                          itemBuilder: (context, index) {
                            final trade = _filteredTrades[index];
                            return _buildTradeCard(trade, theme);
                          },
                        ),
                ],
              ),
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, flutter.Trade trade) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Trade'),
        content: Text(
            'Are you sure you want to delete this trade for ${trade.symbol}? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteTrade(trade);
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: TextStyle(
            color: AppColors.textSecondary,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}
