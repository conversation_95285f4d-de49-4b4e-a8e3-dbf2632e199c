import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/theme/app_text_styles.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import 'dart:math' as math;
import 'package:trade_reviewer_flutter/core/models/trade.dart';
import 'package:trade_reviewer_flutter/core/models/settings.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/time_period_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/account_filter_provider.dart';
import 'package:trade_reviewer_flutter/features/goals/presentation/widgets/goal_metrics_card_gauges.dart';
import 'package:trade_reviewer_flutter/features/goals/presentation/widgets/overlay_info_button.dart';
import 'package:trade_reviewer_flutter/core/services/analytics_service.dart';
import 'package:trade_reviewer_flutter/core/widgets/time_period_filter.dart';

class AnalyticsScreen extends ConsumerStatefulWidget {
  const AnalyticsScreen({super.key});

  @override
  ConsumerState<AnalyticsScreen> createState() => _AnalyticsScreenState();
}

class _AnalyticsScreenState extends ConsumerState<AnalyticsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late TabController _advancedTabController;
  final currencyFormat = NumberFormat.currency(symbol: '\$', decimalDigits: 2);
  final percentFormat = NumberFormat.percentPattern();
  List<Trade> _trades = [];
  bool _isLoading = true;
  late AnalyticsService _analyticsService;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _advancedTabController = TabController(length: 2, vsync: this);
    _analyticsService = ref.read(analyticsServiceProvider);
    _loadTrades();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _advancedTabController.dispose();
    super.dispose();
  }

  Future<void> _loadTrades() async {
    if (!mounted) return;

    try {
      setState(() => _isLoading = true);

      final tradeService = ref.read(tradeServiceProvider);
      final settingsService = ref.read(settingsServiceProvider);

      // Load settings first to get userId
      final settings = await settingsService.getSettings();
      if (settings == null) {
        throw Exception('Settings not found');
      }

      // Then load trades with the valid userId
      final trades = await tradeService.getTrades(settings.userId);

      if (!mounted) return;

      setState(() {
        _trades = trades.map((t) => Trade.fromCore(t)).toList();
        _isLoading = false;
      });

      print('Loaded ${_trades.length} trades for analytics');
    } catch (e) {
      print('Error loading trades: $e');

      if (!mounted) return;

      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading trades: $e')),
      );
    }
  }

  List<Trade> _filterTradesByTimeframe(
      List<Trade> trades, TimePeriodState timePeriod) {
    final startDate = ref.read(timePeriodProvider.notifier).getStartDate();
    final endDate = ref.read(timePeriodProvider.notifier).getEndDate();

    if (startDate == null || endDate == null) return trades;

    var filtered = trades.where((trade) {
      return trade.entryDate.isAfter(startDate) &&
          trade.entryDate.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();

    // Apply account filter
    final accountFilter = ref.read(accountFilterProvider);
    if (accountFilter.selectedAccountId != null &&
        accountFilter.selectedAccountId != 'all') {
      filtered = filtered
          .where((trade) => trade.accountId == accountFilter.selectedAccountId)
          .toList();
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    final timePeriod = ref.watch(timePeriodProvider);

    // Watch account filter changes
    ref.watch(accountFilterProvider);

    final filteredTrades = _filterTradesByTimeframe(_trades, timePeriod);

    return FutureBuilder<Settings?>(
      future: ref.read(settingsServiceProvider).getSettings().then(
          (settings) => settings != null ? Settings.fromCore(settings) : null),
      builder: (context, snapshot) {
        // Convert goals to the correct format
        Map<String, double> goals = {...AnalyticsService.defaultMetricGoals};
        if (snapshot.data?.goals != null) {
          final metricGoals = snapshot.data!.goals.metricGoals;
          if (metricGoals != null) {
            goals = Map<String, double>.from(metricGoals);
          }
        }

        // Get the account settings
        final accounts = snapshot.data?.accounts;
        double? initialCapital;
        String? selectedAccountId;
        if (accounts != null && accounts.tradingAccounts.isNotEmpty) {
          final firstAccount = accounts.tradingAccounts.first;
          initialCapital = firstAccount.initialCapital;
          selectedAccountId = firstAccount.id;
        }

        final analytics = _analyticsService.calculateMetrics(
            filteredTrades
                .where((t) =>
                    t.status == TradeStatus.CLOSED &&
                    (selectedAccountId == null ||
                        t.accountId == selectedAccountId))
                .toList(),
            goals,
            selectedAccountId,
            initialCapital);

        return Scaffold(
          appBar: AppBar(
            backgroundColor: Theme.of(context).scaffoldBackgroundColor,
            elevation: 0,
            titleSpacing: 16,
            title: const Padding(
              padding: EdgeInsets.only(top: 8),
              child: TimePeriodFilter(),
            ),
            centerTitle: false,
            toolbarHeight: 56,
          ),
          body: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : RefreshIndicator(
                  onRefresh: _loadTrades,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Goal Metrics Card
                        GoalMetricsCardGauges(
                          profitProgress:
                              analytics.gaugeProgress.profitProgress.isNaN
                                  ? 0.0
                                  : analytics.gaugeProgress.profitProgress,
                          guardProgress:
                              analytics.gaugeProgress.guardProgress.isNaN
                                  ? 0.0
                                  : analytics.gaugeProgress.guardProgress,
                          focusProgress:
                              analytics.gaugeProgress.focusProgress.isNaN
                                  ? 0.0
                                  : analytics.gaugeProgress.focusProgress,
                          winRate: analytics.summary.winRate.isNaN
                              ? 0.0
                              : analytics.summary.winRate,
                          profitFactor:
                              analytics.summary.profitFactor.isInfinite ||
                                      analytics.summary.profitFactor.isNaN
                                  ? 0.0
                                  : analytics.summary.profitFactor,
                        ),
                        const SizedBox(height: 24),
                        // Compliance Analysis Section
                        Text(
                          'COMPLIANCE ANALYSIS',
                          style: AppTextStyles.h3.copyWith(
                            color: AppColors.textSecondary,
                            fontSize: 12,
                            letterSpacing: 0.5,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Container(
                          decoration: BoxDecoration(
                            color: AppColors.backgroundAlt,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: AppColors.border),
                          ),
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  Expanded(
                                    child: _buildComplianceCard(
                                      title: 'Position Size',
                                      value:
                                          '${analytics.compliance.positionSizeCompliance.toStringAsFixed(1)}%',
                                      subtitle:
                                          '0 violations • Max size: \$100k',
                                      color: analytics.compliance
                                                  .positionSizeCompliance >=
                                              80
                                          ? AppColors.successGreen
                                          : AppColors.dangerRed,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: _buildComplianceCard(
                                      title: 'Stop Loss',
                                      value:
                                          '${analytics.compliance.stopLossCompliance.toStringAsFixed(1)}%',
                                      subtitle:
                                          '${analytics.compliance.stopLossViolations} violations • Max risk: 2%',
                                      color: analytics.compliance
                                                  .stopLossCompliance >=
                                              80
                                          ? AppColors.successGreen
                                          : AppColors.dangerRed,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              Row(
                                children: [
                                  Expanded(
                                    child: _buildComplianceCard(
                                      title: 'Risk/Reward',
                                      value:
                                          '${analytics.compliance.riskRewardCompliance.toStringAsFixed(1)}%',
                                      subtitle:
                                          '${analytics.compliance.riskRewardViolations} violations • Min ratio: 1.5:1',
                                      color: analytics.compliance
                                                  .riskRewardCompliance >=
                                              80
                                          ? AppColors.successGreen
                                          : AppColors.dangerRed,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: _buildComplianceCard(
                                      title: 'Profit Factor',
                                      value: analytics.summary.profitFactor
                                          .toStringAsFixed(2),
                                      subtitle:
                                          '${currencyFormat.format(analytics.summary.netPnL)} Net P&L',
                                      color:
                                          analytics.summary.profitFactor >= 1.5
                                              ? AppColors.successGreen
                                              : AppColors.dangerRed,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 24),
                        // Performance Metrics Section
                        SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: [
                              _buildMetricCard(
                                title: 'Maximum Drawdown',
                                value:
                                    '${analytics.risk.maxDrawdown.toStringAsFixed(2)}%',
                                subtitle: 'Peak to trough decline',
                              ),
                              const SizedBox(width: 12),
                              _buildMetricCard(
                                title: 'Daily Volatility',
                                value:
                                    '${analytics.volatility.dailyVolatility.toStringAsFixed(2)}%',
                                subtitle: 'Average daily fluctuation',
                              ),
                              const SizedBox(width: 12),
                              _buildMetricCard(
                                title: 'Average Exposure',
                                value:
                                    '${analytics.risk.avgExposure.toStringAsFixed(2)}%',
                                subtitle: 'Typical position size',
                              ),
                              const SizedBox(width: 12),
                              _buildMetricCard(
                                title: 'Win/Loss Streak',
                                value:
                                    '${analytics.summary.maxWinStreak}/${analytics.summary.maxLossStreak}',
                                subtitle: 'Consecutively',
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 24),
                        // Charts Section
                        Container(
                          decoration: BoxDecoration(
                            color: AppColors.primaryBlue.withOpacity(0.03),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: AppColors.primaryBlue.withOpacity(0.08),
                            ),
                          ),
                          child: Column(
                            children: [
                              TabBar(
                                padding: const EdgeInsets.all(4),
                                indicator: BoxDecoration(
                                  color: AppColors.primaryBlue.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color:
                                        AppColors.primaryBlue.withOpacity(0.2),
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: AppColors.primaryBlue
                                          .withOpacity(0.05),
                                      blurRadius: 4,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                controller: _tabController,
                                labelColor: AppColors.primaryBlue,
                                unselectedLabelColor: AppColors.textSecondary,
                                labelStyle: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                                unselectedLabelStyle: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                                dividerColor: Colors.transparent,
                                tabs: const [
                                  Tab(
                                    height: 40,
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.bar_chart,
                                          size: 18,
                                        ),
                                        SizedBox(width: 8),
                                        Text('Performance'),
                                      ],
                                    ),
                                  ),
                                  Tab(
                                    height: 40,
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.timeline,
                                          size: 18,
                                        ),
                                        SizedBox(width: 8),
                                        Text('Time Analysis'),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 300,
                                child: TabBarView(
                                  controller: _tabController,
                                  children: [
                                    _buildTradingPatternsChart(),
                                    _buildTimeAnalysisChart(),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 24),
                        // Advanced Analysis Section
                        Text(
                          'ADVANCED ANALYSIS',
                          style: AppTextStyles.h3.copyWith(
                            color: AppColors.textSecondary,
                            fontSize: 12,
                            letterSpacing: 0.5,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Container(
                          decoration: BoxDecoration(
                            color: AppColors.primaryBlue.withOpacity(0.03),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: AppColors.primaryBlue.withOpacity(0.08),
                            ),
                          ),
                          child: Column(
                            children: [
                              TabBar(
                                padding: const EdgeInsets.all(4),
                                indicator: BoxDecoration(
                                  color: AppColors.primaryBlue.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color:
                                        AppColors.primaryBlue.withOpacity(0.2),
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: AppColors.primaryBlue
                                          .withOpacity(0.05),
                                      blurRadius: 4,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                controller: _advancedTabController,
                                labelColor: AppColors.primaryBlue,
                                unselectedLabelColor: AppColors.textSecondary,
                                labelStyle: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                                unselectedLabelStyle: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                                dividerColor: Colors.transparent,
                                tabs: const [
                                  Tab(
                                    height: 40,
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.analytics_outlined,
                                          size: 18,
                                        ),
                                        SizedBox(width: 8),
                                        Text('Advanced Metrics'),
                                      ],
                                    ),
                                  ),
                                  Tab(
                                    height: 40,
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.show_chart,
                                          size: 18,
                                        ),
                                        SizedBox(width: 8),
                                        Text('Trading Patterns'),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 300,
                                child: TabBarView(
                                  controller: _advancedTabController,
                                  children: [
                                    _buildAdvancedMetricsTab(),
                                    _buildPatternsTab(),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
        );
      },
    );
  }

  Widget _buildTradingPatternsChart() {
    final timePeriod = ref.watch(timePeriodProvider);
    final filteredTrades = _filterTradesByTimeframe(_trades, timePeriod);
    final analytics = _analyticsService.calculateMetrics(
        filteredTrades.where((t) => t.status == TradeStatus.CLOSED).toList());

    // Group trades by day of week
    final dailyPnL = <int, double>{};
    for (final trade in filteredTrades) {
      final dayOfWeek = trade.exitDate?.weekday ?? trade.entryDate.weekday;
      dailyPnL[dayOfWeek] = (dailyPnL[dayOfWeek] ?? 0.0) + trade.calculatePnL();
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: BarChart(
        BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY: dailyPnL.values.isEmpty
              ? 2000
              : dailyPnL.values.reduce(math.max) * 1.2,
          minY: dailyPnL.values.isEmpty
              ? -500
              : dailyPnL.values.reduce(math.min) * 1.2,
          barTouchData: BarTouchData(
            enabled: true,
            touchTooltipData: BarTouchTooltipData(
              tooltipBgColor: AppColors.backgroundAlt,
              tooltipRoundedRadius: 8,
              tooltipBorder: BorderSide(
                color: AppColors.border,
                width: 1,
              ),
              tooltipPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                return BarTooltipItem(
                  currencyFormat.format(rod.toY),
                  TextStyle(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w500,
                  ),
                );
              },
            ),
          ),
          titlesData: FlTitlesData(
            show: true,
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
                getTitlesWidget: (value, meta) {
                  const titles = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'];
                  if (value >= 0 && value < titles.length) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        titles[value.toInt()],
                        style: TextStyle(
                          color: AppColors.textSecondary,
                          fontSize: 12,
                        ),
                      ),
                    );
                  }
                  return const Text('');
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 60,
                interval: 500,
                getTitlesWidget: (value, meta) {
                  return Text(
                    currencyFormat.format(value),
                    style: TextStyle(
                      color: AppColors.textSecondary,
                      fontSize: 12,
                    ),
                  );
                },
              ),
            ),
            rightTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            topTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
          ),
          gridData: FlGridData(
            show: true,
            drawVerticalLine: false,
            horizontalInterval: 500,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: AppColors.border,
                strokeWidth: 1,
                dashArray: [4, 4],
              );
            },
          ),
          borderData: FlBorderData(show: false),
          barGroups: [
            _buildBarGroup(
                0,
                dailyPnL[1] ?? 0,
                dailyPnL[1] == null || dailyPnL[1]! >= 0
                    ? AppColors.successGreen
                    : AppColors.dangerRed),
            _buildBarGroup(
                1,
                dailyPnL[2] ?? 0,
                dailyPnL[2] == null || dailyPnL[2]! >= 0
                    ? AppColors.successGreen
                    : AppColors.dangerRed),
            _buildBarGroup(
                2,
                dailyPnL[3] ?? 0,
                dailyPnL[3] == null || dailyPnL[3]! >= 0
                    ? AppColors.successGreen
                    : AppColors.dangerRed),
            _buildBarGroup(
                3,
                dailyPnL[4] ?? 0,
                dailyPnL[4] == null || dailyPnL[4]! >= 0
                    ? AppColors.successGreen
                    : AppColors.dangerRed),
            _buildBarGroup(
                4,
                dailyPnL[5] ?? 0,
                dailyPnL[5] == null || dailyPnL[5]! >= 0
                    ? AppColors.successGreen
                    : AppColors.dangerRed),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeAnalysisChart() {
    final timePeriod = ref.watch(timePeriodProvider);
    final filteredTrades = _filterTradesByTimeframe(_trades, timePeriod);
    final analytics = _analyticsService.calculateMetrics(
        filteredTrades.where((t) => t.status == TradeStatus.CLOSED).toList());

    // Group trades by hour
    final hourlyPnL = <int, double>{};
    for (final trade in filteredTrades) {
      final hour = trade.exitDate?.hour ?? trade.entryDate.hour;
      hourlyPnL[hour] = (hourlyPnL[hour] ?? 0.0) + trade.calculatePnL();
    }

    // Create spots for line chart - map trading hours (9:30 AM to 4:00 PM)
    final spots = <FlSpot>[];
    double maxY = -double.infinity;
    double minY = double.infinity;
    double cumulative = 0.0;

    for (int hour = 9; hour <= 16; hour++) {
      // For 9:30 AM, add half of 9 AM's value
      if (hour == 9) {
        cumulative += (hourlyPnL[hour] ?? 0.0) / 2;
        spots.add(FlSpot(0, cumulative));
        maxY = math.max(maxY, cumulative);
        minY = math.min(minY, cumulative);
        continue;
      }

      cumulative += hourlyPnL[hour] ?? 0.0;
      final spotX = (hour - 9) * 1.0; // Scale hours to chart x-axis
      spots.add(FlSpot(spotX, cumulative));
      maxY = math.max(maxY, cumulative);
      minY = math.min(minY, cumulative);
    }

    // If no data, provide default spots
    if (spots.isEmpty) {
      spots.addAll(const [
        FlSpot(0, 0),
        FlSpot(1, 0),
        FlSpot(2, 0),
        FlSpot(3, 0),
        FlSpot(4, 0),
      ]);
      maxY = 2000;
      minY = -500;
    }

    // Add padding to min/max for better visualization
    final yPadding = (maxY - minY) * 0.1;
    maxY += yPadding;
    minY -= yPadding;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: LineChart(
        LineChartData(
          gridData: FlGridData(
            show: true,
            drawVerticalLine: false,
            horizontalInterval: 500,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: AppColors.border,
                strokeWidth: 1,
                dashArray: [4, 4],
              );
            },
          ),
          titlesData: FlTitlesData(
            show: true,
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
                getTitlesWidget: (value, meta) {
                  const times = ['9:30', '11:00', '12:30', '2:00', '3:30'];
                  if (value >= 0 && value < times.length) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        times[value.toInt()],
                        style: TextStyle(
                          color: AppColors.textSecondary,
                          fontSize: 12,
                        ),
                      ),
                    );
                  }
                  return const Text('');
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 60,
                interval: 500,
                getTitlesWidget: (value, meta) {
                  return Text(
                    currencyFormat.format(value),
                    style: TextStyle(
                      color: AppColors.textSecondary,
                      fontSize: 12,
                    ),
                  );
                },
              ),
            ),
            rightTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            topTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
          ),
          borderData: FlBorderData(show: false),
          lineTouchData: LineTouchData(
            enabled: true,
            touchTooltipData: LineTouchTooltipData(
              tooltipBgColor: AppColors.backgroundAlt,
              tooltipRoundedRadius: 8,
              tooltipBorder: BorderSide(
                color: AppColors.border,
                width: 1,
              ),
              tooltipPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
              getTooltipItems: (touchedSpots) {
                return touchedSpots.map((spot) {
                  return LineTooltipItem(
                    currencyFormat.format(spot.y),
                    TextStyle(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w500,
                    ),
                  );
                }).toList();
              },
            ),
          ),
          lineBarsData: [
            LineChartBarData(
              spots: spots,
              isCurved: true,
              color: AppColors.primaryBlue,
              barWidth: 2,
              isStrokeCapRound: true,
              dotData: FlDotData(
                show: true,
                getDotPainter: (spot, percent, barData, index) {
                  return FlDotCirclePainter(
                    radius: 4,
                    color: AppColors.background,
                    strokeWidth: 2,
                    strokeColor: AppColors.primaryBlue,
                  );
                },
              ),
              belowBarData: BarAreaData(
                show: true,
                color: AppColors.primaryBlue.withOpacity(0.1),
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppColors.primaryBlue.withOpacity(0.2),
                    AppColors.primaryBlue.withOpacity(0.0),
                  ],
                ),
              ),
            ),
          ],
          minX: 0,
          maxX: 7,
          minY: minY,
          maxY: maxY,
        ),
      ),
    );
  }

  BarChartGroupData _buildBarGroup(int x, double value, Color color) {
    return BarChartGroupData(
      x: x,
      barRods: [
        BarChartRodData(
          toY: value,
          color: color,
          width: 16,
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(4),
            bottom: Radius.circular(4),
          ),
        ),
      ],
    );
  }

  Widget _buildComplianceCard({
    required String title,
    required String value,
    required String subtitle,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  color: AppColors.textPrimary,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 4),
              OverlayInfoButton(
                description: _getComplianceDescription(title),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  String _getComplianceDescription(String title) {
    switch (title) {
      case 'Position Size':
        return 'Monitors adherence to position sizing rules\n'
            'Tracks violations of maximum position limits\n'
            'Helps maintain proper risk management';
      case 'Stop Loss':
        return 'Tracks use of stop loss orders\n'
            'Monitors risk per trade compliance\n'
            'Ensures capital preservation rules';
      case 'Risk/Reward':
        return 'Measures trade setup quality\n'
            'Ensures minimum reward-to-risk ratios\n'
            'Promotes high-probability trading';
      case 'Profit Factor':
        return 'Ratio of gross profits to gross losses\n'
            'Indicates trading strategy effectiveness\n'
            'Higher values show better performance';
      default:
        return 'No description available';
    }
  }

  Widget _buildMetricCard({
    required String title,
    required String value,
    required String subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.backgroundAlt,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: AppColors.textPrimary,
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  String _getDateRangeText() {
    final timePeriod = ref.watch(timePeriodProvider);

    if (timePeriod.selectedTimeframe != 'Custom') {
      return timePeriod.selectedTimeframe;
    }

    if (timePeriod.startDate == null || timePeriod.endDate == null) {
      return 'Select Date Range';
    }

    return '${DateFormat('M/d/y').format(timePeriod.startDate!)} - ${DateFormat('M/d/y').format(timePeriod.endDate!)}';
  }

  void _showFilterBottomSheet(BuildContext context) {
    final theme = Theme.of(context);
    final timePeriodNotifier = ref.read(timePeriodProvider.notifier);
    final timePeriod = ref.read(timePeriodProvider);

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.85,
          ),
          decoration: BoxDecoration(
            color: theme.scaffoldBackgroundColor,
            borderRadius: const BorderRadius.vertical(
              top: Radius.circular(20),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 8),
                width: 40,
                height: 4,
                alignment: Alignment.center,
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Date Range',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
              ),
              const Divider(height: 1),
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Text(
                        'Time Period',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: [
                          _buildTimeframeChip('Today'),
                          _buildTimeframeChip('Yesterday'),
                          _buildTimeframeChip('7D'),
                          _buildTimeframeChip('30D'),
                          _buildTimeframeChip('90D'),
                          _buildTimeframeChip('YTD'),
                          _buildTimeframeChip('All'),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Custom Range',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton.icon(
                              onPressed: () => _selectDate(context, true),
                              icon: const Icon(Icons.calendar_today),
                              label: Text(
                                timePeriod.startDate == null
                                    ? 'Start Date'
                                    : DateFormat('M/d/y')
                                        .format(timePeriod.startDate!),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: OutlinedButton.icon(
                              onPressed: () => _selectDate(context, false),
                              icon: const Icon(Icons.calendar_today),
                              label: Text(
                                timePeriod.endDate == null
                                    ? 'End Date'
                                    : DateFormat('M/d/y')
                                        .format(timePeriod.endDate!),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          timePeriodNotifier.updateTimePeriod(
                            timeframe: 'Today',
                            startDate: null,
                            endDate: null,
                          );
                          Navigator.pop(context);
                        },
                        child: const Text('Reset'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: FilledButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        child: const Text('Apply'),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTimeframeChip(String label) {
    final timePeriod = ref.watch(timePeriodProvider);
    final isSelected = timePeriod.selectedTimeframe == label;

    return FilterChip(
      selected: isSelected,
      label: Text(label),
      onSelected: (selected) {
        if (selected) {
          ref.read(timePeriodProvider.notifier).updateTimePeriod(
                timeframe: label,
                startDate: null,
                endDate: null,
              );
        }
      },
      backgroundColor: Colors.transparent,
      selectedColor: AppColors.primaryBlue.withOpacity(0.1),
      side: BorderSide(
        color:
            isSelected ? AppColors.primaryBlue : Colors.grey.withOpacity(0.3),
      ),
      labelStyle: TextStyle(
        color: isSelected ? AppColors.primaryBlue : Colors.grey,
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 8),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStart) async {
    final timePeriod = ref.read(timePeriodProvider);
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStart
          ? (timePeriod.startDate ?? DateTime.now())
          : (timePeriod.endDate ?? DateTime.now()),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      ref.read(timePeriodProvider.notifier).updateTimePeriod(
            timeframe: 'Custom',
            startDate: isStart ? picked : timePeriod.startDate,
            endDate: isStart ? timePeriod.endDate : picked,
          );
    }
  }

  Widget _buildAdvancedMetricsTab() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final timePeriod = ref.watch(timePeriodProvider);
    final filteredTrades = _filterTradesByTimeframe(_trades, timePeriod);
    final analytics = _analyticsService.calculateMetrics(
        filteredTrades.where((t) => t.status == TradeStatus.CLOSED).toList());

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAdvancedMetricItem(
            'Risk-Adjusted Return',
            analytics.volatility.sharpeRatio.toStringAsFixed(2),
            'Target: 2.0+',
            AppColors.successGreen,
            Icons.trending_up,
            isDarkMode,
          ),
          const SizedBox(height: 16),
          _buildAdvancedMetricItem(
            'Calmar Ratio',
            analytics.risk.calmarRatio.toStringAsFixed(2),
            'Target: 3.0+',
            AppColors.primaryBlue,
            Icons.show_chart,
            isDarkMode,
          ),
          const SizedBox(height: 16),
          _buildAdvancedMetricItem(
            'Volatility',
            '${analytics.volatility.dailyVolatility.toStringAsFixed(2)}%',
            'Target: < 15%',
            AppColors.warningYellow,
            Icons.ssid_chart,
            isDarkMode,
          ),
        ],
      ),
    );
  }

  Widget _buildPatternsTab() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final timePeriod = ref.watch(timePeriodProvider);
    final filteredTrades = _filterTradesByTimeframe(_trades, timePeriod);
    final analytics = _analyticsService.calculateMetrics(
        filteredTrades.where((t) => t.status == TradeStatus.CLOSED).toList());

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Momentum',
            style: AppTextStyles.h3.copyWith(
              color: AppColors.textSecondary,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          _buildPatternMetricItem(
            'Win Streak',
            analytics.summary.maxWinStreak.toString(),
            AppColors.primaryBlue,
            Icons.trending_up_outlined,
            isDarkMode,
          ),
          const SizedBox(height: 12),
          _buildPatternMetricItem(
            'Consistency',
            '${analytics.summary.consistency.toStringAsFixed(2)}%',
            AppColors.successGreen,
            Icons.timeline,
            isDarkMode,
          ),
          const SizedBox(height: 24),
          Text(
            'Reversals',
            style: AppTextStyles.h3.copyWith(
              color: AppColors.textSecondary,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          _buildPatternMetricItem(
            'Recovery Rate',
            '${analytics.risk.recoveryRate.toStringAsFixed(2)}%',
            AppColors.successGreen,
            Icons.sync,
            isDarkMode,
          ),
          const SizedBox(height: 12),
          _buildPatternMetricItem(
            'Bounce Back',
            '${analytics.risk.bounceBack.toStringAsFixed(2)}%',
            AppColors.primaryBlue,
            Icons.trending_up,
            isDarkMode,
          ),
          const SizedBox(height: 24),
          Text(
            'Volatility',
            style: AppTextStyles.h3.copyWith(
              color: AppColors.textSecondary,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          _buildPatternMetricItem(
            'Daily Range',
            '${analytics.volatility.dailyVolatility.toStringAsFixed(2)}%',
            AppColors.warningYellow,
            Icons.bar_chart,
            isDarkMode,
          ),
          const SizedBox(height: 12),
          _buildPatternMetricItem(
            'Stability',
            '${analytics.volatility.stability.toStringAsFixed(2)}%',
            AppColors.primaryBlue,
            Icons.show_chart,
            isDarkMode,
          ),
        ],
      ),
    );
  }

  Widget _buildAdvancedMetricItem(
    String title,
    String value,
    String target,
    Color color,
    IconData icon,
    bool isDarkMode,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.backgroundAlt,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.h3.copyWith(
                    color: AppColors.textSecondary,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: AppTextStyles.h2.copyWith(
                    color: AppColors.textPrimary,
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  target,
                  style: AppTextStyles.body2.copyWith(
                    color: AppColors.textSecondary,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPatternMetricItem(
    String title,
    String value,
    Color color,
    IconData icon,
    bool isDarkMode,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.backgroundAlt,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.h3.copyWith(
                    color: AppColors.textSecondary,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: AppTextStyles.h2.copyWith(
                    color: AppColors.textPrimary,
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricItem(String label, String value, bool isDarkMode) {
    Color getMetricColor() {
      switch (label) {
        case 'Avg Win/Loss':
          return const Color(0xFF22C55E); // Green
        case 'Trade Count':
          return const Color(0xFF3B82F6); // Blue
        case 'Net P&L':
          return const Color(0xFFF59E0B); // Orange
        case 'Daily Win Rate':
          return const Color(0xFF8B5CF6); // Purple
        case 'Average Trade':
          return const Color(0xFF10B981); // Teal
        default:
          return const Color(0xFF3B82F6);
      }
    }

    final color = getMetricColor();

    return Container(
      margin: const EdgeInsets.only(right: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E293B) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDarkMode ? Colors.white10 : Colors.black.withOpacity(0.05),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                  color: isDarkMode ? Colors.white60 : Colors.grey[600],
                ),
              ),
              const SizedBox(width: 4),
              OverlayInfoButton(
                description: _getMetricDescription(label),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? Colors.white : const Color(0xFF1F2937),
            ),
          ),
        ],
      ),
    );
  }

  String _getMetricDescription(String label) {
    switch (label) {
      case 'Avg Win/Loss':
        return 'Average profit from winning trades vs\n'
            'average loss from losing trades.\n'
            'Helps evaluate trade sizing effectiveness.';
      case 'Trade Count':
        return 'Total number of trades executed\n'
            'during the selected time period.\n'
            'Indicates trading frequency and activity.';
      case 'Net P&L':
        return 'Total profit or loss after all fees\n'
            'and commissions are deducted.\n'
            'Shows overall trading performance.';
      case 'Daily Win Rate':
        return 'Percentage of profitable days vs\n'
            'total trading days.\n'
            'Measures daily trading consistency.';
      case 'Average Trade':
        return 'Mean profit or loss per trade\n'
            'across all executed trades.\n'
            'Indicates typical trade performance.';
      default:
        return 'No description available';
    }
  }
}
