import 'package:flutter/material.dart';
import 'package:trade_reviewer_flutter/features/settings/presentation/widgets/settings_bottom_sheet.dart';

class SettingsNavigation {
  static void showSettingsBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.94,
        minChildSize: 0.5,
        maxChildSize: 0.94,
        builder: (context, scrollController) => SettingsBottomSheet(
          scrollController: scrollController,
        ),
      ),
    );
  }

  static void navigateToSettingsScreen(BuildContext context, Widget screen) {
    Navigator.of(context).pop(); // Close bottom sheet
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => WillPopScope(
          onWillPop: () async {
            Navigator.of(context).pop(); // Pop current screen
            showSettingsBottomSheet(context); // Show settings bottom sheet
            return false;
          },
          child: screen,
        ),
      ),
    );
  }
}
