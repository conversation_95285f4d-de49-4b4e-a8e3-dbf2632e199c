import 'package:flutter/material.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'dart:math' as math;

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  String? expandedSection;
  late final List<_SettingsSection> _sections;
  bool isEditing = false;
  Map<String, dynamic> editedUser = {
    'name': 'User Name',
    'email': '<EMAIL>',
    'timezone': 'America/New_York',
    'tradingExperience': '1-3 years',
    'bio': '',
    'preferredMarkets': <String>['Stocks', 'Options'],
  };
  bool isSaving = false;

  @override
  void initState() {
    super.initState();
    _sections = [
      _SettingsSection(
        icon: Icons.person_outline,
        title: 'Profile',
        contentBuilder: (context) => _buildProfileContent(),
      ),
      _SettingsSection(
        icon: Icons.settings_outlined,
        title: 'General',
        contentBuilder: (context) => _buildGeneralContent(),
      ),
      _SettingsSection(
        icon: Icons.account_balance_wallet_outlined,
        title: 'Accounts',
        contentBuilder: (context) => _buildAccountsContent(),
      ),
      _SettingsSection(
        icon: Icons.notifications_outlined,
        title: 'Notifications',
        contentBuilder: (context) => _buildNotificationsContent(),
      ),
      _SettingsSection(
        icon: Icons.security_outlined,
        title: 'Security',
        contentBuilder: (context) => _buildSecurityContent(),
      ),
      _SettingsSection(
        icon: Icons.lock_outline,
        title: 'Privacy',
        contentBuilder: (context) => _buildPrivacyContent(),
      ),
      _SettingsSection(
        icon: Icons.storage_outlined,
        title: 'Data',
        contentBuilder: (context) => _buildDataManagementContent(),
      ),
      _SettingsSection(
        icon: Icons.bar_chart_outlined,
        title: 'Goals',
        contentBuilder: (context) => _buildGoalCardContent(),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: SingleChildScrollView(
          child: ListView.separated(
            padding: const EdgeInsets.all(16),
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _sections.length,
            separatorBuilder: (context, index) => const SizedBox(height: 8),
            itemBuilder: (context, index) => _buildAccordionSection(
              section: _sections[index],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAccordionSection({
    required _SettingsSection section,
  }) {
    final theme = Theme.of(context);
    final isExpanded = expandedSection == section.title;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      decoration: BoxDecoration(
        color: isExpanded ? theme.cardColor : Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.dividerColor.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                setState(() {
                  expandedSection = isExpanded ? null : section.title;
                });
              },
              borderRadius: BorderRadius.circular(12),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(
                      section.icon,
                      color: theme.textTheme.bodyLarge?.color,
                      size: 20,
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        section.title,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    AnimatedRotation(
                      duration: const Duration(milliseconds: 200),
                      turns: isExpanded ? 0.5 : 0,
                      child: Icon(
                        Icons.keyboard_arrow_down,
                        color: theme.textTheme.bodyLarge?.color,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          AnimatedCrossFade(
            firstChild: const SizedBox(height: 0),
            secondChild: Container(
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(
                    color: theme.dividerColor.withOpacity(0.1),
                  ),
                ),
              ),
              child: section.contentBuilder(context),
            ),
            crossFadeState: isExpanded
                ? CrossFadeState.showSecond
                : CrossFadeState.showFirst,
            duration: const Duration(milliseconds: 200),
          ),
        ],
      ),
    );
  }

  // Update the Goal card related methods
  Widget _buildCategoryCard({
    required String title,
    required Color color,
    required IconData icon,
    required List<_MetricItem> metrics,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.cardColor.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: theme.textTheme.titleSmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: metrics.length,
              separatorBuilder: (context, index) => const SizedBox(height: 12),
              itemBuilder: (context, index) {
                final metric = metrics[index];
                return _buildMetricItem(metric);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricItem(_MetricItem metric) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.cardColor.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  metric.name,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Switch.adaptive(
                value: metric.defaultValue,
                onChanged: (value) {},
                activeColor: AppColors.primaryBlue,
              ),
            ],
          ),
          if (metric.defaultValue) ...[
            const SizedBox(height: 8),
            Text(
              metric.description,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.textTheme.bodySmall?.color?.withOpacity(0.8),
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Goal',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.textTheme.bodySmall?.color
                              ?.withOpacity(0.8),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: theme.cardColor.withOpacity(0.5),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              '${metric.defaultGoal}${metric.unit}',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Icon(
                              Icons.edit_outlined,
                              size: 16,
                              color: theme.textTheme.bodyMedium?.color
                                  ?.withOpacity(0.8),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                _buildGauge(
                  progress: metric.defaultGoal / metric.max * 100,
                  color: AppColors.primaryBlue,
                  icon: Icons.check_circle_outline,
                  isDark: isDark,
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProfileContent() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile Header
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Avatar
              Stack(
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: AppColors.primaryBlue.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.person_outline,
                      size: 40,
                      color: AppColors.primaryBlue,
                    ),
                  ),
                  if (isEditing)
                    Positioned(
                      right: 0,
                      bottom: 0,
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: theme.cardColor,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: theme.dividerColor.withOpacity(0.2),
                          ),
                        ),
                        child: Icon(
                          Icons.camera_alt_outlined,
                          size: 16,
                          color: theme.textTheme.bodyMedium?.color
                              ?.withOpacity(0.8),
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(width: 24),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      editedUser['name'],
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      editedUser['email'],
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color:
                            theme.textTheme.bodySmall?.color?.withOpacity(0.8),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        if (!isEditing)
                          TextButton.icon(
                            onPressed: () {
                              setState(() {
                                isEditing = true;
                              });
                            },
                            icon: const Icon(Icons.edit_outlined, size: 16),
                            label: const Text('Edit Profile'),
                            style: TextButton.styleFrom(
                              foregroundColor:
                                  theme.textTheme.bodyMedium?.color,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              backgroundColor: theme.cardColor.withOpacity(0.5),
                            ),
                          )
                        else ...[
                          TextButton(
                            onPressed: () {
                              setState(() {
                                isEditing = false;
                              });
                            },
                            style: TextButton.styleFrom(
                              foregroundColor:
                                  theme.textTheme.bodyMedium?.color,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              backgroundColor: theme.cardColor.withOpacity(0.5),
                            ),
                            child: const Text('Cancel'),
                          ),
                          const SizedBox(width: 12),
                          TextButton(
                            onPressed: isSaving
                                ? null
                                : () {
                                    setState(() {
                                      isSaving = true;
                                      // TODO: Implement save functionality
                                      Future.delayed(const Duration(seconds: 1),
                                          () {
                                        if (mounted) {
                                          setState(() {
                                            isSaving = false;
                                            isEditing = false;
                                          });
                                        }
                                      });
                                    });
                                  },
                            style: TextButton.styleFrom(
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              backgroundColor: AppColors.primaryBlue,
                            ),
                            child:
                                Text(isSaving ? 'Saving...' : 'Save Changes'),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 32),

          // Profile Information
          Text(
            'Profile Information',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 24),

          // Basic Information
          LayoutBuilder(
            builder: (context, constraints) {
              final isMobile = constraints.maxWidth < 600;
              return Column(
                children: [
                  if (isMobile) ...[
                    _buildTextField(
                      label: 'Full Name',
                      value: editedUser['name'],
                      onChanged: (value) {
                        if (isEditing) {
                          setState(() {
                            editedUser['name'] = value;
                          });
                        }
                      },
                      enabled: isEditing,
                    ),
                    const SizedBox(height: 24),
                    _buildTextField(
                      label: 'Email',
                      value: editedUser['email'],
                      onChanged: (value) {
                        if (isEditing) {
                          setState(() {
                            editedUser['email'] = value;
                          });
                        }
                      },
                      enabled: isEditing,
                    ),
                    const SizedBox(height: 24),
                    _buildDropdownField(
                      label: 'Time Zone',
                      value: editedUser['timezone'],
                      items: const [
                        'America/New_York',
                        'America/Chicago',
                        'America/Denver',
                        'America/Los_Angeles',
                      ],
                      itemLabels: const [
                        'Eastern Time (ET)',
                        'Central Time (CT)',
                        'Mountain Time (MT)',
                        'Pacific Time (PT)',
                      ],
                      enabled: isEditing,
                      onChanged: (value) {
                        if (isEditing && value != null) {
                          setState(() {
                            editedUser['timezone'] = value;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 24),
                    _buildDropdownField(
                      label: 'Trading Experience',
                      value: editedUser['tradingExperience'],
                      items: const [
                        '< 1 year',
                        '1-3 years',
                        '3-5 years',
                        '5+ years',
                      ],
                      enabled: isEditing,
                      onChanged: (value) {
                        if (isEditing && value != null) {
                          setState(() {
                            editedUser['tradingExperience'] = value;
                          });
                        }
                      },
                    ),
                  ] else ...[
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: _buildTextField(
                            label: 'Full Name',
                            value: editedUser['name'],
                            onChanged: (value) {
                              if (isEditing) {
                                setState(() {
                                  editedUser['name'] = value;
                                });
                              }
                            },
                            enabled: isEditing,
                          ),
                        ),
                        const SizedBox(width: 24),
                        Expanded(
                          child: _buildTextField(
                            label: 'Email',
                            value: editedUser['email'],
                            onChanged: (value) {
                              if (isEditing) {
                                setState(() {
                                  editedUser['email'] = value;
                                });
                              }
                            },
                            enabled: isEditing,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: _buildDropdownField(
                            label: 'Time Zone',
                            value: editedUser['timezone'],
                            items: const [
                              'America/New_York',
                              'America/Chicago',
                              'America/Denver',
                              'America/Los_Angeles',
                            ],
                            itemLabels: const [
                              'Eastern Time (ET)',
                              'Central Time (CT)',
                              'Mountain Time (MT)',
                              'Pacific Time (PT)',
                            ],
                            enabled: isEditing,
                            onChanged: (value) {
                              if (isEditing && value != null) {
                                setState(() {
                                  editedUser['timezone'] = value;
                                });
                              }
                            },
                          ),
                        ),
                        const SizedBox(width: 24),
                        Expanded(
                          child: _buildDropdownField(
                            label: 'Trading Experience',
                            value: editedUser['tradingExperience'],
                            items: const [
                              '< 1 year',
                              '1-3 years',
                              '3-5 years',
                              '5+ years',
                            ],
                            enabled: isEditing,
                            onChanged: (value) {
                              if (isEditing && value != null) {
                                setState(() {
                                  editedUser['tradingExperience'] = value;
                                });
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              );
            },
          ),
          const SizedBox(height: 24),

          // Bio
          _buildTextField(
            label: 'Bio',
            value: editedUser['bio'],
            onChanged: (value) {
              if (isEditing) {
                setState(() {
                  editedUser['bio'] = value;
                });
              }
            },
            enabled: isEditing,
            maxLines: 4,
          ),
          const SizedBox(height: 24),

          // Preferred Markets
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Preferred Markets',
                style: theme.textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  'Stocks',
                  'Options',
                  'Futures',
                  'Forex',
                  'Crypto',
                ].map((market) {
                  final isSelected =
                      editedUser['preferredMarkets'].contains(market);
                  return InkWell(
                    onTap: isEditing
                        ? () {
                            setState(() {
                              if (isSelected) {
                                editedUser['preferredMarkets'].remove(market);
                              } else {
                                editedUser['preferredMarkets'].add(market);
                              }
                            });
                          }
                        : null,
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? AppColors.primaryBlue.withOpacity(0.1)
                            : theme.cardColor.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isSelected
                              ? AppColors.primaryBlue.withOpacity(0.2)
                              : theme.dividerColor.withOpacity(0.2),
                        ),
                      ),
                      child: Text(
                        market,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: isSelected
                              ? AppColors.primaryBlue
                              : theme.textTheme.bodyMedium?.color,
                          fontWeight: isSelected ? FontWeight.w500 : null,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Update the _buildTextField method to include onChanged and enabled parameters
  Widget _buildTextField({
    required String label,
    String? value,
    String? hint,
    bool isPassword = false,
    String? prefix,
    ValueChanged<String>? onChanged,
    bool enabled = true,
    int? maxLines,
  }) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          initialValue: value,
          obscureText: isPassword,
          enabled: enabled,
          maxLines: maxLines ?? 1,
          style: theme.textTheme.bodyMedium,
          onChanged: onChanged,
          decoration: InputDecoration(
            hintText: hint,
            prefixText: prefix,
            hintStyle: TextStyle(
              color: theme.textTheme.bodySmall?.color?.withOpacity(0.6),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: theme.dividerColor.withOpacity(0.2),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: theme.dividerColor.withOpacity(0.2),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: AppColors.primaryBlue,
                width: 1.5,
              ),
            ),
            filled: true,
            fillColor: theme.cardColor.withOpacity(0.5),
          ),
        ),
      ],
    );
  }

  // Update the _buildDropdownField method to include onChanged and enabled parameters
  Widget _buildDropdownField({
    required String label,
    required String value,
    required List<String> items,
    List<String>? itemLabels,
    ValueChanged<String?>? onChanged,
    bool enabled = true,
  }) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: value,
          items: List.generate(
            items.length,
            (index) => DropdownMenuItem(
              value: items[index],
              child: Text(
                itemLabels?[index] ?? items[index],
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: enabled
                      ? theme.textTheme.bodyMedium?.color
                      : theme.textTheme.bodyMedium?.color?.withOpacity(0.6),
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          onChanged: enabled ? onChanged : null,
          isExpanded: true,
          decoration: InputDecoration(
            isDense: true,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 12,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: theme.dividerColor.withOpacity(0.2),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: theme.dividerColor.withOpacity(0.2),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: AppColors.primaryBlue,
                width: 1.5,
              ),
            ),
            filled: true,
            fillColor: theme.cardColor.withOpacity(0.5),
          ),
          icon: Icon(
            Icons.keyboard_arrow_down,
            color: theme.textTheme.bodyLarge?.color?.withOpacity(0.8),
            size: 20,
          ),
          dropdownColor: theme.cardColor,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: enabled
                ? theme.textTheme.bodyMedium?.color
                : theme.textTheme.bodyMedium?.color?.withOpacity(0.6),
          ),
        ),
      ],
    );
  }

  Widget _buildGeneralContent() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTextField(
            label: 'Full Name',
            value: 'User Name',
          ),
          const SizedBox(height: 24),
          _buildTextField(
            label: 'Email',
            value: '<EMAIL>',
          ),
          const SizedBox(height: 24),
          _buildDropdownField(
            label: 'Time Zone',
            value: 'America/New_York',
            items: const [
              'America/New_York',
              'America/Chicago',
              'America/Denver',
              'America/Los_Angeles',
            ],
          ),
          const SizedBox(height: 24),
          _buildDropdownField(
            label: 'Theme',
            value: 'system',
            items: const ['system', 'light', 'dark'],
            itemLabels: const ['System Default', 'Light Mode', 'Dark Mode'],
          ),
        ],
      ),
    );
  }

  Widget _buildAccountsContent() {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Trading Accounts',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 24),
          _buildTextField(
            label: 'Account Name',
            hint: 'Enter account name',
          ),
          const SizedBox(height: 24),
          _buildTextField(
            label: 'Initial Capital',
            hint: 'Enter initial capital',
            prefix: '\$',
          ),
          const SizedBox(height: 24),
          _buildDropdownField(
            label: 'Account Type',
            value: 'live',
            items: const ['live', 'demo', 'paper'],
            itemLabels: const ['Live Trading', 'Demo Account', 'Paper Trading'],
          ),
          const SizedBox(height: 24),
          _buildTextField(
            label: 'Broker',
            hint: 'Enter broker name',
          ),
          const SizedBox(height: 32),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {},
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryBlue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 0,
              ),
              child: const Text('Add Account'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationsContent() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSettingTile(
            title: 'Email Notifications',
            subtitle: 'Receive email notifications for important updates',
            onTap: () {},
          ),
          _buildSettingTile(
            title: 'Trade Alerts',
            subtitle: 'Get notified about trade executions and updates',
            onTap: () {},
          ),
          _buildSettingTile(
            title: 'Goal Alerts',
            subtitle:
                'Receive notifications when goals are met or need attention',
            onTap: () {},
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityContent() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTextField(
            label: 'Current Password',
            isPassword: true,
          ),
          const SizedBox(height: 24),
          _buildTextField(
            label: 'New Password',
            isPassword: true,
          ),
          const SizedBox(height: 24),
          _buildTextField(
            label: 'Confirm New Password',
            isPassword: true,
          ),
          const SizedBox(height: 32),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {},
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryBlue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 0,
              ),
              child: const Text('Update Password'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrivacyContent() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSettingTile(
            title: 'Data Collection',
            subtitle: 'Control how your trading data is collected and used',
            onTap: () {},
          ),
          _buildSettingTile(
            title: 'Data Sharing',
            subtitle: 'Manage how your data is shared with third parties',
            onTap: () {},
          ),
          _buildSettingTile(
            title: 'Account Privacy',
            subtitle: 'Control who can view your trading activity',
            onTap: () {},
          ),
        ],
      ),
    );
  }

  Widget _buildDataManagementContent() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSettingTile(
            title: 'Import Trades',
            subtitle: 'Import trades from CSV or broker statements',
            onTap: () {},
          ),
          _buildSettingTile(
            title: 'Export Data',
            subtitle: 'Download your trading data and analytics',
            onTap: () {},
          ),
          _buildSettingTile(
            title: 'Backup Settings',
            subtitle: 'Configure automatic data backups',
            onTap: () {},
          ),
          _buildSettingTile(
            title: 'Clear Data',
            subtitle: 'Remove all trades and analytics data',
            onTap: () {},
          ),
        ],
      ),
    );
  }

  Widget _buildGoalCardContent() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Goal Card Settings',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              TextButton.icon(
                onPressed: () {
                  // TODO: Implement reset functionality
                },
                icon: const Icon(Icons.refresh, size: 16),
                label: const Text('Reset to Default'),
                style: TextButton.styleFrom(
                  foregroundColor: theme.textTheme.bodyMedium?.color,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          LayoutBuilder(
            builder: (context, constraints) {
              final isMobile = constraints.maxWidth < 768;
              return GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: isMobile ? 1 : 2,
                mainAxisSpacing: 16,
                crossAxisSpacing: 16,
                childAspectRatio: isMobile ? 1.1 : 0.85,
                children: [
                  _buildCategoryCard(
                    title: 'PERFORMANCE',
                    color: const Color(0xFF3B82F6),
                    icon: Icons.speed_outlined,
                    metrics: [
                      _MetricItem(
                        id: 'overallProgress',
                        name: 'Overall Progress',
                        description: 'Combined score of all trading metrics',
                        defaultValue: true,
                        defaultGoal: 75,
                        unit: '%',
                        min: 0,
                        max: 100,
                      ),
                    ],
                  ),
                  _buildCategoryCard(
                    title: 'PROFIT',
                    color: const Color(0xFF16A34A),
                    icon: Icons.trending_up_outlined,
                    metrics: [
                      _MetricItem(
                        id: 'winRate',
                        name: 'Win Rate',
                        description: 'Percentage of profitable trades',
                        defaultValue: true,
                        defaultGoal: 55,
                        unit: '%',
                        min: 0,
                        max: 100,
                      ),
                      _MetricItem(
                        id: 'profitFactor',
                        name: 'Profit Factor',
                        description: 'Ratio of gross profits to gross losses',
                        defaultValue: true,
                        defaultGoal: 1.5,
                        unit: '',
                        min: 1,
                        max: 10,
                        step: 0.1,
                      ),
                      _MetricItem(
                        id: 'avgWinLoss',
                        name: 'Average Win/Loss',
                        description:
                            'Comparison of average winning and losing trades',
                        defaultValue: true,
                        defaultGoal: 1.5,
                        unit: '',
                        min: 0.1,
                        max: 10,
                        step: 0.1,
                      ),
                    ],
                  ),
                  _buildCategoryCard(
                    title: 'GUARD',
                    color: const Color(0xFFEF4444),
                    icon: Icons.shield_outlined,
                    metrics: [
                      _MetricItem(
                        id: 'sharpeRatio',
                        name: 'Sharpe Ratio',
                        description: 'Risk-adjusted return measurement',
                        defaultValue: true,
                        defaultGoal: 1.5,
                        unit: '',
                        min: 0,
                        max: 5,
                        step: 0.1,
                      ),
                      _MetricItem(
                        id: 'riskReward',
                        name: 'Risk/Reward',
                        description:
                            'Ratio of potential profit to potential loss',
                        defaultValue: true,
                        defaultGoal: 2,
                        unit: '',
                        min: 0.5,
                        max: 10,
                        step: 0.1,
                      ),
                      _MetricItem(
                        id: 'maxDrawdown',
                        name: 'Max Drawdown',
                        description: 'Largest peak-to-trough decline',
                        defaultValue: true,
                        defaultGoal: 10,
                        unit: '%',
                        min: 1,
                        max: 50,
                      ),
                    ],
                  ),
                  _buildCategoryCard(
                    title: 'FOCUS',
                    color: const Color(0xFF8B5CF6),
                    icon: Icons.track_changes_outlined,
                    metrics: [
                      _MetricItem(
                        id: 'consecutiveWins',
                        name: 'Consecutive Wins',
                        description: 'Number of winning trades in a row',
                        defaultValue: true,
                        defaultGoal: 5,
                        unit: '',
                        min: 1,
                        max: 20,
                      ),
                      _MetricItem(
                        id: 'tradingFrequency',
                        name: 'Trading Frequency',
                        description: 'How often trades are placed',
                        defaultValue: true,
                        defaultGoal: 85,
                        unit: '%',
                        min: 0,
                        max: 100,
                      ),
                      _MetricItem(
                        id: 'avgDuration',
                        name: 'Average Duration',
                        description: 'Average time spent in trades',
                        defaultValue: true,
                        defaultGoal: 45,
                        unit: 'min',
                        min: 1,
                        max: 480,
                      ),
                    ],
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSettingTile({
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 4),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.textTheme.bodySmall?.color?.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
          TextButton(
            onPressed: onTap,
            style: TextButton.styleFrom(
              foregroundColor: AppColors.primaryBlue,
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 8,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('Configure'),
          ),
        ],
      ),
    );
  }

  Widget _buildGauge({
    required Color color,
    required double progress,
    required IconData icon,
    required bool isDark,
  }) {
    return Container(
      width: 44,
      height: 44,
      decoration: BoxDecoration(
        color: Colors.transparent,
        shape: BoxShape.circle,
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 2,
        ),
      ),
      child: Stack(
        children: [
          // Dashed background
          CustomPaint(
            painter: DashedCirclePainter(color: color),
            size: const Size.square(double.infinity),
          ),
          // Progress arc
          CustomPaint(
            painter: GaugeProgressPainter(
              progress: progress,
              color: color,
            ),
            size: const Size.square(double.infinity),
          ),
          // Icon
          Center(
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
        ],
      ),
    );
  }
}

class _SettingsSection {
  final IconData icon;
  final String title;
  final Widget Function(BuildContext) contentBuilder;

  const _SettingsSection({
    required this.icon,
    required this.title,
    required this.contentBuilder,
  });
}

class _MetricItem {
  final String id;
  final String name;
  final String description;
  final bool defaultValue;
  final double defaultGoal;
  final String unit;
  final double min;
  final double max;
  final double? step;

  const _MetricItem({
    required this.id,
    required this.name,
    required this.description,
    required this.defaultValue,
    required this.defaultGoal,
    required this.unit,
    required this.min,
    required this.max,
    this.step,
  });
}

class DashedCirclePainter extends CustomPainter {
  final Color color;

  DashedCirclePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;
    final paint = Paint()
      ..color = color.withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    const dashCount = 20;
    const dashLength = 0.1;
    for (var i = 0; i < dashCount; i++) {
      final startAngle = (i * 2 * math.pi / dashCount);
      final endAngle = startAngle + (2 * math.pi * dashLength / dashCount);
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        endAngle - startAngle,
        false,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(DashedCirclePainter oldDelegate) =>
      oldDelegate.color != color;
}

class GaugeProgressPainter extends CustomPainter {
  final double progress;
  final Color color;

  GaugeProgressPainter({
    required this.progress,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.4;
    final startAngle = -math.pi / 2;
    final sweepAngle = 2 * math.pi * (progress / 100);

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle,
      sweepAngle,
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(GaugeProgressPainter oldDelegate) =>
      oldDelegate.progress != progress || oldDelegate.color != color;
}
