import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/providers/auth_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart'
    show Settings, DisplaySettings, PnLDisplayMode;

class GeneralSettingsScreen extends ConsumerStatefulWidget {
  const GeneralSettingsScreen({super.key});

  @override
  ConsumerState<GeneralSettingsScreen> createState() =>
      _GeneralSettingsScreenState();
}

class _GeneralSettingsScreenState extends ConsumerState<GeneralSettingsScreen> {
  bool _isLoading = true;
  Settings? _settings;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final userId = ref.read(currentUserIdProvider);
      print('Loading settings for user: $userId');

      final settingsService = ref.read(settingsServiceProvider);

      // Try to get settings first
      try {
        final settings = await settingsService.getSettings();
        setState(() {
          _settings = settings;
          _isLoading = false;
        });
        print('Loaded existing settings: ${settings.toJson()}');
        return;
      } catch (e) {
        print('Settings not initialized, initializing now: $e');
        // Initialize only if getting settings failed
        await settingsService.initialize(userId);
        final settings = await settingsService.getSettings();
        print('Initialized new settings: ${settings.toJson()}');
        setState(() {
          _settings = settings;
          _isLoading = false;
        });
      }
    } catch (e, stackTrace) {
      print('Error loading settings: $e');
      print('Stack trace: $stackTrace');
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _updateDisplaySettings(DisplaySettings newDisplay) async {
    try {
      final settingsService = ref.read(settingsServiceProvider);
      final updatedSettings =
          await settingsService.updateDisplaySettings(newDisplay);

      setState(() => _settings = updatedSettings);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to update settings'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('General'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final displaySettings = _settings?.display;
    if (displaySettings == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('General'),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text('Settings not available'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadSettings,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('General'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Appearance',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.dividerColor.withOpacity(0.2),
                ),
              ),
              child: Column(
                children: [
                  ListTile(
                    title: const Text('Theme'),
                    subtitle: Text(displaySettings.theme),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () async {
                      final newTheme = await showDialog<String>(
                        context: context,
                        builder: (context) => SimpleDialog(
                          title: const Text('Select Theme'),
                          children: [
                            SimpleDialogOption(
                              onPressed: () => Navigator.pop(context, 'system'),
                              child: const Text('System Default'),
                            ),
                            SimpleDialogOption(
                              onPressed: () => Navigator.pop(context, 'light'),
                              child: const Text('Light'),
                            ),
                            SimpleDialogOption(
                              onPressed: () => Navigator.pop(context, 'dark'),
                              child: const Text('Dark'),
                            ),
                          ],
                        ),
                      );

                      if (newTheme != null) {
                        final newDisplay = displaySettings.copyWith(
                          theme: newTheme,
                        );
                        await _updateDisplaySettings(newDisplay);
                      }
                    },
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Compact Mode'),
                    subtitle: const Text('Show more content in less space'),
                    trailing: Switch.adaptive(
                      value: displaySettings.compactMode,
                      onChanged: (value) async {
                        final newDisplay = displaySettings.copyWith(
                          compactMode: value,
                        );
                        await _updateDisplaySettings(newDisplay);
                      },
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),
            Text(
              'Display',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.dividerColor.withOpacity(0.2),
                ),
              ),
              child: Column(
                children: [
                  ListTile(
                    title: const Text('Show Running P&L'),
                    subtitle: const Text('Display running profit/loss'),
                    trailing: Switch.adaptive(
                      value: displaySettings.showRunningPnL,
                      onChanged: (value) async {
                        final newDisplay = displaySettings.copyWith(
                          showRunningPnL: value,
                        );
                        await _updateDisplaySettings(newDisplay);
                      },
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Show Equity Curve'),
                    subtitle: const Text('Display equity curve on dashboard'),
                    trailing: Switch.adaptive(
                      value: displaySettings.showEquityCurve,
                      onChanged: (value) async {
                        final newDisplay = displaySettings.copyWith(
                          showEquityCurve: value,
                        );
                        await _updateDisplaySettings(newDisplay);
                      },
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Show Trade Statistics'),
                    subtitle: const Text('Display trade statistics'),
                    trailing: Switch.adaptive(
                      value: displaySettings.showTradeStatistics,
                      onChanged: (value) async {
                        final newDisplay = displaySettings.copyWith(
                          showTradeStatistics: value,
                        );
                        await _updateDisplaySettings(newDisplay);
                      },
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),
            Text(
              'Format',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.dividerColor.withOpacity(0.2),
                ),
              ),
              child: Column(
                children: [
                  ListTile(
                    title: const Text('P&L Display Mode'),
                    subtitle: Text(displaySettings.pnlDisplayMode.name),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () async {
                      final newMode = await showDialog<PnLDisplayMode>(
                        context: context,
                        builder: (context) => SimpleDialog(
                          title: const Text('Select P&L Display Mode'),
                          children: PnLDisplayMode.values
                              .map(
                                (mode) => SimpleDialogOption(
                                  onPressed: () => Navigator.pop(context, mode),
                                  child: Text(mode.name),
                                ),
                              )
                              .toList(),
                        ),
                      );

                      if (newMode != null) {
                        final newDisplay = displaySettings.copyWith(
                          pnlDisplayMode: newMode,
                        );
                        await _updateDisplaySettings(newDisplay);
                      }
                    },
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Date Format'),
                    subtitle: Text(displaySettings.dateFormat),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () async {
                      final newFormat = await showDialog<String>(
                        context: context,
                        builder: (context) => SimpleDialog(
                          title: const Text('Select Date Format'),
                          children: [
                            SimpleDialogOption(
                              onPressed: () =>
                                  Navigator.pop(context, 'yyyy-MM-dd'),
                              child: const Text('YYYY-MM-DD'),
                            ),
                            SimpleDialogOption(
                              onPressed: () =>
                                  Navigator.pop(context, 'MM/dd/yyyy'),
                              child: const Text('MM/DD/YYYY'),
                            ),
                            SimpleDialogOption(
                              onPressed: () =>
                                  Navigator.pop(context, 'dd/MM/yyyy'),
                              child: const Text('DD/MM/YYYY'),
                            ),
                          ],
                        ),
                      );

                      if (newFormat != null) {
                        final newDisplay = displaySettings.copyWith(
                          dateFormat: newFormat,
                        );
                        await _updateDisplaySettings(newDisplay);
                      }
                    },
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Time Format'),
                    subtitle: Text(displaySettings.timeFormat),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () async {
                      final newFormat = await showDialog<String>(
                        context: context,
                        builder: (context) => SimpleDialog(
                          title: const Text('Select Time Format'),
                          children: [
                            SimpleDialogOption(
                              onPressed: () =>
                                  Navigator.pop(context, 'HH:mm:ss'),
                              child: const Text('24-hour (HH:mm:ss)'),
                            ),
                            SimpleDialogOption(
                              onPressed: () =>
                                  Navigator.pop(context, 'hh:mm:ss a'),
                              child: const Text('12-hour (hh:mm:ss AM/PM)'),
                            ),
                          ],
                        ),
                      );

                      if (newFormat != null) {
                        final newDisplay = displaySettings.copyWith(
                          timeFormat: newFormat,
                        );
                        await _updateDisplaySettings(newDisplay);
                      }
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
