import 'package:flutter/material.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart' as core;
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_flutter/shared/widgets/profile_picture.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'dart:io';

class ProfileSettingsScreen extends ConsumerStatefulWidget {
  final String userId;

  const ProfileSettingsScreen({
    super.key,
    required this.userId,
  });

  @override
  ConsumerState<ProfileSettingsScreen> createState() =>
      _ProfileSettingsScreenState();
}

class _ProfileSettingsScreenState extends ConsumerState<ProfileSettingsScreen> {
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  bool _isSaving = false;
  String _selectedLanguage = 'English';
  String _selectedTimeZone = 'UTC-8 (Pacific Time)';
  core.User? _currentProfile;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadProfile();
    });
  }

  Future<void> _loadProfile() async {
    try {
      final profileService = ref.read(profileServiceProvider);
      final profile = await profileService.getProfile(widget.userId);
      if (profile != null) {
        setState(() {
          _currentProfile = profile;
          _nameController.text = profile.name;
          _emailController.text = profile.email;
          _selectedLanguage = profile.preferences.language;
          _selectedTimeZone = profile.preferences.timezone;
        });
      }
    } catch (e) {
      print('Error loading profile: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to load profile'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _saveProfile() async {
    try {
      setState(() => _isSaving = true);

      final updatedPreferences =
          (_currentProfile?.preferences ?? const core.UserPreferences())
              .copyWith(
        language: _selectedLanguage,
        timezone: _selectedTimeZone,
      );

      print('Saving profile with preferences: ${updatedPreferences.toJson()}');

      final updates = {
        'name': _nameController.text,
        'email': _emailController.text,
        'preferences': updatedPreferences,
      };

      print('Full update data: $updates');

      final profileService = ref.read(profileServiceProvider);
      final updatedProfile = await profileService.updateProfile(
        widget.userId,
        updates,
      );

      if (updatedProfile != null) {
        setState(() => _currentProfile = updatedProfile);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Profile updated successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }

        // Verify the update was persisted
        final verifyProfile = await profileService.getProfile(widget.userId);
        print('Verification - Retrieved profile: ${verifyProfile?.toJson()}');

        if (verifyProfile == null) {
          throw Exception('Profile verification failed - profile not found');
        }
      } else {
        throw Exception('Failed to update profile');
      }
    } catch (e) {
      print('Error saving profile: $e');
      print('Stack trace: ${StackTrace.current}');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save profile: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSaving = false);
      }
    }
  }

  void _showLanguagePicker() {
    final languages = ['English', 'Spanish', 'French', 'German', 'Chinese'];
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Language'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: languages.length,
            itemBuilder: (context, index) {
              final language = languages[index];
              return ListTile(
                title: Text(language),
                selected: _selectedLanguage == language,
                onTap: () {
                  setState(() => _selectedLanguage = language);
                  Navigator.pop(context);
                  _saveProfile();
                },
              );
            },
          ),
        ),
      ),
    );
  }

  void _showTimeZonePicker() {
    final timeZones = [
      'UTC-8 (Pacific Time)',
      'UTC-7 (Mountain Time)',
      'UTC-6 (Central Time)',
      'UTC-5 (Eastern Time)',
      'UTC+0 (GMT)',
      'UTC+1 (CET)',
      'UTC+8 (China/Singapore)',
    ];
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Time Zone'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: timeZones.length,
            itemBuilder: (context, index) {
              final timeZone = timeZones[index];
              return ListTile(
                title: Text(timeZone),
                selected: _selectedTimeZone == timeZone,
                onTap: () {
                  setState(() => _selectedTimeZone = timeZone);
                  Navigator.pop(context);
                  _saveProfile();
                },
              );
            },
          ),
        ),
      ),
    );
  }

  void _showImagePickerOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.photo_camera),
              title: const Text('Take a photo'),
              onTap: () {
                Navigator.pop(context);
                _pickImage(ImageSource.camera);
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('Choose from gallery'),
              onTap: () {
                Navigator.pop(context);
                _pickImage(ImageSource.gallery);
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: source,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        print('Image picked from: ${pickedFile.path}');

        // Get the app's documents directory for persistent storage
        final appDir = await getApplicationDocumentsDirectory();
        final profileImagesDir = Directory('${appDir.path}/profile_images');

        // Create the profile images directory if it doesn't exist
        if (!await profileImagesDir.exists()) {
          await profileImagesDir.create(recursive: true);
        }

        // Generate a unique filename with timestamp
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final fileName =
            'profile_${widget.userId}_$timestamp${path.extension(pickedFile.path)}';
        final savedImagePath = '${profileImagesDir.path}/$fileName';
        final relativeImagePath = 'profile_images/$fileName';

        print('Absolute path: $savedImagePath');
        print('Relative path: $relativeImagePath');

        // Delete existing profile image if it exists
        if (_currentProfile?.profileImageUrl != null) {
          try {
            final existingFile =
                File('${appDir.path}/${_currentProfile!.profileImageUrl}');
            if (await existingFile.exists()) {
              await existingFile.delete();
              print('Deleted existing profile image');
            }
          } catch (e) {
            print('Error deleting existing profile image: $e');
          }
        }

        // Copy new image
        final newImage = File(pickedFile.path);
        final bytes = await newImage.readAsBytes();
        if (bytes.isEmpty) {
          throw Exception('Selected image is empty');
        }

        final savedImage = await newImage.copy(savedImagePath);
        print('Saved new image to: ${savedImage.path}');

        // Verify the new image exists and is readable
        if (await savedImage.exists()) {
          final savedBytes = await savedImage.readAsBytes();
          if (savedBytes.isEmpty) {
            throw Exception('Saved image file is empty');
          }
          print(
              'Verified new image exists and is readable at: ${savedImage.path}');
        } else {
          print('ERROR: New image file does not exist after saving');
          throw Exception('Failed to save image file');
        }

        // Update profile with new image path
        final profileService = ref.read(profileServiceProvider);
        final updatedProfile = await profileService.updateProfile(
          widget.userId,
          {
            'profileImageUrl': relativeImagePath,
          },
        );

        if (updatedProfile != null) {
          print(
              'Profile updated with new image path: ${updatedProfile.profileImageUrl}');

          // Verify the profile update was persisted
          final verifiedProfile =
              await profileService.getProfile(widget.userId);
          if (verifiedProfile?.profileImageUrl != relativeImagePath) {
            throw Exception('Profile image path not persisted correctly');
          }

          setState(() {
            _currentProfile = updatedProfile;
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Profile picture updated successfully'),
                backgroundColor: Colors.green,
              ),
            );
          }

          // Force a rebuild of widgets that depend on the profile
          ref.invalidate(profileServiceProvider);
        } else {
          throw Exception('Failed to update profile with new image');
        }
      }
    } catch (e) {
      print('Error picking/saving image: $e');
      print('Stack trace: ${StackTrace.current}');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update profile picture: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: ProfilePicture(
                imageUrl: _currentProfile?.profileImageUrl,
                size: 80,
                onTap: _showImagePickerOptions,
                showEditButton: true,
              ),
            ),
            const SizedBox(height: 32),
            Text(
              'Personal Information',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.dividerColor.withOpacity(0.2),
                ),
              ),
              child: Column(
                children: [
                  _buildTextField(
                    label: 'Full Name',
                    controller: _nameController,
                  ),
                  const SizedBox(height: 16),
                  _buildTextField(
                    label: 'Email',
                    controller: _emailController,
                  ),
                  const SizedBox(height: 24),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isSaving ? null : _saveProfile,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryBlue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 16,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 0,
                      ),
                      child: Text(_isSaving ? 'Saving...' : 'Save Changes'),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),
            Text(
              'Preferences',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.dividerColor.withOpacity(0.2),
                ),
              ),
              child: Column(
                children: [
                  ListTile(
                    title: const Text('Language'),
                    subtitle: Text(_selectedLanguage),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: _showLanguagePicker,
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Time Zone'),
                    subtitle: Text(_selectedTimeZone),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: _showTimeZonePicker,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.dividerColor.withOpacity(0.2),
                ),
              ),
              child: ListTile(
                leading: const Icon(
                  Icons.logout,
                  color: Colors.red,
                ),
                title: const Text(
                  'Sign Out',
                  style: TextStyle(
                    color: Colors.red,
                  ),
                ),
                onTap: () {
                  // Show confirmation dialog
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('Sign Out'),
                      content: const Text('Are you sure you want to sign out?'),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: const Text('Cancel'),
                        ),
                        TextButton(
                          onPressed: () {
                            // TODO: Implement sign out logic
                            Navigator.pop(context); // Close dialog
                            context.go('/login'); // Navigate to login screen
                          },
                          style: TextButton.styleFrom(
                            foregroundColor: Colors.red,
                          ),
                          child: const Text('Sign Out'),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
  }) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 6),
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            isDense: true,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 10,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: theme.dividerColor.withOpacity(0.2),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: theme.dividerColor.withOpacity(0.2),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: AppColors.primaryBlue,
                width: 1.5,
              ),
            ),
            filled: true,
            fillColor: theme.cardColor.withOpacity(0.5),
          ),
        ),
      ],
    );
  }
}
