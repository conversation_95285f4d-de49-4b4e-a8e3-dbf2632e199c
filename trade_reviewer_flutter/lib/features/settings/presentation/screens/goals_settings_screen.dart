import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/providers/auth_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart'
    show Settings, GoalSettings;
import 'dart:math' as math;

class GoalsSettingsScreen extends ConsumerStatefulWidget {
  const GoalsSettingsScreen({super.key});

  @override
  ConsumerState<GoalsSettingsScreen> createState() =>
      _GoalsSettingsScreenState();
}

class _GoalsSettingsScreenState extends ConsumerState<GoalsSettingsScreen> {
  bool _isLoading = true;
  Settings? _settings;
  Map<String, bool> _activeMetrics = {
    'targetPnL': true,
    'winRate': true,
    'profitFactor': true,
    'averageRR': true,
    'maxDrawdown': true,
    'riskPerTrade': true,
    'maxOpenTrades': true,
    'tradesPerWeek': true,
    'journalEntries': true,
    'planAdherence': true,
  };

  Map<String, double> _metricGoals = {
    'targetPnL': 1000,
    'winRate': 60,
    'profitFactor': 2,
    'averageRR': 2,
    'maxDrawdown': 10,
    'riskPerTrade': 1,
    'maxOpenTrades': 3,
    'tradesPerWeek': 10,
    'journalEntries': 5,
    'planAdherence': 90,
  };

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final userId = ref.read(currentUserIdProvider);
      if (userId == null) {
        throw Exception('User ID not found');
      }

      final settingsService = ref.read(settingsServiceProvider);
      Settings? settings;

      try {
        settings = await settingsService.getSettings();
      } catch (e) {
        print('Settings not initialized, initializing now...');
        await settingsService.initialize(userId);
        settings = await settingsService.getSettings();
      }

      if (settings == null) {
        throw Exception('Failed to load settings');
      }

      // Initialize with default values if no goals exist
      if (settings.goals == null || settings.goals.activeMetrics.isEmpty) {
        await settingsService.updateGoalSettings(
          GoalSettings(
            activeMetrics: _activeMetrics,
            metricGoals: _metricGoals,
          ),
        );
        settings = await settingsService.getSettings();
      }

      if (!mounted) return;

      setState(() {
        _settings = settings;
        // Load saved metrics from settings
        if (settings?.goals != null) {
          _activeMetrics =
              Map<String, bool>.from(settings!.goals.activeMetrics);
          _metricGoals = Map<String, double>.from(settings.goals.metricGoals);
        }
        _isLoading = false;
      });
      print('Loaded settings: ${settings.toJson()}');
    } catch (e) {
      print('Error loading settings: $e');
      if (!mounted) return;
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load settings: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _updateMetricGoal(String metricId, double value) async {
    setState(() {
      _metricGoals[metricId] = value;
    });

    try {
      final settingsService = ref.read(settingsServiceProvider);
      await settingsService.updateGoalSettings(
        GoalSettings(
          activeMetrics: _activeMetrics,
          metricGoals: _metricGoals,
        ),
      );
    } catch (e) {
      print('Error updating metric goal: $e');
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to update metric: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _toggleMetric(String metricId) async {
    try {
      setState(() {
        _activeMetrics[metricId] = !_activeMetrics[metricId]!;
      });

      final settingsService = ref.read(settingsServiceProvider);
      await settingsService.updateGoalSettings(
        GoalSettings(
          activeMetrics: _activeMetrics,
          metricGoals: _metricGoals,
        ),
      );
    } catch (e) {
      print('Error toggling metric: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save metric toggle: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _resetCategory(String category) async {
    try {
      final defaultMetrics = <String, bool>{};
      final defaultGoals = <String, double>{};

      // Get metrics for this category
      final metrics = _getCategoryMetrics(category);
      for (final metric in metrics) {
        defaultMetrics[metric.id] = metric.defaultValue;
        defaultGoals[metric.id] = metric.defaultGoal;
      }

      // Update state and settings
      setState(() {
        _activeMetrics.addAll(defaultMetrics);
        _metricGoals.addAll(defaultGoals);
      });

      final settingsService = ref.read(settingsServiceProvider);
      await settingsService.updateGoalSettings(
        GoalSettings(
          activeMetrics: _activeMetrics,
          metricGoals: _metricGoals,
        ),
      );

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Reset $category goals to default values'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      print('Error resetting category: $e');
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to reset category: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  List<_MetricItem> _getCategoryMetrics(String category) {
    switch (category.toLowerCase()) {
      case 'performance':
        return [
          _MetricItem(
            id: 'targetPnL',
            name: 'Target P&L',
            description: 'Target profit and loss goal',
            defaultValue: true,
            defaultGoal: 1000.0,
            unit: '\$',
            min: 100,
            max: 100000,
            step: 100,
          ),
          _MetricItem(
            id: 'winRate',
            name: 'Win Rate',
            description: 'Percentage of winning trades',
            defaultValue: true,
            defaultGoal: 60.0,
            unit: '%',
            min: 0,
            max: 100,
          ),
          _MetricItem(
            id: 'profitFactor',
            name: 'Profit Factor',
            description: 'Ratio of gross profit to gross loss',
            defaultValue: true,
            defaultGoal: 2.0,
            unit: '',
            min: 0,
            max: 10,
            step: 0.1,
          ),
          _MetricItem(
            id: 'averageRR',
            name: 'Average R:R',
            description: 'Average reward to risk ratio',
            defaultValue: true,
            defaultGoal: 2.0,
            unit: '',
            min: 0,
            max: 10,
            step: 0.1,
          ),
        ];
      case 'risk management':
        return [
          _MetricItem(
            id: 'maxDrawdown',
            name: 'Max Drawdown',
            description: 'Maximum peak-to-trough decline',
            defaultValue: true,
            defaultGoal: 10.0,
            unit: '%',
            min: 0,
            max: 100,
          ),
          _MetricItem(
            id: 'riskPerTrade',
            name: 'Risk per Trade',
            description: 'Maximum risk per trade',
            defaultValue: true,
            defaultGoal: 1.0,
            unit: '%',
            min: 0,
            max: 10,
            step: 0.1,
          ),
          _MetricItem(
            id: 'maxOpenTrades',
            name: 'Max Open Trades',
            description: 'Maximum number of concurrent open trades',
            defaultValue: true,
            defaultGoal: 3.0,
            unit: '',
            min: 1,
            max: 10,
          ),
        ];
      case 'consistency':
        return [
          _MetricItem(
            id: 'tradesPerWeek',
            name: 'Trades per Week',
            description: 'Target number of trades per week',
            defaultValue: true,
            defaultGoal: 10.0,
            unit: '',
            min: 0,
            max: 100,
          ),
          _MetricItem(
            id: 'journalEntries',
            name: 'Journal Entries',
            description: 'Target number of journal entries per week',
            defaultValue: true,
            defaultGoal: 5.0,
            unit: '',
            min: 0,
            max: 50,
          ),
          _MetricItem(
            id: 'planAdherence',
            name: 'Plan Adherence',
            description: 'Percentage of trades following the trading plan',
            defaultValue: true,
            defaultGoal: 90.0,
            unit: '%',
            min: 0,
            max: 100,
          ),
        ];
      default:
        return [];
    }
  }

  Future<void> _showGoalValueEditor(_MetricItem metric) async {
    final controller =
        TextEditingController(text: metric.defaultGoal.toString());
    final formKey = GlobalKey<FormState>();

    final result = await showDialog<double>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit ${metric.name} Goal'),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(metric.description),
              const SizedBox(height: 16),
              TextFormField(
                controller: controller,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                decoration: InputDecoration(
                  labelText: 'Goal Value',
                  suffixText: metric.unit,
                  border: const OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a value';
                  }
                  final number = double.tryParse(value);
                  if (number == null) {
                    return 'Please enter a valid number';
                  }
                  if (number < metric.min) {
                    return 'Value must be at least ${metric.min}${metric.unit}';
                  }
                  if (number > metric.max) {
                    return 'Value must be at most ${metric.max}${metric.unit}';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              if (formKey.currentState?.validate() ?? false) {
                final value = double.parse(controller.text);
                Navigator.of(context).pop(value);
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );

    if (result != null) {
      await _updateMetricGoal(metric.id, result);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Goal Settings'),
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Goal Settings Help'),
                  content: const Text(
                    'Configure your trading goals for different metrics.\n\n'
                    'Toggle metrics on/off and set target values for each metric.\n\n'
                    'Use the reset button to restore default values for a category.',
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('OK'),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadSettings,
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  _buildCategoryCard(
                    mainTitle: 'PROFIT',
                    subtitle:
                        'Performance: specify goals of trading profitability',
                    icon: Icons.monetization_on,
                    color: const Color(0xFF22C55E),
                  ),
                  const SizedBox(height: 16),
                  _buildCategoryCard(
                    mainTitle: 'GUARD',
                    subtitle:
                        'Risk Management: set limits to protect your capital',
                    icon: Icons.shield,
                    color: const Color(0xFFB7C1CF),
                  ),
                  const SizedBox(height: 16),
                  _buildCategoryCard(
                    mainTitle: 'FOCUS',
                    subtitle:
                        'Consistency: define targets for trading discipline',
                    icon: Icons.center_focus_strong,
                    color: const Color(0xFF3B82F6),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildCategoryCard({
    required String mainTitle,
    required String subtitle,
    required IconData icon,
    required Color color,
  }) {
    final category = subtitle.split(':')[0];
    final metrics = _getCategoryMetrics(category);
    if (metrics.isEmpty) return const SizedBox.shrink();

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      SizedBox(
                        width: 40,
                        height: 40,
                        child: CustomPaint(
                          painter: CircleGaugePainter(
                            progress: 1.0,
                            color: color,
                          ),
                          child: Center(
                            child: Icon(icon, color: color, size: 20),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              mainTitle,
                              style: Theme.of(context)
                                  .textTheme
                                  .titleLarge
                                  ?.copyWith(
                                    color: color,
                                    fontWeight: FontWeight.w700,
                                    letterSpacing: 1.2,
                                  ),
                            ),
                            Text(
                              subtitle,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    color: Theme.of(context)
                                        .textTheme
                                        .bodySmall
                                        ?.color
                                        ?.withOpacity(0.7),
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () => _resetCategory(category),
                  tooltip: 'Reset to defaults',
                  padding: EdgeInsets.zero,
                  visualDensity: VisualDensity.compact,
                ),
              ],
            ),
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 8),
              child: Divider(height: 1),
            ),
            ...metrics
                .map((metric) => _buildMetricTile(metric, color))
                .toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricTile(_MetricItem metric, Color categoryColor) {
    final isActive = _activeMetrics[metric.id] ?? metric.defaultValue;
    final goal = _metricGoals[metric.id] ?? metric.defaultGoal;

    return Column(
      children: [
        ListTile(
          title: Text(metric.name),
          subtitle: Text(
            metric.description,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context)
                      .textTheme
                      .bodySmall
                      ?.color
                      ?.withOpacity(0.7),
                  fontSize: 11,
                ),
          ),
          trailing: Switch(
            value: isActive,
            onChanged: (value) => _updateMetricActive(metric.id, value),
            activeColor: categoryColor,
          ),
          dense: true,
          visualDensity: VisualDensity.compact,
        ),
        if (isActive) ...[
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 4),
            child: Row(
              children: [
                Expanded(
                  child: SliderTheme(
                    data: SliderThemeData(
                      activeTrackColor: categoryColor,
                      thumbColor: categoryColor,
                      inactiveTrackColor: categoryColor.withOpacity(0.2),
                    ),
                    child: Slider(
                      value: goal,
                      min: metric.min.toDouble(),
                      max: metric.max.toDouble(),
                      divisions:
                          ((metric.max - metric.min) ~/ (metric.step ?? 1))
                              .clamp(1, 100),
                      label: '${goal.toStringAsFixed(1)}${metric.unit}',
                      onChanged: (value) => _updateMetricGoal(metric.id, value),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${goal.toStringAsFixed(1)}${metric.unit}',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: categoryColor,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
        ],
      ],
    );
  }

  Future<void> _updateMetricActive(String metricId, bool value) async {
    setState(() {
      _activeMetrics[metricId] = value;
    });

    try {
      final settingsService = ref.read(settingsServiceProvider);
      await settingsService.updateGoalSettings(
        GoalSettings(
          activeMetrics: _activeMetrics,
          metricGoals: _metricGoals,
        ),
      );
    } catch (e) {
      print('Error updating metric active state: $e');
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to update metric: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}

class _MetricItem {
  final String id;
  final String name;
  final String description;
  final bool defaultValue;
  final double defaultGoal;
  final String unit;
  final double min;
  final double max;
  final double? step;

  const _MetricItem({
    required this.id,
    required this.name,
    required this.description,
    required this.defaultValue,
    required this.defaultGoal,
    required this.unit,
    required this.min,
    required this.max,
    this.step,
  });
}

class CircleGaugePainter extends CustomPainter {
  final double progress;
  final Color color;

  CircleGaugePainter({
    required this.progress,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.45;

    // Draw background circle
    final backgroundPaint = Paint()
      ..color = color.withOpacity(0.15)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4;
    canvas.drawCircle(center, radius, backgroundPaint);

    // Draw progress arc
    final progressPaint = Paint()
      ..shader = SweepGradient(
        colors: [
          color,
          color.withOpacity(0.8),
          color.withOpacity(0.6),
        ],
        stops: const [0.0, 0.5, 1.0],
        startAngle: -1.5708, // -90 degrees in radians
        endAngle: 4.71239, // 270 degrees in radians
      ).createShader(Rect.fromCircle(center: center, radius: radius))
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -1.5708, // Start from top (-90 degrees in radians)
      progress * 6.28319, // Full circle is 2*pi radians
      false,
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
