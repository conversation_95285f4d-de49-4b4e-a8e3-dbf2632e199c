import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/auth_provider.dart';
import 'package:trade_reviewer_flutter/core/models/settings.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart' as core;
import 'package:dio/dio.dart';
import 'dart:io';
import 'dart:convert';
import 'package:intl/intl.dart';
import 'package:trade_reviewer_flutter/core/theme/app_icons.dart';
import 'package:trade_reviewer_flutter/core/theme/app_logo.dart';

class TestConnectionResult {
  final String status;
  final String accountType;
  final List<String> availableData;
  final List<Map<String, dynamic>> availableAccounts;

  TestConnectionResult({
    required this.status,
    required this.accountType,
    required this.availableData,
    this.availableAccounts = const [],
  });
}

class AccountsSettingsScreen extends ConsumerStatefulWidget {
  const AccountsSettingsScreen({super.key});

  @override
  ConsumerState<AccountsSettingsScreen> createState() =>
      _AccountsSettingsScreenState();
}

class _AccountsSettingsScreenState
    extends ConsumerState<AccountsSettingsScreen> {
  late core.SettingsService _settingsService;
  Settings? _settings;
  bool _isLoading = true;
  bool _isSyncing = false;

  @override
  void initState() {
    super.initState();
    _settingsService = ref.read(settingsServiceProvider);
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      setState(() => _isLoading = true);

      // Get the current user ID
      final userId = ref.read(currentUserIdProvider);

      // Initialize settings service if needed
      await _settingsService.initialize(userId);

      // Get settings
      final settings = await _settingsService.getSettings();
      setState(() {
        _settings = Settings.fromCore(settings);
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading settings: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Error loading settings')),
        );
      }
      setState(() => _isLoading = false);
    }
  }

  Future<void> _handleAccountConnection(TradingAccount account) async {
    try {
      setState(() => _isLoading = true);
      if (account.status == AccountStatus.active.index) {
        await _settingsService.removeTradingAccount(account.id);
      } else {
        await _settingsService.addTradingAccount(account.toCore());
      }
      await _loadSettings();
    } catch (e) {
      print('Error handling account connection: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(
                  'Error ${account.status == AccountStatus.active.index ? 'disconnecting' : 'connecting'} account')),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _handleApiKeyAction(String action, ApiKey? apiKey) async {
    try {
      setState(() => _isLoading = true);
      switch (action) {
        case 'add':
          final result = await showDialog<ApiKey>(
            context: context,
            builder: (context) => const _ApiKeyDialog(),
          );
          if (result != null) {
            await _settingsService.addApiKey(result.toCore());
          }
          break;
        case 'edit':
          if (apiKey != null) {
            final result = await showDialog<ApiKey>(
              context: context,
              builder: (context) => _ApiKeyDialog(apiKey: apiKey),
            );
            if (result != null) {
              await _settingsService.updateApiKey(result.toCore());
            }
          }
          break;
        case 'delete':
          if (apiKey != null) {
            final confirm = await showDialog<bool>(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('Delete API Key'),
                content: Text(
                    'Are you sure you want to delete the API key "${apiKey.name}"?'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context, false),
                    child: const Text('Cancel'),
                  ),
                  TextButton(
                    style: TextButton.styleFrom(foregroundColor: Colors.red),
                    onPressed: () => Navigator.pop(context, true),
                    child: const Text('Delete'),
                  ),
                ],
              ),
            );
            if (confirm == true) {
              await _settingsService.removeApiKey(apiKey.id);
            }
          }
          break;
      }
      await _loadSettings();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Error managing API key')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _handleAddAccount() async {
    try {
      final result = await showDialog<TradingAccount>(
        context: context,
        builder: (context) => const _AddAccountDialog(),
      );
      if (result != null) {
        setState(() => _isLoading = true);
        await _settingsService.addTradingAccount(result.toCore());
        await _loadSettings();
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Error adding account')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _syncAccounts() async {
    try {
      setState(() => _isSyncing = true);
      // TODO: Implement account sync
      await Future.delayed(const Duration(seconds: 2)); // Simulated sync
      await _loadSettings();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Accounts synced successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Error syncing accounts')),
      );
    } finally {
      setState(() => _isSyncing = false);
    }
  }

  Future<void> _handleEditAccount(TradingAccount account) async {
    try {
      final result = await showDialog<TradingAccount>(
        context: context,
        builder: (context) => _AddAccountDialog(account: account),
      );
      if (result != null) {
        setState(() => _isLoading = true);
        await _settingsService.updateTradingAccount(result.toCore());
        await _loadSettings();
      }
    } catch (e) {
      print('Error editing account: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Error editing account')),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    if (_settings == null) {
      return const Scaffold(
        body: Center(child: Text('Error: Settings not loaded')),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Accounts'),
        actions: [
          if (_settings!.accounts.tradingAccounts.isNotEmpty)
            IconButton(
              icon: _isSyncing
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.sync),
              onPressed: _isSyncing ? null : _syncAccounts,
              tooltip: 'Sync Accounts',
            ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadSettings,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Trading Accounts Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Trading Accounts',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        ElevatedButton.icon(
                          onPressed: _handleAddAccount,
                          icon: const Icon(Icons.add),
                          label: const Text('Add Account'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    if (_settings!.accounts.tradingAccounts.isEmpty)
                      const Center(
                        child: Padding(
                          padding: EdgeInsets.all(32),
                          child: Column(
                            children: [
                              Icon(Icons.account_balance,
                                  size: 48, color: Colors.grey),
                              SizedBox(height: 16),
                              Text(
                                'No trading accounts connected',
                                style: TextStyle(color: Colors.grey),
                              ),
                              SizedBox(height: 8),
                              Text(
                                'Add a trading account to get started',
                                style: TextStyle(color: Colors.grey),
                              ),
                            ],
                          ),
                        ),
                      )
                    else
                      ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: _settings!.accounts.tradingAccounts.length,
                        separatorBuilder: (context, index) => const Divider(),
                        itemBuilder: (context, index) {
                          final account =
                              _settings!.accounts.tradingAccounts[index];
                          return ListTile(
                            leading: const Icon(Icons.account_balance),
                            title: Text(account.name),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(account.provider),
                                Text(
                                  'Last synced: ${DateFormat.yMd().add_jm().format(account.lastSynced)}',
                                  style: Theme.of(context).textTheme.bodySmall,
                                ),
                              ],
                            ),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: account.status ==
                                            AccountStatus.active.index
                                        ? Colors.green.withOpacity(0.1)
                                        : Colors.red.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    account.status == AccountStatus.active.index
                                        ? 'Connected'
                                        : 'Disconnected',
                                    style: TextStyle(
                                      color: account.status ==
                                              AccountStatus.active.index
                                          ? Colors.green
                                          : Colors.red,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                                PopupMenuButton<String>(
                                  icon: const Icon(Icons.more_vert),
                                  itemBuilder: (context) => [
                                    const PopupMenuItem(
                                      value: 'sync',
                                      child: Row(
                                        children: [
                                          Icon(Icons.sync),
                                          SizedBox(width: 8),
                                          Text('Sync Now'),
                                        ],
                                      ),
                                    ),
                                    const PopupMenuItem(
                                      value: 'edit',
                                      child: Row(
                                        children: [
                                          Icon(Icons.edit),
                                          SizedBox(width: 8),
                                          Text('Edit'),
                                        ],
                                      ),
                                    ),
                                    PopupMenuItem(
                                      value: 'disconnect',
                                      child: Row(
                                        children: [
                                          Icon(
                                            account.status ==
                                                    AccountStatus.active.index
                                                ? Icons.link_off
                                                : Icons.link,
                                            color: account.status ==
                                                    AccountStatus.active.index
                                                ? Colors.red
                                                : null,
                                          ),
                                          const SizedBox(width: 8),
                                          Text(
                                            account.status ==
                                                    AccountStatus.active.index
                                                ? 'Disconnect'
                                                : 'Connect',
                                            style: TextStyle(
                                              color: account.status ==
                                                      AccountStatus.active.index
                                                  ? Colors.red
                                                  : null,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                  onSelected: (action) {
                                    switch (action) {
                                      case 'sync':
                                        _syncAccounts();
                                        break;
                                      case 'edit':
                                        _handleEditAccount(account);
                                        break;
                                      case 'disconnect':
                                        _handleAccountConnection(account);
                                        break;
                                    }
                                  },
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            // API Keys Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'API Keys',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        ElevatedButton.icon(
                          onPressed: () => _handleApiKeyAction('add', null),
                          icon: const Icon(Icons.add),
                          label: const Text('Add API Key'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    if (_settings!.accounts.apiKeys.isEmpty)
                      const Center(
                        child: Padding(
                          padding: EdgeInsets.all(32),
                          child: Column(
                            children: [
                              Icon(Icons.vpn_key, size: 48, color: Colors.grey),
                              SizedBox(height: 16),
                              Text(
                                'No API keys added',
                                style: TextStyle(color: Colors.grey),
                              ),
                              SizedBox(height: 8),
                              Text(
                                'Add an API key to enable automated trading',
                                style: TextStyle(color: Colors.grey),
                              ),
                            ],
                          ),
                        ),
                      )
                    else
                      ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: _settings!.accounts.apiKeys.length,
                        separatorBuilder: (context, index) => const Divider(),
                        itemBuilder: (context, index) {
                          final apiKey = _settings!.accounts.apiKeys[index];
                          return ListTile(
                            leading: const Icon(Icons.vpn_key),
                            title: Text(apiKey.name),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Key: ${apiKey.key}'),
                                if (apiKey.expiryDate != null)
                                  Text(
                                    'Expires: ${DateFormat.yMd().format(apiKey.expiryDate!)}',
                                    style:
                                        Theme.of(context).textTheme.bodySmall,
                                  ),
                              ],
                            ),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: apiKey.status ==
                                            ApiKeyStatus.active.index
                                        ? Colors.green.withOpacity(0.1)
                                        : Colors.red.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    apiKey.status == ApiKeyStatus.active.index
                                        ? 'Active'
                                        : 'Inactive',
                                    style: TextStyle(
                                      color: apiKey.status ==
                                              ApiKeyStatus.active.index
                                          ? Colors.green
                                          : Colors.red,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                                PopupMenuButton<String>(
                                  icon: const Icon(Icons.more_vert),
                                  onSelected: (action) =>
                                      _handleApiKeyAction(action, apiKey),
                                  itemBuilder: (context) => [
                                    const PopupMenuItem(
                                      value: 'edit',
                                      child: Row(
                                        children: [
                                          Icon(Icons.edit),
                                          SizedBox(width: 8),
                                          Text('Edit'),
                                        ],
                                      ),
                                    ),
                                    const PopupMenuItem(
                                      value: 'delete',
                                      child: Row(
                                        children: [
                                          Icon(Icons.delete, color: Colors.red),
                                          SizedBox(width: 8),
                                          Text('Delete',
                                              style:
                                                  TextStyle(color: Colors.red)),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _AddAccountDialog extends StatefulWidget {
  final TradingAccount? account;

  const _AddAccountDialog({this.account});

  @override
  State<_AddAccountDialog> createState() => _AddAccountDialogState();
}

class _AddAccountDialogState extends State<_AddAccountDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _providerController = TextEditingController();
  final _initialCapitalController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Pre-populate fields if editing
    if (widget.account != null) {
      _nameController.text = widget.account!.name;
      _providerController.text = widget.account!.provider;
      _initialCapitalController.text =
          widget.account!.initialCapital.toString();
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _providerController.dispose();
    _initialCapitalController.dispose();
    super.dispose();
  }

  void _submit() {
    if (_formKey.currentState?.validate() ?? false) {
      final account = TradingAccount(
        id: widget.account?.id ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        name: _nameController.text,
        provider: _providerController.text,
        initialCapital: double.parse(_initialCapitalController.text),
        status: widget.account?.status ?? AccountStatus.active.index,
        lastSyncedMillis: widget.account?.lastSyncedMillis ??
            DateTime.now().millisecondsSinceEpoch,
        isActive: widget.account?.isActive ?? true,
      );
      Navigator.pop(context, account);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.account == null
          ? 'Add Trading Account'
          : 'Edit Trading Account'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Account Name',
                hintText: 'Enter account name',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter an account name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _providerController,
              decoration: const InputDecoration(
                labelText: 'Provider',
                hintText: 'Enter broker/provider name',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a provider';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _initialCapitalController,
              decoration: const InputDecoration(
                labelText: 'Initial Capital',
                hintText: 'Enter initial capital',
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter initial capital';
                }
                if (double.tryParse(value) == null) {
                  return 'Please enter a valid number';
                }
                return null;
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _submit,
          child: Text(widget.account == null ? 'Add' : 'Save'),
        ),
      ],
    );
  }
}

class _ApiKeyDialog extends StatefulWidget {
  final ApiKey? apiKey;

  const _ApiKeyDialog({this.apiKey});

  @override
  State<_ApiKeyDialog> createState() => _ApiKeyDialogState();
}

class _ApiKeyDialogState extends State<_ApiKeyDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _keyController = TextEditingController();
  final _secretController = TextEditingController();
  DateTime? _expiryDate;

  @override
  void initState() {
    super.initState();
    if (widget.apiKey != null) {
      _nameController.text = widget.apiKey!.name;
      _keyController.text = widget.apiKey!.key;
      _secretController.text = widget.apiKey!.secret ?? '';
      _expiryDate = widget.apiKey!.expiryDate;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _keyController.dispose();
    _secretController.dispose();
    super.dispose();
  }

  Future<void> _selectExpiryDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _expiryDate ?? DateTime.now().add(const Duration(days: 365)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 3650)),
    );
    if (picked != null) {
      setState(() => _expiryDate = picked);
    }
  }

  void _submit() {
    if (_formKey.currentState?.validate() ?? false) {
      final apiKey = ApiKey(
        id: widget.apiKey?.id ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        name: _nameController.text,
        key: _keyController.text,
        secret: _secretController.text.isEmpty ? null : _secretController.text,
        status: ApiKeyStatus.active.index,
        expiryDateMillis: _expiryDate?.millisecondsSinceEpoch,
      );
      Navigator.pop(context, apiKey);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.apiKey == null ? 'Add API Key' : 'Edit API Key'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Name',
                hintText: 'Enter a name for this API key',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _keyController,
              decoration: const InputDecoration(
                labelText: 'API Key',
                hintText: 'Enter your API key',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter an API key';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _secretController,
              decoration: const InputDecoration(
                labelText: 'API Secret (optional)',
                hintText: 'Enter your API secret',
              ),
              obscureText: true,
            ),
            const SizedBox(height: 16),
            ListTile(
              title: const Text('Expiry Date'),
              subtitle: Text(_expiryDate == null
                  ? 'No expiry date set'
                  : DateFormat.yMd().format(_expiryDate!)),
              trailing: IconButton(
                icon: const Icon(Icons.calendar_today),
                onPressed: _selectExpiryDate,
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _submit,
          child: Text(widget.apiKey == null ? 'Add' : 'Save'),
        ),
      ],
    );
  }
}
