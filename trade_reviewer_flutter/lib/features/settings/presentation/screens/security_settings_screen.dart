import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/providers/auth_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart' as core;

class SecuritySettingsScreen extends ConsumerStatefulWidget {
  const SecuritySettingsScreen({super.key});

  @override
  ConsumerState<SecuritySettingsScreen> createState() =>
      _SecuritySettingsScreenState();
}

class _SecuritySettingsScreenState
    extends ConsumerState<SecuritySettingsScreen> {
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _isSaving = false;
  bool _isLoading = true;
  core.User? _user;

  @override
  void initState() {
    super.initState();
    _loadProfile();
  }

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _loadProfile() async {
    try {
      print('SecuritySettingsScreen - Starting profile load...');
      final userId = ref.read(currentUserIdProvider);
      print('SecuritySettingsScreen - User ID from provider: $userId');

      if (userId.isEmpty) {
        print('SecuritySettingsScreen - Invalid user ID');
        throw Exception('Invalid user ID');
      }

      final profileService = ref.read(profileServiceProvider);
      print('SecuritySettingsScreen - Profile service obtained');

      // First try to initialize the profile
      print('SecuritySettingsScreen - Attempting to initialize profile...');
      final initializedProfile = await profileService.initialize(userId);
      print('SecuritySettingsScreen - Profile initialized successfully');

      setState(() {
        _user = initializedProfile;
        _isLoading = false;
      });
    } catch (e, stackTrace) {
      print('SecuritySettingsScreen - Error loading profile: $e');
      print('Stack trace: $stackTrace');
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load profile: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: _loadProfile,
            ),
          ),
        );
      }
    }
  }

  Future<void> _updateSecurityPreferences({
    bool? allowDataCollection,
    bool? allowCrashReports,
    bool? allowAnalytics,
  }) async {
    if (_user == null) return;

    try {
      setState(() => _isSaving = true);
      final profileService = ref.read(profileServiceProvider);

      final updatedPreferences = _user!.preferences.copyWith(
        allowDataCollection:
            allowDataCollection ?? _user!.preferences.allowDataCollection,
        allowCrashReports:
            allowCrashReports ?? _user!.preferences.allowCrashReports,
        allowAnalytics: allowAnalytics ?? _user!.preferences.allowAnalytics,
      );

      final updatedUser = await profileService.updateProfile(
        _user!.id,
        {
          'preferences': updatedPreferences,
        },
      );

      if (updatedUser != null) {
        setState(() => _user = updatedUser);
      } else {
        throw Exception('Failed to update profile');
      }
    } catch (e) {
      print('Error updating security preferences: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('Failed to update security settings: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: () => _updateSecurityPreferences(),
            ),
          ),
        );
      }
    } finally {
      setState(() => _isSaving = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Security'),
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Loading security settings...'),
            ],
          ),
        ),
      );
    }

    if (_user == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Security'),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text('Failed to load profile'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadProfile,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Security'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Data Security',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.dividerColor.withOpacity(0.2),
                ),
              ),
              child: Column(
                children: [
                  ListTile(
                    title: const Text('Allow Data Collection'),
                    subtitle:
                        const Text('Help us improve by sharing usage data'),
                    trailing: Switch.adaptive(
                      value: _user!.preferences.allowDataCollection,
                      onChanged: _isSaving
                          ? null
                          : (value) => _updateSecurityPreferences(
                                allowDataCollection: value,
                              ),
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Allow Crash Reports'),
                    subtitle:
                        const Text('Send crash reports to help fix issues'),
                    trailing: Switch.adaptive(
                      value: _user!.preferences.allowCrashReports,
                      onChanged: _isSaving
                          ? null
                          : (value) => _updateSecurityPreferences(
                                allowCrashReports: value,
                              ),
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Allow Analytics'),
                    subtitle: const Text('Share anonymous usage statistics'),
                    trailing: Switch.adaptive(
                      value: _user!.preferences.allowAnalytics,
                      onChanged: _isSaving
                          ? null
                          : (value) => _updateSecurityPreferences(
                                allowAnalytics: value,
                              ),
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.dividerColor.withOpacity(0.2),
                ),
              ),
              child: ListTile(
                leading: const Icon(
                  Icons.logout,
                  color: Colors.red,
                ),
                title: const Text(
                  'Sign Out',
                  style: TextStyle(
                    color: Colors.red,
                  ),
                ),
                onTap: () {
                  // Show confirmation dialog
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('Sign Out'),
                      content: const Text('Are you sure you want to sign out?'),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: const Text('Cancel'),
                        ),
                        TextButton(
                          onPressed: () {
                            // TODO: Implement sign out logic
                            Navigator.pop(context); // Close dialog
                            context.go('/login'); // Navigate to login screen
                          },
                          style: TextButton.styleFrom(
                            foregroundColor: Colors.red,
                          ),
                          child: const Text('Sign Out'),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
