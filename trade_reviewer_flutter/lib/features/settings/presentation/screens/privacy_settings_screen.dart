import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/providers/auth_provider.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart' as core;

class PrivacySettingsScreen extends ConsumerStatefulWidget {
  const PrivacySettingsScreen({super.key});

  @override
  ConsumerState<PrivacySettingsScreen> createState() =>
      _PrivacySettingsScreenState();
}

class _PrivacySettingsScreenState extends ConsumerState<PrivacySettingsScreen> {
  bool _isLoading = true;
  bool _isSaving = false;
  core.User? _user;

  @override
  void initState() {
    super.initState();
    _loadProfile();
  }

  Future<void> _loadProfile() async {
    try {
      print('PrivacySettingsScreen - Starting profile load...');
      final userId = ref.read(currentUserIdProvider);
      print('PrivacySettingsScreen - User ID from provider: $userId');

      if (userId.isEmpty) {
        print('PrivacySettingsScreen - Invalid user ID');
        throw Exception('Invalid user ID');
      }

      final profileService = ref.read(profileServiceProvider);
      print('PrivacySettingsScreen - Profile service obtained');

      // First try to initialize the profile
      print('PrivacySettingsScreen - Attempting to initialize profile...');
      final initializedProfile = await profileService.initialize(userId);
      print('PrivacySettingsScreen - Profile initialized successfully');

      setState(() {
        _user = initializedProfile;
        _isLoading = false;
      });
    } catch (e, stackTrace) {
      print('PrivacySettingsScreen - Error loading profile: $e');
      print('Stack trace: $stackTrace');
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load profile: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: _loadProfile,
            ),
          ),
        );
      }
    }
  }

  Future<void> _updatePrivacyPreferences({
    bool? profilePublic,
    bool? showTradeHistory,
    bool? showProfitLoss,
    bool? showPortfolioValue,
    bool? allowDataCollection,
    bool? allowCrashReports,
    bool? allowAnalytics,
  }) async {
    if (_user == null) return;

    try {
      setState(() => _isSaving = true);
      final profileService = ref.read(profileServiceProvider);

      final updatedPreferences = _user!.preferences.copyWith(
        profilePublic: profilePublic ?? _user!.preferences.profilePublic,
        showTradeHistory:
            showTradeHistory ?? _user!.preferences.showTradeHistory,
        showProfitLoss: showProfitLoss ?? _user!.preferences.showProfitLoss,
        showPortfolioValue:
            showPortfolioValue ?? _user!.preferences.showPortfolioValue,
        allowDataCollection:
            allowDataCollection ?? _user!.preferences.allowDataCollection,
        allowCrashReports:
            allowCrashReports ?? _user!.preferences.allowCrashReports,
        allowAnalytics: allowAnalytics ?? _user!.preferences.allowAnalytics,
      );

      final updatedUser = await profileService.updateProfile(
        _user!.id,
        {'preferences': updatedPreferences},
      );

      if (updatedUser != null) {
        setState(() => _user = updatedUser);
      } else {
        throw Exception('Failed to update profile');
      }
    } catch (e) {
      print('Error updating privacy settings: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update privacy settings: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: () => _updatePrivacyPreferences(),
            ),
          ),
        );
      }
    } finally {
      setState(() => _isSaving = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Privacy'),
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Loading privacy settings...'),
            ],
          ),
        ),
      );
    }

    if (_user == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Privacy'),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text('Failed to load profile'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadProfile,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Privacy'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Profile Privacy',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.dividerColor.withOpacity(0.2),
                ),
              ),
              child: Column(
                children: [
                  ListTile(
                    title: const Text('Public Profile'),
                    subtitle: const Text('Make your profile visible to others'),
                    trailing: Switch.adaptive(
                      value: _user!.preferences.profilePublic,
                      onChanged: _isSaving
                          ? null
                          : (value) => _updatePrivacyPreferences(
                                profilePublic: value,
                              ),
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Show Trade History'),
                    subtitle: const Text(
                        'Display your trading history on your profile'),
                    trailing: Switch.adaptive(
                      value: _user!.preferences.showTradeHistory,
                      onChanged: _isSaving
                          ? null
                          : (value) => _updatePrivacyPreferences(
                                showTradeHistory: value,
                              ),
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Show Profit/Loss'),
                    subtitle: const Text('Display your P&L on your profile'),
                    trailing: Switch.adaptive(
                      value: _user!.preferences.showProfitLoss,
                      onChanged: _isSaving
                          ? null
                          : (value) => _updatePrivacyPreferences(
                                showProfitLoss: value,
                              ),
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Show Portfolio Value'),
                    subtitle: const Text(
                        'Display your portfolio value on your profile'),
                    trailing: Switch.adaptive(
                      value: _user!.preferences.showPortfolioValue,
                      onChanged: _isSaving
                          ? null
                          : (value) => _updatePrivacyPreferences(
                                showPortfolioValue: value,
                              ),
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),
            Text(
              'Data Collection',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.dividerColor.withOpacity(0.2),
                ),
              ),
              child: Column(
                children: [
                  ListTile(
                    title: const Text('Allow Data Collection'),
                    subtitle:
                        const Text('Help us improve by sharing usage data'),
                    trailing: Switch.adaptive(
                      value: _user!.preferences.allowDataCollection,
                      onChanged: _isSaving
                          ? null
                          : (value) => _updatePrivacyPreferences(
                                allowDataCollection: value,
                              ),
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Allow Crash Reports'),
                    subtitle:
                        const Text('Send crash reports to help fix issues'),
                    trailing: Switch.adaptive(
                      value: _user!.preferences.allowCrashReports,
                      onChanged: _isSaving
                          ? null
                          : (value) => _updatePrivacyPreferences(
                                allowCrashReports: value,
                              ),
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Allow Analytics'),
                    subtitle: const Text('Share anonymous usage statistics'),
                    trailing: Switch.adaptive(
                      value: _user!.preferences.allowAnalytics,
                      onChanged: _isSaving
                          ? null
                          : (value) => _updatePrivacyPreferences(
                                allowAnalytics: value,
                              ),
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
