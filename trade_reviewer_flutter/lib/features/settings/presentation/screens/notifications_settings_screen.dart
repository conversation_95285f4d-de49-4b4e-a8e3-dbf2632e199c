import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trade_reviewer_flutter/core/theme/app_colors.dart';
import 'package:trade_reviewer_flutter/core/providers/service_provider.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart' show Settings;

class NotificationsSettingsScreen extends ConsumerStatefulWidget {
  const NotificationsSettingsScreen({super.key});

  @override
  ConsumerState<NotificationsSettingsScreen> createState() =>
      _NotificationsSettingsScreenState();
}

class _NotificationsSettingsScreenState
    extends ConsumerState<NotificationsSettingsScreen> {
  bool _isLoading = true;
  Settings? _settings;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final settingsService = ref.read(settingsServiceProvider);
      final settings = await settingsService.getSettings();
      setState(() {
        _settings = settings;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading settings: $e');
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _updateNotificationSettings({
    bool? tradeAlertsEnabled,
    bool? marketNewsEnabled,
    bool? priceAlertsEnabled,
    bool? journalRemindersEnabled,
    bool? emailNotificationsEnabled,
    bool? pushNotificationsEnabled,
    bool? smsNotificationsEnabled,
  }) async {
    if (_settings == null) return;

    try {
      final settingsService = ref.read(settingsServiceProvider);
      final newDisplay = _settings!.display.copyWith(
        tradeAlertsEnabled:
            tradeAlertsEnabled ?? _settings!.display.tradeAlertsEnabled,
        marketNewsEnabled:
            marketNewsEnabled ?? _settings!.display.marketNewsEnabled,
        priceAlertsEnabled:
            priceAlertsEnabled ?? _settings!.display.priceAlertsEnabled,
        journalRemindersEnabled: journalRemindersEnabled ??
            _settings!.display.journalRemindersEnabled,
        emailNotificationsEnabled: emailNotificationsEnabled ??
            _settings!.display.emailNotificationsEnabled,
        pushNotificationsEnabled: pushNotificationsEnabled ??
            _settings!.display.pushNotificationsEnabled,
        smsNotificationsEnabled: smsNotificationsEnabled ??
            _settings!.display.smsNotificationsEnabled,
      );

      final updatedSettings =
          await settingsService.updateDisplaySettings(newDisplay);
      setState(() => _settings = updatedSettings);
    } catch (e) {
      print('Error updating notification settings: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to update notification settings'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_settings == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Notifications'),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text('Settings not available'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadSettings,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    final displaySettings = _settings!.display;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Notifications'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Alert Types',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.dividerColor.withOpacity(0.2),
                ),
              ),
              child: Column(
                children: [
                  ListTile(
                    title: const Text('Trade Alerts'),
                    subtitle:
                        const Text('Get notified about trade opportunities'),
                    trailing: Switch.adaptive(
                      value: displaySettings.tradeAlertsEnabled,
                      onChanged: (value) async {
                        await _updateNotificationSettings(
                          tradeAlertsEnabled: value,
                        );
                      },
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Market News'),
                    subtitle: const Text('Stay updated with market news'),
                    trailing: Switch.adaptive(
                      value: displaySettings.marketNewsEnabled,
                      onChanged: (value) async {
                        await _updateNotificationSettings(
                          marketNewsEnabled: value,
                        );
                      },
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Price Alerts'),
                    subtitle: const Text('Get notified about price movements'),
                    trailing: Switch.adaptive(
                      value: displaySettings.priceAlertsEnabled,
                      onChanged: (value) async {
                        await _updateNotificationSettings(
                          priceAlertsEnabled: value,
                        );
                      },
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Journal Reminders'),
                    subtitle: const Text(
                        'Get reminded to update your trading journal'),
                    trailing: Switch.adaptive(
                      value: displaySettings.journalRemindersEnabled,
                      onChanged: (value) async {
                        await _updateNotificationSettings(
                          journalRemindersEnabled: value,
                        );
                      },
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),
            Text(
              'Notification Methods',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.dividerColor.withOpacity(0.2),
                ),
              ),
              child: Column(
                children: [
                  ListTile(
                    title: const Text('Email Notifications'),
                    subtitle: const Text('Receive notifications via email'),
                    trailing: Switch.adaptive(
                      value: displaySettings.emailNotificationsEnabled,
                      onChanged: (value) async {
                        await _updateNotificationSettings(
                          emailNotificationsEnabled: value,
                        );
                      },
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('Push Notifications'),
                    subtitle: const Text('Receive push notifications'),
                    trailing: Switch.adaptive(
                      value: displaySettings.pushNotificationsEnabled,
                      onChanged: (value) async {
                        await _updateNotificationSettings(
                          pushNotificationsEnabled: value,
                        );
                      },
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: theme.dividerColor.withOpacity(0.2),
                  ),
                  ListTile(
                    title: const Text('SMS Notifications'),
                    subtitle: const Text('Receive notifications via SMS'),
                    trailing: Switch.adaptive(
                      value: displaySettings.smsNotificationsEnabled,
                      onChanged: (value) async {
                        await _updateNotificationSettings(
                          smsNotificationsEnabled: value,
                        );
                      },
                      activeColor: AppColors.primaryBlue,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
