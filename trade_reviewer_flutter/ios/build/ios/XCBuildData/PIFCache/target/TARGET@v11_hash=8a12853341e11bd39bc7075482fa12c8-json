{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9870aa59e49ab538193d085fdfd43256bc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/usr/local/Caskroom/flutter/3.24.5/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/usr/local/Caskroom/flutter/3.24.5/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ee49f65d26d8a0c930cc03d16e0ffcc8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d6238c072d92677a470b41f02eda5117", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/usr/local/Caskroom/flutter/3.24.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/usr/local/Caskroom/flutter/3.24.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987d3fcd01a34fff9e18dec0764bcc371e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d6238c072d92677a470b41f02eda5117", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/usr/local/Caskroom/flutter/3.24.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/usr/local/Caskroom/flutter/3.24.5/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984def481d54810d0de6d3335b228b2a5d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a64792e150c88f45aae90653860ab593", "guid": "bfdfe7dc352907fc980b868725387e98f74bfe561cdc142d140be02f934f1dd5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad39cc02b33ef6edb9209c7c009770cc", "guid": "bfdfe7dc352907fc980b868725387e9810aea85d8ad8d15e5b0374f61d48fe01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af1466180cbea7fde0bb43cf6c11efc8", "guid": "bfdfe7dc352907fc980b868725387e9815b408d283d71fb3d6cb01a44d1f2a54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ddfa8826ecef419fe89d6a7bbba87d3", "guid": "bfdfe7dc352907fc980b868725387e98836c593d9a4291361d7b8a77cb055b02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6eae7833d32e262fd731034b2042b42", "guid": "bfdfe7dc352907fc980b868725387e9886c2c0842860213304828c80bcb81cb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5ba04ed814a689b1ddbfd4f394a4aa3", "guid": "bfdfe7dc352907fc980b868725387e98ec515f348311a836c0bf22ee2227efe8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9101c82b964bf4b0ca67c461ee0df57", "guid": "bfdfe7dc352907fc980b868725387e984d446ebf6feef07b2fd8a7578bcc41db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c22a9e7fa227b3902186b6b160331340", "guid": "bfdfe7dc352907fc980b868725387e9853e33ac32c6ef5beb07ecd2589bef2be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a6d858efadcd531d6b8eac4430bc09c", "guid": "bfdfe7dc352907fc980b868725387e98b8c62a8fb76671e2a47af8d0a51790e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4a6700a93c53bcb5882ab531a36799a", "guid": "bfdfe7dc352907fc980b868725387e98b9b31ffd9cd77c712771379a2c9e81b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d5b1ba65888bdec373e7e54ad8058e1", "guid": "bfdfe7dc352907fc980b868725387e989ebfdd20d90d1e110c1fde33110598c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b2c41204b74a8eec62ccc6956964aac", "guid": "bfdfe7dc352907fc980b868725387e982a69ae930ad4fc30a8ec02549a556baa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2bbbef1132a469ac24af2fb1399d46c", "guid": "bfdfe7dc352907fc980b868725387e98f516afb1325393b3fa17434eaa3ca25f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867bbd69fdf367c5992ae19183ee77f14", "guid": "bfdfe7dc352907fc980b868725387e98867aeea1671cac4998bfb2ad5e21b29f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98cc465b98567e5be1dff8b7284a07e4e3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c0dd8542280bbe3d0dd599f5a017aaa1", "guid": "bfdfe7dc352907fc980b868725387e985adaa6c4e40f33315ac0cec984200988"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a1c0bbb5a09e0dc2affe36fcca0203f", "guid": "bfdfe7dc352907fc980b868725387e98c40b175ab2b99de3712343a7d9ade9fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802be6051023e5c605a2ce623b0aaaf26", "guid": "bfdfe7dc352907fc980b868725387e98eb3f03316d281fe7e958424f859f48e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8c7a8d8149407df387ac8d30f2fa0eb", "guid": "bfdfe7dc352907fc980b868725387e989522cc09f5c25c01e8f4806cd08c2e8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd48582cc9d33f06d260e20eb54df9e6", "guid": "bfdfe7dc352907fc980b868725387e9880fdc977a6e5c50ef8eece7c7f90134e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983366473e0df9b3047a2d18a09f5c25a8", "guid": "bfdfe7dc352907fc980b868725387e98135a4fbca33996cb589289a2372bf033"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808d1f5801875a743fd17674b18dfd43f", "guid": "bfdfe7dc352907fc980b868725387e984b693c14f7fa3cef85c2b2f3a9d252e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a799b0db8dd39892ef4564e0ce617216", "guid": "bfdfe7dc352907fc980b868725387e987580688cd0106f74ed5359a2eb403001"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98934e02b16dca89185bc7b7c56da42349", "guid": "bfdfe7dc352907fc980b868725387e985aaab9db3fa3282cb36eec48c3745c30"}], "guid": "bfdfe7dc352907fc980b868725387e9837c2f0a37c50e959478519168227e455", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834abeccf5f0514ce4b36ae66e5d3f205", "guid": "bfdfe7dc352907fc980b868725387e982f461d51c284a55b8f869fc9092ae5dc"}], "guid": "bfdfe7dc352907fc980b868725387e98dd47f73652ff7b522b7942f6a87afd23", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d17544c34b81de618417de5f9c91b4ec", "targetReference": "bfdfe7dc352907fc980b868725387e9883134bb5f399cb37a1eb075d4fea30d8"}], "guid": "bfdfe7dc352907fc980b868725387e98e60a652c76bfee084293e97b00176921", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9883134bb5f399cb37a1eb075d4fea30d8", "name": "sqflite_darwin-sqflite_darwin_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e981304d3d2169071b3ca365b19f5340b7c", "name": "sqflite_darwin", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98dbbec3eebed26c79cc653713be723aba", "name": "sqflite_darwin.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}