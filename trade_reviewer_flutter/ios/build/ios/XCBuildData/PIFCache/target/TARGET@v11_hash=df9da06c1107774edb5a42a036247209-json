{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9833f5e1e7717d16bfda3de7796e8a5ecf", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879abc44adfd138a1ca4bd77987d7c533", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9825edf1b19f8dc61f13b708c7e1435705", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a764a913555fe616b4774779333f4b26", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9825edf1b19f8dc61f13b708c7e1435705", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986e7b21d3547c306d44089907c912e8b4", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980ec1345dfaa131f29793686f4b94d099", "guid": "bfdfe7dc352907fc980b868725387e9817c7fc00121f635675befb5d767ee5cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbd6e71b21f7736866e22b352cf7cbee", "guid": "bfdfe7dc352907fc980b868725387e988bad446ec3464aeb342e02f50db90059", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a048c0b79ac68912d43b39154ccea86b", "guid": "bfdfe7dc352907fc980b868725387e986e33932ddaa6afb8d98b6fbe8c7026fa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980afee240a88cbfaed23c9d800c75894b", "guid": "bfdfe7dc352907fc980b868725387e98fe1c30ac85f3ef090ff8eb383b5a05e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3bae1b0f263ed2bbee0a5db6cc0b013", "guid": "bfdfe7dc352907fc980b868725387e9899f9f05e7e1aeb66c2a103f3a7ba2ac7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985577c526770b320b1fb3b182aeb905d5", "guid": "bfdfe7dc352907fc980b868725387e98243330eff769022a0145ea86079c6c93", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca88e452542489960b93501b509442b0", "guid": "bfdfe7dc352907fc980b868725387e98c3f68649fa6e314c3ca0a2da3c798bbe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985af35bca467725216016658d40590bbd", "guid": "bfdfe7dc352907fc980b868725387e98336095b6c3c5b9f60665a84681cba799"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825172ab0b912e97b7dcc505c7debaa79", "guid": "bfdfe7dc352907fc980b868725387e98a5755698e2688620f7d2992f5973e3e0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9851792bec27ec7fff1b72e8d0bb3e0f18", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98986ca1f9c77d0eeecf87d5af2f5aef1d", "guid": "bfdfe7dc352907fc980b868725387e98f01b6e998649a74218ba2c512114b24c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a2441038e042b8fbab5c5caf4a165e2", "guid": "bfdfe7dc352907fc980b868725387e98e2146e0109698c21408ba651a95b8922"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a27f9bd1044a356585ef9f605a65ea50", "guid": "bfdfe7dc352907fc980b868725387e98e38946af3e31f61ae2076012a63472cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d84c7ed4ba8566b4a3b7d5f66b7ac10f", "guid": "bfdfe7dc352907fc980b868725387e98cbc35f471ee32e4d025547e867d30884"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cb0be0a67777f1910ffc4ff1622896a", "guid": "bfdfe7dc352907fc980b868725387e984a98683874ee07eff70c6ebb239deff9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987dab19505bc1a7e52699dbe283e03015", "guid": "bfdfe7dc352907fc980b868725387e98fc3182751caddb139a5120117acd5da9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea8e20a992b618b9b343e74ae87bfaa1", "guid": "bfdfe7dc352907fc980b868725387e98aafd715a54ce9f83351b07a57e9d5f71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7a7c74634be2b7416b0985f667df5a6", "guid": "bfdfe7dc352907fc980b868725387e98ccd2be04054dd0794f6444e2d9686c3b"}], "guid": "bfdfe7dc352907fc980b868725387e98e9191dd8a7e2c1e33e2b0c68822a4036", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834abeccf5f0514ce4b36ae66e5d3f205", "guid": "bfdfe7dc352907fc980b868725387e98d4a617713c167694a45155bc69c9ab56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818bfcd5829ce999850ab16669472f70e", "guid": "bfdfe7dc352907fc980b868725387e986a03e79974d1c98f5a424e62080c44aa"}], "guid": "bfdfe7dc352907fc980b868725387e981afdeb5c085f08d8e3726eb6015e641c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98dd43a8ab58d7f81bf407aa066fa8c2b0", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e98c88f2a6185ee8b4ac45c22833b2ce406", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e98dbf7378540b28ec2caa3fb0b7101e750", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}