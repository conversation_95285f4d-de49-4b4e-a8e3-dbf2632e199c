{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987b93d2ae0500f6cb24bd009da850a79f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ecd0d94dadb1655287cb3e287322fa30", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985a3d466b5a06dda4e315ad10683a3fa5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b37c45e6f7afefe453091de810112637", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985a3d466b5a06dda4e315ad10683a3fa5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreExtension/FirebaseCoreExtension.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreExtension", "PRODUCT_NAME": "FirebaseCoreExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984cefc7059a5ec6cd6b96f02b8210531c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b0c19c3597f980596663a2640d422256", "guid": "bfdfe7dc352907fc980b868725387e98bc99aae148e6d853e8c8fef22abad77b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fe23d2b9153e5c5944383c170534240", "guid": "bfdfe7dc352907fc980b868725387e9817d682f4a781fa21e4681f512e21a5dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8d0a7a68b3de72744b3fa42d7b6f979", "guid": "bfdfe7dc352907fc980b868725387e980d040fe3c969fc7df2828f96131203ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce6c664f77c1735e6c4055d2ba75cac5", "guid": "bfdfe7dc352907fc980b868725387e98e9485a266c16091a02f94b281be8fe86", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d868e52f2bb0e1c2dbafcd2f8bb4c80", "guid": "bfdfe7dc352907fc980b868725387e98bdf9656e04dd7878454a99b9a2f46bc3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ecbe867dabb747f1f88e16506b3fb2e", "guid": "bfdfe7dc352907fc980b868725387e98b98f6fe7ed6551cc52418d29789c39be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d14f60b1347f8f82bd350e60ca5505a4", "guid": "bfdfe7dc352907fc980b868725387e982f0ae864bc3f8aa93cdc48a824c35e52", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cff771cab76a465e3d9a0e8655b3697", "guid": "bfdfe7dc352907fc980b868725387e98c752bde2fec332db2deaa44cf509c87e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb2843d94a61a5d2848872be2df8058f", "guid": "bfdfe7dc352907fc980b868725387e98aef8b1c045f7d981725d299cfc7f05cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987173da8eb33713b5a02aad72d78c14f0", "guid": "bfdfe7dc352907fc980b868725387e9864fe8ef0ee50eb42a520505291331006", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f9ef313f1534dae36ecfdb04a5867d3", "guid": "bfdfe7dc352907fc980b868725387e98b6a2ad42dc0b1dea95b257c6d248e1df", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98bab8075774af5fda45397d4bf1d2a025", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98358b66b0822930a4f5702908871a5d29", "guid": "bfdfe7dc352907fc980b868725387e9840497e1063fb1051e0f631c22683894c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f36b1de88f9805ce615112184ba53d5", "guid": "bfdfe7dc352907fc980b868725387e985e147684e728c5024fddeb253fe5ef7c"}], "guid": "bfdfe7dc352907fc980b868725387e987e0c0f9a17fea240b09ec9a87b40dfa6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834abeccf5f0514ce4b36ae66e5d3f205", "guid": "bfdfe7dc352907fc980b868725387e9814c52c7b54144b118bfefbc37b6abb9f"}], "guid": "bfdfe7dc352907fc980b868725387e98c3ddd855f3882740bb19488fa2cef935", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e988bb14774c3df0c2a961c05079dcd9c94", "targetReference": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881"}], "guid": "bfdfe7dc352907fc980b868725387e98b4576a3511cc39242f3366528ad82a70", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98c04ead258c2ba3f656422d1784107881", "name": "FirebaseCoreExtension-FirebaseCoreExtension_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98311e6292af5af43c801705cd189cc184", "name": "FirebaseCoreExtension.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}