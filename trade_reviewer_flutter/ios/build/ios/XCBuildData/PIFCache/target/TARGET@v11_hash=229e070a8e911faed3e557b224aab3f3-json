{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982ad71208f1cce30fabfae51e6df28a7f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9880a9ffd6e530b27ec6ca753462cb54d3", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aa5a0b47040119494baafe3d14638eca", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985047c44ba97ac919d643f1a9ca7b5b7a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aa5a0b47040119494baafe3d14638eca", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989719ac3bccafd7fd171f9f7a8c8aa559", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c614b353faf1158d9d89f13554ca977d", "guid": "bfdfe7dc352907fc980b868725387e98ea9adc4717e6c19f91c284889713ea35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7a0d099416685a588e173af2019deb5", "guid": "bfdfe7dc352907fc980b868725387e98e64cb76547e28a392f9b4e483ab05e8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861f27c37ec2967807581d944a86384f1", "guid": "bfdfe7dc352907fc980b868725387e98632f831aa3ec070207547c6b9617d534"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98834cdde44241a6c767dd296178fe957e", "guid": "bfdfe7dc352907fc980b868725387e98d6d98a722f8d4d24bbf462863279483c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c027fac87a3d5c8a2073e2b27be0f24", "guid": "bfdfe7dc352907fc980b868725387e989c154d0144d1cc6e6e27917b25a0a217"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b2a537c60face76d14491fd27e9b032", "guid": "bfdfe7dc352907fc980b868725387e98053f990077b1d39965152f957eba24ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98757dbc0a694e8a53a84dc03fc0ef1375", "guid": "bfdfe7dc352907fc980b868725387e98e8867e15f9c5c54a35a173a97a117c36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eab0aa1f89703be8493a668094077ded", "guid": "bfdfe7dc352907fc980b868725387e98d7daa8bdaf15608c38f14b252ccf4c0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894d52bbca0d31315de7e118a0e71a129", "guid": "bfdfe7dc352907fc980b868725387e98abc6db9ba073b6718e1d84657e4eaa30", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b073ef06b475f98c2704bcc8ea4c47a2", "guid": "bfdfe7dc352907fc980b868725387e98d5f64bb7668cd62d347c7ab0a1de03a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869a8f340e948b7b7a093b0daf1321491", "guid": "bfdfe7dc352907fc980b868725387e98aeca0f00f44635c9e83a2aedf5230e8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98671b0669b406ca0c954a8af773fb96f1", "guid": "bfdfe7dc352907fc980b868725387e985524a272eac1bc4a1d9a8a7511497f32", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cf16167329a6fb31830b98bc6e1fede", "guid": "bfdfe7dc352907fc980b868725387e985b212ddd7d9f6141edb17a8f51ca06da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bb4c4ee953f2588a7cecc47dfbdda3d", "guid": "bfdfe7dc352907fc980b868725387e9851eea85a1b0a56443e2cc7ad84575dbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f6da0a37412191da20b6110b571416d", "guid": "bfdfe7dc352907fc980b868725387e984ff3f1ebf1c54de7f6b54402671c6a89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9c2e6cc95208ca1dca55fea08dea27b", "guid": "bfdfe7dc352907fc980b868725387e9855f85e7bb3d5e0475b5f6466059b0192"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf96cc9eea729e70fe5ae199a30d68d2", "guid": "bfdfe7dc352907fc980b868725387e987bb5d47c1f181af331cbdbb194ba3bce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872950ef1078d541735f748785bdcc9b9", "guid": "bfdfe7dc352907fc980b868725387e98503cbb7d7db5ccc06b1fae8eec830eae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d81d6cad49742348911051d1a23ef5e2", "guid": "bfdfe7dc352907fc980b868725387e98e18caa61964aae154f24fddad600e079", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d529b6dfcd327fcda0fbd472962ce5e8", "guid": "bfdfe7dc352907fc980b868725387e986c298222311c4f9283a60ab7fff4f7ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b0d8e85dbabbcdb38d3423998b720c9", "guid": "bfdfe7dc352907fc980b868725387e983bdb1f431da77ef2f2dcd81b2683622c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983144965240378b3366f13afb679f0ee5", "guid": "bfdfe7dc352907fc980b868725387e9896037f2524c2f4bbb9adbdbc091758dd", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e5f4b120ead383efe90b60688eed95a3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9846ee9df9445a139dfc28fa8d46d298d0", "guid": "bfdfe7dc352907fc980b868725387e980bf0006ed315f9d67ba0236b32ce4e0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9e47051b87f01c8347bfd990d7a3c08", "guid": "bfdfe7dc352907fc980b868725387e98c73cbb81708dc7e7c7d45761401a42ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bcb84b0dc99f50eb2280899d8411393", "guid": "bfdfe7dc352907fc980b868725387e98fbbcfd1480d11cb15def1fbe7bc98b9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866f34f14dd4d325cd84660b0d4ad3a96", "guid": "bfdfe7dc352907fc980b868725387e9819203c330147af9389c7c743e449e6bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef347869d545d98cf8e4ba75f2f9794e", "guid": "bfdfe7dc352907fc980b868725387e988d9a71db5b6627aee73e83d7d308d4ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3d828d484d4d15845495a0df4f4a240", "guid": "bfdfe7dc352907fc980b868725387e981295044b3dd8b9fcd59ec260231a189b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872ab838a85e30729e327fb61dd26c0fa", "guid": "bfdfe7dc352907fc980b868725387e9879bbcf17dfe62fef041672638411bcde"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9fd03de69a4412a24089244cd3e4cf6", "guid": "bfdfe7dc352907fc980b868725387e98a2bbfa6189f1deff25ec12d49a50b43c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a401ef1c2ea8306f29af602e9e215759", "guid": "bfdfe7dc352907fc980b868725387e982a288c9c5e1902cb95b19d4934983152"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c777d4b23198325122d06143e9f1a04b", "guid": "bfdfe7dc352907fc980b868725387e9830c3dee5f169db3d0520e7a568466797"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b1e6efad70bedc38adbc9209bea0bab", "guid": "bfdfe7dc352907fc980b868725387e984d3160c628a7ebb18b0dd7884113ab8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4bacb562b61816b8b19e672f0559cf5", "guid": "bfdfe7dc352907fc980b868725387e98e6ed103c2540bb9ddfefd706e1dabca7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824d35ff5100a33e77fc5f54d3f1a60bd", "guid": "bfdfe7dc352907fc980b868725387e98d404bd371fb003a09106b134d0a6fdac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805a596ca881062de947d773d85f4257c", "guid": "bfdfe7dc352907fc980b868725387e98e99fd53c337ce307b08c9ec1ffc57290"}], "guid": "bfdfe7dc352907fc980b868725387e984f4fc27f32bd86d34b07ede074643ee3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834abeccf5f0514ce4b36ae66e5d3f205", "guid": "bfdfe7dc352907fc980b868725387e986970f5d7131f93d4e289a558ed82b975"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987855f49851150cde53c491fc5afe9a43", "guid": "bfdfe7dc352907fc980b868725387e98afeb73f377840a6b0869cad05a373d04"}], "guid": "bfdfe7dc352907fc980b868725387e984bafe2efd562bb6eb30eafa3dc09ba30", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e989c5db0749d5ca79b5374dbb9c7d93fc8", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98106305fb1367437b162521a972e441b9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}