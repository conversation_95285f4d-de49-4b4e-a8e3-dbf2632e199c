{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c97e45c51cf61d064c207fe0ed43c1a5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989b1c62c43a9a0543369a7784ac93ac3c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98211236c92ad865bca3ce6d786a9db8cd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a43b56a8c4ce2482fddaf2492099a48e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98211236c92ad865bca3ce6d786a9db8cd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98902c894e140af3b692ce6b3133440f94", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982e5c7800831c2175acd9343b71bdd5cc", "guid": "bfdfe7dc352907fc980b868725387e983f9a3d586a5513d8a0776612003fe3de", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e988244533d1d3fd9610c4e3428a6028ce8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d8ecfe02aceac76987770e27b04c4eec", "guid": "bfdfe7dc352907fc980b868725387e9846826e4b888797a2677ffc7a09fecdab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6af24abbb8b65a011f5667ab06132ab", "guid": "bfdfe7dc352907fc980b868725387e988633124c54d8236147f55e859a5e02b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f431776ebb21cfaaad9df59337e33b1d", "guid": "bfdfe7dc352907fc980b868725387e9866689c7cadf7fe9de888f9f445edff55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e90ba5e4affee87b7bd6ccc2fb2e263", "guid": "bfdfe7dc352907fc980b868725387e983f86a9e1054d0d2accba2fb05b6b7f5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857362c875e2bec01c983f9e9df54a42e", "guid": "bfdfe7dc352907fc980b868725387e9833c4cd7929ebc47a4bdb074c9b93369d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf5b3df501cae9788479059684aab16a", "guid": "bfdfe7dc352907fc980b868725387e987fd55a5866082886cc460c7b37bdd884"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883d7881455f8a3d52a464b7a41911cf7", "guid": "bfdfe7dc352907fc980b868725387e980ab2e1cb767bcec94f1227443dc5f9ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f7cbbd2825e0650292237627b1ce48f", "guid": "bfdfe7dc352907fc980b868725387e9880caa136cf45614054f7e554b5451a6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f239a356ad0d213e781feac063fbafc", "guid": "bfdfe7dc352907fc980b868725387e981c9fbb1c3c100981653639f433f989f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d085da3af46252531c6f3f55aaef561a", "guid": "bfdfe7dc352907fc980b868725387e982232e2e2ab2b2a1737a6524cdbc27ac5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d485886995dd2e78b7026b839a825754", "guid": "bfdfe7dc352907fc980b868725387e986a549307f3ba75077f62391f9e4bb4ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6ba3072b9459e2d9b79a03f8daa3653", "guid": "bfdfe7dc352907fc980b868725387e9802d94fcc396e6e12494e609614daa7da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b65fa94da14ef9f802e04870adbaa4c", "guid": "bfdfe7dc352907fc980b868725387e98c09152d97c07f2006caff75e3188ae6e"}], "guid": "bfdfe7dc352907fc980b868725387e989e360db73ff6c0f34d01f1c4f5fe76ca", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834abeccf5f0514ce4b36ae66e5d3f205", "guid": "bfdfe7dc352907fc980b868725387e9872f338d81a28fbd6e6dc6b141830c99c"}], "guid": "bfdfe7dc352907fc980b868725387e982daa9cd091c7f589d4e83edbd45c7cca", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fa5dfae83e2c5a1345032af1e2fcc7c5", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98e4817899ba5bde0c24dc47327db51b89", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}