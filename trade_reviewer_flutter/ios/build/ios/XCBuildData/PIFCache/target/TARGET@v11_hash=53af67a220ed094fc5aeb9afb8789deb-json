{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9868f9f4a79cd830c920867d935edb5409", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986880a21eaec4def8eb8026fc2707d4a8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d993eb5ff6372f9dcce33cb22df80d30", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986c27db6a45f10f659942b64b34051fcf", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d993eb5ff6372f9dcce33cb22df80d30", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9823dab54ed92d41da75066171aabecef4", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984552ee848b723ccc2f1746224e08efa7", "guid": "bfdfe7dc352907fc980b868725387e98cad3a3d76dbf9ba42ca614aad29d8c5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98490c8e2f0359c4adaff60872f93d0244", "guid": "bfdfe7dc352907fc980b868725387e988203300676d76a391af5ade9065a6e7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98284be1c1e336d5eb4915e56d3b909f58", "guid": "bfdfe7dc352907fc980b868725387e98e6b625da5dc6a9926775e586763dfb36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982811ff274088d0fca525e5b9f854bc14", "guid": "bfdfe7dc352907fc980b868725387e980b0ca9d76098db0eb1be570cdaf759ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988baa99c85b76cd6455650571754cd887", "guid": "bfdfe7dc352907fc980b868725387e9862a60358df6ba7581877007135ed2009"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bef931931b5d5cc418050ee8f3fdcd47", "guid": "bfdfe7dc352907fc980b868725387e98996cb4d8b82bd36c4265af83039c762e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c907254b0c8d4e69add43923268d945e", "guid": "bfdfe7dc352907fc980b868725387e98a8b12a7988757a71fd886955de660fea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d24bfc5a2586d3eb33fe634d12645158", "guid": "bfdfe7dc352907fc980b868725387e981cfc7a7c7d7a3b73fc81c5c92df72a75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881f5a056fbcb2deec91c83acd1dbf322", "guid": "bfdfe7dc352907fc980b868725387e98e087e0b2e8e9627ae3f59cb0bace13e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecac597a57319e295c4b06815f93e648", "guid": "bfdfe7dc352907fc980b868725387e98fcb51fdfd5f87e54cd6d18f70e404df2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b016be2c56b20d0165b68e84d4a69165", "guid": "bfdfe7dc352907fc980b868725387e98dca561c72d2163a21aa220cfc3fff6ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad9baa90229eb5a681f6f0936d2ff746", "guid": "bfdfe7dc352907fc980b868725387e98e602f746c37d4f3ad47fe7ef5da37544", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986834ba09bf2e71e1b0fffcc558248fd3", "guid": "bfdfe7dc352907fc980b868725387e98861173fccab607beca608fbdb32f6901"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b861ce865c584464b3cfe85c88c382f", "guid": "bfdfe7dc352907fc980b868725387e98853e32349a44a8d35c4b760ac95b6737", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98440d3aeb4caf7f3406d41ccbf9f324c8", "guid": "bfdfe7dc352907fc980b868725387e984f96105907bb651eb3930d2747c68f64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd2dbbbd71ee037a2c9eb26241a6b40e", "guid": "bfdfe7dc352907fc980b868725387e98925d100eab0bd78bcac081e604062bdf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d37a20724b111e2f582a60d767f7ada6", "guid": "bfdfe7dc352907fc980b868725387e984f8145ae898543f54614620efd6351f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcf971128c2858f2c74f1e76f7773760", "guid": "bfdfe7dc352907fc980b868725387e984be6683b50e27ba2d4e4a52c62665e60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986126bc481641701182e7e39c1188f948", "guid": "bfdfe7dc352907fc980b868725387e9842d91e1786e5c8cc9db84858197e0f27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ac95db48e43237b4650dc3a1ca8889b", "guid": "bfdfe7dc352907fc980b868725387e98876c55f54d27eef30b5286e6966bc9da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d637a32f6704849ab027b5d792e70d92", "guid": "bfdfe7dc352907fc980b868725387e986900f256cedec477353d1d79a4a18443"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2531ccb81c870e1381af260696d04ea", "guid": "bfdfe7dc352907fc980b868725387e98830260fb87917a9a2d199bea616d3815"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4e27d578ed9dfd230180b949b88f16f", "guid": "bfdfe7dc352907fc980b868725387e98f331ab54a84c126b4b3130613358dabf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4cc329efc8fe7ddbf9e7ad27df14233", "guid": "bfdfe7dc352907fc980b868725387e9837ae7bda3d9604c3565e867a10309dc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4059adc62f8d517c37fa31edcdfe6f9", "guid": "bfdfe7dc352907fc980b868725387e98491e74ba2db2c44a4d347ce3e2e672df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd2108a295b376491116a46671c5bb06", "guid": "bfdfe7dc352907fc980b868725387e988be8f60782b0a8bf4d25e6b899d0823d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c06ad6556088a2ee357d13b0bcb70ad", "guid": "bfdfe7dc352907fc980b868725387e9880e42fa0d28846354c122cf568e6d770"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bd61cb6e7dca6fa7b7ff970add5721a", "guid": "bfdfe7dc352907fc980b868725387e98c0fb8d5484d60f7b8c678ab37dc6a23e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed95d4569b17850b2b631cffe9c19235", "guid": "bfdfe7dc352907fc980b868725387e982d834bb09fdb81aa16db7d8a8849cd0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e04608d0c9a0232bb4a7ca38f00f68d", "guid": "bfdfe7dc352907fc980b868725387e98116c35b16e003fa26b50d01e0a98dd36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a3528c84aef98db6622faa384520fe3", "guid": "bfdfe7dc352907fc980b868725387e98306a210af5a0c3d455417099183c62e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da7d33ea9d5087b579ad6210bb3090e7", "guid": "bfdfe7dc352907fc980b868725387e98fa65264ccc3a1004ed69618968b8e041"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98942ee7f6cc53cfb2d217be4327db99d8", "guid": "bfdfe7dc352907fc980b868725387e9826dbb666a18f45e9378d47cc6af0272d"}], "guid": "bfdfe7dc352907fc980b868725387e98209a2c2190e23b9ddbdec14b9646dc87", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ae40b81120753820530addc65ddba5b5", "guid": "bfdfe7dc352907fc980b868725387e98c5af16ae2ab718ee021ccf54c84fe64e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981928713fab27b25767f2d6cfeb109233", "guid": "bfdfe7dc352907fc980b868725387e98bbc9fb31d2de3d2f9fd891dbb44f869f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ac49971bedde3553bdd1f0d6da249ff", "guid": "bfdfe7dc352907fc980b868725387e98f2062c9ae19771659c1c23f18c8bc825"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985735b732f9c52cf4bb6fcee19be86aea", "guid": "bfdfe7dc352907fc980b868725387e98ec9e82e4d6b68b32d901c2a1637a015b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edbf769643a74515fd926f93182f3072", "guid": "bfdfe7dc352907fc980b868725387e980129dc9f16183e42dbdb3d03537a7b73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0fbb0b89dee4109fe40c8615030211c", "guid": "bfdfe7dc352907fc980b868725387e98a0dce1cbe740362310d8320fc4ccc49c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a96d0d3e93041d7a3f05d00c18016250", "guid": "bfdfe7dc352907fc980b868725387e987bd0d5f755cd292b03c3bfe015569afc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e5225da0173381fa5e9dd9293565b05", "guid": "bfdfe7dc352907fc980b868725387e98c5fa640770734133ad1beeb34286b4c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d83646cdcc5ee2b8484d36a95a2892c9", "guid": "bfdfe7dc352907fc980b868725387e98763cde50d5867723956a915d5767c013"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988440b8469ee6fe012317db2f3089aee6", "guid": "bfdfe7dc352907fc980b868725387e98a149cc87ffc8f1a463f962c097157323"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd5f945a271b762dff1c9702cbba36c5", "guid": "bfdfe7dc352907fc980b868725387e98c4d1400af933e4e62c1acf538394a8db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f93ce1ca466a5cc5dbb14d83ad41dc5", "guid": "bfdfe7dc352907fc980b868725387e986939c7b6cb627ffdefa50dfd0952d979"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db9328c3cb50982bc98868a65c26d07f", "guid": "bfdfe7dc352907fc980b868725387e98d03f971297a5cab783fc2ab1faad1341"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983399df27e3459f633aac736d236b2798", "guid": "bfdfe7dc352907fc980b868725387e98a998c21c7e7fbc35ad42041b1d1b93f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f7218ecd19b35c7f6f78e886a9bb6f5", "guid": "bfdfe7dc352907fc980b868725387e983e8af8bf4058ba30465fc49893e78610"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982911577e279d3e5710e5167168c7d936", "guid": "bfdfe7dc352907fc980b868725387e98bd552bf8e91e7ba550847d8c71095c1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c023b8e2fd7fcdc8421dc46868a74d2", "guid": "bfdfe7dc352907fc980b868725387e98fb9dc963720afa34f3b11989c6b78249"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985808f056ea8dc0be20dee0727cf7bebd", "guid": "bfdfe7dc352907fc980b868725387e98bf7e269113b405b5942765bc1c086386"}], "guid": "bfdfe7dc352907fc980b868725387e989238a782a0813fd4b4898af8fc119007", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834abeccf5f0514ce4b36ae66e5d3f205", "guid": "bfdfe7dc352907fc980b868725387e98394f53cd4621a2e0685dea14b2d1b0c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818bfcd5829ce999850ab16669472f70e", "guid": "bfdfe7dc352907fc980b868725387e988c7b6db7408f002eaa52f088ba163de6"}], "guid": "bfdfe7dc352907fc980b868725387e985fb7250a5e12da1998375b19a6794e20", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b187c8c97d86826022adc2e6c1694614", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98effdb0cde84b82079fd0bc275c9b021d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}