{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9868f9f4a79cd830c920867d935edb5409", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseInstallations", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "FirebaseInstallations", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/ResourceBundle-FirebaseInstallations_Privacy-FirebaseInstallations-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "FirebaseInstallations_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98e5e2326a7bf6a96d7584dd9d7537d143", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d993eb5ff6372f9dcce33cb22df80d30", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseInstallations", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "FirebaseInstallations", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/ResourceBundle-FirebaseInstallations_Privacy-FirebaseInstallations-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "PRODUCT_NAME": "FirebaseInstallations_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98687fec586f1ab7fefc59aa3f4d0edb0b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d993eb5ff6372f9dcce33cb22df80d30", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseInstallations", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "FirebaseInstallations", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/ResourceBundle-FirebaseInstallations_Privacy-FirebaseInstallations-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "PRODUCT_NAME": "FirebaseInstallations_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9884fc5a284eccea594891c71b6e4d087b", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98559423d36532fe5bd9b82e7696cdc9d2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98cf3cc8f4539cfd699f8b4782f6a8a0c7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fd93092076b4c32d7cf60ba3e8453baa", "guid": "bfdfe7dc352907fc980b868725387e98bbbfa3b0752e8cf557cd5ec3b70cc215"}], "guid": "bfdfe7dc352907fc980b868725387e987a3cbcd757427721c6807db74bac7a77", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981703d6bed554c9878c28cb40b989a332", "name": "FirebaseInstallations_Privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}