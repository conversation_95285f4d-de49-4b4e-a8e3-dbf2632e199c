{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98732c1b642d97f2c6092b2ae250bc56fb", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985ecc4c51c2f9fd8267c89fde57af70e9", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a5a57983d21600f6cc146a3931381948", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9873ea6d813ce45eaf2a169e96177709a2", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a5a57983d21600f6cc146a3931381948", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a9f97bb6abac927e731b867cc79a6d05", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d2561f99fa6d9fd93fbfa2717a64aa8c", "guid": "bfdfe7dc352907fc980b868725387e9834e15c7f01967559f975514a7956d036", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850bbe0ffc2620ee75011289bcb3675a0", "guid": "bfdfe7dc352907fc980b868725387e98c8f149ad68e8b48922ca6c33aa9602ed", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d94530b278c731c71fe966751b3aac48", "guid": "bfdfe7dc352907fc980b868725387e987e69af61058ece2f1e80722814eaac90", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a87a13f1d8c005e6f36c4906c071b458", "guid": "bfdfe7dc352907fc980b868725387e98ed85cf962ec404336d7969bc89213bdd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982337906ab2f3725259a6c16faec08f3f", "guid": "bfdfe7dc352907fc980b868725387e98d54705febb910f7383d0e052683d401a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6185426af6e4c17d1419ea569902784", "guid": "bfdfe7dc352907fc980b868725387e986b942e0f1b31399a9f6ad1a90bed3df2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7fc5749b95631c0b3c5eb169fd8a317", "guid": "bfdfe7dc352907fc980b868725387e98ae13df6cbd928adecac3857b8a9801a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e130de3b227b0b59c43d9eb73199e5d", "guid": "bfdfe7dc352907fc980b868725387e98856ec7e9969e09b2d6f22daf499498ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d65c5e0497a5d145c3a937bce0a39efb", "guid": "bfdfe7dc352907fc980b868725387e98ed0d137d01942312ffdb26297efc551c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d73656cf4eb8a469bcfe0322e492377", "guid": "bfdfe7dc352907fc980b868725387e9865ba0377cd4551e54c3641e368bb8512", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ce772b587d19b43872f2db11de1e4f2", "guid": "bfdfe7dc352907fc980b868725387e986a275a14143346a5cfc4b57a0d1746fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbf620c6bd311dc3e794ad72616721b6", "guid": "bfdfe7dc352907fc980b868725387e982984098ef8899aabca06456be91b16ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8bff7323da6e7a4854930f9af91c411", "guid": "bfdfe7dc352907fc980b868725387e988cc446cc2924beba35561598b81103bf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf8bf8580be63d9ebda295cd8840e2b3", "guid": "bfdfe7dc352907fc980b868725387e982c2e866bd8214e73b4e5d289d457c774", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856dc45f325d863cf431d2aa1dd960278", "guid": "bfdfe7dc352907fc980b868725387e98d77eebd614dc0c6b6e514357fd8e2a15", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98309be3606f487d26a9f41b870cb26699", "guid": "bfdfe7dc352907fc980b868725387e986c5c7146ed7b511dcf567efcd0437757", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4a48745ccccb19be2f3d4ba39be7009", "guid": "bfdfe7dc352907fc980b868725387e98e199ced7f9d70e4cf7a93ca68509da89", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ed512157ce827f70ca0d07362722b28", "guid": "bfdfe7dc352907fc980b868725387e980ed39f5dafea8d01e7563a0543dd93e8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e4578d3f33bea24b51a5c5e6d1dce27", "guid": "bfdfe7dc352907fc980b868725387e982994d91933889dc1601b942d02f19070", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98008508e18a2faa7061c16ec8a7e2c258", "guid": "bfdfe7dc352907fc980b868725387e98eb15cc50a4f0c8ddaf5fc146bad84bb1", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf8005ec354a36ca1ba41a0d3e795c8c", "guid": "bfdfe7dc352907fc980b868725387e988acb4941f0670e29208135cdcc684af0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4b54411004f10d611c72f048fd49595", "guid": "bfdfe7dc352907fc980b868725387e987f85e71f62bf6126502eb349ab979733", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983604dba2c21a6596e7cb9de84000f5d3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988fb755d200cdaeec04fd6149557f546d", "guid": "bfdfe7dc352907fc980b868725387e98c4bf3648b668787b6e85a2922746a1c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d69daffd7f83e51e8e10472ee3d0b5db", "guid": "bfdfe7dc352907fc980b868725387e98f99cf8e22f1e9a34e4366b3202e1eb10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b125beb2f0c0e561332683e9b1335bd1", "guid": "bfdfe7dc352907fc980b868725387e98b73da83b3fd8870f99c626fe8a8b9daf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809fe2b59a89c56148afcf15bf79a620a", "guid": "bfdfe7dc352907fc980b868725387e98542449855878503a5ce64de8eaba61a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98687cb2e7aaeca0732b70e0f153c6dce0", "guid": "bfdfe7dc352907fc980b868725387e981f66da20b37c47469bb2c9ef4bd6af8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ba0d0acce713de83f3efba41e7a6c2b", "guid": "bfdfe7dc352907fc980b868725387e98259f83c1c2b3f926116941a9e30125c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98417c59b675772f401ca31f7b9c3c07ab", "guid": "bfdfe7dc352907fc980b868725387e982700e47b568844585c5581581374da4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987835f3b6f027237c774ffb7e666ce7bb", "guid": "bfdfe7dc352907fc980b868725387e98d2d235d8539ca2d084349a70097a01e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c37631bb2df59b71f92390ebc72ad064", "guid": "bfdfe7dc352907fc980b868725387e981ac287909e57c2c7d616d9f589efb09a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815a69756422d6aa1d31bae8847ff9015", "guid": "bfdfe7dc352907fc980b868725387e989a9589398d736647555a98973dca658d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd64cb8c4608b09a27a50748a7b2ed5a", "guid": "bfdfe7dc352907fc980b868725387e98a47654a1c5379ef20377af1f82e26e8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c900fa4e53c028dead484696e7e170d1", "guid": "bfdfe7dc352907fc980b868725387e98f9912340fe8098c86cf32ca7dc4370af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986faf7c13fd33859d194735fe5fef867e", "guid": "bfdfe7dc352907fc980b868725387e98c5fcf98bcf1b5c6a40014b9acde4e901"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2c68760e19e6b11cceaea982cd4215c", "guid": "bfdfe7dc352907fc980b868725387e98a99a5e22d5d920396e2a5f981630cb85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f9516bc132cc34d99f0a8164a00e363", "guid": "bfdfe7dc352907fc980b868725387e980908fc5da41cd08c2cb47e6ced13bff6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98021df2871ff2bfeb56ba3595e723abe1", "guid": "bfdfe7dc352907fc980b868725387e9871c92abcab619786a0ca247cc03b7645"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a17382ab0a3e0173fae2d5a8c516f7f", "guid": "bfdfe7dc352907fc980b868725387e9873d4b11004936e090acd37cb016685bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c60fb6ba9b2b9c6c11ca67e0cbfb0d04", "guid": "bfdfe7dc352907fc980b868725387e98d239e6744822f12f6e022938c0977018"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f291cc59408a19048bf60a8055da3a9a", "guid": "bfdfe7dc352907fc980b868725387e981f1651f295b393d7ca99daab53ab9752"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f0f06a57a20ebb6debf2407cb9cfd8f", "guid": "bfdfe7dc352907fc980b868725387e9841a8d8c4e539dc328f9d42166d9e5602"}], "guid": "bfdfe7dc352907fc980b868725387e98c84ec5976afc3437a56116308bba558a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834abeccf5f0514ce4b36ae66e5d3f205", "guid": "bfdfe7dc352907fc980b868725387e98d7ce17bc551cf31f5fbbde41924ff447"}], "guid": "bfdfe7dc352907fc980b868725387e98a43483e4262191e47576c7ad03193de4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d2f9cac885367a6326fd01918d4c4a4b", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98830e2f8ef4561058b4de5cf2fbaea7a3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}