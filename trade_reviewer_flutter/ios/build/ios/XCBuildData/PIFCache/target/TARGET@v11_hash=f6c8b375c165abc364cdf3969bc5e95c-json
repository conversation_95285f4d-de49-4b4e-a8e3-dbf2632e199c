{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ebd2dd8d35d019fa16995829a137e82b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98aa1f53115bf254d55dcde31f9a5da6b3", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ac60709501661acaf5493268e912726d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9848648a2d5f8db771f84c247de934b80a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ac60709501661acaf5493268e912726d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a79b55d9fc948500b133b69bcbf4718d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988ad433b0cbdf576e1c4adfb88269e572", "guid": "bfdfe7dc352907fc980b868725387e984c92bb413e9cc123f9254f0a3b5897db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4565a08190c75ab3cf8de8ec2ac871a", "guid": "bfdfe7dc352907fc980b868725387e9833ca6c8923f891f2c50768fc9d5d9ac2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f30c36d00afca97f2de3e6849d71a74c", "guid": "bfdfe7dc352907fc980b868725387e98dfe413c3533350d8263f71ada0a9c291", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ccf2b363a475e3422cf8748ffc778dd", "guid": "bfdfe7dc352907fc980b868725387e9830710142e45a5e8eea669aec29102ba5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98849d2ba7a97d2c1868d820697a41ecaa", "guid": "bfdfe7dc352907fc980b868725387e9804168cd6a6105e10385cd77d7a4de761", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9f2e930f8d89d51f785461563beac7a", "guid": "bfdfe7dc352907fc980b868725387e98c83a92ea1c73004830669c330e1b1457", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b99bd7c8e98bc853e058b9cfcab6742", "guid": "bfdfe7dc352907fc980b868725387e98f22401365bd759042e3f177092d3cd0c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98421980cbb912b2da7b0603ca8f9df800", "guid": "bfdfe7dc352907fc980b868725387e980d6109c1dff92ec74d730ce5d6762ee7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98395284a5d31524c826b59bcdf0280ef3", "guid": "bfdfe7dc352907fc980b868725387e98ade7e684f6ec8d7769957c2ba0a634ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e02283d517206c44829d93547ad5ab1", "guid": "bfdfe7dc352907fc980b868725387e9895268a6d6277feaeda6d47b93f2b4910", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885237de8b7024a9d46e3fa1343e4552a", "guid": "bfdfe7dc352907fc980b868725387e981fa4aaa27fec8e5ab02c2939d0b98dfd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b87e25789f54419927a891cfbdba464c", "guid": "bfdfe7dc352907fc980b868725387e9898e98a656c3c62dbd6b5763fc0efbf1a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1798de305717b9fb6a05df1ca3ae7cd", "guid": "bfdfe7dc352907fc980b868725387e98f199a6649e4dd1712575ed63fc37ff1a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e008db3e66ffe1b5a569a26635c162c", "guid": "bfdfe7dc352907fc980b868725387e9848895152f6b8d4a09c6c06233cfe4ab7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861fc0eddd6d91a204a92e6e25dca2322", "guid": "bfdfe7dc352907fc980b868725387e98688ae1fc6b76a0f007a6a9492227f780", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f63551c4379610e282c84f035b96e15c", "guid": "bfdfe7dc352907fc980b868725387e98b29f2c6d425fbaa60c55c5955f0e8c24", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98170eccf6d72ef10c46ecd82a88f6e8f3", "guid": "bfdfe7dc352907fc980b868725387e980e644c1390d129ddef616ef9c3bedf0b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987829436dd08a071fd2bf882041db7ea4", "guid": "bfdfe7dc352907fc980b868725387e987a747d4b3c1b27282ddda00961b3363d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825ffe7e34756aa82e5df018e7aed9208", "guid": "bfdfe7dc352907fc980b868725387e98f3b5ee7124640d7f3f4c0fa40ffc3636", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f2dc30a9e172c90077da7cb8c7a619d", "guid": "bfdfe7dc352907fc980b868725387e98215ccc2c6c8a16b5ffda1fb7d4cd307d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987adb3988ba362f7ad50134b0537233c8", "guid": "bfdfe7dc352907fc980b868725387e9864a0aac833c9f5a5ce6b6de3d0b27bbb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a0a9ea8a332ba282b7853d08877b69b", "guid": "bfdfe7dc352907fc980b868725387e9853888a888b3828dbdb496347745fa3d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da06dfb096031400cb9b0d1bf1df10c2", "guid": "bfdfe7dc352907fc980b868725387e9870e14f27401060aab8f7cfcc70ef5405", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818f79354b70982b4fadd7a35866880f4", "guid": "bfdfe7dc352907fc980b868725387e98bb5c8d8a01974777cfd15439546a3b82", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcb080ecd1a6c62e32bfdfb6d35fa711", "guid": "bfdfe7dc352907fc980b868725387e98a141dbbcc70d1c99dafb1d07df393757", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98994ec333597f21fa6452d5871e9184ab", "guid": "bfdfe7dc352907fc980b868725387e984560991c6e059df49acdef09488c32f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831fce621b89c46f00262bcbcebdd6113", "guid": "bfdfe7dc352907fc980b868725387e98af0e4e1f0003726f672d78658855f731", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818741c4fb4fe24f3f4884377e414e84c", "guid": "bfdfe7dc352907fc980b868725387e9821d139ab0e21ab470c095ba126068688", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffe0189dc72350f0c3f659045eb4a490", "guid": "bfdfe7dc352907fc980b868725387e9839d08a89e9ccfa3e12de3c48fd07d3cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c30585da56c50e9b1c7cc2992d2aa0af", "guid": "bfdfe7dc352907fc980b868725387e9847eb4804e601850e48ae0608e38f6478", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9887d685357da5bfc0b1803ff8e8b50649", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f3e62c3264dec17d52b77e235de3c900", "guid": "bfdfe7dc352907fc980b868725387e98cd4b2b68fd7fd2de229e356632389b06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0c6ed306db8c57e00bf676855f18fc5", "guid": "bfdfe7dc352907fc980b868725387e983f9e524b18b3623fe78261fcdafb78c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98260a8a6132b0255c5aad96269af678ab", "guid": "bfdfe7dc352907fc980b868725387e98b87911df6d014e8d04b2b7568c265ea3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a8943fb2c0abee6884bbee7a61bb21f", "guid": "bfdfe7dc352907fc980b868725387e986f883200a361aeb6eb17793ceff24066"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831733417e38e9b2528665a4ef05ad3e1", "guid": "bfdfe7dc352907fc980b868725387e98d7c02339c3639c1108e4df92a016a3d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db835c039a99c09fcaff4a27a3d4b99d", "guid": "bfdfe7dc352907fc980b868725387e9818744c7174d8aca3d83ae0b43891870c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb48328405c4171d94b4ca5423bd746d", "guid": "bfdfe7dc352907fc980b868725387e987cf3ef3df59bb2871c4b3b9f5a6df6ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a5cdb7b432162e48103ce523952c9c6", "guid": "bfdfe7dc352907fc980b868725387e981cc6ebaa878b64685398a7aa7e4a899b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b194552bc52c228565e240f9cccee3d0", "guid": "bfdfe7dc352907fc980b868725387e98e2393b677bb6b17a6583a5d50805e7fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887c5ad826f01eeeb09bb40c81324cf5a", "guid": "bfdfe7dc352907fc980b868725387e98d90b2b26b341db6532ad5d1850cc7830"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845d1e0b10b890a29028170d3798ca3a6", "guid": "bfdfe7dc352907fc980b868725387e9871a39a77282fd37736d00a9d778e09e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fe9099fddef59e1332553cc7694fddd", "guid": "bfdfe7dc352907fc980b868725387e98ea44c5cda639eccb799c867d9423008d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f589b7d3f998bdc94d9a4871c0cff25", "guid": "bfdfe7dc352907fc980b868725387e987ad6f85516a465279480b63be164a584"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c5650707b6abb80d9a41eaca623c210", "guid": "bfdfe7dc352907fc980b868725387e98cb8bcb98184f0dabfe96d42aa9f54284"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98431e2dfd71a554fcba1aabdae2fa4ff6", "guid": "bfdfe7dc352907fc980b868725387e987091c89601f8a7716c08e6a340ea7c58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d08b6b9cbba26e3122880c2ce83e8459", "guid": "bfdfe7dc352907fc980b868725387e98b39d10ddaa6891478f7b6db0ee47d13b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98480eb9e4ebd7550cdf08c55105deeb24", "guid": "bfdfe7dc352907fc980b868725387e9815d908eaab157f61e7a40ace11b2f9ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983424a17f03e3fc253053fa805a3a1801", "guid": "bfdfe7dc352907fc980b868725387e9827755e2ce897eff1b1b2d947b315bf60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98743a41ed090a9654eee85f06863f937a", "guid": "bfdfe7dc352907fc980b868725387e98c35c75d95ddbe741f70f0cc2e949a166"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984852835055a3b5c2cce28c7aa52e5453", "guid": "bfdfe7dc352907fc980b868725387e985c7aa9cdc6e9122615bfecd786b5e958"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833ed24ccfa9056c83c86691a900724a7", "guid": "bfdfe7dc352907fc980b868725387e98829f10a10a0a97468dfd74fede6fc3eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986dd69c300cb0505863024761b989a5a1", "guid": "bfdfe7dc352907fc980b868725387e984895a215fdaf420905a212ab1bde1ef4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bd53345e8989c2b3a6168bccd6759be", "guid": "bfdfe7dc352907fc980b868725387e98b2c2772994d35366f61d8d87f9d83603"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fafe2f86a7cc81caca1d1b4697a36db", "guid": "bfdfe7dc352907fc980b868725387e98f255828bf489daaff994ed0432bd6b0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ac108c2596cfa69be6f1b1ce28ff87f", "guid": "bfdfe7dc352907fc980b868725387e98c50dc848ddc14edc4f23b7e128acb807"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98166ac35e76fbfced1b57399a6e32ddc6", "guid": "bfdfe7dc352907fc980b868725387e98e0c51fee1d54b8b86c98f729ae500e63"}], "guid": "bfdfe7dc352907fc980b868725387e9834f0000aa59c6eb1632c448bf7d40483", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834abeccf5f0514ce4b36ae66e5d3f205", "guid": "bfdfe7dc352907fc980b868725387e9803fc89401a9b1bb9e44b237409d1872f"}], "guid": "bfdfe7dc352907fc980b868725387e98eba7f2c65d89678b6804ff9058f5d9bb", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9819544cd85380f7241fcbd99c23452658", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e98c5db418300a92d543f93dcbc0cfdad0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}