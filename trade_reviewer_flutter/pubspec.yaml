name: trade_reviewer_flutter
description: A Flutter version of the TradeREVIEWER app.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2
  # Core Package
  trade_reviewer_core:
    path: ../trade_reviewer_core/flutter_bridge
  
  # State Management
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3
  
  # Navigation
  go_router: ^13.0.0
  
  # Data Management & Storage
  isar: ^3.1.0+1
  isar_flutter_libs: ^3.1.0+1
  path_provider: ^2.1.5
  shared_preferences: ^2.2.2
  
  # UI Components
  fl_chart: ^0.65.0
  flutter_slidable: ^3.0.1
  intl: ^0.18.1
  shimmer: ^3.0.0
  cached_network_image: ^3.3.0
  image_picker: ^1.1.2
  file_picker: 5.5.0
  
  # Date/Time
  flutter_datetime_picker: ^1.5.1
  
  # Networking
  dio: ^5.4.0
  connectivity_plus: ^6.1.1
  
  # Utils
  csv: ^5.1.1
  uuid: ^4.3.3
  logger: ^2.0.2+1
  collection: ^1.18.0
  share_plus: ^7.2.1
  cross_file: ^0.3.3+8
  
  # Analytics
  stats: ^2.1.0
  decimal: ^2.3.3
  syncfusion_flutter_gauges: ^24.1.41
  provider: ^6.1.1
  flutter_launcher_icons: ^0.14.2
  sliding_up_panel: ^2.0.0+1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  mockito: ^5.4.4
  build_runner: ^2.4.7
  riverpod_generator: ^2.3.9
  isar_generator: ^3.1.0+1
  custom_lint: ^0.5.7

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - samples/
    
  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Regular.ttf
          weight: 400
        - asset: assets/fonts/Inter-Medium.ttf
          weight: 500
        - asset: assets/fonts/Inter-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Inter-Bold.ttf
          weight: 700 