import 'package:flutter_test/flutter_test.dart';
import 'package:isar/isar.dart';
import 'package:trade_reviewer_flutter/core/services/settings_service.dart';
import 'package:trade_reviewer_flutter/core/models/settings.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart' as core;
import 'dart:io';
import 'package:path/path.dart' as path;

void main() {
  late Directory tempDir;
  late Isar isar;
  late SettingsService settingsService;
  final testUserId = 'test_user';

  setUpAll(() async {
    await Isar.initializeIsarCore(download: true);
    tempDir = await Directory.systemTemp.createTemp('isar_test_');
    isar = await Isar.open(
      [SettingsSchema],
      directory: tempDir.path,
      inspector: false,
    );
  });

  setUp(() async {
    // Clear the database before each test
    await isar.writeTxn(() async {
      await isar.clear();
    });
    settingsService = SettingsService(isar);
    await settingsService.initialize(testUserId);
  });

  tearDown(() async {
    // Clear the database after each test
    await isar.writeTxn(() async {
      await isar.clear();
    });
  });

  tearDownAll(() async {
    await isar.close(deleteFromDisk: true);
    await tempDir.delete(recursive: true);
  });

  group('Account Settings Tests', () {
    test('Settings ID is preserved when adding accounts', () async {
      // Get initial settings
      final initialSettings =
          await isar.settings.filter().userIdEqualTo(testUserId).findFirst();
      expect(initialSettings, isNotNull);
      final initialId = initialSettings!.id;

      // Add a test trading account
      final testAccount = core.TradingAccount(
        id: 'test_account_1',
        name: 'Test Account',
        provider: 'Test Provider',
        status: core.AccountStatus.CONNECTED,
        lastSynced: DateTime.now(),
        credentials: {'api_key': 'test_key'},
        initialCapital: 10000.0,
        isActive: true,
      );

      // Add the account
      await settingsService.addTradingAccount(testAccount);

      // Get settings and verify ID is preserved
      final updatedSettings =
          await isar.settings.filter().userIdEqualTo(testUserId).findFirst();
      expect(updatedSettings, isNotNull);
      expect(updatedSettings!.id, equals(initialId));
    });

    test('Adding and retrieving trading account', () async {
      // Create a test trading account
      final testAccount = core.TradingAccount(
        id: 'test_account_1',
        name: 'Test Account',
        provider: 'Test Provider',
        status: core.AccountStatus.CONNECTED,
        lastSynced: DateTime.now(),
        credentials: {'api_key': 'test_key'},
        initialCapital: 10000.0,
        isActive: true,
      );

      // Add the account
      await settingsService.addTradingAccount(testAccount);

      // Get settings and verify account was saved
      final settings = await settingsService.getSettings();
      expect(settings.accounts.tradingAccounts.length, 1);
      expect(settings.accounts.tradingAccounts.first.id, testAccount.id);
      expect(settings.accounts.tradingAccounts.first.name, testAccount.name);
      expect(settings.accounts.tradingAccounts.first.provider,
          testAccount.provider);
      expect(settings.accounts.tradingAccounts.first.initialCapital,
          testAccount.initialCapital);

      // Restart the service to verify persistence
      settingsService = SettingsService(isar);
      await settingsService.initialize(testUserId);

      // Verify account still exists after restart
      final settingsAfterRestart = await settingsService.getSettings();
      expect(settingsAfterRestart.accounts.tradingAccounts.length, 1);
      expect(settingsAfterRestart.accounts.tradingAccounts.first.id,
          testAccount.id);
      expect(settingsAfterRestart.accounts.tradingAccounts.first.name,
          testAccount.name);
      expect(settingsAfterRestart.accounts.tradingAccounts.first.provider,
          testAccount.provider);
      expect(settingsAfterRestart.accounts.tradingAccounts.first.initialCapital,
          testAccount.initialCapital);
    });

    test('Multiple accounts persistence', () async {
      // Add multiple accounts
      final accounts = [
        core.TradingAccount(
          id: 'account_1',
          name: 'Account 1',
          provider: 'Provider 1',
          status: core.AccountStatus.CONNECTED,
          lastSynced: DateTime.now(),
          credentials: {'api_key': 'key1'},
          initialCapital: 10000.0,
          isActive: true,
        ),
        core.TradingAccount(
          id: 'account_2',
          name: 'Account 2',
          provider: 'Provider 2',
          status: core.AccountStatus.CONNECTED,
          lastSynced: DateTime.now(),
          credentials: {'api_key': 'key2'},
          initialCapital: 20000.0,
          isActive: true,
        ),
      ];

      // Add accounts sequentially
      for (final account in accounts) {
        await settingsService.addTradingAccount(account);
      }

      // Verify both accounts were saved
      final settings = await settingsService.getSettings();
      expect(settings.accounts.tradingAccounts.length, 2);
      expect(
        settings.accounts.tradingAccounts.map((a) => a.id).toSet(),
        accounts.map((a) => a.id).toSet(),
      );

      // Restart service and verify persistence
      settingsService = SettingsService(isar);
      await settingsService.initialize(testUserId);

      final settingsAfterRestart = await settingsService.getSettings();
      expect(settingsAfterRestart.accounts.tradingAccounts.length, 2);
      expect(
        settingsAfterRestart.accounts.tradingAccounts.map((a) => a.id).toSet(),
        accounts.map((a) => a.id).toSet(),
      );

      // Verify account details were preserved
      for (var i = 0; i < accounts.length; i++) {
        final original = accounts[i];
        final saved = settingsAfterRestart.accounts.tradingAccounts[i];
        expect(saved.id, original.id);
        expect(saved.name, original.name);
        expect(saved.provider, original.provider);
        expect(saved.initialCapital, original.initialCapital);
        expect(saved.isActive, original.isActive);
      }
    });

    test('Account list retrieval and order preservation', () async {
      // Add multiple accounts in a specific order
      final accounts = [
        core.TradingAccount(
          id: 'account_1',
          name: 'Account A',
          provider: 'Provider 1',
          status: core.AccountStatus.CONNECTED,
          lastSynced: DateTime.now(),
          credentials: {'api_key': 'key1'},
          initialCapital: 10000.0,
          isActive: true,
        ),
        core.TradingAccount(
          id: 'account_2',
          name: 'Account B',
          provider: 'Provider 2',
          status: core.AccountStatus.CONNECTED,
          lastSynced: DateTime.now(),
          credentials: {'api_key': 'key2'},
          initialCapital: 20000.0,
          isActive: true,
        ),
        core.TradingAccount(
          id: 'account_3',
          name: 'Account C',
          provider: 'Provider 3',
          status: core.AccountStatus.CONNECTED,
          lastSynced: DateTime.now(),
          credentials: {'api_key': 'key3'},
          initialCapital: 30000.0,
          isActive: true,
        ),
      ];

      // Add accounts sequentially
      for (final account in accounts) {
        await settingsService.addTradingAccount(account);
      }

      // Get settings and verify accounts list
      final settings = await settingsService.getSettings();

      // Verify number of accounts
      expect(settings.accounts.tradingAccounts.length, accounts.length);

      // Verify account order is preserved
      for (var i = 0; i < accounts.length; i++) {
        final expected = accounts[i];
        final actual = settings.accounts.tradingAccounts[i];
        expect(actual.id, expected.id);
        expect(actual.name, expected.name);
        expect(actual.provider, expected.provider);
        expect(actual.initialCapital, expected.initialCapital);
        expect(actual.isActive, expected.isActive);
        expect(actual.status, expected.status);
      }

      // Verify accounts are still accessible after service restart
      settingsService = SettingsService(isar);
      await settingsService.initialize(testUserId);
      final settingsAfterRestart = await settingsService.getSettings();

      // Verify accounts are still present and in the same order
      expect(settingsAfterRestart.accounts.tradingAccounts.length,
          accounts.length);
      for (var i = 0; i < accounts.length; i++) {
        final expected = accounts[i];
        final actual = settingsAfterRestart.accounts.tradingAccounts[i];
        expect(actual.id, expected.id);
        expect(actual.name, expected.name);
        expect(actual.provider, expected.provider);
        expect(actual.initialCapital, expected.initialCapital);
        expect(actual.isActive, expected.isActive);
        expect(actual.status, expected.status);
      }
    });
  });
}
