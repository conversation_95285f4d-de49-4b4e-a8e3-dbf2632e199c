# TradeREVIEWER Design Standards

This document outlines the design standards and component guidelines for the TradeREVIEWER application. Following these standards ensures consistency across the application and improves maintainability.

## Table of Contents
- [Colors](#colors)
- [Typography](#typography)
- [Layout](#layout)
- [Components](#components)
- [Data Visualization](#data-visualization)
- [Animations](#animations)
- [Best Practices](#best-practices)

## Colors

### Trading-Specific Colors
```css
--profit: rgb(34 197 94)      /* Green-500 - Profitable trades */
--loss: rgb(239 68 68)        /* Red-500 - Loss trades */
--neutral: rgb(71 85 105)     /* Slate-600 - Flat trades */
```

### Chart Colors
```css
--chart-primary: rgb(59 130 246)    /* Blue-500 - Primary chart lines */
--chart-secondary: rgb(99 102 241)  /* Indigo-500 - Secondary chart lines */
--chart-grid: rgb(226 232 240)      /* Slate-200 - Chart grid lines */
--chart-tooltip: rgb(15 23 42)      /* Slate-900 - Chart tooltips */
```

### UI Colors
```css
--background-primary: rgb(248 250 252)  /* Slate-50 - Main background */
--background-secondary: rgb(255 255 255) /* White - Card backgrounds */
--border: rgb(226 232 240)              /* Slate-200 - Borders */
--text-primary: rgb(15 23 42)           /* Slate-900 - Primary text */
--text-secondary: rgb(71 85 105)        /* Slate-600 - Secondary text */
```

## Typography

### Font Family
```css
--font-primary: 'Inter', system-ui, sans-serif;
--font-mono: 'JetBrains Mono', monospace;  /* For numbers and data */
```

### Data Display
```css
.metric-value {
  font-family: var(--font-mono);
  font-weight: var(--font-semibold);
}

.profit { color: var(--profit); }
.loss { color: var(--loss); }
```

## Layout

### Dashboard Grid
```css
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-4);
  padding: var(--space-4);
}
```

### Card Layouts
```css
.metric-card {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  padding: var(--space-4);
}

.chart-card {
  aspect-ratio: 16/9;
  padding: var(--space-4);
}
```

## Components

### Trade Journal
```jsx
<TradeJournal
  trades={trades}
  onFilterChange={handleFilterChange}
  onSort={handleSort}
  onExport={handleExport}
/>
```

### Date Range Selector
```jsx
<DateRangeSelector
  startDate={startDate}
  endDate={endDate}
  onChange={handleDateChange}
  presets={[
    { label: 'Today', days: 0 },
    { label: 'Last 7 Days', days: 7 },
    { label: 'Last 30 Days', days: 30 },
    { label: 'Last 90 Days', days: 90 }
  ]}
/>
```

### Performance Metrics
```jsx
<MetricCard
  title="Win Rate"
  value={winRate}
  trend={trendPercentage}
  icon={<ChartBarIcon />}
/>
```

## Data Visualization

### Chart Configuration
```javascript
const chartConfig = {
  responsive: true,
  maintainAspectRatio: false,
  interaction: {
    mode: 'index',
    intersect: false,
  },
  plugins: {
    tooltip: {
      enabled: true,
      position: 'nearest',
    },
    legend: {
      position: 'bottom',
    },
  },
};
```

### Chart Types
- Line charts for time series data (P&L over time)
- Bar charts for distribution analysis (Trade frequency)
- Pie charts for composition analysis (Win/Loss ratio)
- Heat maps for risk analysis (Time/Day performance)

## Animations

### Chart Animations
```javascript
const chartAnimation = {
  duration: 750,
  easing: 'easeOutQuart',
  delay: (context) => context.dataIndex * 50,
};
```

### UI Transitions
```css
.card-transition {
  transition: transform 200ms ease-out, box-shadow 200ms ease-out;
}

.card-transition:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}
```

## Best Practices

### Performance Optimization
1. Use `useMemo` for expensive calculations
2. Implement virtual scrolling for large datasets
3. Lazy load chart components
4. Batch state updates

### Data Management
1. Normalize trade data on import
2. Cache calculated metrics
3. Implement proper error boundaries
4. Use optimistic updates for better UX

### Accessibility
1. Provide text alternatives for charts
2. Ensure keyboard navigation
3. Use semantic HTML
4. Maintain WCAG 2.1 compliance

### State Management
1. Use Zustand for global state
2. Implement proper loading states
3. Handle error states gracefully
4. Maintain consistent data flow

---

This design standard is continuously updated as the application evolves. Refer to this document when implementing new features to maintain consistency. 