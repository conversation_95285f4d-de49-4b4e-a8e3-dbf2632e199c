const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Ensure directories exist
const dirs = ['dist', 'src/css'];
dirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Build process
try {
  // Build Tailwind CSS
  console.log('Building Tailwind CSS...');
  execSync('npm run tailwind', { stdio: 'inherit' });

  // Build JavaScript bundles
  console.log('Building JavaScript bundles...');
  execSync('npm run prod', { stdio: 'inherit' });

  console.log('Build completed successfully!');
} catch (error) {
  console.error('Build failed:', error);
  process.exit(1);
} 