const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  'https://pqgctrqxihbmanzrrjqd.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBxZ2N0cnF4aWhibWFuenJyanFkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM5ODUwNDUsImV4cCI6MjA2OTU2MTA0NX0.BmfShfx9w2PDwOLaaBZCCiqEqC2KGFJa0bIr8-E4Jck'
);

async function debugDashboard() {
  try {
    console.log('🔍 Debugging Dashboard Data Flow...\n');
    
    // 1. Check authentication status
    console.log('1. Checking authentication...');
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('❌ Session error:', sessionError);
      return;
    }
    
    if (!session) {
      console.log('⚠️ No active session - user needs to log in');
      console.log('💡 This explains why dashboard shows 0 values!');
      console.log('📝 Solution: User must be authenticated to see trade data');
      return;
    }
    
    console.log('✅ User authenticated:', session.user.email);
    const userId = session.user.id;
    
    // 2. Check if user exists in users table
    console.log('\n2. Checking user profile...');
    const { data: userProfile, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (userError) {
      if (userError.code === 'PGRST116') {
        console.log('⚠️ User profile not found in database');
        console.log('💡 User may need to complete profile setup');
      } else {
        console.error('❌ User query error:', userError);
      }
    } else {
      console.log('✅ User profile found:', userProfile.email);
    }
    
    // 3. Check trades for this user
    console.log('\n3. Checking trades for user...');
    const { data: trades, error: tradesError } = await supabase
      .from('trades')
      .select('*')
      .eq('user_id', userId)
      .order('entry_date', { ascending: false });
    
    if (tradesError) {
      console.error('❌ Trades query error:', tradesError);
      return;
    }
    
    console.log(`📊 Found ${trades.length} trades for user`);
    
    if (trades.length === 0) {
      console.log('⚠️ No trades found for this user');
      console.log('💡 This explains why dashboard shows 0 values!');
      console.log('📝 Solution: User needs to import trades via CSV');
      return;
    }
    
    // 4. Analyze trade data
    console.log('\n4. Analyzing trade data...');
    const tradesWithPnL = trades.filter(t => t.pnl !== null && t.pnl !== undefined);
    const tradesWithoutPnL = trades.filter(t => t.pnl === null || t.pnl === undefined);
    
    console.log(`✅ Trades with P&L: ${tradesWithPnL.length}`);
    console.log(`⚠️ Trades without P&L: ${tradesWithoutPnL.length}`);
    
    if (tradesWithoutPnL.length > 0) {
      console.log('💡 Some trades missing P&L - this could cause 0 values in dashboard');
      console.log('📝 Sample trades without P&L:');
      tradesWithoutPnL.slice(0, 3).forEach((trade, i) => {
        console.log(`   ${i + 1}. ${trade.symbol}: entry=${trade.entry_price}, exit=${trade.exit_price}, qty=${trade.quantity}, pnl=${trade.pnl}`);
      });
    }
    
    // 5. Calculate metrics like dashboard would
    console.log('\n5. Calculating dashboard metrics...');
    const validTrades = tradesWithPnL.filter(t => t.status === 'CLOSED');
    
    if (validTrades.length === 0) {
      console.log('⚠️ No valid closed trades with P&L');
      console.log('💡 This explains why dashboard shows 0 values!');
      return;
    }
    
    const winningTrades = validTrades.filter(t => t.pnl > 0);
    const losingTrades = validTrades.filter(t => t.pnl < 0);
    const totalPnL = validTrades.reduce((sum, t) => sum + t.pnl, 0);
    const winRate = (winningTrades.length / validTrades.length) * 100;
    
    const grossProfit = winningTrades.reduce((sum, t) => sum + t.pnl, 0);
    const grossLoss = Math.abs(losingTrades.reduce((sum, t) => sum + t.pnl, 0));
    const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit;
    
    console.log('📈 Dashboard Metrics:');
    console.log(`   Total Trades: ${validTrades.length}`);
    console.log(`   Win Rate: ${winRate.toFixed(2)}%`);
    console.log(`   Total P&L: $${totalPnL.toFixed(2)}`);
    console.log(`   Profit Factor: ${profitFactor.toFixed(2)}`);
    console.log(`   Winning Trades: ${winningTrades.length}`);
    console.log(`   Losing Trades: ${losingTrades.length}`);
    
    if (winRate > 0 && profitFactor > 0) {
      console.log('\n✅ Dashboard should show non-zero values!');
      console.log('💡 If dashboard still shows 0, check:');
      console.log('   - User authentication in React app');
      console.log('   - Data transformation in supabaseTradeStore.js');
      console.log('   - Console errors in browser');
    }
    
  } catch (error) {
    console.error('❌ Debug error:', error);
  }
}

debugDashboard();
