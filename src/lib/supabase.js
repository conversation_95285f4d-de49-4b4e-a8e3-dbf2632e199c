import { createClient } from '@supabase/supabase-js';

/**
 * Supabase client configuration for TRADEDGE
 * Provides secure connection to Supabase backend with authentication
 */

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env.local file.');
}

// Create Supabase client with enhanced configuration
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce'
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
});

/**
 * Input sanitization utility
 */
const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input;

  return input
    .trim()
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '');
};

/**
 * Validate email format
 */
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate password strength
 */
const isValidPassword = (password) => {
  return password && password.length >= 8;
};

/**
 * Authentication helper functions with enhanced security
 */
export const auth = {
  /**
   * Sign up a new user with input validation and sanitization
   * @param {string} email - User email address
   * @param {string} password - User password
   * @param {Object} userData - Additional user data
   * @returns {Promise<Object>} Authentication response
   */
  signUp: async (email, password, userData = {}) => {
    // Input validation
    if (!email || !isValidEmail(email)) {
      throw new Error('Please provide a valid email address');
    }

    if (!password || !isValidPassword(password)) {
      throw new Error('Password must be at least 8 characters long');
    }

    // Sanitize inputs
    const sanitizedEmail = sanitizeInput(email.toLowerCase());
    const sanitizedUserData = {
      first_name: sanitizeInput(userData.firstName || ''),
      last_name: sanitizeInput(userData.lastName || ''),
      timezone: userData.timezone || 'UTC',
      currency: userData.currency || 'USD'
    };

    try {
      const { data, error } = await supabase.auth.signUp({
        email: sanitizedEmail,
        password,
        options: {
          data: sanitizedUserData
        }
      });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Sign up error:', error);
      throw new Error(error.message || 'Failed to create account');
    }
  },

  /**
   * Sign in with email and password with enhanced validation
   * @param {string} email - User email address
   * @param {string} password - User password
   * @returns {Promise<Object>} Authentication response
   */
  signIn: async (email, password) => {
    // Input validation
    if (!email || !isValidEmail(email)) {
      throw new Error('Please provide a valid email address');
    }

    if (!password) {
      throw new Error('Password is required');
    }

    // Sanitize email
    const sanitizedEmail = sanitizeInput(email.toLowerCase());

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: sanitizedEmail,
        password
      });

      if (error) {
        // Provide user-friendly error messages
        if (error.message.includes('Invalid login credentials')) {
          throw new Error('Invalid email or password');
        } else if (error.message.includes('Email not confirmed')) {
          throw new Error('Please verify your email address before signing in');
        } else {
          throw new Error(error.message || 'Failed to sign in');
        }
      }

      return data;
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    }
  },

  /**
   * Sign out current user
   */
  signOut: async () => {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  },

  /**
   * Get current user session
   */
  getSession: async () => {
    const { data: { session }, error } = await supabase.auth.getSession();
    if (error) throw error;
    return session;
  },

  /**
   * Get current user
   */
  getUser: async () => {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) throw error;
    return user;
  },

  /**
   * Reset password
   */
  resetPassword: async (email) => {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`
    });
    if (error) throw error;
  },

  /**
   * Update user password
   */
  updatePassword: async (newPassword) => {
    const { error } = await supabase.auth.updateUser({
      password: newPassword
    });
    if (error) throw error;
  }
};

/**
 * Database helper functions
 */
export const db = {
  /**
   * Users table operations
   */
  users: {
    get: async (userId) => {
      try {
        const { data, error } = await supabase
          .from('users')
          .select('*')
          .eq('id', userId)
          .single();

        if (error) {
          // Create a more specific error for not found
          if (error.code === 'PGRST116') {
            const notFoundError = new Error('User profile not found');
            notFoundError.code = 'PGRST116';
            throw notFoundError;
          }
          throw error;
        }
        return data;
      } catch (error) {
        console.error('Database query error:', error);
        throw error;
      }
    },

    create: async (userData) => {
      const { data, error } = await supabase
        .from('users')
        .insert(userData)
        .select()
        .single();

      if (error) throw error;
      return data;
    },

    update: async (userId, updates) => {
      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;
      return data;
    }
  },

  /**
   * Trades table operations
   */
  trades: {
    getAll: async (userId, filters = {}) => {
      let query = supabase
        .from('trades')
        .select(`
          *,
          trade_tags (
            tag
          )
        `)
        .eq('user_id', userId)
        .order('entry_date', { ascending: false });

      // Apply filters
      if (filters.symbol) {
        query = query.eq('symbol', filters.symbol);
      }
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      if (filters.startDate) {
        query = query.gte('entry_date', filters.startDate);
      }
      if (filters.endDate) {
        query = query.lte('entry_date', filters.endDate);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data;
    },

    get: async (tradeId) => {
      const { data, error } = await supabase
        .from('trades')
        .select(`
          *,
          trade_tags (
            tag
          )
        `)
        .eq('id', tradeId)
        .single();

      if (error) throw error;
      return data;
    },

    create: async (tradeData) => {
      const { data, error } = await supabase
        .from('trades')
        .insert(tradeData)
        .select()
        .single();

      if (error) throw error;
      return data;
    },

    update: async (tradeId, updates) => {
      const { data, error } = await supabase
        .from('trades')
        .update(updates)
        .eq('id', tradeId)
        .select()
        .single();

      if (error) throw error;
      return data;
    },

    delete: async (tradeId) => {
      const { error } = await supabase
        .from('trades')
        .delete()
        .eq('id', tradeId);

      if (error) throw error;
    }
  },

  /**
   * Goals table operations
   */
  goals: {
    getAll: async (userId) => {
      const { data, error } = await supabase
        .from('trading_goals')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },

    create: async (goalData) => {
      const { data, error } = await supabase
        .from('trading_goals')
        .insert(goalData)
        .select()
        .single();

      if (error) throw error;
      return data;
    },

    update: async (goalId, updates) => {
      const { data, error } = await supabase
        .from('trading_goals')
        .update(updates)
        .eq('id', goalId)
        .select()
        .single();

      if (error) throw error;
      return data;
    },

    delete: async (goalId) => {
      const { error } = await supabase
        .from('trading_goals')
        .delete()
        .eq('id', goalId);

      if (error) throw error;
    }
  }
};

/**
 * Real-time subscriptions
 */
export const realtime = {
  /**
   * Subscribe to trades changes
   */
  subscribeTrades: (userId, callback) => {
    return supabase
      .channel('trades-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'trades',
          filter: `user_id=eq.${userId}`
        },
        callback
      )
      .subscribe();
  },

  /**
   * Subscribe to goals changes
   */
  subscribeGoals: (userId, callback) => {
    return supabase
      .channel('goals-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'trading_goals',
          filter: `user_id=eq.${userId}`
        },
        callback
      )
      .subscribe();
  }
};

export default supabase;