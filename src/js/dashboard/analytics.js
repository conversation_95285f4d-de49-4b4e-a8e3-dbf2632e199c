import { format, parseISO, differenceInMinutes, startOfDay, endOfDay } from 'date-fns';

export class TradeAnalytics {
  constructor(trades) {
    this.trades = trades;
    this.dailyStats = this.calculateDailyStats();
  }

  calculateDailyStats() {
    const dailyMap = new Map();

    this.trades.forEach(trade => {
      const day = startOfDay(trade.timestamp).getTime();
      if (!dailyMap.has(day)) {
        dailyMap.set(day, {
          date: new Date(day),
          trades: [],
          pnl: 0,
          volume: 0,
          maxDrawdown: 0,
          winStreak: 0,
          currentStreak: 0
        });
      }

      const dayStats = dailyMap.get(day);
      dayStats.trades.push(trade);
      dayStats.pnl += trade.profit_loss;
      dayStats.volume += Math.abs(trade.size * trade.entry_price);
    });

    return Array.from(dailyMap.values());
  }

  get riskMetrics() {
    const trades = this.trades;
    const winningTrades = trades.filter(t => t.profit_loss > 0);
    const losingTrades = trades.filter(t => t.profit_loss < 0);

    const avgWin = winningTrades.reduce((sum, t) => sum + t.profit_loss, 0) / winningTrades.length;
    const avgLoss = Math.abs(losingTrades.reduce((sum, t) => sum + t.profit_loss, 0) / losingTrades.length);

    return {
      sharpeRatio: this.calculateSharpeRatio(),
      maxDrawdown: this.calculateMaxDrawdown(),
      riskRewardRatio: avgWin / avgLoss,
      expectancy: (avgWin * (winningTrades.length / trades.length)) - 
                 (avgLoss * (losingTrades.length / trades.length)),
      kellyPercentage: this.calculateKellyPercentage(winningTrades.length / trades.length, avgWin, avgLoss)
    };
  }

  get timeAnalysis() {
    const hourlyStats = new Array(24).fill(0).map(() => ({
      trades: 0,
      pnl: 0,
      winRate: 0
    }));

    this.trades.forEach(trade => {
      const hour = trade.timestamp.getHours();
      hourlyStats[hour].trades++;
      hourlyStats[hour].pnl += trade.profit_loss;
    });

    return hourlyStats.map((stat, hour) => ({
      hour,
      ...stat,
      winRate: stat.trades > 0 ? stat.pnl / stat.trades : 0
    }));
  }

  calculateMaxDrawdown() {
    let peak = -Infinity;
    let maxDrawdown = 0;
    let runningPnL = 0;

    this.trades.forEach(trade => {
      runningPnL += trade.profit_loss;
      peak = Math.max(peak, runningPnL);
      maxDrawdown = Math.min(maxDrawdown, runningPnL - peak);
    });

    return Math.abs(maxDrawdown);
  }

  calculateSharpeRatio() {
    const returns = this.dailyStats.map(day => day.pnl);
    const avgReturn = returns.reduce((a, b) => a + b, 0) / returns.length;
    const stdDev = Math.sqrt(
      returns.reduce((sq, ret) => sq + Math.pow(ret - avgReturn, 2), 0) / returns.length
    );
    return stdDev !== 0 ? (avgReturn / stdDev) * Math.sqrt(252) : 0;
  }

  calculateKellyPercentage(winRate, avgWin, avgLoss) {
    return ((winRate * avgWin) - ((1 - winRate) * avgLoss)) / avgWin;
  }

  getInstrumentAnalysis() {
    const instrumentMap = new Map();

    this.trades.forEach(trade => {
      if (!instrumentMap.has(trade.instrument)) {
        instrumentMap.set(trade.instrument, {
          totalTrades: 0,
          winningTrades: 0,
          totalPnL: 0,
          volume: 0,
          avgDuration: 0
        });
      }

      const stats = instrumentMap.get(trade.instrument);
      stats.totalTrades++;
      if (trade.profit_loss > 0) stats.winningTrades++;
      stats.totalPnL += trade.profit_loss;
      stats.volume += Math.abs(trade.size * trade.entry_price);
      stats.avgDuration += differenceInMinutes(trade.exit_time, trade.entry_time);
    });

    return Array.from(instrumentMap.entries()).map(([instrument, stats]) => ({
      instrument,
      ...stats,
      winRate: stats.winningTrades / stats.totalTrades,
      avgDuration: stats.avgDuration / stats.totalTrades
    }));
  }
} 