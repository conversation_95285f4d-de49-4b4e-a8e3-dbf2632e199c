import Chart from 'chart.js/auto';
import { format, startOfHour, endOfHour } from 'date-fns';
import { TradeAnalytics } from './analytics';

export class ChartManager {
  constructor() {
    this.charts = new Map();
    this.gradients = new Map();
    this.registerCustomCharts();
  }

  registerCustomCharts() {
    // Custom chart animations and interactions
    Chart.defaults.font.family = 'Inter, sans-serif';
    Chart.defaults.plugins.tooltip.cornerRadius = 4;
    Chart.defaults.plugins.tooltip.padding = 8;
  }

  createPnLChart(ctx, data) {
    const gradient = ctx.createLinearGradient(0, 0, 0, 400);
    gradient.addColorStop(0, 'rgba(34, 197, 94, 0.2)');
    gradient.addColorStop(1, 'rgba(34, 197, 94, 0)');

    return new Chart(ctx, {
      type: 'line',
      data: {
        labels: data.labels,
        datasets: [{
          label: 'Cumulative P&L',
          data: data.values,
          borderColor: '#22c55e',
          backgroundColor: gradient,
          fill: true,
          tension: 0.4,
          pointRadius: 0,
          pointHitRadius: 20,
          pointHoverRadius: 5,
          pointHoverBackgroundColor: '#22c55e'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
          intersect: false,
          mode: 'index'
        },
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            backgroundColor: 'white',
            titleColor: '#1f2937',
            bodyColor: '#1f2937',
            borderColor: '#e5e7eb',
            borderWidth: 1,
            padding: 12,
            displayColors: false,
            callbacks: {
              label: (context) => `P&L: ${new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD'
              }).format(context.raw)}`
            }
          }
        },
        scales: {
          x: {
            grid: {
              display: false
            }
          },
          y: {
            grid: {
              color: '#f3f4f6'
            },
            ticks: {
              callback: (value) => new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 0
              }).format(value)
            }
          }
        }
      }
    });
  }

  createDrawdownChart(ctx, trades) {
    let peak = 0;
    let drawdown = 0;
    const drawdownData = trades.map(trade => {
      peak = Math.max(peak, trade.cumulative_pnl);
      drawdown = ((peak - trade.cumulative_pnl) / peak) * 100;
      return {
        x: trade.timestamp,
        y: drawdown
      };
    });

    return new Chart(ctx, {
      type: 'line',
      data: {
        datasets: [{
          label: 'Drawdown %',
          data: drawdownData,
          borderColor: '#ef4444',
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          fill: true
        }]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            ticks: {
              callback: value => `${value.toFixed(2)}%`
            }
          }
        }
      }
    });
  }

  createTimeDistributionChart(ctx, timeAnalysis) {
    const hourLabels = Array.from({ length: 24 }, (_, i) => 
      format(startOfHour(new Date().setHours(i)), 'ha')
    );

    return new Chart(ctx, {
      type: 'bar',
      data: {
        labels: hourLabels,
        datasets: [
          {
            label: 'Win Rate',
            data: timeAnalysis.map(h => h.winRate * 100),
            yAxisID: 'y1',
            type: 'line',
            borderColor: '#3b82f6',
            tension: 0.4
          },
          {
            label: 'Trade Count',
            data: timeAnalysis.map(h => h.trades),
            backgroundColor: '#93c5fd',
            borderRadius: 4
          }
        ]
      },
      options: {
        responsive: true,
        interaction: {
          mode: 'index',
          intersect: false
        },
        scales: {
          y: {
            title: {
              display: true,
              text: 'Trade Count'
            }
          },
          y1: {
            position: 'right',
            title: {
              display: true,
              text: 'Win Rate %'
            },
            grid: {
              drawOnChartArea: false
            },
            ticks: {
              callback: value => `${value.toFixed(1)}%`
            }
          }
        }
      }
    });
  }

  createRiskRewardHeatmap(ctx, trades) {
    const riskRewardBuckets = this.calculateRiskRewardBuckets(trades);
    
    return new Chart(ctx, {
      type: 'matrix',
      data: {
        datasets: [{
          data: riskRewardBuckets,
          backgroundColor(context) {
            const value = context.dataset.data[context.dataIndex].v;
            const alpha = Math.min(Math.abs(value) / 10, 1);
            return value > 0 
              ? `rgba(34, 197, 94, ${alpha})`
              : `rgba(239, 68, 68, ${alpha})`;
          }
        }]
      },
      options: {
        plugins: {
          tooltip: {
            callbacks: {
              label: (context) => {
                const data = context.dataset.data[context.dataIndex];
                return `Risk: ${data.x}R, Reward: ${data.y}R, Count: ${data.v}`;
              }
            }
          }
        },
        scales: {
          x: {
            title: {
              display: true,
              text: 'Risk (R)'
            }
          },
          y: {
            title: {
              display: true,
              text: 'Reward (R)'
            }
          }
        }
      }
    });
  }

  createVolatilityChart(ctx, trades) {
    const volatilityData = this.calculateVolatility(trades);
    
    return new Chart(ctx, {
      type: 'line',
      data: {
        labels: volatilityData.map(d => format(d.date, 'MMM d')),
        datasets: [{
          label: 'Volatility',
          data: volatilityData.map(d => d.volatility),
          borderColor: '#8b5cf6',
          backgroundColor: 'rgba(139, 92, 246, 0.1)',
          fill: true
        }]
      },
      options: {
        responsive: true,
        plugins: {
          tooltip: {
            callbacks: {
              label: (context) => `Volatility: ${context.raw.toFixed(2)}%`
            }
          }
        }
      }
    });
  }

  // Helper methods
  calculateRiskRewardBuckets(trades) {
    const buckets = new Map();
    trades.forEach(trade => {
      const risk = Math.abs(trade.stop_loss - trade.entry_price);
      const reward = Math.abs(trade.take_profit - trade.entry_price);
      const riskR = Math.floor(risk / trade.atr * 2) / 2; // Round to nearest 0.5R
      const rewardR = Math.floor(reward / trade.atr * 2) / 2;
      
      const key = `${riskR}-${rewardR}`;
      buckets.set(key, (buckets.get(key) || 0) + 1);
    });

    return Array.from(buckets.entries()).map(([key, value]) => {
      const [x, y] = key.split('-').map(Number);
      return { x, y, v: value };
    });
  }

  calculateVolatility(trades) {
    const dailyReturns = new Map();
    trades.forEach(trade => {
      const date = format(trade.timestamp, 'yyyy-MM-dd');
      const returns = dailyReturns.get(date) || [];
      returns.push(trade.profit_loss / trade.entry_price);
      dailyReturns.set(date, returns);
    });

    return Array.from(dailyReturns.entries()).map(([date, returns]) => ({
      date: new Date(date),
      volatility: this.standardDeviation(returns) * Math.sqrt(252) * 100
    }));
  }

  standardDeviation(values) {
    const avg = values.reduce((a, b) => a + b) / values.length;
    const squareDiffs = values.map(value => Math.pow(value - avg, 2));
    return Math.sqrt(squareDiffs.reduce((a, b) => a + b) / values.length);
  }

  updateChart(chartId, newData) {
    const chart = this.charts.get(chartId);
    if (!chart) return;

    chart.data = newData;
    chart.update('none'); // Use 'none' for better performance
  }

  // Additional chart methods...
} 