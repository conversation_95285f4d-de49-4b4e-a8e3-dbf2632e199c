export class PatternRecognition {
  constructor(trades) {
    this.trades = trades;
    this.patterns = this.analyzePatterns();
  }

  analyzePatterns() {
    return {
      momentum: this.findMomentumPatterns(),
      reversals: this.findReversalPatterns(),
      volatility: this.findVolatilityPatterns(),
      timeBasedPatterns: this.findTimeBasedPatterns()
    };
  }

  findMomentumPatterns() {
    const patterns = [];
    let consecutiveWins = 0;
    let consecutiveLosses = 0;

    this.trades.forEach((trade, i) => {
      if (trade.profit_loss > 0) {
        consecutiveWins++;
        consecutiveLosses = 0;
      } else {
        consecutiveLosses++;
        consecutiveWins = 0;
      }

      if (consecutiveWins >= 3) {
        patterns.push({
          type: 'MOMENTUM_UP',
          startIndex: i - 2,
          endIndex: i,
          strength: consecutiveWins,
          avgReturn: this.calculateAverageReturn(i - 2, i)
        });
      }

      if (consecutiveLosses >= 3) {
        patterns.push({
          type: 'MOMENTUM_DOWN',
          startIndex: i - 2,
          endIndex: i,
          strength: consecutiveLosses,
          avgLoss: this.calculateAverageReturn(i - 2, i)
        });
      }
    });

    return patterns;
  }

  findReversalPatterns() {
    const patterns = [];
    const lookback = 5;

    this.trades.forEach((trade, i) => {
      if (i < lookback) return;

      const previousTrades = this.trades.slice(i - lookback, i);
      const currentTrade = trade;

      // Check for reversal pattern
      const previousTrend = this.calculateTrend(previousTrades);
      const currentReturn = currentTrade.profit_loss / Math.abs(currentTrade.entry_price);

      if (Math.abs(currentReturn) > Math.abs(previousTrend) * 1.5) {
        patterns.push({
          type: previousTrend > 0 ? 'BEARISH_REVERSAL' : 'BULLISH_REVERSAL',
          index: i,
          strength: Math.abs(currentReturn / previousTrend),
          previousTrend,
          reversalReturn: currentReturn
        });
      }
    });

    return patterns;
  }

  findVolatilityPatterns() {
    const volatilityWindows = this.calculateVolatilityWindows();
    const patterns = [];

    volatilityWindows.forEach((volatility, i) => {
      if (i < 5) return;

      const avgVolatility = this.average(volatilityWindows.slice(i - 5, i));
      if (volatility > avgVolatility * 1.5) {
        patterns.push({
          type: 'VOLATILITY_EXPANSION',
          index: i,
          volatility,
          averageVolatility: avgVolatility
        });
      } else if (volatility < avgVolatility * 0.5) {
        patterns.push({
          type: 'VOLATILITY_CONTRACTION',
          index: i,
          volatility,
          averageVolatility: avgVolatility
        });
      }
    });

    return patterns;
  }

  findTimeBasedPatterns() {
    const hourlyPerformance = new Array(24).fill(0).map(() => ({
      trades: 0,
      wins: 0,
      totalReturn: 0
    }));

    this.trades.forEach(trade => {
      const hour = new Date(trade.timestamp).getHours();
      hourlyPerformance[hour].trades++;
      if (trade.profit_loss > 0) hourlyPerformance[hour].wins++;
      hourlyPerformance[hour].totalReturn += trade.profit_loss;
    });

    return hourlyPerformance.map((perf, hour) => ({
      hour,
      winRate: perf.trades > 0 ? perf.wins / perf.trades : 0,
      averageReturn: perf.trades > 0 ? perf.totalReturn / perf.trades : 0,
      volume: perf.trades
    }));
  }

  // Helper methods
  calculateTrend(trades) {
    const returns = trades.map(t => t.profit_loss / Math.abs(t.entry_price));
    return this.average(returns);
  }

  calculateVolatilityWindows() {
    const windowSize = 20;
    const volatilities = [];

    for (let i = windowSize; i < this.trades.length; i++) {
      const windowTrades = this.trades.slice(i - windowSize, i);
      const returns = windowTrades.map(t => t.profit_loss / Math.abs(t.entry_price));
      volatilities.push(this.standardDeviation(returns));
    }

    return volatilities;
  }

  average(values) {
    return values.reduce((a, b) => a + b, 0) / values.length;
  }

  standardDeviation(values) {
    const avg = this.average(values);
    const squareDiffs = values.map(value => Math.pow(value - avg, 2));
    return Math.sqrt(this.average(squareDiffs));
  }
} 