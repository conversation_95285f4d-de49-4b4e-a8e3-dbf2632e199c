import Chart from 'chart.js/auto';
import { format, subMonths, parseISO } from 'date-fns';
import { debounce } from 'lodash';

class Dashboard {
  constructor() {
    this.db = null;
    this.charts = {};
    this.currentPeriod = '1M';
    this.filters = {
      search: '',
      dateRange: null,
      instrument: null
    };

    this.init();
  }

  async init() {
    await this.initDatabase();
    this.initEventListeners();
    this.initCharts();
    this.setupCSVImport();
    await this.loadData();
  }

  async initDatabase() {
    // Legacy function - no longer needed with Supabase
    console.log('initDatabase called - no longer needed with Supabase');
    this.db = null;
  }

  initEventListeners() {
    // Search functionality
    const searchInput = document.querySelector('.search-input');
    searchInput.addEventListener('input', debounce(async (e) => {
      this.filters.search = e.target.value;
      await this.updateTradesTable();
    }, 300));

    // Period selector for charts
    document.querySelectorAll('.chart-period-btn').forEach(btn => {
      btn.addEventListener('click', async (e) => {
        this.setActivePeriod(e.target.dataset.period);
        await this.updateCharts();
      });
    });

    // Filter button
    document.querySelector('.filter-btn').addEventListener('click', () => {
      this.showFilterModal();
    });
  }

  initCharts() {
    // P&L Chart
    const pnlCtx = document.getElementById('pnlChart').getContext('2d');
    this.charts.pnl = new Chart(pnlCtx, {
      type: 'line',
      data: {
        labels: [],
        datasets: [{
          label: 'Daily P&L',
          data: [],
          borderColor: '#22c55e',
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
          intersect: false,
          mode: 'index'
        },
        plugins: {
          tooltip: {
            callbacks: {
              label: (context) => `P&L: $${context.raw.toFixed(2)}`
            }
          }
        }
      }
    });

    // Distribution Chart
    const distCtx = document.getElementById('distributionChart').getContext('2d');
    this.charts.distribution = new Chart(distCtx, {
      type: 'bar',
      data: {
        labels: ['Wins', 'Losses'],
        datasets: [{
          data: [0, 0],
          backgroundColor: ['#22c55e', '#ef4444']
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false
      }
    });
  }

  setupCSVImport() {
    const csvInput = document.getElementById('csvInput');
    csvInput.addEventListener('change', async (e) => {
      const file = e.target.files[0];
      if (file) {
        try {
          await this.processCSVFile(file);
          await this.loadData();
          this.showNotification('CSV import successful', 'success');
        } catch (error) {
          this.showNotification('Error importing CSV: ' + error.message, 'error');
        }
      }
    });
  }

  async processCSVFile(file) {
    const text = await file.text();
    const rows = text.split('\n').map(row => row.split(','));
    const headers = rows[0];
    const trades = rows.slice(1).map(row => {
      const trade = {};
      headers.forEach((header, i) => {
        trade[header.trim()] = row[i]?.trim();
      });
      return this.normalizeTradeData(trade);
    });

    await this.importTrades(trades);
  }

  normalizeTradeData(trade) {
    // Validate and ensure quantity is a valid positive integer
    const quantity = parseInt(trade.Quantity);
    const validQuantity = (isNaN(quantity) || quantity <= 0) ? 1 : quantity;

    if (validQuantity !== quantity) {
      console.warn(`Invalid quantity "${trade.Quantity}" for trade ${trade.Symbol}, using ${validQuantity}`);
    }

    return {
      timestamp: parseISO(trade.Date),
      instrument: trade.Symbol,
      position_type: trade.Side,
      entry_price: parseFloat(trade.Entry),
      exit_price: parseFloat(trade.Exit),
      profit_loss: parseFloat(trade.PnL),
      size: validQuantity,
      duration: trade.Duration,
      commission: parseFloat(trade.Commission) || 0
    };
  }

  async importTrades(trades) {
    // Legacy function - CSV import now handled by React components
    console.log('importTrades called - now handled by React CSV import component');
    return { success: true, message: 'Import handled by React components' };
  }

  async loadData() {
    // Legacy function - data loading now handled by React components
    console.log('loadData called - now handled by React components with Supabase');
    this.updateLastRefresh();
  }

  async getFilteredTrades() {
    // Legacy function - filtering now handled by React components
    console.log('getFilteredTrades called - now handled by React components with Supabase');
    return [];
  }

  async updateStats(trades) {
    const stats = this.calculateStats(trades);
    
    // Update UI elements
    document.getElementById('total-pnl').textContent = this.formatCurrency(stats.totalPnL);
    document.getElementById('win-rate').textContent = `${(stats.winRate * 100).toFixed(1)}%`;
    
    // Add more stats
    this.updateStatCard('avg-trade', this.formatCurrency(stats.avgTrade));
    this.updateStatCard('largest-win', this.formatCurrency(stats.largestWin));
    this.updateStatCard('largest-loss', this.formatCurrency(stats.largestLoss));
    this.updateStatCard('profit-factor', stats.profitFactor.toFixed(2));
    
    // Update monthly comparison
    const pnlChange = this.calculateMonthlyChange(trades);
    const pnlChangeElement = document.getElementById('pnl-change');
    pnlChangeElement.textContent = `${pnlChange >= 0 ? '+' : ''}${pnlChange.toFixed(1)}%`;
    pnlChangeElement.className = `text-sm ${pnlChange >= 0 ? 'text-emerald-500' : 'text-red-500'}`;
  }

  calculateStats(trades) {
    const stats = {
      totalPnL: 0,
      winCount: 0,
      lossCount: 0,
      totalWins: 0,
      totalLosses: 0,
      largestWin: 0,
      largestLoss: 0
    };

    trades.forEach(trade => {
      stats.totalPnL += trade.profit_loss;
      
      if (trade.profit_loss > 0) {
        stats.winCount++;
        stats.totalWins += trade.profit_loss;
        stats.largestWin = Math.max(stats.largestWin, trade.profit_loss);
      } else {
        stats.lossCount++;
        stats.totalLosses += Math.abs(trade.profit_loss);
        stats.largestLoss = Math.min(stats.largestLoss, trade.profit_loss);
      }
    });

    return {
      ...stats,
      winRate: stats.winCount / (stats.winCount + stats.lossCount) || 0,
      avgTrade: stats.totalPnL / trades.length || 0,
      profitFactor: Math.abs(stats.totalWins / stats.totalLosses) || 0
    };
  }

  async updateCharts(trades) {
    this.updatePnLChart(trades);
    this.updateDistributionChart(trades);
    this.updateInstrumentChart(trades);
    this.updateTimeOfDayChart(trades);
  }

  updatePnLChart(trades) {
    const dailyPnL = this.aggregateDailyPnL(trades);
    const cumulativePnL = this.calculateCumulativePnL(dailyPnL);

    this.charts.pnl.data.labels = cumulativePnL.map(d => format(d.date, 'MMM d'));
    this.charts.pnl.data.datasets[0].data = cumulativePnL.map(d => d.value);
    this.charts.pnl.update();
  }

  updateDistributionChart(trades) {
    const distribution = this.calculatePnLDistribution(trades);
    
    this.charts.distribution.data.labels = distribution.map(d => `$${d.range}`);
    this.charts.distribution.data.datasets[0].data = distribution.map(d => d.count);
    this.charts.distribution.data.datasets[0].backgroundColor = distribution.map(d => 
      d.range.includes('+') ? '#22c55e' : '#ef4444'
    );
    this.charts.distribution.update();
  }

  async updateTradesTable(trades) {
    const tbody = document.getElementById('trades-table-body');
    tbody.innerHTML = '';

    trades.slice(0, 50).forEach(trade => {
      const row = document.createElement('tr');
      row.className = 'trade-row';
      
      row.innerHTML = `
        <td class="trade-table-cell">${format(trade.timestamp, 'MMM d, yyyy HH:mm')}</td>
        <td class="trade-table-cell font-medium">${trade.instrument}</td>
        <td class="trade-table-cell">
          <span class="px-2 py-1 rounded-full text-xs font-medium ${
            trade.position_type === 'LONG' ? 'bg-emerald-100 text-emerald-800' : 'bg-red-100 text-red-800'
          }">
            ${trade.position_type}
          </span>
        </td>
        <td class="trade-table-cell">${this.formatCurrency(trade.entry_price)}</td>
        <td class="trade-table-cell">${this.formatCurrency(trade.exit_price)}</td>
        <td class="trade-table-cell ${trade.profit_loss >= 0 ? 'profit' : 'loss'}">
          ${this.formatCurrency(trade.profit_loss)}
        </td>
        <td class="trade-table-cell">
          <button class="text-slate-400 hover:text-slate-600" onclick="showTradeDetails(${trade.id})">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
            </svg>
          </button>
        </td>
      `;
      
      tbody.appendChild(row);
    });
  }

  // Helper methods
  formatCurrency(value) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value);
  }

  updateLastRefresh() {
    const lastUpdate = document.getElementById('lastUpdate');
    lastUpdate.textContent = `Last updated: ${format(new Date(), 'HH:mm:ss')}`;
  }

  showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `fixed bottom-4 right-4 px-6 py-3 rounded-lg text-white ${
      type === 'success' ? 'bg-emerald-500' : 'bg-red-500'
    } transform transition-transform duration-300 translate-y-0`;
    
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.classList.add('translate-y-full');
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }
}

export default new Dashboard(); 