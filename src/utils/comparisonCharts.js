export function getComparisonChartData(comparison) {
  return {
    labels: ['Risk Score', 'Discipline', 'R/R Compliance', 'Overall'],
    datasets: [
      {
        label: 'Current Period',
        data: [
          comparison.current.riskScore,
          comparison.current.disciplineScore,
          comparison.current.rrCompliance,
          comparison.current.overallScore
        ],
        backgroundColor: 'rgba(59, 130, 246, 0.5)',
        borderColor: 'rgb(59, 130, 246)',
        borderWidth: 1
      },
      {
        label: 'Previous Period',
        data: [
          comparison.previous.riskScore,
          comparison.previous.disciplineScore,
          comparison.previous.rrCompliance,
          comparison.previous.overallScore
        ],
        backgroundColor: 'rgba(156, 163, 175, 0.5)',
        borderColor: 'rgb(156, 163, 175)',
        borderWidth: 1
      }
    ]
  };
}

export function getComparisonChartOptions() {
  return {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        title: {
          display: true,
          text: 'Score (%)'
        }
      }
    },
    plugins: {
      legend: {
        position: 'top'
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return `${context.dataset.label}: ${context.raw.toFixed(1)}%`;
          }
        }
      }
    }
  };
} 