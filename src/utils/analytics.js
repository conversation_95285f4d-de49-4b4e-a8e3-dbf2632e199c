import { startOfDay, endOfDay, differenceInMinutes } from 'date-fns';

export function calculateInstrumentStats(trades) {
  if (!trades?.length) return [];

  const instrumentMap = trades.reduce((acc, trade) => {
    if (!acc[trade.instrument]) {
      acc[trade.instrument] = {
        instrument: trade.instrument,
        trades: [],
        pnl: 0,
        winningTrades: 0,
        commission: 0
      };
    }

    acc[trade.instrument].trades.push(trade);
    acc[trade.instrument].pnl += trade.profit_loss;
    acc[trade.instrument].commission += trade.commission;
    if (trade.profit_loss - trade.commission > 0) {
      acc[trade.instrument].winningTrades++;
    }

    return acc;
  }, {});

  return Object.values(instrumentMap).map(stats => ({
    instrument: stats.instrument,
    trades: stats.trades.length,
    winRate: (stats.winningTrades / stats.trades.length) * 100,
    pnl: stats.pnl - stats.commission,
    avgTrade: (stats.pnl - stats.commission) / stats.trades.length
  })).sort((a, b) => b.pnl - a.pnl);
}

export function calculateTimeStats(trades) {
  if (!trades?.length) return [];

  const hourMap = new Array(24).fill(null).map((_, hour) => ({
    hour,
    trades: [],
    pnl: 0,
    winningTrades: 0,
    commission: 0
  }));

  trades.forEach(trade => {
    const hour = new Date(trade.timestamp).getHours();
    hourMap[hour].trades.push(trade);
    hourMap[hour].pnl += trade.profit_loss;
    hourMap[hour].commission += trade.commission;
    if (trade.profit_loss - trade.commission > 0) {
      hourMap[hour].winningTrades++;
    }
  });

  return hourMap
    .filter(stats => stats.trades.length > 0)
    .map(stats => ({
      hour: stats.hour,
      trades: stats.trades.length,
      winRate: (stats.winningTrades / stats.trades.length) * 100,
      pnl: stats.pnl - stats.commission,
      avgTrade: (stats.pnl - stats.commission) / stats.trades.length
    }))
    .sort((a, b) => b.pnl - a.pnl);
}

export function calculateRiskStats(trades) {
  if (!trades?.length) {
    return {
      maxDrawdown: 0,
      peakEquity: 0,
      valleyEquity: 0,
      avgRisk: 0,
      maxRisk: 0,
      minRisk: 0,
      riskRewardRatio: 0,
      avgWin: 0,
      avgLoss: 0
    };
  }

  // Calculate drawdown with commissions
  let runningPnL = 0;
  let peakEquity = 0;
  let maxDrawdown = 0;
  let valleyEquity = 0;

  trades.forEach(trade => {
    runningPnL += (trade.profit_loss - trade.commission);
    
    if (runningPnL > peakEquity) {
      peakEquity = runningPnL;
    }

    const drawdown = peakEquity > 0 ? ((peakEquity - runningPnL) / peakEquity) * 100 : 0;
    if (drawdown > maxDrawdown) {
      maxDrawdown = drawdown;
      valleyEquity = runningPnL;
    }
  });

  // Calculate risk metrics including commissions
  const winningTrades = trades.filter(t => t.profit_loss - t.commission > 0);
  const losingTrades = trades.filter(t => t.profit_loss - t.commission < 0);

  const avgWin = winningTrades.length 
    ? winningTrades.reduce((sum, t) => sum + (t.profit_loss - t.commission), 0) / winningTrades.length 
    : 0;
  const avgLoss = losingTrades.length 
    ? Math.abs(losingTrades.reduce((sum, t) => sum + (t.profit_loss - t.commission), 0)) / losingTrades.length 
    : 0;

  const risks = trades.map(trade => ({
    risk: Math.abs(trade.entry_price - (trade.stop_loss || trade.entry_price)) * trade.size,
    reward: Math.abs((trade.target_price || trade.exit_price) - trade.entry_price) * trade.size
  }));

  const avgRisk = risks.reduce((sum, r) => sum + r.risk, 0) / risks.length;
  const maxRisk = Math.max(...risks.map(r => r.risk));
  const minRisk = Math.min(...risks.map(r => r.risk));
  const avgReward = risks.reduce((sum, r) => sum + r.reward, 0) / risks.length;

  return {
    maxDrawdown,
    peakEquity,
    valleyEquity,
    avgRisk,
    maxRisk,
    minRisk,
    riskRewardRatio: avgRisk ? avgReward / avgRisk : 0,
    avgWin,
    avgLoss
  };
}

export function calculateComplianceStats(trades) {
  if (!trades?.length) {
    return {
      dailyLossLimitViolations: [],
      positionSizeViolations: [],
      overallCompliance: 100
    };
  }

  const DAILY_LOSS_LIMIT = -5000; // $5,000 daily loss limit
  const MAX_POSITION_SIZE = 100; // Maximum position size

  // Group trades by day and include commissions
  const dailyTrades = trades.reduce((acc, trade) => {
    const date = startOfDay(new Date(trade.timestamp)).toISOString();
    if (!acc[date]) {
      acc[date] = {
        trades: [],
        pnl: 0,
        commission: 0
      };
    }
    acc[date].trades.push(trade);
    acc[date].pnl += trade.profit_loss;
    acc[date].commission += trade.commission;
    return acc;
  }, {});

  // Check daily loss limit violations including commissions
  const dailyLossLimitViolations = Object.entries(dailyTrades)
    .map(([date, day]) => {
      const netPnL = day.pnl - day.commission;
      return {
        date,
        pnl: netPnL,
        violated: netPnL < DAILY_LOSS_LIMIT
      };
    })
    .filter(day => day.violated);

  // Check position size violations
  const positionSizeViolations = trades
    .filter(trade => trade.size > MAX_POSITION_SIZE)
    .map(trade => ({
      timestamp: trade.timestamp,
      size: trade.size,
      instrument: trade.instrument
    }));

  // Calculate overall compliance
  const totalViolations = dailyLossLimitViolations.length + positionSizeViolations.length;
  const overallCompliance = Math.max(0, 100 - (totalViolations / trades.length) * 100);

  return {
    dailyLossLimitViolations,
    positionSizeViolations,
    overallCompliance
  };
}

export function calculateTradePatterns(trades) {
  if (!trades?.length) return [];

  const patterns = [];
  let consecutiveWins = 0;
  let consecutiveLosses = 0;
  let currentPnL = 0;

  trades.forEach((trade, index) => {
    const netPnL = trade.profit_loss - trade.commission;
    currentPnL += netPnL;
    const isWin = netPnL > 0;
    
    // Update consecutive counts
    if (isWin) {
      consecutiveWins++;
      consecutiveLosses = 0;
    } else {
      consecutiveLosses++;
      consecutiveWins = 0;
    }

    // Check for patterns
    if (consecutiveWins >= 3) {
      patterns.push({
        type: 'winning_streak',
        count: consecutiveWins,
        startIndex: index - consecutiveWins + 1,
        endIndex: index,
        trades: trades.slice(index - consecutiveWins + 1, index + 1),
        totalPnL: currentPnL
      });
    }

    if (consecutiveLosses >= 3) {
      patterns.push({
        type: 'losing_streak',
        count: consecutiveLosses,
        startIndex: index - consecutiveLosses + 1,
        endIndex: index,
        trades: trades.slice(index - consecutiveLosses + 1, index + 1),
        totalPnL: currentPnL
      });
    }
  });

  return patterns;
}

export class TradeAnalytics {
  constructor(trades) {
    this.trades = trades || [];
    this.summary = this.calculateSummary();
    this.volatility = this.calculateVolatility();
  }

  calculateSummary() {
    if (!this.trades.length) {
      return {
        winRate: 0,
        profitFactor: 0,
        expectancy: 0,
        maxDrawdown: 0,
        avgTrade: 0,
        avgWin: 0,
        avgLoss: 0,
        netPnL: 0,
        grossProfit: 0,
        grossLoss: 0,
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        profitableDays: 0,
        tradingDays: 0
      };
    }

    const winningTrades = this.trades.filter(t => (t.profit_loss || 0) > 0);
    const losingTrades = this.trades.filter(t => (t.profit_loss || 0) < 0);
    const grossProfit = winningTrades.reduce((sum, t) => sum + (t.profit_loss || 0), 0);
    const grossLoss = Math.abs(losingTrades.reduce((sum, t) => sum + (t.profit_loss || 0), 0));
    const netPnL = this.trades.reduce((sum, t) => sum + (t.profit_loss || 0), 0);

    // Calculate daily stats
    const dailyPnL = this.trades.reduce((acc, trade) => {
      const date = new Date(trade.timestamp).toDateString();
      if (!acc[date]) acc[date] = 0;
      acc[date] += trade.profit_loss || 0;
      return acc;
    }, {});

    const profitableDays = Object.values(dailyPnL).filter(pnl => pnl > 0).length;
    const tradingDays = Object.keys(dailyPnL).length;

    return {
      winRate: (winningTrades.length / this.trades.length) * 100,
      profitFactor: grossLoss === 0 ? grossProfit : grossProfit / grossLoss,
      expectancy: winningTrades.length && losingTrades.length ? 
        (grossProfit / winningTrades.length) * (winningTrades.length / this.trades.length) -
        (grossLoss / losingTrades.length) * (losingTrades.length / this.trades.length) : 0,
      maxDrawdown: this.calculateMaxDrawdown(),
      avgTrade: netPnL / this.trades.length,
      avgWin: winningTrades.length ? grossProfit / winningTrades.length : 0,
      avgLoss: losingTrades.length ? -grossLoss / losingTrades.length : 0,
      netPnL,
      grossProfit,
      grossLoss,
      totalTrades: this.trades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      profitableDays,
      tradingDays
    };
  }

  calculateVolatility() {
    if (!this.trades.length) {
      return {
        sharpeRatio: 0,
        standardDeviation: 0,
        annualizedVolatility: 0
      };
    }

    const returns = this.trades.map(t => t.profit_loss || 0);
    const meanReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - meanReturn, 2), 0) / returns.length;
    const standardDeviation = Math.sqrt(variance);
    const annualizedVolatility = standardDeviation * Math.sqrt(252); // Assuming 252 trading days per year

    // Calculate Sharpe Ratio (assuming risk-free rate of 2%)
    const riskFreeRate = 0.02 / 252; // Daily risk-free rate
    const excessReturn = meanReturn - riskFreeRate;
    const sharpeRatio = standardDeviation === 0 ? 0 : (excessReturn / standardDeviation) * Math.sqrt(252);

    return {
      sharpeRatio,
      standardDeviation,
      annualizedVolatility
    };
  }

  calculateMaxDrawdown() {
    if (!this.trades.length) return 0;

    let peak = -Infinity;
    let maxDrawdown = 0;
    let runningPnL = 0;

    for (const trade of this.trades) {
      runningPnL += trade.profit_loss || 0;
      peak = Math.max(peak, runningPnL);
      const drawdown = ((peak - runningPnL) / Math.abs(peak)) * 100;
      maxDrawdown = Math.max(maxDrawdown, drawdown);
    }

    return maxDrawdown;
  }

  getPerformanceScore() {
    if (!this.trades?.length) return 0;

    const weights = {
      winRate: 0.25,
      profitFactor: 0.25,
      expectancy: 0.25,
      drawdown: 0.25
    };

    // Get base metrics with validation
    const baseWinRate = this.summary.winRate;
    const winRate = isFinite(baseWinRate) ? Math.min(Math.max(baseWinRate, 0), 100) : 0;

    const baseProfitFactor = this.summary.profitFactor;
    const profitFactor = isFinite(baseProfitFactor) ? Math.min(baseProfitFactor, 3) : 0;

    const baseExpectancy = this.summary.expectancy;
    const expectancy = isFinite(baseExpectancy) ? Math.min(Math.max(baseExpectancy, -100), 100) : 0;

    const baseDrawdown = this.summary.maxDrawdown;
    const drawdown = isFinite(baseDrawdown) ? Math.min(Math.max(baseDrawdown, 0), 100) : 100;

    // Normalize metrics to 0-1 scale
    const normalizedMetrics = {
      winRate: winRate / 100,  // 0-100% -> 0-1
      profitFactor: profitFactor / 3,  // 0-3 -> 0-1
      expectancy: (expectancy + 100) / 200,  // -100 to 100 -> 0-1
      drawdown: 1 - (drawdown / 100)  // 0-100% -> 1-0 (inverted)
    };

    // Calculate weighted score
    const score = Object.entries(weights).reduce((total, [metric, weight]) => {
      const value = normalizedMetrics[metric];
      return total + (value * weight);
    }, 0);

    // Convert to 0-100 scale
    return Math.round(score * 100);
  }

  getRecommendations() {
    if (!this.trades?.length) {
      return {
        strengths: [],
        improvements: []
      };
    }

    const strengths = [];
    const improvements = [];

    // Win Rate Analysis
    if (this.summary.winRate >= 55) {
      strengths.push({
        metric: 'Win Rate',
        value: `${this.summary.winRate.toFixed(1)}%`,
        message: 'Strong win rate above target'
      });
    } else if (this.summary.winRate < 45) {
      improvements.push({
        metric: 'Win Rate',
        value: `${this.summary.winRate.toFixed(1)}%`,
        message: 'Consider reviewing entry criteria to improve win rate'
      });
    }

    // Profit Factor Analysis
    if (this.summary.profitFactor >= 1.5) {
      strengths.push({
        metric: 'Profit Factor',
        value: this.summary.profitFactor.toFixed(2),
        message: 'Healthy profit factor indicating good risk management'
      });
    } else if (this.summary.profitFactor < 1.2) {
      improvements.push({
        metric: 'Profit Factor',
        value: this.summary.profitFactor.toFixed(2),
        message: 'Focus on increasing average win size relative to losses'
      });
    }

    // Drawdown Analysis
    if (this.summary.maxDrawdown <= 10) {
      strengths.push({
        metric: 'Max Drawdown',
        value: `${this.summary.maxDrawdown.toFixed(1)}%`,
        message: 'Well-controlled drawdown indicating good risk management'
      });
    } else if (this.summary.maxDrawdown > 20) {
      improvements.push({
        metric: 'Max Drawdown',
        value: `${this.summary.maxDrawdown.toFixed(1)}%`,
        message: 'Consider reducing position sizes to manage drawdown risk'
      });
    }

    // Sharpe Ratio Analysis
    if (this.volatility.sharpeRatio >= 1.5) {
      strengths.push({
        metric: 'Sharpe Ratio',
        value: this.volatility.sharpeRatio.toFixed(2),
        message: 'Strong risk-adjusted returns'
      });
    } else if (this.volatility.sharpeRatio < 1) {
      improvements.push({
        metric: 'Sharpe Ratio',
        value: this.volatility.sharpeRatio.toFixed(2),
        message: 'Consider optimizing strategy for better risk-adjusted returns'
      });
    }

    // Average Trade Analysis
    if (this.summary.avgTrade > 0) {
      strengths.push({
        metric: 'Average Trade',
        value: formatCurrency(this.summary.avgTrade),
        message: 'Positive average trade indicating consistent profitability'
      });
    } else if (this.summary.avgTrade < 0) {
      improvements.push({
        metric: 'Average Trade',
        value: formatCurrency(this.summary.avgTrade),
        message: 'Focus on improving average trade profitability'
      });
    }

    // Win/Loss Ratio Analysis
    const winLossRatio = Math.abs(this.summary.avgWin / this.summary.avgLoss);
    if (winLossRatio >= 1.5) {
      strengths.push({
        metric: 'Win/Loss Ratio',
        value: winLossRatio.toFixed(2),
        message: 'Strong win/loss ratio indicating good trade management'
      });
    } else if (winLossRatio < 1) {
      improvements.push({
        metric: 'Win/Loss Ratio',
        value: winLossRatio.toFixed(2),
        message: 'Work on improving win size relative to loss size'
      });
    }

    return {
      strengths: strengths.slice(0, 3), // Return top 3 strengths
      improvements: improvements.slice(0, 3) // Return top 3 improvements
    };
  }
}

// Helper function for currency formatting
function formatCurrency(value) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
}