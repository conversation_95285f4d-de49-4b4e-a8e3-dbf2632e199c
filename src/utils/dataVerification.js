/**
 * Data Verification Utilities for TRADEDGE
 * Comprehensive testing of Supabase data persistence and retrieval
 */

import { db } from '../lib/supabase';

/**
 * Verify user data persistence and retrieval
 */
export const verifyUserData = async (userId) => {
  try {
    console.log('🔍 Verifying user data persistence...');
    
    // Test 1: Retrieve user profile
    const userProfile = await db.users.get(userId);
    console.log('✅ User profile retrieved:', userProfile);
    
    // Test 2: Update user profile
    const updates = {
      timezone: 'America/New_York',
      currency: 'USD',
      preferences: {
        theme: 'dark',
        notifications: {
          email: true,
          trades: true
        }
      }
    };
    
    const updatedProfile = await db.users.update(userId, updates);
    console.log('✅ User profile updated:', updatedProfile);
    
    return {
      success: true,
      profile: updatedProfile
    };
  } catch (error) {
    console.error('❌ User data verification failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Verify trade data persistence and retrieval
 */
export const verifyTradeData = async (userId) => {
  try {
    console.log('🔍 Verifying trade data persistence...');
    
    // Test 1: Create a new trade
    const newTrade = {
      user_id: userId,
      symbol: 'TSLA',
      trade_type: 'LONG',
      entry_price: 200.00,
      exit_price: 210.00,
      quantity: 50,
      entry_date: new Date('2025-01-31T10:00:00Z').toISOString(),
      exit_date: new Date('2025-01-31T15:30:00Z').toISOString(),
      fees: 1.50,
      status: 'CLOSED',
      notes: 'Test trade for data verification',
      strategy: 'Momentum',
      market_conditions: 'Bullish'
    };
    
    const createdTrade = await db.trades.create(newTrade);
    console.log('✅ Trade created:', createdTrade);
    
    // Test 2: Retrieve all trades
    const allTrades = await db.trades.getAll(userId);
    console.log('✅ All trades retrieved:', allTrades.length, 'trades');
    
    // Test 3: Update the trade
    const tradeUpdates = {
      notes: 'Updated test trade - verification successful',
      strategy: 'Updated Momentum Strategy'
    };
    
    const updatedTrade = await db.trades.update(createdTrade.id, tradeUpdates);
    console.log('✅ Trade updated:', updatedTrade);
    
    // Test 4: Retrieve single trade
    const singleTrade = await db.trades.get(createdTrade.id);
    console.log('✅ Single trade retrieved:', singleTrade);
    
    // Test 5: Filter trades
    const filteredTrades = await db.trades.getAll(userId, {
      symbol: 'TSLA',
      status: 'CLOSED'
    });
    console.log('✅ Filtered trades retrieved:', filteredTrades.length, 'TSLA trades');
    
    return {
      success: true,
      tradesCount: allTrades.length,
      testTrade: updatedTrade
    };
  } catch (error) {
    console.error('❌ Trade data verification failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Verify goals data persistence and retrieval
 */
export const verifyGoalsData = async (userId) => {
  try {
    console.log('🔍 Verifying goals data persistence...');
    
    // Test 1: Create a new goal
    const newGoal = {
      user_id: userId,
      title: 'Monthly Profit Target',
      description: 'Achieve $5000 profit this month',
      goal_type: 'profit_target',
      target_value: 5000.00,
      current_value: 1250.00,
      unit: 'USD',
      period_type: 'monthly',
      start_date: '2025-01-01',
      end_date: '2025-01-31',
      status: 'active',
      priority: 1
    };
    
    const createdGoal = await db.goals.create(newGoal);
    console.log('✅ Goal created:', createdGoal);
    
    // Test 2: Retrieve all goals
    const allGoals = await db.goals.getAll(userId);
    console.log('✅ All goals retrieved:', allGoals.length, 'goals');
    
    // Test 3: Update goal progress
    const goalUpdates = {
      current_value: 2500.00,
      status: 'active'
    };
    
    const updatedGoal = await db.goals.update(createdGoal.id, goalUpdates);
    console.log('✅ Goal updated:', updatedGoal);
    
    return {
      success: true,
      goalsCount: allGoals.length,
      testGoal: updatedGoal
    };
  } catch (error) {
    console.error('❌ Goals data verification failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Comprehensive data verification test
 */
export const runDataVerification = async (userId) => {
  console.log('🚀 Starting comprehensive data verification for user:', userId);
  
  const results = {
    user: await verifyUserData(userId),
    trades: await verifyTradeData(userId),
    goals: await verifyGoalsData(userId)
  };
  
  const allSuccessful = Object.values(results).every(result => result.success);
  
  console.log('📊 Data Verification Results:');
  console.log('- User Data:', results.user.success ? '✅ PASS' : '❌ FAIL');
  console.log('- Trade Data:', results.trades.success ? '✅ PASS' : '❌ FAIL');
  console.log('- Goals Data:', results.goals.success ? '✅ PASS' : '❌ FAIL');
  console.log('- Overall:', allSuccessful ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED');
  
  return {
    success: allSuccessful,
    results,
    summary: {
      userProfile: results.user.success,
      tradesCount: results.trades.success ? results.trades.tradesCount : 0,
      goalsCount: results.goals.success ? results.goals.goalsCount : 0
    }
  };
};

/**
 * Test Row Level Security (RLS) by attempting to access another user's data
 */
export const testRLS = async (currentUserId) => {
  try {
    console.log('🔒 Testing Row Level Security...');
    
    // Try to access all users (should only return current user)
    const { data: allUsers, error } = await supabase
      .from('users')
      .select('id, email');
    
    if (error) {
      console.log('✅ RLS working - cannot access users table directly');
    } else {
      console.log('📊 Accessible users:', allUsers.length, '(should be 1 or 0)');
    }
    
    // Try to access all trades (should only return current user's trades)
    const { data: allTrades, error: tradesError } = await supabase
      .from('trades')
      .select('id, user_id, symbol');
    
    if (tradesError) {
      console.log('✅ RLS working - cannot access trades table directly');
    } else {
      const uniqueUserIds = [...new Set(allTrades.map(t => t.user_id))];
      console.log('📊 Accessible trades:', allTrades.length, 'from', uniqueUserIds.length, 'user(s)');
      console.log('✅ RLS verified - only current user data accessible');
    }
    
    return {
      success: true,
      message: 'RLS is working correctly'
    };
  } catch (error) {
    console.error('❌ RLS test failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
