export function calculateHistoricalComparison(trades, period) {
  if (!trades?.length) {
    return {
      current: getEmptyMetrics(),
      previous: getEmptyMetrics()
    };
  }

  const periodDays = getPeriodDays(period);
  const now = new Date();
  const periodStart = new Date(now.setDate(now.getDate() - periodDays));
  const previousStart = new Date(periodStart.setDate(periodStart.getDate() - periodDays));

  const currentTrades = trades.filter(t => new Date(t.timestamp) >= periodStart);
  const previousTrades = trades.filter(t => 
    new Date(t.timestamp) >= previousStart && new Date(t.timestamp) < periodStart
  );

  return {
    current: calculateMetrics(currentTrades),
    previous: calculateMetrics(previousTrades)
  };
}

function calculateMetrics(trades) {
  if (!trades.length) return getEmptyMetrics();

  const stopLossUsage = trades.filter(t => t.stop_loss).length / trades.length * 100;
  const positionSizeCompliance = trades.filter(t => t.size <= 100).length / trades.length * 100;
  const planAdherence = calculatePlanAdherence(trades);
  const ruleViolations = countRuleViolations(trades);
  const rrCompliance = calculateRRCompliance(trades);
  const targetAchievement = calculateTargetAchievement(trades);

  return {
    stopLossUsage,
    positionSizeCompliance,
    planAdherence,
    ruleViolations,
    rrCompliance,
    targetAchievement,
    disciplineScore: (planAdherence + positionSizeCompliance) / 2,
    riskScore: (stopLossUsage + rrCompliance) / 2,
    overallScore: (stopLossUsage + positionSizeCompliance + planAdherence + rrCompliance) / 4
  };
}

function getEmptyMetrics() {
  return {
    stopLossUsage: 0,
    positionSizeCompliance: 0,
    planAdherence: 0,
    ruleViolations: 0,
    rrCompliance: 0,
    targetAchievement: 0,
    disciplineScore: 0,
    riskScore: 0,
    overallScore: 0
  };
}

function getPeriodDays(period) {
  switch (period) {
    case '1M': return 30;
    case '3M': return 90;
    case '6M': return 180;
    case '1Y': return 365;
    default: return 30;
  }
}

// Helper functions for metric calculations
function calculatePlanAdherence(trades) {
  // Implementation depends on your specific trading plan rules
  return 80; // Placeholder
}

function countRuleViolations(trades) {
  let violations = 0;
  trades.forEach(trade => {
    if (trade.size > 100) violations++;
    if (trade.profit_loss < -5000) violations++;
    if (!trade.stop_loss) violations++;
  });
  return violations;
}

function calculateRRCompliance(trades) {
  const compliantTrades = trades.filter(trade => {
    const riskReward = Math.abs((trade.target_price - trade.entry_price) / 
                               (trade.entry_price - trade.stop_loss));
    return riskReward >= 1.5;
  });
  return (compliantTrades.length / trades.length) * 100;
}

function calculateTargetAchievement(trades) {
  const achievedTrades = trades.filter(trade => 
    trade.profit_loss > 0 && trade.exit_price >= trade.target_price
  );
  return (achievedTrades.length / trades.length) * 100;
} 