const { differenceInMinutes } = require('date-fns');

// NOTE: This file only uses pre-calculated P&L values from trades
// P&L calculation is handled by the centralized pnlCalculator utility and database trigger

function calculateTradeMetrics(trades) {
  if (!trades?.length) {
    return {
      basic: getEmptyBasicMetrics(),
      risk: getEmptyRiskMetrics(),
      volatility: getEmptyVolatilityMetrics()
    };
  }

  // Group trades by day for proper P&L calculation
  const dailyTrades = trades.reduce((acc, trade) => {
    const date = new Date(trade.timestamp).toISOString().split('T')[0];
    if (!acc[date]) {
      acc[date] = { trades: [], pnl: 0, commission: 0 };
    }
    acc[date].trades.push(trade);
    acc[date].pnl += (trade.profit_loss || 0);
    acc[date].commission += (trade.commission || 0);
    return acc;
  }, {});

  // Calculate winning and losing trades properly
  const winningTrades = trades.filter(t => (t.profit_loss || 0) > 0);
  const losingTrades = trades.filter(t => (t.profit_loss || 0) < 0);
  const breakEvenTrades = trades.filter(t => (t.profit_loss || 0) === 0);

  // Calculate basic metrics
  const totalTrades = trades.length;
  const winningTradesCount = winningTrades.length;
  const losingTradesCount = losingTrades.length;
  const winRate = (winningTradesCount / totalTrades) * 100;

  // Calculate P&L metrics with proper commission handling
  const totalCommission = trades.reduce((sum, t) => sum + (t.commission || 0), 0);
  const grossProfit = winningTrades.reduce((sum, t) => sum + (t.profit_loss || 0), 0);
  const grossLoss = Math.abs(losingTrades.reduce((sum, t) => sum + (t.profit_loss || 0), 0));
  const netPnL = trades.reduce((sum, t) => sum + ((t.profit_loss || 0) - (t.commission || 0)), 0);
  const profitFactor = grossLoss !== 0 ? grossProfit / grossLoss : grossProfit > 0 ? Infinity : 0;

  // Calculate average trade metrics
  const avgWin = winningTradesCount > 0 ? grossProfit / winningTradesCount : 0;
  const avgLoss = losingTradesCount > 0 ? grossLoss / losingTradesCount : 0;
  const avgTrade = totalTrades > 0 ? netPnL / totalTrades : 0;

  // Calculate drawdown
  let maxDrawdown = 0;
  let maxDrawdownAmount = 0;
  let debugDrawdown = [];

  // Sort trades by timestamp for accurate drawdown calculation
  const sortedTrades = [...trades].sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
  
  // Calculate running P&L and drawdown
  let runningPnL = 0;
  let highWaterMark = 0;

  sortedTrades.forEach((trade, index) => {
    const tradePnL = (trade.profit_loss || 0) - (trade.commission || 0);
    runningPnL += tradePnL;
    
    if (runningPnL > highWaterMark) {
      highWaterMark = runningPnL;
    }
    
    const currentDrawdown = highWaterMark - runningPnL;
    const drawdownPercent = highWaterMark > 0 ? (currentDrawdown / highWaterMark) * 100 : 0;
    
    debugDrawdown.push({
      tradeIndex: index + 1,
      timestamp: trade.timestamp,
      pnl: trade.profit_loss,
      commission: trade.commission,
      tradePnL,
      runningPnL,
      highWaterMark,
      currentDrawdown,
      drawdownPercent
    });
    
    if (drawdownPercent > maxDrawdown) {
      maxDrawdown = drawdownPercent;
      maxDrawdownAmount = currentDrawdown;
    }
  });

  console.log('\nDrawdown Calculation:');
  console.table(debugDrawdown);

  // Calculate Sharpe Ratio using daily returns
  const dailyReturns = Object.values(dailyTrades).map(day => day.pnl - day.commission);
  const averageReturn = dailyReturns.reduce((sum, r) => sum + r, 0) / dailyReturns.length;
  const variance = dailyReturns.reduce((sum, r) => sum + Math.pow(r - averageReturn, 2), 0) / dailyReturns.length;
  const stdDev = Math.sqrt(variance || 0);
  const annualFactor = Math.sqrt(252); // Annualization factor for daily returns
  const riskFreeRate = 0.02 / 252; // Daily risk-free rate (2% annual)
  const annualizedReturn = averageReturn * 252;
  const sharpeRatio = stdDev > 0 ? 
    ((averageReturn - riskFreeRate) * annualFactor) / stdDev : 
    averageReturn > 0 ? 3 : 0; // Default to 3 for positive returns with no volatility

  // Calculate expectancy
  const winProbability = winRate / 100;
  const lossProbability = 1 - winProbability;
  const expectancy = (avgWin * winProbability) - (avgLoss * lossProbability);

  return {
    basic: {
      totalTrades,
      winningTrades: winningTradesCount,
      losingTrades: losingTradesCount,
      breakEvenTrades: breakEvenTrades.length,
      winRate,
      profitFactor,
      netPnL,
      grossProfit,
      grossLoss,
      avgTrade,
      avgWin,
      avgLoss,
      largestWin: winningTrades.length > 0 ? Math.max(...winningTrades.map(t => t.profit_loss || 0)) : 0,
      largestLoss: losingTrades.length > 0 ? Math.min(...losingTrades.map(t => t.profit_loss || 0)) : 0
    },
    risk: {
      maxDrawdown,
      maxDrawdownAmount,
      expectancy,
      riskRewardRatio: Math.abs(avgLoss) !== 0 ? avgWin / Math.abs(avgLoss) : 0,
      totalCommission,
      avgCommission: totalCommission / totalTrades
    },
    volatility: {
      sharpeRatio,
      stdDev,
      averageReturn,
      annualizedReturn
    }
  };
}

function getEmptyBasicMetrics() {
  return {
    totalTrades: 0,
    winningTrades: 0,
    losingTrades: 0,
    breakEvenTrades: 0,
    winRate: 0,
    profitFactor: 0,
    netPnL: 0,
    grossProfit: 0,
    grossLoss: 0,
    avgTrade: 0,
    avgWin: 0,
    avgLoss: 0,
    largestWin: 0,
    largestLoss: 0
  };
}

function getEmptyRiskMetrics() {
  return {
    maxDrawdown: 0,
    maxDrawdownAmount: 0,
    expectancy: 0,
    riskRewardRatio: 0,
    totalCommission: 0,
    avgCommission: 0
  };
}

function getEmptyVolatilityMetrics() {
  return {
    sharpeRatio: 0,
    stdDev: 0,
    averageReturn: 0,
    annualizedReturn: 0
  };
}

function formatMetric(value, type) {
  if (value === undefined || value === null || isNaN(value)) {
    return 'N/A';
  }

  switch (type) {
    case 'currency':
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(value);
    
    case 'percentage':
      return new Intl.NumberFormat('en-US', {
        style: 'percent',
        minimumFractionDigits: 1,
        maximumFractionDigits: 1
      }).format(value / 100);
    
    case 'decimal':
      return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(value);
    
    default:
      return value.toString();
  }
}

module.exports = {
  calculateTradeMetrics,
  formatMetric
}; 