/**
 * Centralized P&L Calculation Utility
 * 
 * This is the SINGLE source of truth for P&L calculations across the entire application.
 * All other P&L calculations should be removed and replaced with calls to this utility.
 * 
 * The database trigger should use the same logic as this utility to ensure consistency.
 */

// Contract multipliers for futures symbols
const CONTRACT_MULTIPLIERS = {
  'NQ': 20,    // Nasdaq-100 E-mini: $20 per point
  'ES': 50,    // S&P 500 E-mini: $50 per point
  'YM': 5,     // Dow E-mini: $5 per point
  'RTY': 50,   // Russell 2000 E-mini: $50 per point
  'CL': 1000,  // Crude Oil: $1000 per point
  'GC': 100,   // Gold: $100 per point
  'SI': 5000,  // Silver: $5000 per point
};

/**
 * Get the contract multiplier for a given symbol
 * @param {string} symbol - The trading symbol
 * @returns {number} The contract multiplier (1 for stocks, specific multiplier for futures)
 */
export function getContractMultiplier(symbol) {
  if (!symbol) return 1;

  const symbolUpper = symbol.toUpperCase();

  // Check for futures symbols
  for (const [key, multiplier] of Object.entries(CONTRACT_MULTIPLIERS)) {
    if (symbolUpper.includes(key)) {
      return multiplier;
    }
  }

  return 1; // Default for stocks
}

/**
 * Check if a symbol is a futures contract
 * @param {string} symbol - The trading symbol
 * @returns {boolean} True if the symbol is a futures contract
 */
export function isFuturesSymbol(symbol) {
  if (!symbol) return false;

  const symbolUpper = symbol.toUpperCase();
  return Object.keys(CONTRACT_MULTIPLIERS).some(key => symbolUpper.includes(key));
}

/**
 * Calculate P&L for a trade
 * This is the AUTHORITATIVE P&L calculation function
 * 
 * @param {Object} trade - Trade object
 * @param {number} trade.entryPrice - Entry price
 * @param {number} trade.exitPrice - Exit price (null for open trades)
 * @param {number} trade.quantity - Quantity/size
 * @param {string} trade.tradeType - 'LONG' or 'SHORT'
 * @param {string} trade.symbol - Trading symbol
 * @param {number} trade.fees - Fees/commission (default: 0)
 * @returns {number|null} P&L amount (null for open trades)
 */
export function calculatePnL(trade) {
  const {
    entryPrice,
    exitPrice,
    quantity,
    tradeType,
    symbol,
    fees = 0
  } = trade;

  // Validate inputs
  if (!entryPrice || entryPrice <= 0) return null;
  if (!exitPrice || exitPrice <= 0) return null; // Open trade
  if (!quantity || quantity <= 0) return null;
  if (!tradeType) return null;

  const parsedEntryPrice = parseFloat(entryPrice);
  const parsedExitPrice = parseFloat(exitPrice);
  const parsedQuantity = parseInt(quantity);
  const parsedFees = parseFloat(fees) || 0;
  const normalizedTradeType = tradeType.toUpperCase();

  // Validate parsed values
  if (isNaN(parsedEntryPrice) || isNaN(parsedExitPrice) || isNaN(parsedQuantity)) {
    return null;
  }

  // Get contract multiplier
  const contractMultiplier = getContractMultiplier(symbol);

  // Calculate gross P&L based on trade type
  let grossPnL;
  if (normalizedTradeType === 'LONG') {
    grossPnL = (parsedExitPrice - parsedEntryPrice) * parsedQuantity * contractMultiplier;
  } else if (normalizedTradeType === 'SHORT') {
    grossPnL = (parsedEntryPrice - parsedExitPrice) * parsedQuantity * contractMultiplier;
  } else {
    throw new Error(`Invalid trade type: ${tradeType}. Must be 'LONG' or 'SHORT'`);
  }

  // Subtract fees to get net P&L
  const netPnL = grossPnL - parsedFees;

  return parseFloat(netPnL.toFixed(2));
}

/**
 * Calculate unrealized P&L for an open trade
 * @param {Object} trade - Trade object
 * @param {number} currentPrice - Current market price
 * @returns {number|null} Unrealized P&L amount
 */
export function calculateUnrealizedPnL(trade, currentPrice) {
  if (!currentPrice || currentPrice <= 0) return null;

  return calculatePnL({
    ...trade,
    exitPrice: currentPrice
  });
}

/**
 * Calculate P&L percentage
 * @param {Object} trade - Trade object with calculated P&L
 * @param {number} pnl - The calculated P&L amount
 * @returns {number|null} P&L percentage
 */
export function calculatePnLPercentage(trade, pnl) {
  const { entryPrice, quantity, symbol } = trade;
  
  if (!entryPrice || !quantity || pnl === null || pnl === undefined) {
    return null;
  }

  const contractMultiplier = getContractMultiplier(symbol);
  const totalInvestment = parseFloat(entryPrice) * parseInt(quantity) * contractMultiplier;
  
  if (totalInvestment === 0) return null;
  
  return parseFloat(((pnl / totalInvestment) * 100).toFixed(2));
}

/**
 * Validate trade data for P&L calculation
 * @param {Object} trade - Trade object
 * @returns {Object} Validation result with isValid boolean and errors array
 */
export function validateTradeForPnL(trade) {
  const errors = [];

  if (!trade.entryPrice || parseFloat(trade.entryPrice) <= 0) {
    errors.push('Invalid entry price');
  }

  if (!trade.quantity || parseInt(trade.quantity) <= 0) {
    errors.push('Invalid quantity');
  }

  if (!trade.tradeType || !['LONG', 'SHORT'].includes(trade.tradeType.toUpperCase())) {
    errors.push('Invalid trade type (must be LONG or SHORT)');
  }

  if (!trade.symbol) {
    errors.push('Missing symbol');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Create a standardized trade object for P&L calculation
 * This handles different property names from various data sources
 * @param {Object} rawTrade - Raw trade data from any source
 * @returns {Object} Standardized trade object
 */
export function standardizeTradeObject(rawTrade) {
  return {
    entryPrice: rawTrade.entry_price || rawTrade.entryPrice,
    exitPrice: rawTrade.exit_price || rawTrade.exitPrice,
    quantity: rawTrade.size || rawTrade.quantity,
    tradeType: rawTrade.trade_type || rawTrade.side || rawTrade.position_type,
    symbol: rawTrade.instrument || rawTrade.symbol,
    fees: rawTrade.commission || rawTrade.fees || 0
  };
}

// Export the contract multipliers for use in database scripts
export { CONTRACT_MULTIPLIERS };
