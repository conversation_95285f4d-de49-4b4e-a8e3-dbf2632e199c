/**
 * P&L Fallback Calculation Utility
 * 
 * This utility provides fallback P&L calculations for scenarios where:
 * 1. Database P&L is not available (e.g., real-time calculations)
 * 2. Client-side calculations are needed before database persistence
 * 3. Validation of database-calculated P&L values
 * 
 * This should ONLY be used as a fallback. The primary source of P&L should be the database.
 */

import { calculatePnL, standardizeTradeObject, validateTradeForPnL } from './pnlCalculator';

/**
 * Calculate P&L with fallback handling
 * This function tries to use database P&L first, then falls back to calculation
 * 
 * @param {Object} trade - Trade object (can be from database or raw data)
 * @param {Object} options - Options for fallback behavior
 * @param {boolean} options.preferDatabaseValue - Whether to prefer database P&L over calculation (default: true)
 * @param {boolean} options.validateCalculation - Whether to validate calculated P&L against database value (default: false)
 * @param {number} options.tolerancePercent - Tolerance percentage for validation (default: 0.01 = 1%)
 * @returns {Object} Result object with P&L value and metadata
 */
export function calculatePnLWithFallback(trade, options = {}) {
  const {
    preferDatabaseValue = true,
    validateCalculation = false,
    tolerancePercent = 0.01
  } = options;

  const result = {
    pnl: null,
    source: null,
    isValid: true,
    warnings: [],
    errors: []
  };

  // Try to use database P&L first
  const databasePnL = trade.pnl || trade.profit_loss;
  const hasDatabasePnL = databasePnL !== null && databasePnL !== undefined && !isNaN(databasePnL);

  if (hasDatabasePnL && preferDatabaseValue) {
    result.pnl = parseFloat(databasePnL);
    result.source = 'database';
    
    // Optionally validate against calculated value
    if (validateCalculation) {
      try {
        const standardizedTrade = standardizeTradeObject(trade);
        const calculatedPnL = calculatePnL(standardizedTrade);
        
        if (calculatedPnL !== null) {
          const difference = Math.abs(result.pnl - calculatedPnL);
          const tolerance = Math.abs(result.pnl) * tolerancePercent;
          
          if (difference > tolerance) {
            result.warnings.push(
              `Database P&L (${result.pnl}) differs from calculated P&L (${calculatedPnL}) by ${difference.toFixed(2)}`
            );
          }
        }
      } catch (error) {
        result.warnings.push(`Could not validate database P&L: ${error.message}`);
      }
    }
    
    return result;
  }

  // Fallback to calculation
  try {
    const standardizedTrade = standardizeTradeObject(trade);
    const validation = validateTradeForPnL(standardizedTrade);
    
    if (!validation.isValid) {
      result.isValid = false;
      result.errors = validation.errors;
      result.source = 'calculation_failed';
      return result;
    }
    
    const calculatedPnL = calculatePnL(standardizedTrade);
    
    if (calculatedPnL !== null) {
      result.pnl = calculatedPnL;
      result.source = hasDatabasePnL ? 'calculation_override' : 'calculation';
      
      if (hasDatabasePnL && !preferDatabaseValue) {
        result.warnings.push(
          `Used calculated P&L (${calculatedPnL}) instead of database P&L (${databasePnL})`
        );
      } else if (!hasDatabasePnL) {
        result.warnings.push('Database P&L not available, used calculation');
      }
    } else {
      result.isValid = false;
      result.errors.push('Could not calculate P&L - trade may be open or have invalid data');
      result.source = 'calculation_failed';
    }
    
  } catch (error) {
    result.isValid = false;
    result.errors.push(`P&L calculation failed: ${error.message}`);
    result.source = 'calculation_error';
  }

  return result;
}

/**
 * Calculate unrealized P&L for open positions
 * This is always calculated client-side since it requires current market prices
 * 
 * @param {Object} trade - Trade object
 * @param {number} currentPrice - Current market price
 * @returns {Object} Result object with unrealized P&L
 */
export function calculateUnrealizedPnLWithFallback(trade, currentPrice) {
  const result = {
    unrealizedPnL: null,
    source: 'calculation',
    isValid: true,
    warnings: [],
    errors: []
  };

  if (!currentPrice || currentPrice <= 0) {
    result.isValid = false;
    result.errors.push('Invalid current price for unrealized P&L calculation');
    return result;
  }

  try {
    const standardizedTrade = standardizeTradeObject({
      ...trade,
      exitPrice: currentPrice
    });
    
    const validation = validateTradeForPnL(standardizedTrade);
    
    if (!validation.isValid) {
      result.isValid = false;
      result.errors = validation.errors;
      return result;
    }
    
    const unrealizedPnL = calculatePnL(standardizedTrade);
    
    if (unrealizedPnL !== null) {
      result.unrealizedPnL = unrealizedPnL;
    } else {
      result.isValid = false;
      result.errors.push('Could not calculate unrealized P&L');
    }
    
  } catch (error) {
    result.isValid = false;
    result.errors.push(`Unrealized P&L calculation failed: ${error.message}`);
  }

  return result;
}

/**
 * Batch calculate P&L for multiple trades with fallback
 * Useful for processing large datasets or validating multiple trades
 * 
 * @param {Array} trades - Array of trade objects
 * @param {Object} options - Options for fallback behavior
 * @returns {Array} Array of result objects
 */
export function batchCalculatePnLWithFallback(trades, options = {}) {
  if (!Array.isArray(trades)) {
    throw new Error('Trades must be an array');
  }

  return trades.map((trade, index) => {
    try {
      const result = calculatePnLWithFallback(trade, options);
      return {
        index,
        tradeId: trade.id || trade.trade_id || `trade_${index}`,
        ...result
      };
    } catch (error) {
      return {
        index,
        tradeId: trade.id || trade.trade_id || `trade_${index}`,
        pnl: null,
        source: 'batch_error',
        isValid: false,
        warnings: [],
        errors: [`Batch processing error: ${error.message}`]
      };
    }
  });
}

/**
 * Get summary statistics for batch P&L calculation results
 * @param {Array} results - Results from batchCalculatePnLWithFallback
 * @returns {Object} Summary statistics
 */
export function getBatchPnLSummary(results) {
  const summary = {
    total: results.length,
    successful: 0,
    failed: 0,
    sources: {
      database: 0,
      calculation: 0,
      calculation_override: 0,
      calculation_failed: 0,
      calculation_error: 0,
      batch_error: 0
    },
    totalWarnings: 0,
    totalErrors: 0,
    totalPnL: 0
  };

  results.forEach(result => {
    if (result.isValid && result.pnl !== null) {
      summary.successful++;
      summary.totalPnL += result.pnl;
    } else {
      summary.failed++;
    }

    if (result.source && summary.sources.hasOwnProperty(result.source)) {
      summary.sources[result.source]++;
    }

    summary.totalWarnings += result.warnings?.length || 0;
    summary.totalErrors += result.errors?.length || 0;
  });

  return summary;
}
