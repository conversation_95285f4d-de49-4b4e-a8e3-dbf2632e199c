export const formatDecimal = (value, places = 2) => {
  if (typeof value !== 'number' || isNaN(value)) return '0.00';
  return value.toFixed(places);
};

export const formatCurrency = (value, currency = 'USD') => {
  if (typeof value !== 'number' || isNaN(value)) return '$0.00';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(value);
};

export const formatPercent = (value, places = 2) => {
  if (typeof value !== 'number' || isNaN(value)) return '0.00%';
  return `${value.toFixed(places)}%`;
};

export const formatNumber = (value, places = 2) => {
  if (typeof value !== 'number' || isNaN(value)) return '0';
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: places,
    maximumFractionDigits: places
  }).format(value);
};

export function formatDate(date) {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

export function formatTime(date) {
  return new Date(date).toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  });
} 