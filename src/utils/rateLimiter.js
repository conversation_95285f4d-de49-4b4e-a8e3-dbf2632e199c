/**
 * Client-side rate limiter for API calls
 * Prevents abuse and implements exponential backoff
 */

class RateLimiter {
  constructor() {
    this.attempts = new Map();
    this.blockedUntil = new Map();
  }

  /**
   * Check if an action is rate limited
   * @param {string} key - Unique identifier for the action
   * @param {number} maxAttempts - Maximum attempts allowed
   * @param {number} windowMs - Time window in milliseconds
   * @returns {boolean} True if action is allowed
   */
  isAllowed(key, maxAttempts = 5, windowMs = 15 * 60 * 1000) {
    const now = Date.now();
    
    // Check if currently blocked
    if (this.blockedUntil.has(key)) {
      const blockedUntil = this.blockedUntil.get(key);
      if (now < blockedUntil) {
        return false;
      } else {
        this.blockedUntil.delete(key);
        this.attempts.delete(key);
      }
    }

    // Get current attempts
    const attemptData = this.attempts.get(key) || { count: 0, firstAttempt: now };
    
    // Reset if window has passed
    if (now - attemptData.firstAttempt > windowMs) {
      attemptData.count = 0;
      attemptData.firstAttempt = now;
    }

    // Check if limit exceeded
    if (attemptData.count >= maxAttempts) {
      // Block for exponential backoff time
      const blockDuration = Math.min(windowMs * Math.pow(2, attemptData.count - maxAttempts), 60 * 60 * 1000); // Max 1 hour
      this.blockedUntil.set(key, now + blockDuration);
      return false;
    }

    return true;
  }

  /**
   * Record an attempt
   * @param {string} key - Unique identifier for the action
   */
  recordAttempt(key) {
    const now = Date.now();
    const attemptData = this.attempts.get(key) || { count: 0, firstAttempt: now };
    
    attemptData.count++;
    this.attempts.set(key, attemptData);
  }

  /**
   * Reset attempts for a key (on successful action)
   * @param {string} key - Unique identifier for the action
   */
  reset(key) {
    this.attempts.delete(key);
    this.blockedUntil.delete(key);
  }

  /**
   * Get remaining time until unblocked
   * @param {string} key - Unique identifier for the action
   * @returns {number} Milliseconds until unblocked, 0 if not blocked
   */
  getBlockedTime(key) {
    if (!this.blockedUntil.has(key)) return 0;
    
    const blockedUntil = this.blockedUntil.get(key);
    const remaining = blockedUntil - Date.now();
    
    return Math.max(0, remaining);
  }

  /**
   * Format blocked time for user display
   * @param {string} key - Unique identifier for the action
   * @returns {string} Human-readable time remaining
   */
  getBlockedTimeString(key) {
    const ms = this.getBlockedTime(key);
    if (ms === 0) return '';
    
    const seconds = Math.ceil(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''}`;
    } else if (minutes > 0) {
      return `${minutes} minute${minutes > 1 ? 's' : ''}`;
    } else {
      return `${seconds} second${seconds > 1 ? 's' : ''}`;
    }
  }
}

// Create singleton instance
const rateLimiter = new RateLimiter();

/**
 * Rate limit authentication attempts
 * @param {string} email - User email for rate limiting key
 * @returns {Object} Rate limit status
 */
export const checkAuthRateLimit = (email) => {
  const key = `auth:${email}`;
  const isAllowed = rateLimiter.isAllowed(key, 5, 15 * 60 * 1000); // 5 attempts per 15 minutes
  
  if (!isAllowed) {
    const blockedTime = rateLimiter.getBlockedTimeString(key);
    return {
      allowed: false,
      message: `Too many login attempts. Please try again in ${blockedTime}.`
    };
  }
  
  return { allowed: true };
};

/**
 * Record authentication attempt
 * @param {string} email - User email
 * @param {boolean} success - Whether the attempt was successful
 */
export const recordAuthAttempt = (email, success) => {
  const key = `auth:${email}`;
  
  if (success) {
    rateLimiter.reset(key);
  } else {
    rateLimiter.recordAttempt(key);
  }
};

/**
 * Rate limit API calls
 * @param {string} endpoint - API endpoint
 * @param {string} userId - User ID (optional)
 * @returns {Object} Rate limit status
 */
export const checkApiRateLimit = (endpoint, userId = 'anonymous') => {
  const key = `api:${endpoint}:${userId}`;
  const isAllowed = rateLimiter.isAllowed(key, 100, 15 * 60 * 1000); // 100 requests per 15 minutes
  
  if (!isAllowed) {
    const blockedTime = rateLimiter.getBlockedTimeString(key);
    return {
      allowed: false,
      message: `Rate limit exceeded. Please try again in ${blockedTime}.`
    };
  }
  
  rateLimiter.recordAttempt(key);
  return { allowed: true };
};

export default rateLimiter;
