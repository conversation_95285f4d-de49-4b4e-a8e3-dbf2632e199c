import { format } from 'date-fns';

export class ChartManager {
  getPnLChartConfig(trades) {
    const dailyPnL = this.aggregateDailyPnL(trades);

    return {
      data: {
        labels: dailyPnL.map(d => format(d.date, 'MMM d')),
        datasets: [{
          label: 'Daily P&L',
          data: dailyPnL.map(d => d.value),
          borderColor: '#22c55e',
          backgroundColor: 'rgba(34, 197, 94, 0.1)',
          fill: true,
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: (context) => `P&L: ${this.formatCurrency(context.raw)}`
            }
          }
        },
        scales: {
          x: {
            type: 'category',
            grid: {
              display: false
            }
          },
          y: {
            type: 'linear',
            grid: {
              color: '#f3f4f6'
            },
            ticks: {
              callback: (value) => this.formatCurrency(value)
            }
          }
        }
      }
    };
  }

  getDistributionChartConfig(trades) {
    const distribution = this.calculatePnLDistribution(trades);

    return {
      data: {
        labels: distribution.map(d => d.range),
        datasets: [{
          data: distribution.map(d => d.count),
          backgroundColor: distribution.map(d => 
            d.range.includes('+') ? '#22c55e' : '#ef4444'
          )
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          x: {
            type: 'category',
            grid: {
              display: false
            }
          },
          y: {
            type: 'linear',
            beginAtZero: true,
            grid: {
              color: '#f3f4f6'
            }
          }
        }
      }
    };
  }

  aggregateDailyPnL(trades) {
    const dailyMap = new Map();

    trades.forEach(trade => {
      const date = new Date(trade.timestamp);
      date.setHours(0, 0, 0, 0);
      const key = date.getTime();

      if (!dailyMap.has(key)) {
        dailyMap.set(key, {
          date,
          value: 0
        });
      }

      dailyMap.get(key).value += trade.profit_loss;
    });

    return Array.from(dailyMap.values())
      .sort((a, b) => a.date - b.date);
  }

  calculatePnLDistribution(trades) {
    const ranges = [
      { min: -Infinity, max: -1000, label: '< -$1000' },
      { min: -1000, max: -500, label: '-$500 to -$1000' },
      { min: -500, max: -100, label: '-$100 to -$500' },
      { min: -100, max: 0, label: '-$0 to -$100' },
      { min: 0, max: 100, label: '+$0 to +$100' },
      { min: 100, max: 500, label: '+$100 to +$500' },
      { min: 500, max: 1000, label: '+$500 to +$1000' },
      { min: 1000, max: Infinity, label: '> +$1000' }
    ];

    const distribution = ranges.map(range => ({
      range: range.label,
      count: trades.filter(t => 
        t.profit_loss > range.min && 
        t.profit_loss <= range.max
      ).length
    }));

    return distribution;
  }

  formatCurrency(value) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(value);
  }
} 