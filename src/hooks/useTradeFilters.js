import { useState, useCallback } from 'react';

export function useTradeFilters(initialTrades) {
  const [filteredTrades, setFilteredTrades] = useState(initialTrades);
  const [activeFilters, setActiveFilters] = useState(null);

  const handleFilterChange = useCallback((trades, filters) => {
    setActiveFilters(filters);
    setFilteredTrades(applyFilters(trades, filters));
  }, []);

  const clearFilters = useCallback(() => {
    setActiveFilters(null);
    setFilteredTrades(initialTrades);
  }, [initialTrades]);

  return {
    filteredTrades,
    activeFilters,
    handleFilterChange,
    clearFilters
  };
} 