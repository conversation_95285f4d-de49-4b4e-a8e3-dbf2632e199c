import React from 'react';
import { useNavigate } from 'react-router-dom';
import GoalCardSettings from '../components/settings/GoalCardSettings';

const GoalCardSettingsRoute = () => {
  const navigate = useNavigate();

  const handleSaveSettings = (settings) => {
    // Save settings to localStorage or your backend
    localStorage.setItem('goalCardSettings', JSON.stringify(settings));
    // Navigate back to dashboard
    navigate('/dashboard');
  };

  const loadInitialSettings = () => {
    const savedSettings = localStorage.getItem('goalCardSettings');
    return savedSettings ? JSON.parse(savedSettings) : null;
  };

  return (
    <GoalCardSettings 
      onSave={handleSaveSettings}
      initialSettings={loadInitialSettings()}
    />
  );
};

export default GoalCardSettingsRoute; 