import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase, auth, db } from '../lib/supabase';
import { checkAuthRateLimit, recordAuthAttempt } from '../utils/rateLimiter';

/**
 * Enhanced AuthContext using Supabase authentication
 * Provides secure authentication with real-time session management
 */

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Initialize auth state and set up listener
  useEffect(() => {
    let mounted = true;

    // Get initial session
    const initializeAuth = async () => {
      try {
        const session = await auth.getSession();

        if (mounted) {
          if (session?.user) {
            setUser(session.user);
            await loadUserProfile(session.user.id);
          }
          setLoading(false);
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        if (mounted) {
          setError(error.message);
          setLoading(false);
        }
      }
    };

    initializeAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!mounted) return;

        console.log('Auth state changed:', event, session?.user?.email);

        if (session?.user) {
          setUser(session.user);
          await loadUserProfile(session.user.id);
        } else {
          setUser(null);
          setProfile(null);
        }

        setLoading(false);
        setError(null);
      }
    );

    return () => {
      mounted = false;
      subscription?.unsubscribe();
    };
  }, []);

  // Load user profile from database
  const loadUserProfile = async (userId) => {
    try {
      const userProfile = await db.users.get(userId);
      setProfile(userProfile);
    } catch (error) {
      console.error('Error loading user profile:', error);
      // Profile might not exist yet for new users - create it
      if (error.code === 'PGRST116') {
        console.log('Profile not found, creating new profile for user:', userId);
        await createUserProfile(userId);
      } else {
        setError(error.message);
      }
    }
  };

  // Create user profile manually (fallback for when triggers don't work)
  const createUserProfile = async (userId) => {
    try {
      // Use the current user from state instead of making another API call
      if (!user) {
        console.log('No user available for profile creation');
        return;
      }

      console.log('Creating profile for user:', user.email, 'with metadata:', user.user_metadata);

      const profileData = {
        id: userId,
        email: user.email,
        first_name: user.user_metadata?.first_name || user.email?.split('@')[0] || 'User',
        last_name: user.user_metadata?.last_name || 'User',
        avatar_url: user.user_metadata?.avatar_url || null,
        email_verified: !!user.email_confirmed_at,
        email_verified_at: user.email_confirmed_at || null,
        timezone: 'UTC',
        currency: 'USD',
        status: 'active'
      };

      console.log('Attempting to create profile with data:', profileData);

      const newProfile = await db.users.create(profileData);
      setProfile(newProfile);
      console.log('User profile created successfully:', newProfile);
    } catch (error) {
      console.error('Error creating user profile:', error);

      // If it's a duplicate key error, try to fetch the existing profile
      if (error.code === '23505') {
        console.log('Profile already exists, attempting to fetch...');
        try {
          const existingProfile = await db.users.get(userId);
          setProfile(existingProfile);
          console.log('Found existing profile:', existingProfile);
        } catch (fetchError) {
          console.error('Could not fetch existing profile:', fetchError);
        }
      }
    }
  };

  // Sign up new user
  const signUp = async (email, password, userData = {}) => {
    try {
      setLoading(true);
      setError(null);

      const { user: newUser, session } = await auth.signUp(email, password, userData);

      return {
        success: true,
        user: newUser,
        session,
        message: 'Account created successfully! Please check your email to verify your account.'
      };
    } catch (error) {
      console.error('Sign up error:', error);
      setError(error.message);
      return {
        success: false,
        error: error.message
      };
    } finally {
      setLoading(false);
    }
  };

  // Sign in user with rate limiting
  const signIn = async (email, password) => {
    try {
      setLoading(true);
      setError(null);

      // Check rate limit
      const rateLimitCheck = checkAuthRateLimit(email);
      if (!rateLimitCheck.allowed) {
        setError(rateLimitCheck.message);
        return {
          success: false,
          error: rateLimitCheck.message
        };
      }

      const { user: signedInUser, session } = await auth.signIn(email, password);

      // Record successful attempt
      recordAuthAttempt(email, true);

      return {
        success: true,
        user: signedInUser,
        session,
        message: 'Signed in successfully!'
      };
    } catch (error) {
      console.error('Sign in error:', error);

      // Record failed attempt
      recordAuthAttempt(email, false);

      setError(error.message);
      return {
        success: false,
        error: error.message
      };
    } finally {
      setLoading(false);
    }
  };

  // Sign out user
  const signOut = async () => {
    try {
      setLoading(true);
      setError(null);

      await auth.signOut();
      setUser(null);
      setProfile(null);

      return {
        success: true,
        message: 'Signed out successfully!'
      };
    } catch (error) {
      console.error('Sign out error:', error);
      setError(error.message);
      return {
        success: false,
        error: error.message
      };
    } finally {
      setLoading(false);
    }
  };

  // Update user profile
  const updateProfile = async (updates) => {
    try {
      if (!user) throw new Error('No user logged in');

      setLoading(true);
      setError(null);

      const updatedProfile = await db.users.update(user.id, updates);
      setProfile(updatedProfile);

      return {
        success: true,
        profile: updatedProfile,
        message: 'Profile updated successfully!'
      };
    } catch (error) {
      console.error('Update profile error:', error);
      setError(error.message);
      return {
        success: false,
        error: error.message
      };
    } finally {
      setLoading(false);
    }
  };

  // Reset password
  const resetPassword = async (email) => {
    try {
      setLoading(true);
      setError(null);

      await auth.resetPassword(email);

      return {
        success: true,
        message: 'Password reset email sent! Please check your inbox.'
      };
    } catch (error) {
      console.error('Reset password error:', error);
      setError(error.message);
      return {
        success: false,
        error: error.message
      };
    } finally {
      setLoading(false);
    }
  };

  // Check if user is authenticated
  const isAuthenticated = () => {
    return !!user && !!user.email_confirmed_at;
  };

  // Get user display name
  const getDisplayName = () => {
    if (profile?.first_name && profile?.last_name) {
      return `${profile.first_name} ${profile.last_name}`;
    }
    if (profile?.first_name) {
      return profile.first_name;
    }
    return user?.email?.split('@')[0] || 'User';
  };

  const value = {
    // User state
    user,
    profile,
    loading,
    error,

    // Authentication methods
    signUp,
    signIn,
    signOut,

    // Profile methods
    updateProfile,

    // Password methods
    resetPassword,

    // Utility methods
    isAuthenticated,
    getDisplayName,

    // Clear error
    clearError: () => setError(null)
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};