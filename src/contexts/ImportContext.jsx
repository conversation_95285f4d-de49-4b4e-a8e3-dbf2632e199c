import React, { createContext, useState } from 'react';

export const ImportContext = createContext();

export function ImportProvider({ children }) {
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [importHandler, setImportHandler] = useState(null);

  const showImportModal = () => {
    setIsImportModalOpen(true);
  };

  const hideImportModal = () => {
    setIsImportModalOpen(false);
  };

  const registerImportHandler = (handler) => {
    setImportHandler(() => handler);
  };

  const handleImportComplete = async (result) => {
    // Call the registered handler first (e.g., Dashboard's handler)
    if (importHandler) {
      await importHandler(result);
    }

    // Then handle modal closing
    if (!result.keepOpen) {
      hideImportModal();
    }
  };

  return (
    <ImportContext.Provider value={{
      showImportModal,
      isImportModalOpen,
      hideImportModal,
      handleImportComplete,
      registerImportHandler
    }}>
      {children}
    </ImportContext.Provider>
  );
}