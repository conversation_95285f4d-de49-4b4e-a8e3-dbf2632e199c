import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { profileService } from '../services/profileService';
import SupabaseSettingsService from '../services/supabaseSettingsService';
import { useAuth } from './AuthContext';
import { supabase } from '../lib/supabase';

const DEFAULT_PROFILE = {
  name: 'Development User',
  email: '<EMAIL>',
  avatar: null,
  timezone: 'America/New_York',
  tradingExperience: '',
  preferredMarkets: [],
  bio: ''
};

const DEFAULT_SETTINGS = {
  timeZone: 'America/New_York',
  notifications: {
    email: true,
    desktop: true,
    tradeAlerts: true
  },
  privacy: {
    profileVisibility: 'private',
    showTradingStats: false
  },
  accounts: []
};

const SYNC_INTERVAL = 30000; // 30 seconds

// Check if we're in development mode
const isDevelopment = process.env.NODE_ENV === 'development';

const ProfileContext = createContext();

export function ProfileProvider({ children }) {
  const { user } = useAuth();

  // Use refs for mutable values that shouldn't trigger re-renders
  const syncTimeoutRef = useRef(null);
  const isMountedRef = useRef(true);
  const isInitialLoadRef = useRef(true);
  
  const [profile, setProfile] = useState(() => {
    try {
      const savedProfile = localStorage.getItem('user_profile');
      return savedProfile ? JSON.parse(savedProfile) : DEFAULT_PROFILE;
    } catch (e) {
      return DEFAULT_PROFILE;
    }
  });
  
  const [settings, setSettings] = useState(() => {
    try {
      const savedSettings = localStorage.getItem('user_settings');
      return savedSettings ? JSON.parse(savedSettings) : DEFAULT_SETTINGS;
    } catch (e) {
      return DEFAULT_SETTINGS;
    }
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastSynced, setLastSynced] = useState(null);

  const loadProfile = useCallback(async (force = false) => {
    if (!user?.id) {
      console.log('⏳ Waiting for user authentication...');
      return;
    }

    if (!force && lastSynced && Date.now() - lastSynced < SYNC_INTERVAL) {
      return;
    }

    if (!isMountedRef.current) return;
    setIsLoading(true);
    setError(null);

    try {
      console.log('🔄 Loading profile and settings from Supabase...');

      // Load settings from Supabase (includes accounts)
      const userSettings = await SupabaseSettingsService.getSettings(user.id);

      // Create profile object with user data and settings
      const profileData = {
        id: user.id,
        email: user.email,
        firstName: user.user_metadata?.firstName || user.user_metadata?.first_name || '',
        lastName: user.user_metadata?.lastName || user.user_metadata?.last_name || '',
        fullName: user.user_metadata?.full_name || `${user.user_metadata?.firstName || ''} ${user.user_metadata?.lastName || ''}`.trim(),
        avatar: user.user_metadata?.avatar_url || '',
        settings: userSettings,
        accounts: userSettings.accounts || []
      };

      if (!isMountedRef.current) return;

      setProfile(profileData);
      setSettings(userSettings);
      setLastSynced(Date.now());
      console.log('✅ Profile and settings loaded from Supabase');

      // Migrate localStorage settings if this is first time
      if (isInitialLoadRef.current) {
        await SupabaseSettingsService.migrateLocalSettings(user.id);
      }
    } catch (error) {
      console.error('Error loading profile data:', error);
      if (isMountedRef.current) {
        setError('Using local data.');
      }
    } finally {
      if (isMountedRef.current) {
        setIsLoading(false);
        isInitialLoadRef.current = false;
      }
    }
  }, [lastSynced]);

  const updateProfile = async (profileData) => {
    if (!user?.id) {
      throw new Error('User not authenticated');
    }

    try {
      setError(null);
      console.log('🔄 Updating profile in Supabase...');

      // Update local state immediately for better UX
      setProfile(profileData);

      // Update user metadata in Supabase Auth
      const { error: authError } = await supabase.auth.updateUser({
        data: {
          firstName: profileData.firstName,
          lastName: profileData.lastName,
          full_name: profileData.fullName || `${profileData.firstName} ${profileData.lastName}`.trim()
        }
      });

      if (authError) throw authError;

      // Also save to localStorage as backup
      localStorage.setItem('user_profile', JSON.stringify(profileData));

      setLastSynced(Date.now());
      console.log('✅ Profile updated in Supabase');
      return profileData;
    } catch (error) {
      console.error('❌ Error updating profile:', error);
      if (isMountedRef.current) {
        setError('Failed to update profile. Changes saved locally.');
      }
      return profileData;
    }
  };

  const updateSettings = async (settingsData) => {
    if (!user?.id) {
      throw new Error('User not authenticated');
    }

    try {
      setError(null);
      console.log('🔄 Updating settings in Supabase...');

      // Update local state immediately for better UX
      setSettings(settingsData);

      // Save settings to Supabase
      await SupabaseSettingsService.saveSettings(user.id, settingsData);

      // Update profile with new settings
      setProfile(prev => ({
        ...prev,
        settings: settingsData,
        accounts: settingsData.accounts || prev.accounts
      }));

      setLastSynced(Date.now());
      console.log('✅ Settings updated in Supabase');
      return settingsData;
    } catch (error) {
      console.error('❌ Error updating settings:', error);

      // Save to localStorage as fallback
      localStorage.setItem('user_settings', JSON.stringify(settingsData));

      if (isMountedRef.current) {
        setError('Settings saved locally. Will sync when connection is restored.');
      }
      return settingsData;
    }
  };

  const syncWithServer = useCallback(async () => {
    // No server sync in development mode
    if (isDevelopment) return;
    
    if (!isMountedRef.current) return;
    setError(null);
    setIsLoading(true);

    try {
      const { profile: newProfile, settings: newSettings } = await profileService.syncWithServer();
      if (!isMountedRef.current) return;
      
      setProfile(newProfile || DEFAULT_PROFILE);
      setSettings(newSettings || DEFAULT_SETTINGS);
      setLastSynced(Date.now());
    } catch (error) {
      console.error('Error syncing with server:', error);
      if (isMountedRef.current) {
        setError('Using local data.');
      }
    } finally {
      if (isMountedRef.current) {
        setIsLoading(false);
      }
    }
  }, []);

  // Setup periodic sync
  useEffect(() => {
    // Initial load
    loadProfile(true);

    // Setup periodic sync only in production
    if (!isDevelopment) {
      const setupSync = () => {
        if (syncTimeoutRef.current) {
          clearTimeout(syncTimeoutRef.current);
        }
        syncTimeoutRef.current = setTimeout(() => {
          if (isMountedRef.current) {
            loadProfile(true).then(() => {
              setupSync(); // Setup next sync after current one completes
            });
          }
        }, SYNC_INTERVAL);
      };

      setupSync();
    }

    // Cleanup
    return () => {
      isMountedRef.current = false;
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current);
      }
    };
  }, [loadProfile]);

  // Account management functions
  const addAccount = async (accountData) => {
    if (!user?.id) {
      throw new Error('User not authenticated');
    }

    try {
      console.log('🔄 Adding account to Supabase...');
      const newAccount = await SupabaseSettingsService.addAccount(user.id, accountData);

      // Update local state
      const updatedSettings = {
        ...settings,
        accounts: [...(settings.accounts || []), newAccount]
      };

      setSettings(updatedSettings);
      setProfile(prev => ({ ...prev, accounts: updatedSettings.accounts }));

      console.log('✅ Account added to Supabase');
      return newAccount;
    } catch (error) {
      console.error('❌ Error adding account:', error);
      throw error;
    }
  };

  const updateAccount = async (accountId, updateData) => {
    if (!user?.id) {
      throw new Error('User not authenticated');
    }

    try {
      console.log('🔄 Updating account in Supabase...');
      const updatedAccount = await SupabaseSettingsService.updateAccount(user.id, accountId, updateData);

      // Update local state
      const updatedAccounts = (settings.accounts || []).map(account =>
        account.id === accountId ? updatedAccount : account
      );

      const updatedSettings = {
        ...settings,
        accounts: updatedAccounts
      };

      setSettings(updatedSettings);
      setProfile(prev => ({ ...prev, accounts: updatedAccounts }));

      console.log('✅ Account updated in Supabase');
      return updatedAccount;
    } catch (error) {
      console.error('❌ Error updating account:', error);
      throw error;
    }
  };

  const deleteAccount = async (accountId) => {
    if (!user?.id) {
      throw new Error('User not authenticated');
    }

    try {
      console.log('🔄 Deleting account from Supabase...');
      const updatedAccounts = await SupabaseSettingsService.deleteAccount(user.id, accountId);

      // Update local state
      const updatedSettings = {
        ...settings,
        accounts: updatedAccounts
      };

      setSettings(updatedSettings);
      setProfile(prev => ({ ...prev, accounts: updatedAccounts }));

      console.log('✅ Account deleted from Supabase');
      return updatedAccounts;
    } catch (error) {
      console.error('❌ Error deleting account:', error);
      throw error;
    }
  };

  const value = {
    profile,
    settings,
    isLoading,
    error,
    lastSynced,
    updateProfile,
    updateSettings,
    syncWithServer,
    loadProfile,
    setError,
    // Account management
    addAccount,
    updateAccount,
    deleteAccount
  };

  return (
    <ProfileContext.Provider value={value}>
      {children}
    </ProfileContext.Provider>
  );
}

export function useProfile() {
  const context = useContext(ProfileContext);
  if (!context) {
    throw new Error('useProfile must be used within a ProfileProvider');
  }
  return context;
} 