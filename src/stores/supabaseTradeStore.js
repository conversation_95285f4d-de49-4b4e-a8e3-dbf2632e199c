import { create } from 'zustand';
import { db, realtime, supabase } from '../lib/supabase';
import { calculatePnL, standardizeTradeObject } from '../utils/pnlCalculator';

// DEPRECATED: Contract multiplier function moved to centralized utility
// This function is kept temporarily for backward compatibility
// TODO: Remove after all references are updated to use pnlCalculator
const getContractMultiplier = (symbol) => {
  console.warn('DEPRECATED: getContractMultiplier in supabaseTradeStore.js. Use pnlCalculator instead.');
  if (!symbol) return 1;

  const symbolUpper = symbol.toUpperCase();

  // Futures contract multipliers
  if (symbolUpper.includes('NQ')) return 20;    // Nasdaq-100 E-mini: $20 per point
  if (symbolUpper.includes('ES')) return 50;    // S&P 500 E-mini: $50 per point
  if (symbolUpper.includes('YM')) return 5;     // Dow E-mini: $5 per point
  if (symbolUpper.includes('RTY')) return 50;   // Russell 2000 E-mini: $50 per point
  if (symbolUpper.includes('CL')) return 1000;  // Crude Oil: $1000 per point
  if (symbolUpper.includes('GC')) return 100;   // Gold: $100 per point
  if (symbolUpper.includes('SI')) return 5000;  // Silver: $5000 per point

  // Default to 1 for stocks or unknown symbols
  return 1;
};

/**
 * Supabase-based Trade Store for TRADEDGE
 * Replaces IndexedDB with cloud-based storage and real-time updates
 */

const useSupabaseTradeStore = create((set, get) => ({
  trades: [],
  currentAccount: 'supabase',
  isLoading: false,
  error: null,
  subscription: null,
  userId: null,

  // Initialize store with user ID and set up real-time subscription
  initialize: async (userId) => {
    if (!userId) {
      console.error('Cannot initialize trade store without user ID');
      return;
    }

    set({ userId, isLoading: true, error: null });

    try {
      // Load initial trades
      console.log('🔄 Loading initial trades for user:', userId);
      const trades = await get().loadTrades();
      console.log('📊 Loaded trades during initialization:', trades?.length || 0);

      // Set up real-time subscription
      const subscription = realtime.subscribeTrades(userId, (payload) => {
        console.log('Real-time trade update:', payload);

        switch (payload.eventType) {
          case 'INSERT':
            set(state => ({
              trades: [get().transformSupabaseToLocal(payload.new), ...state.trades]
            }));
            break;
          case 'UPDATE':
            set(state => ({
              trades: state.trades.map(trade =>
                trade.id === payload.new.id ? get().transformSupabaseToLocal(payload.new) : trade
              )
            }));
            break;
          case 'DELETE':
            set(state => ({
              trades: state.trades.filter(trade => trade.id !== payload.old.id)
            }));
            break;
        }
      });

      set({ subscription });
      console.log('✅ Supabase trade store initialized with real-time updates');

      // Log current state for debugging
      const currentState = get();
      console.log('🔍 Current store state after initialization:', {
        userId: currentState.userId,
        tradesCount: currentState.trades?.length || 0,
        isLoading: currentState.isLoading,
        error: currentState.error
      });
    } catch (error) {
      console.error('❌ Failed to initialize trade store:', error);
      set({ error: error.message });
    } finally {
      set({ isLoading: false });
    }
  },

  // Transform Supabase trade data to local format for compatibility
  transformSupabaseToLocal: (supabaseTrade) => {
    // Use P&L from database (calculated by trigger) as the single source of truth
    let calculatedPnL = supabaseTrade.pnl;

    // Only calculate P&L as fallback if database value is missing AND trade is closed
    if ((calculatedPnL === null || calculatedPnL === undefined) &&
        supabaseTrade.status === 'CLOSED' &&
        supabaseTrade.exit_price) {

      console.warn(`⚠️ P&L was null for closed trade ${supabaseTrade.id}, using fallback calculation`);

      // Use centralized calculation as fallback
      const standardizedTrade = standardizeTradeObject({
        entry_price: supabaseTrade.entry_price,
        exit_price: supabaseTrade.exit_price,
        quantity: supabaseTrade.quantity,
        trade_type: supabaseTrade.trade_type,
        symbol: supabaseTrade.symbol,
        fees: supabaseTrade.fees
      });

      calculatedPnL = calculatePnL(standardizedTrade) || 0;
    } else if (calculatedPnL === null || calculatedPnL === undefined) {
      // Open trade or missing data
      calculatedPnL = 0;
    }

    // Ensure timestamp is properly formatted
    const timestamp = supabaseTrade.entry_date ? new Date(supabaseTrade.entry_date).toISOString() : new Date().toISOString();
    const exitTimestamp = supabaseTrade.exit_date ? new Date(supabaseTrade.exit_date).toISOString() : null;

    const transformedTrade = {
      id: supabaseTrade.id,
      instrument: supabaseTrade.symbol || 'UNKNOWN',
      side: (supabaseTrade.trade_type || 'LONG').toLowerCase(),
      position_type: (supabaseTrade.trade_type || 'LONG').toUpperCase(),
      quantity: parseInt(supabaseTrade.quantity) || 1,
      size: parseInt(supabaseTrade.quantity) || 1,
      entryPrice: parseFloat(supabaseTrade.entry_price) || 0,
      entry_price: parseFloat(supabaseTrade.entry_price) || 0,
      exitPrice: parseFloat(supabaseTrade.exit_price) || 0,
      exit_price: parseFloat(supabaseTrade.exit_price) || 0,
      timestamp: timestamp,
      exitTimestamp: exitTimestamp,
      pnl: calculatedPnL,
      profit_loss: calculatedPnL, // Critical for analytics
      pnlPercentage: supabaseTrade.pnl_percentage || 0,
      fees: parseFloat(supabaseTrade.fees) || 0,
      commission: parseFloat(supabaseTrade.fees) || 0,
      status: (supabaseTrade.status || 'closed').toLowerCase(),
      notes: supabaseTrade.notes || '',
      strategy: supabaseTrade.strategy || '',
      marketConditions: supabaseTrade.market_conditions || '',
      market_conditions: supabaseTrade.market_conditions || '',
      tags: supabaseTrade.trade_tags?.map(tag => tag.tag) || [],
      account: 'supabase',
      _supabaseData: supabaseTrade
    };

    return transformedTrade;
  },

  // Transform local trade data to Supabase format
  transformLocalToSupabase: (localTrade) => {
    // Validate and ensure quantity is a valid positive integer
    const quantity = localTrade.quantity || localTrade.size;
    const validQuantity = (typeof quantity === 'number' && !isNaN(quantity) && quantity > 0)
      ? Math.floor(quantity)
      : 1; // Default to 1 if invalid

    if (validQuantity !== quantity) {
      console.warn(`Invalid quantity ${quantity} for trade ${localTrade.instrument || localTrade.symbol}, using ${validQuantity}`);
    }

    // Validate exit data for constraint compliance
    const exitPrice = localTrade.exitPrice || localTrade.exit_price;
    const exitDate = localTrade.exitTimestamp || localTrade.exit_date;
    const entryDate = localTrade.timestamp || localTrade.entry_date;
    const entryPrice = localTrade.entryPrice || localTrade.entry_price;
    const fees = localTrade.fees || localTrade.commission || 0;

    // Determine status based on available data
    // Database constraint: if status='CLOSED', both exit_price and exit_date must be NOT NULL
    const hasValidExitPrice = exitPrice && !isNaN(parseFloat(exitPrice)) && parseFloat(exitPrice) > 0;
    const hasValidExitDate = exitDate && exitDate !== entryDate;
    const requestedStatus = (localTrade.status || 'CLOSED').toUpperCase();
    const shouldBeClosed = (requestedStatus === 'CLOSED') && hasValidExitPrice && hasValidExitDate;

    // Calculate P&L for closed trades using centralized utility
    let calculatedPnL = null;
    if (shouldBeClosed && entryPrice && exitPrice) {
      // Use predefined P&L from CSV if available (for imported data)
      if (localTrade.profit_loss !== undefined && localTrade.profit_loss !== null) {
        calculatedPnL = parseFloat(localTrade.profit_loss);
        console.log(`💰 Using CSV P&L: $${calculatedPnL}`);
      } else if (localTrade.predefinedPnL !== undefined && localTrade.predefinedPnL !== null) {
        calculatedPnL = parseFloat(localTrade.predefinedPnL);
        console.log(`💰 Using predefined P&L: $${calculatedPnL}`);
      } else {
        // Use centralized P&L calculation
        const standardizedTrade = standardizeTradeObject({
          entry_price: entryPrice,
          exit_price: exitPrice,
          quantity: validQuantity,
          trade_type: localTrade.side || localTrade.trade_type,
          symbol: localTrade.instrument || localTrade.symbol,
          fees: fees
        });

        calculatedPnL = calculatePnL(standardizedTrade);
        console.log(`💰 Calculated P&L using centralized utility: $${calculatedPnL}`);
      }

      // Round to 2 decimal places
      if (calculatedPnL !== null) {
        calculatedPnL = Math.round(calculatedPnL * 100) / 100;
      }
    }

    const supabaseTrade = {
      user_id: get().userId,
      symbol: localTrade.instrument || localTrade.symbol,
      trade_type: (localTrade.side || localTrade.trade_type || 'LONG').toUpperCase(),
      entry_price: parseFloat(entryPrice),
      exit_price: shouldBeClosed ? parseFloat(exitPrice) : null,
      quantity: validQuantity,
      entry_date: entryDate,
      exit_date: shouldBeClosed ? exitDate : null,
      fees: fees,
      status: shouldBeClosed ? 'CLOSED' : (requestedStatus === 'CANCELLED' ? 'CANCELLED' : 'OPEN'),
      notes: localTrade.notes || '',
      strategy: localTrade.strategy || '',
      market_conditions: localTrade.marketConditions || localTrade.market_conditions || '',
      pnl: calculatedPnL // Include calculated P&L
    };

    console.log('🔄 Transformed trade for Supabase:', {
      symbol: supabaseTrade.symbol,
      type: supabaseTrade.trade_type,
      entry: supabaseTrade.entry_price,
      exit: supabaseTrade.exit_price,
      quantity: supabaseTrade.quantity,
      pnl: supabaseTrade.pnl,
      status: supabaseTrade.status
    });

    return supabaseTrade;
  },

  // Load trades from Supabase
  loadTrades: async (filters = {}) => {
    const { userId } = get();
    if (!userId) {
      console.error('Cannot load trades without user ID');
      return [];
    }

    set({ isLoading: true, error: null });

    try {
      const trades = await db.trades.getAll(userId, filters);
      const transformedTrades = trades.map(trade => get().transformSupabaseToLocal(trade));

      set({ trades: transformedTrades, isLoading: false });
      console.log('✅ Loaded', transformedTrades.length, 'trades from Supabase');

      // Return the transformed trades for immediate use
      return transformedTrades;
    } catch (error) {
      console.error('❌ Failed to load trades:', error);
      set({ error: error.message, isLoading: false });
      return [];
    }
  },

  // Add trades to Supabase
  addTrades: async (newTrades) => {
    if (!Array.isArray(newTrades) || newTrades.length === 0) {
      throw new Error('No trades to add');
    }

    const { userId } = get();
    if (!userId) {
      throw new Error('User not authenticated');
    }

    try {
      set({ isLoading: true, error: null });
      
      // Transform trades to Supabase format
      const supabaseTrades = newTrades.map(trade => get().transformLocalToSupabase(trade));

      console.log('🔄 Saving', supabaseTrades.length, 'trades to Supabase...');

      // Batch insert trades to Supabase
      const { data, error } = await supabase
        .from('trades')
        .insert(supabaseTrades)
        .select();

      if (error) throw error;

      console.log('✅ Successfully saved', data.length, 'trades to Supabase');

      // Refresh trades from Supabase
      await get().loadTrades();

      set({ isLoading: false });
      return { 
        success: true, 
        message: `Successfully imported ${data.length} trades to Supabase`,
        trades: data
      };
    } catch (error) {
      console.error('❌ Error adding trades to Supabase:', error);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  // Add single trade
  addTrade: async (trade) => {
    return get().addTrades([trade]);
  },

  // Update trade
  updateTrade: async (tradeId, updates) => {
    try {
      set({ isLoading: true, error: null });
      
      const updatedTrade = await db.trades.update(tradeId, updates);
      
      // Update local state
      set(state => ({
        trades: state.trades.map(trade => 
          trade.id === tradeId ? get().transformSupabaseToLocal(updatedTrade) : trade
        ),
        isLoading: false
      }));

      return updatedTrade;
    } catch (error) {
      console.error('❌ Error updating trade:', error);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  // Delete trade
  deleteTrade: async (tradeId) => {
    try {
      set({ isLoading: true, error: null });
      
      await db.trades.delete(tradeId);
      
      // Update local state
      set(state => ({
        trades: state.trades.filter(trade => trade.id !== tradeId),
        isLoading: false
      }));

      console.log('✅ Trade deleted successfully');
    } catch (error) {
      console.error('❌ Error deleting trade:', error);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  // Get trades by date range
  getTradesByDateRange: async (startDate, endDate) => {
    const { userId } = get();
    if (!userId) return [];

    try {
      const filters = {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      };
      
      const trades = await db.trades.getAll(userId, filters);
      return trades.map(trade => get().transformSupabaseToLocal(trade));
    } catch (error) {
      console.error('❌ Error getting trades by date range:', error);
      return [];
    }
  },

  // Get trades by symbol
  getTradesBySymbol: async (symbol) => {
    const { userId } = get();
    if (!userId) return [];

    try {
      const filters = { symbol: symbol.toUpperCase() };
      const trades = await db.trades.getAll(userId, filters);
      return trades.map(trade => get().transformSupabaseToLocal(trade));
    } catch (error) {
      console.error('❌ Error getting trades by symbol:', error);
      return [];
    }
  },

  // Clean up subscription
  cleanup: () => {
    const { subscription } = get();
    if (subscription) {
      subscription.unsubscribe();
      set({ subscription: null });
    }
  },

  // Legacy compatibility methods
  initDB: async () => {
    console.log('initDB called - using Supabase instead of IndexedDB');
    return Promise.resolve();
  },

  closeDB: () => {
    console.log('closeDB called - no action needed with Supabase');
  },

  // Clear all data (for testing)
  clearAllTrades: async () => {
    const { userId } = get();
    if (!userId) return;

    try {
      set({ isLoading: true });
      
      // Delete all user's trades from Supabase
      const { error } = await supabase
        .from('trades')
        .delete()
        .eq('user_id', userId);

      if (error) throw error;

      set({ trades: [], isLoading: false });
      console.log('✅ All trades cleared from Supabase');
    } catch (error) {
      console.error('❌ Error clearing trades:', error);
      set({ error: error.message, isLoading: false });
    }
  }
}));

export default useSupabaseTradeStore;
