import { create } from 'zustand';
import { supabase } from '../lib/supabase';

/**
 * Trade Store using Supabase
 * Cloud-based storage with real-time updates
 */

const useTradeStore = create((set, get) => ({
  trades: [],
  currentAccount: null,
  isLoading: false,
  error: null,
  subscription: null,
  userId: null,

  // Initialize store with user ID and set up real-time subscription
  initialize: async (userId) => {
    if (!userId) {
      console.error('Cannot initialize trade store without user ID');
      return;
    }

    set({ userId, isLoading: true, error: null });

    try {
      // Load initial trades
      await get().loadTrades();

      // Set up real-time subscription
      const subscription = realtime.subscribeTrades(userId, (payload) => {
        console.log('Real-time trade update:', payload);

        switch (payload.eventType) {
          case 'INSERT':
            set(state => ({
              trades: [payload.new, ...state.trades]
            }));
            break;
          case 'UPDATE':
            set(state => ({
              trades: state.trades.map(trade =>
                trade.id === payload.new.id ? payload.new : trade
              )
            }));
            break;
          case 'DELETE':
            set(state => ({
              trades: state.trades.filter(trade => trade.id !== payload.old.id)
            }));
            break;
        }
      });

      set({ subscription });
      console.log('✅ Trade store initialized with real-time updates');
    } catch (error) {
      console.error('❌ Failed to initialize trade store:', error);
      set({ error: error.message });
    } finally {
      set({ isLoading: false });
    }
  },

  // Clean up subscription
  cleanup: () => {
    const { subscription } = get();
    if (subscription) {
      subscription.unsubscribe();
      set({ subscription: null });
    }
  },

  // Legacy function - no longer needed with Supabase
  initDB: async () => {
    console.log('initDB called - no longer needed with Supabase');
    return null;
  },

  clearAllData: async () => {
    try {
      set({ isLoading: true, error: null });

      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Delete all trades for the current user
      const { error } = await supabase
        .from('trades')
        .delete()
        .eq('user_id', user.id);

      if (error) throw error;

      set({
        trades: [],
        isLoading: false
      });
      return { success: true, message: 'All data cleared successfully' };
    } catch (error) {
      console.error('Error clearing data:', error);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  deleteTrade: async (tradeId) => {
    try {
      set({ isLoading: true, error: null });

      // Delete from Supabase
      const { error } = await supabase
        .from('trades')
        .delete()
        .eq('id', tradeId);

      if (error) throw error;

      // Update state by removing the deleted trade
      set(state => ({
        trades: state.trades.filter(t => t.id !== tradeId),
        isLoading: false
      }));

      return { success: true, message: 'Trade deleted successfully' };
    } catch (error) {
      console.error('Error deleting trade:', error);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  setCurrentAccount: (accountId) => {
    set({ currentAccount: accountId });
    get().loadTrades(true); // Reload trades for the new account
  },

  loadTrades: async (forceRefresh = false) => {
    if (get().trades.length > 0 && !forceRefresh) return;

    const { userId } = get();
    if (!userId) {
      console.error('Cannot load trades without user ID');
      return;
    }

    try {
      set({ isLoading: true, error: null });

      // Build query
      let query = supabase
        .from('trades')
        .select('*')
        .eq('user_id', userId);

      const currentAccount = get().currentAccount;
      if (currentAccount) {
        query = query.eq('account_id', currentAccount);
      }

      const { data: trades, error } = await query.order('entry_date', { ascending: false });

      if (error) throw error;

      set({ trades: trades || [], isLoading: false });
    } catch (error) {
      console.error('Error loading trades:', error);
      set({ error: error.message, isLoading: false });
    }
  },

  // Add trades to Supabase (replaces IndexedDB)
  addTrades: async (newTrades) => {
    if (!Array.isArray(newTrades) || newTrades.length === 0) {
      throw new Error('No trades to add');
    }

    const { userId } = get();
    if (!userId) {
      throw new Error('User not authenticated');
    }

    try {
      set({ isLoading: true, error: null });

      // Transform trades to Supabase format with validation
      const supabaseTrades = newTrades.map(trade => {
        // Validate and ensure quantity is a valid positive integer
        const quantity = trade.size || trade.quantity;
        const validQuantity = (typeof quantity === 'number' && !isNaN(quantity) && quantity > 0)
          ? Math.floor(quantity)
          : 1; // Default to 1 if invalid

        if (validQuantity !== quantity) {
          console.warn(`Invalid quantity ${quantity} for trade ${trade.instrument || trade.symbol}, using ${validQuantity}`);
        }

        // Validate exit data for constraint compliance
        const exitPrice = trade.exit_price || trade.exitPrice;
        const exitTimestamp = trade.trade_details?.exitTimestamp || trade.exitTimestamp;
        const entryTimestamp = trade.timestamp;

        // Determine status based on available data
        // Database constraint: if status='CLOSED', both exit_price and exit_date must be NOT NULL
        const hasValidExitPrice = exitPrice && !isNaN(parseFloat(exitPrice)) && parseFloat(exitPrice) > 0;
        const hasValidExitDate = exitTimestamp && exitTimestamp !== entryTimestamp;
        const shouldBeClosed = hasValidExitPrice && hasValidExitDate;

        return {
          user_id: userId,
          symbol: trade.instrument || trade.symbol,
          trade_type: (trade.position_type || trade.side || 'LONG').toUpperCase(),
          entry_price: trade.entry_price || trade.entryPrice,
          exit_price: shouldBeClosed ? parseFloat(exitPrice) : null,
          quantity: validQuantity,
          entry_date: entryTimestamp || new Date().toISOString(),
          exit_date: shouldBeClosed ? exitTimestamp : null,
          fees: trade.commission || trade.fees || 0,
          status: shouldBeClosed ? 'CLOSED' : 'OPEN',
          notes: trade.notes || 'Imported from CSV',
          strategy: trade.strategy || '',
          market_conditions: trade.market_conditions?.trend || '',
          pnl: trade.profit_loss || null // Let database calculate if not provided
        };
      });

      console.log('🔄 Saving', supabaseTrades.length, 'trades to Supabase...');

      // Batch insert trades to Supabase
      const { data, error } = await supabase
        .from('trades')
        .insert(supabaseTrades)
        .select();

      if (error) throw error;

      console.log('✅ Successfully saved', data.length, 'trades to Supabase');

      // Refresh trades from Supabase
      await get().loadTrades();

      set({ isLoading: false });
      return {
        success: true,
        message: `Successfully imported ${data.length} trades to Supabase`,
        trades: data
      };
    } catch (error) {
      console.error('❌ Error adding trades to Supabase:', error);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  updateTradeNotes: async (tradeId, updates) => {
    try {
      set({ isLoading: true, error: null });

      // Update trade in Supabase
      const { data: updatedTrade, error } = await supabase
        .from('trades')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', tradeId)
        .select()
        .single();

      if (error) throw error;

      if (!updatedTrade) {
        throw new Error('Trade not found');
      }

      // Update state
      set(state => ({
        trades: state.trades.map(t =>
          t.id === tradeId ? updatedTrade : t
        ),
        isLoading: false
      }));

      return updatedTrade;
    } catch (error) {
      console.error('Error updating trade notes:', error);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  getTradesByAccount: async (accountId) => {
    try {
      set({ isLoading: true, error: null });

      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Query trades from Supabase
      const { data: trades, error } = await supabase
        .from('trades')
        .select('*')
        .eq('user_id', user.id)
        .eq('account_id', accountId)
        .order('entry_date', { ascending: false });

      if (error) throw error;

      set({ isLoading: false });
      return trades || [];
    } catch (error) {
      console.error('Error getting trades by account:', error);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  getTradesByDateRange: async (startDate, endDate, accountId = null) => {
    try {
      set({ isLoading: true, error: null });

      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Build query
      let query = supabase
        .from('trades')
        .select('*')
        .eq('user_id', user.id)
        .gte('entry_date', startDate.toISOString())
        .lte('entry_date', endDate.toISOString());

      // Filter by account if specified
      if (accountId) {
        query = query.eq('account_id', accountId);
      }

      const { data: trades, error } = await query.order('entry_date', { ascending: false });

      if (error) throw error;

      set({ isLoading: false });
      return trades || [];
    } catch (error) {
      console.error('Error getting trades by date range:', error);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  }
}));

export { useTradeStore }; 