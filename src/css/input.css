@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .stats-card {
    @apply bg-white rounded-xl shadow-sm p-4 transition-all hover:shadow-md;
  }
  
  .stat-label {
    @apply text-sm font-medium text-slate-500;
  }
  
  .stat-value {
    @apply text-2xl font-bold mt-1;
  }

  .stat-icon {
    @apply p-3 rounded-lg;
  }

  .dashboard-card {
    @apply bg-white rounded-xl shadow-sm p-6;
  }

  .card-title {
    @apply text-lg font-semibold text-slate-800;
  }

  .chart-period-btn {
    @apply px-3 py-1 text-sm font-medium rounded-md text-slate-600 hover:bg-slate-100 transition-colors;
  }

  .chart-period-btn.active {
    @apply bg-blue-50 text-blue-600;
  }

  .search-input {
    @apply px-4 py-2 border border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  }

  .filter-btn {
    @apply p-2 text-slate-600 hover:bg-slate-100 rounded-lg transition-colors;
  }

  .trade-table {
    @apply min-w-full divide-y divide-slate-200;
  }

  .trade-table-header {
    @apply px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider;
  }

  .trade-table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-slate-900;
  }

  .trade-row {
    @apply hover:bg-slate-50 transition-colors;
  }

  .profit {
    @apply text-emerald-600 font-medium;
  }

  .loss {
    @apply text-red-600 font-medium;
  }
} 