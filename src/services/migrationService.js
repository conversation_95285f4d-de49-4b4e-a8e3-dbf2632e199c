import { openDB } from 'idb';
import { supabase } from '../lib/supabase';
import SupabaseGoalService from './supabaseGoalService';

/**
 * Migration Service
 * Handles migration from IndexedDB to Supabase
 */

const DB_NAME = 'trade_reviewer_db';
const DB_VERSION = 3;

export class MigrationService {
  /**
   * Check if IndexedDB data exists
   * @returns {Promise<Object>} Status of IndexedDB data
   */
  static async checkIndexedDBData() {
    try {
      const db = await openDB(DB_NAME, DB_VERSION);
      
      const result = {
        trades: 0,
        goalHistory: 0,
        settings: 0,
        hasData: false
      };

      // Check trades
      if (db.objectStoreNames.contains('trades')) {
        const tx = db.transaction('trades', 'readonly');
        const trades = await tx.store.getAll();
        result.trades = trades.length;
      }

      // Check goal history
      if (db.objectStoreNames.contains('goal_history')) {
        const tx = db.transaction('goal_history', 'readonly');
        const goalHistory = await tx.store.getAll();
        result.goalHistory = goalHistory.length;
      }

      // Check settings
      if (db.objectStoreNames.contains('settings')) {
        const tx = db.transaction('settings', 'readonly');
        const settings = await tx.store.getAll();
        result.settings = settings.length;
      }

      result.hasData = result.trades > 0 || result.goalHistory > 0 || result.settings > 0;
      
      db.close();
      return result;
    } catch (error) {
      console.error('Error checking IndexedDB data:', error);
      return {
        trades: 0,
        goalHistory: 0,
        settings: 0,
        hasData: false,
        error: error.message
      };
    }
  }

  /**
   * Migrate all data from IndexedDB to Supabase
   * @param {string} userId - User ID for Supabase
   * @returns {Promise<Object>} Migration result
   */
  static async migrateAllData(userId) {
    if (!userId) {
      throw new Error('User ID is required for migration');
    }

    try {
      console.log('🚀 Starting migration from IndexedDB to Supabase...');
      
      const db = await openDB(DB_NAME, DB_VERSION);
      const results = {
        trades: { migrated: 0, errors: 0, errorDetails: [] },
        goalHistory: { migrated: 0, errors: 0, errorDetails: [] },
        settings: { migrated: 0, errors: 0, errorDetails: [] },
        success: true,
        totalMigrated: 0,
        totalErrors: 0
      };

      // Migrate trades
      if (db.objectStoreNames.contains('trades')) {
        console.log('📊 Migrating trades...');
        const tradesResult = await this.migrateTrades(db, userId);
        results.trades = tradesResult;
      }

      // Migrate goal history
      if (db.objectStoreNames.contains('goal_history')) {
        console.log('🎯 Migrating goal history...');
        const goalResult = await this.migrateGoalHistory(db, userId);
        results.goalHistory = goalResult;
      }

      // Migrate settings
      if (db.objectStoreNames.contains('settings')) {
        console.log('⚙️ Migrating settings...');
        const settingsResult = await this.migrateSettings(db, userId);
        results.settings = settingsResult;
      }

      // Calculate totals
      results.totalMigrated = results.trades.migrated + results.goalHistory.migrated + results.settings.migrated;
      results.totalErrors = results.trades.errors + results.goalHistory.errors + results.settings.errors;
      results.success = results.totalErrors === 0;

      db.close();
      
      console.log('🎉 Migration completed:', results);
      return results;
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  /**
   * Migrate trades from IndexedDB to Supabase
   * @param {IDBDatabase} db - IndexedDB instance
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Migration result
   */
  static async migrateTrades(db, userId) {
    try {
      const tx = db.transaction('trades', 'readonly');
      const trades = await tx.store.getAll();
      
      console.log(`Found ${trades.length} trades to migrate`);
      
      const result = { migrated: 0, errors: 0, errorDetails: [] };
      
      for (const trade of trades) {
        try {
          const supabaseTrade = {
            user_id: userId,
            symbol: trade.instrument || trade.symbol,
            trade_type: (trade.position_type || trade.side || 'LONG').toUpperCase(),
            entry_price: trade.entry_price || trade.entryPrice,
            exit_price: trade.exit_price || trade.exitPrice,
            quantity: trade.size || trade.quantity || 1,
            entry_date: trade.timestamp || new Date().toISOString(),
            exit_date: trade.exitTimestamp || trade.exit_date,
            fees: trade.commission || trade.fees || 0,
            status: trade.status || 'CLOSED',
            notes: trade.notes || '',
            strategy: trade.strategy || '',
            market_conditions: trade.market_conditions || '',
            pnl: trade.profit_loss || null,
            created_at: trade.createdAt || new Date().toISOString(),
            updated_at: trade.updatedAt || new Date().toISOString()
          };

          const { error } = await supabase
            .from('trades')
            .insert(supabaseTrade);

          if (error) throw error;
          
          result.migrated++;
          console.log(`✅ Migrated trade: ${trade.instrument || trade.symbol}`);
        } catch (error) {
          console.error(`❌ Error migrating trade:`, error);
          result.errors++;
          result.errorDetails.push({
            trade: trade.instrument || trade.symbol,
            error: error.message
          });
        }
      }
      
      return result;
    } catch (error) {
      console.error('Error in migrateTrades:', error);
      throw error;
    }
  }

  /**
   * Migrate goal history from IndexedDB to Supabase
   * @param {IDBDatabase} db - IndexedDB instance
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Migration result
   */
  static async migrateGoalHistory(db, userId) {
    try {
      const tx = db.transaction('goal_history', 'readonly');
      const goalHistory = await tx.store.getAll();
      
      console.log(`Found ${goalHistory.length} goal history records to migrate`);
      
      return await SupabaseGoalService.migrateFromIndexedDB(userId, goalHistory);
    } catch (error) {
      console.error('Error in migrateGoalHistory:', error);
      throw error;
    }
  }

  /**
   * Migrate settings from IndexedDB to Supabase
   * @param {IDBDatabase} db - IndexedDB instance
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Migration result
   */
  static async migrateSettings(db, userId) {
    try {
      const tx = db.transaction('settings', 'readonly');
      const settings = await tx.store.getAll();
      
      console.log(`Found ${settings.length} settings to migrate`);
      
      const result = { migrated: 0, errors: 0, errorDetails: [] };
      
      for (const setting of settings) {
        try {
          const { error } = await supabase
            .from('user_settings')
            .upsert({
              user_id: userId,
              setting_key: setting.key || setting.id,
              setting_value: setting.value,
              created_at: setting.createdAt || new Date().toISOString(),
              updated_at: setting.updatedAt || new Date().toISOString()
            }, { onConflict: 'user_id,setting_key' });

          if (error) throw error;
          
          result.migrated++;
          console.log(`✅ Migrated setting: ${setting.key || setting.id}`);
        } catch (error) {
          console.error(`❌ Error migrating setting:`, error);
          result.errors++;
          result.errorDetails.push({
            setting: setting.key || setting.id,
            error: error.message
          });
        }
      }
      
      return result;
    } catch (error) {
      console.error('Error in migrateSettings:', error);
      throw error;
    }
  }

  /**
   * Clear IndexedDB data after successful migration
   * @returns {Promise<boolean>} Success status
   */
  static async clearIndexedDBData() {
    try {
      console.log('🧹 Clearing IndexedDB data...');
      
      const db = await openDB(DB_NAME, DB_VERSION);
      
      // Clear all stores
      const storeNames = ['trades', 'goal_history', 'settings'];
      
      for (const storeName of storeNames) {
        if (db.objectStoreNames.contains(storeName)) {
          const tx = db.transaction(storeName, 'readwrite');
          await tx.store.clear();
          console.log(`✅ Cleared ${storeName} store`);
        }
      }
      
      db.close();
      console.log('🎉 IndexedDB data cleared successfully');
      return true;
    } catch (error) {
      console.error('❌ Error clearing IndexedDB data:', error);
      throw error;
    }
  }

  /**
   * Complete migration workflow
   * @param {string} userId - User ID
   * @param {boolean} clearAfterMigration - Whether to clear IndexedDB after migration
   * @returns {Promise<Object>} Complete migration result
   */
  static async completeMigration(userId, clearAfterMigration = true) {
    try {
      // Check if there's data to migrate
      const dataCheck = await this.checkIndexedDBData();
      if (!dataCheck.hasData) {
        return {
          success: true,
          message: 'No IndexedDB data found to migrate',
          dataCheck
        };
      }

      // Perform migration
      const migrationResult = await this.migrateAllData(userId);
      
      // Clear IndexedDB if migration was successful and requested
      if (migrationResult.success && clearAfterMigration) {
        await this.clearIndexedDBData();
        migrationResult.indexedDBCleared = true;
      }

      return {
        success: migrationResult.success,
        message: migrationResult.success 
          ? 'Migration completed successfully' 
          : 'Migration completed with errors',
        dataCheck,
        migrationResult
      };
    } catch (error) {
      console.error('❌ Complete migration failed:', error);
      throw error;
    }
  }
}

export default MigrationService;
