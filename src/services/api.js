import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// User API
export const userAPI = {
  getProfile: () => api.get('/users/profile'),
  updateProfile: (data) => api.put('/users/profile', data),
  getSettings: () => api.get('/users/settings'),
  updateSettings: (data) => api.put('/users/settings', data)
};

// Trading Account API
export const tradingAccountAPI = {
  getAll: () => api.get('/trading-accounts'),
  getOne: (id) => api.get(`/trading-accounts/${id}`),
  create: (data) => api.post('/trading-accounts', data),
  update: (id, data) => api.put(`/trading-accounts/${id}`, data),
  delete: (id) => api.delete(`/trading-accounts/${id}`)
};

export default api; 