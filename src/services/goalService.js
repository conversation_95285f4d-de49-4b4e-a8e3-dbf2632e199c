import { format } from 'date-fns';
import { supabase } from '../lib/supabase';
import SupabaseGoalService from './supabaseGoalService';

/**
 * Goal Service - Updated to use Supabase instead of IndexedDB
 * Maintains backward compatibility with existing API
 */

// Legacy function for backward compatibility
export const closeDB = () => {
  console.log('closeDB called - no longer needed with Supabase');
};

// Helper function to get current user ID from auth context
const getCurrentUserId = async () => {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }
  return user.id;
};

// Save current goal progress to history
export const saveGoalHistory = async (goalData, userId = null) => {
  try {
    const actualUserId = userId || await getCurrentUserId();
    return await SupabaseGoalService.saveGoalHistory(actualUserId, goalData);
  } catch (error) {
    console.error('Error in saveGoalHistory:', error);
    throw error;
  }
};

// Get goal progress for a specific date
export const getGoalProgress = async (date, userId = null) => {
  try {
    const actualUserId = userId || await getCurrentUserId();
    return await SupabaseGoalService.getGoalProgress(actualUserId, date);
  } catch (error) {
    console.error('Error in getGoalProgress:', error);
    return null;
  }
};

// Get goal progress for a date range
export const getGoalProgressRange = async (startDate, endDate, userId = null) => {
  try {
    const actualUserId = userId || await getCurrentUserId();
    return await SupabaseGoalService.getGoalProgressRange(actualUserId, startDate, endDate);
  } catch (error) {
    console.error('Error in getGoalProgressRange:', error);
    return [];
  }
};

// Get all goal history
export const getAllGoalHistory = async (userId = null) => {
  try {
    const actualUserId = userId || await getCurrentUserId();
    return await SupabaseGoalService.getAllGoalHistory(actualUserId);
  } catch (error) {
    console.error('Error in getAllGoalHistory:', error);
    return [];
  }
};

// Delete goal history for a specific date
export const deleteGoalHistory = async (date, userId = null) => {
  try {
    const actualUserId = userId || await getCurrentUserId();
    return await SupabaseGoalService.deleteGoalHistory(actualUserId, date);
  } catch (error) {
    console.error('Error in deleteGoalHistory:', error);
    throw error;
  }
};

// Migration function to move IndexedDB data to Supabase
export const migrateGoalHistoryToSupabase = async (userId) => {
  if (!userId) {
    throw new Error('User ID is required for migration');
  }

  try {
    // This would need to be implemented to read from IndexedDB first
    console.log('Migration from IndexedDB to Supabase not yet implemented');
    console.log('Please use the migration utility in the admin panel');
    return { success: false, message: 'Migration utility needed' };
  } catch (error) {
    console.error('Error in migration:', error);
    throw error;
  }
};