import User from '../models/User';

class UserService {
  /**
   * Get user profile by ID
   */
  async getUserProfile(userId) {
    try {
      const user = await User.findById(userId);
      return user ? user.toProfileJSON() : null;
    } catch (error) {
      throw new Error(`Error fetching user profile: ${error.message}`);
    }
  }

  /**
   * Update user profile
   */
  async updateUserProfile(userId, profileData) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Update allowed fields
      const allowedFields = [
        'name',
        'title',
        'bio',
        'tradingFirm',
        'tradingSince',
        'tradingRegion',
        'tradingStyle'
      ];

      allowedFields.forEach(field => {
        if (profileData[field] !== undefined) {
          user[field] = profileData[field];
        }
      });

      await user.save();
      return user.toProfileJSON();
    } catch (error) {
      throw new Error(`Error updating user profile: ${error.message}`);
    }
  }

  /**
   * Get user settings
   */
  async getUserSettings(userId) {
    try {
      const user = await User.findById(userId);
      return user ? user.toSettingsJSON() : null;
    } catch (error) {
      throw new Error(`Error fetching user settings: ${error.message}`);
    }
  }

  /**
   * Update user settings
   */
  async updateUserSettings(userId, settingsData) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Update settings object
      if (settingsData.timeZone) {
        user.settings.timeZone = settingsData.timeZone;
      }

      if (settingsData.notifications) {
        Object.assign(user.settings.notifications, settingsData.notifications);
      }

      if (settingsData.privacy) {
        Object.assign(user.settings.privacy, settingsData.privacy);
      }

      await user.save();
      return user.toSettingsJSON();
    } catch (error) {
      throw new Error(`Error updating user settings: ${error.message}`);
    }
  }

  /**
   * Delete user account
   */
  async deleteUser(userId) {
    try {
      const result = await User.findByIdAndDelete(userId);
      return !!result;
    } catch (error) {
      throw new Error(`Error deleting user: ${error.message}`);
    }
  }
}

export default new UserService(); 