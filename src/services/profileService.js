import { userAPI } from './api';

const PROFILE_KEY = 'user_profile';
const SETTINGS_KEY = 'user_settings';

const DEFAULT_PROFILE = {
  name: '',
  email: '',
  avatar: null,
  timezone: 'America/New_York',
  tradingExperience: '',
  preferredMarkets: [],
  bio: ''
};

const DEFAULT_SETTINGS = {
  timeZone: 'America/New_York',
  notifications: {
    email: true,
    desktop: true,
    tradeAlerts: true
  },
  privacy: {
    profileVisibility: 'private',
    showTradingStats: false
  },
  accounts: []
};

// Load profile from local storage
const loadFromStorage = (key) => {
  try {
    const data = localStorage.getItem(key);
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.error(`Error loading ${key} from storage:`, error);
    return null;
  }
};

// Save profile to local storage
const saveToStorage = (key, data) => {
  try {
    if (!data) return false;
    localStorage.setItem(key, JSON.stringify(data));
    return true;
  } catch (error) {
    console.error(`Error saving ${key} to storage:`, error);
    return false;
  }
};

export const profileService = {
  // Get profile from local storage first, then update from server if available
  async getProfile() {
    try {
      // Always get local data first for immediate display
      let profile = loadFromStorage(PROFILE_KEY) || DEFAULT_PROFILE;
      
      try {
        // Try to get fresh data from server
        const response = await userAPI.getProfile();
        if (response.data) {
          profile = response.data;
          // Update local storage with fresh data
          saveToStorage(PROFILE_KEY, profile);
        }
      } catch (error) {
        console.error('Error fetching profile from server:', error);
        // If we have local data, use it
        if (!profile) {
          profile = DEFAULT_PROFILE;
        }
      }
      
      return profile;
    } catch (error) {
      console.error('Error in getProfile:', error);
      return DEFAULT_PROFILE;
    }
  },

  // Update profile both locally and on server
  async updateProfile(profileData) {
    try {
      // Always save locally first for immediate feedback
      saveToStorage(PROFILE_KEY, profileData);
      
      try {
        // Attempt server update
        const response = await userAPI.updateProfile(profileData);
        const updatedProfile = response.data;
        // Update local storage with server response
        if (updatedProfile) {
          saveToStorage(PROFILE_KEY, updatedProfile);
          return updatedProfile;
        }
      } catch (error) {
        console.error('Error updating profile on server:', error);
      }
      
      // Return local data if server update fails
      return profileData;
    } catch (error) {
      console.error('Error in updateProfile:', error);
      return profileData;
    }
  },

  // Get settings from local storage first, then update from server if available
  async getSettings() {
    try {
      // Always get local data first for immediate display
      let settings = loadFromStorage(SETTINGS_KEY) || DEFAULT_SETTINGS;
      
      try {
        // Try to get fresh data from server
        const response = await userAPI.getSettings();
        if (response.data) {
          settings = response.data;
          // Update local storage with fresh data
          saveToStorage(SETTINGS_KEY, settings);
        }
      } catch (error) {
        console.error('Error fetching settings from server:', error);
        // If we have local data, use it
        if (!settings) {
          settings = DEFAULT_SETTINGS;
        }
      }
      
      return settings;
    } catch (error) {
      console.error('Error in getSettings:', error);
      return DEFAULT_SETTINGS;
    }
  },

  // Update settings both locally and on server
  async updateSettings(settingsData) {
    try {
      // Always save locally first for immediate feedback
      saveToStorage(SETTINGS_KEY, settingsData);
      
      try {
        // Attempt server update
        const response = await userAPI.updateSettings(settingsData);
        const updatedSettings = response.data;
        // Update local storage with server response
        if (updatedSettings) {
          saveToStorage(SETTINGS_KEY, updatedSettings);
          return updatedSettings;
        }
      } catch (error) {
        console.error('Error updating settings on server:', error);
      }
      
      // Return local data if server update fails
      return settingsData;
    } catch (error) {
      console.error('Error in updateSettings:', error);
      return settingsData;
    }
  },

  // Clear local storage data
  clearLocalData() {
    try {
      localStorage.removeItem(PROFILE_KEY);
      localStorage.removeItem(SETTINGS_KEY);
    } catch (error) {
      console.error('Error clearing local data:', error);
    }
  },

  // Force sync with server
  async syncWithServer() {
    try {
      let profile = DEFAULT_PROFILE;
      let settings = DEFAULT_SETTINGS;

      try {
        const profileResponse = await userAPI.getProfile();
        if (profileResponse.data) {
          profile = profileResponse.data;
          saveToStorage(PROFILE_KEY, profile);
        }
      } catch (error) {
        console.error('Error syncing profile:', error);
        profile = loadFromStorage(PROFILE_KEY) || DEFAULT_PROFILE;
      }
      
      try {
        const settingsResponse = await userAPI.getSettings();
        if (settingsResponse.data) {
          settings = settingsResponse.data;
          saveToStorage(SETTINGS_KEY, settings);
        }
      } catch (error) {
        console.error('Error syncing settings:', error);
        settings = loadFromStorage(SETTINGS_KEY) || DEFAULT_SETTINGS;
      }

      return { profile, settings };
    } catch (error) {
      console.error('Error syncing with server:', error);
      // Return local data as fallback
      return {
        profile: loadFromStorage(PROFILE_KEY) || DEFAULT_PROFILE,
        settings: loadFromStorage(SETTINGS_KEY) || DEFAULT_SETTINGS
      };
    }
  }
} 