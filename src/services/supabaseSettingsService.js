import { supabase } from '../lib/supabase';

/**
 * Supabase Settings Service for TRADEDGE
 * Replaces localStorage with cloud-based settings storage
 */

const DEFAULT_SETTINGS = {
  timeZone: 'America/New_York',
  currency: 'USD',
  theme: 'dark',
  notifications: {
    email: true,
    desktop: true,
    tradeAlerts: true,
    goalAlerts: true
  },
  privacy: {
    profileVisibility: 'private',
    showTradingStats: false,
    allowAnalytics: false
  },
  trading: {
    defaultRisk: 1,
    defaultPosition: 100,
    defaultCommission: 0,
    defaultFees: 0,
    riskCalculationType: 'percentage'
  },
  display: {
    dateFormat: 'MM/dd/yyyy',
    timeFormat: '12h',
    numberFormat: 'US',
    chartTheme: 'dark'
  }
};

const DEFAULT_ACCOUNT = {
  name: 'Main Account',
  type: 'live',
  broker: '',
  initialCapital: 10000,
  currency: 'USD',
  isDefault: true
};

class SupabaseSettingsService {
  /**
   * Get user settings from Supabase
   */
  static async getSettings(userId) {
    try {
      // First try to get from Supabase
      const { data, error } = await supabase
        .from('user_settings')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      if (data) {
        return {
          ...DEFAULT_SETTINGS,
          ...data.settings,
          accounts: data.accounts || [DEFAULT_ACCOUNT]
        };
      }

      // If no settings found, create default settings
      const defaultSettings = {
        ...DEFAULT_SETTINGS,
        accounts: [DEFAULT_ACCOUNT]
      };

      await this.saveSettings(userId, defaultSettings);
      return defaultSettings;

    } catch (error) {
      console.error('❌ Error getting settings from Supabase:', error);
      
      // Fallback to localStorage
      try {
        const localSettings = localStorage.getItem('user_settings');
        if (localSettings) {
          return JSON.parse(localSettings);
        }
      } catch (localError) {
        console.error('❌ Error getting settings from localStorage:', localError);
      }

      return {
        ...DEFAULT_SETTINGS,
        accounts: [DEFAULT_ACCOUNT]
      };
    }
  }

  /**
   * Save user settings to Supabase
   */
  static async saveSettings(userId, settings) {
    try {
      const { data, error } = await supabase
        .from('user_settings')
        .upsert({
          user_id: userId,
          settings: {
            timeZone: settings.timeZone,
            currency: settings.currency,
            theme: settings.theme,
            notifications: settings.notifications,
            privacy: settings.privacy,
            trading: settings.trading,
            display: settings.display
          },
          accounts: settings.accounts || [DEFAULT_ACCOUNT],
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      // Also save to localStorage as backup
      localStorage.setItem('user_settings', JSON.stringify(settings));

      console.log('✅ Settings saved to Supabase');
      return data;

    } catch (error) {
      console.error('❌ Error saving settings to Supabase:', error);
      
      // Fallback to localStorage
      try {
        localStorage.setItem('user_settings', JSON.stringify(settings));
        console.log('⚠️ Settings saved to localStorage as fallback');
      } catch (localError) {
        console.error('❌ Error saving to localStorage:', localError);
      }

      throw error;
    }
  }

  /**
   * Update specific setting
   */
  static async updateSetting(userId, key, value) {
    try {
      const currentSettings = await this.getSettings(userId);
      const updatedSettings = {
        ...currentSettings,
        [key]: value
      };

      return await this.saveSettings(userId, updatedSettings);
    } catch (error) {
      console.error('❌ Error updating setting:', error);
      throw error;
    }
  }

  /**
   * Add new account
   */
  static async addAccount(userId, accountData) {
    try {
      const currentSettings = await this.getSettings(userId);
      const accounts = currentSettings.accounts || [];

      // Check for duplicate names
      if (accounts.some(acc => acc.name.toLowerCase() === accountData.name.toLowerCase())) {
        throw new Error('An account with this name already exists');
      }

      const newAccount = {
        ...accountData,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isDefault: accounts.length === 0
      };

      const updatedSettings = {
        ...currentSettings,
        accounts: [...accounts, newAccount]
      };

      await this.saveSettings(userId, updatedSettings);
      return newAccount;

    } catch (error) {
      console.error('❌ Error adding account:', error);
      throw error;
    }
  }

  /**
   * Update account
   */
  static async updateAccount(userId, accountId, updateData) {
    try {
      const currentSettings = await this.getSettings(userId);
      const accounts = currentSettings.accounts || [];

      const updatedAccounts = accounts.map(account => {
        if (account.id === accountId) {
          return {
            ...account,
            ...updateData,
            updatedAt: new Date().toISOString()
          };
        }
        return account;
      });

      const updatedSettings = {
        ...currentSettings,
        accounts: updatedAccounts
      };

      await this.saveSettings(userId, updatedSettings);
      return updatedAccounts.find(acc => acc.id === accountId);

    } catch (error) {
      console.error('❌ Error updating account:', error);
      throw error;
    }
  }

  /**
   * Delete account
   */
  static async deleteAccount(userId, accountId) {
    try {
      const currentSettings = await this.getSettings(userId);
      const accounts = currentSettings.accounts || [];

      const updatedAccounts = accounts.filter(account => account.id !== accountId);

      // If we deleted the default account, make the first remaining account default
      if (updatedAccounts.length > 0 && !updatedAccounts.some(acc => acc.isDefault)) {
        updatedAccounts[0].isDefault = true;
      }

      const updatedSettings = {
        ...currentSettings,
        accounts: updatedAccounts
      };

      await this.saveSettings(userId, updatedSettings);
      return updatedAccounts;

    } catch (error) {
      console.error('❌ Error deleting account:', error);
      throw error;
    }
  }

  /**
   * Migrate localStorage settings to Supabase
   */
  static async migrateLocalSettings(userId) {
    try {
      console.log('🔄 Migrating localStorage settings to Supabase...');

      const localSettings = localStorage.getItem('user_settings');
      if (!localSettings) {
        console.log('No local settings to migrate');
        return;
      }

      const settings = JSON.parse(localSettings);
      await this.saveSettings(userId, settings);

      console.log('✅ Successfully migrated settings to Supabase');
    } catch (error) {
      console.error('❌ Error migrating settings:', error);
    }
  }
}

export default SupabaseSettingsService;
