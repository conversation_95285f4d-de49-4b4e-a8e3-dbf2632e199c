import TradingAccount from '../models/TradingAccount';

class TradingAccountService {
  /**
   * Get all trading accounts for a user
   */
  async getUserAccounts(userId) {
    try {
      const accounts = await TradingAccount.find({ userId })
        .sort({ isDefault: -1, createdAt: -1 });
      return accounts.map(account => account.toJSON());
    } catch (error) {
      throw new Error(`Error fetching trading accounts: ${error.message}`);
    }
  }

  /**
   * Get a single trading account by ID
   */
  async getAccountById(userId, accountId) {
    try {
      const account = await TradingAccount.findOne({ _id: accountId, userId });
      return account ? account.toJSON() : null;
    } catch (error) {
      throw new Error(`Error fetching trading account: ${error.message}`);
    }
  }

  /**
   * Create a new trading account
   */
  async createAccount(userId, accountData) {
    try {
      // If this is the first account, make it default
      const existingAccounts = await TradingAccount.countDocuments({ userId });
      const isDefault = existingAccounts === 0;

      const account = new TradingAccount({
        userId,
        ...accountData,
        isDefault
      });

      await account.save();
      return account.toJSON();
    } catch (error) {
      throw new Error(`Error creating trading account: ${error.message}`);
    }
  }

  /**
   * Update a trading account
   */
  async updateAccount(userId, accountId, updateData) {
    try {
      const account = await TradingAccount.findOne({ _id: accountId, userId });
      if (!account) {
        throw new Error('Trading account not found');
      }

      // Update allowed fields
      const allowedFields = [
        'name',
        'initialCapital',
        'type',
        'broker',
        'status',
        'metadata'
      ];

      allowedFields.forEach(field => {
        if (updateData[field] !== undefined) {
          account[field] = updateData[field];
        }
      });

      await account.save();
      return account.toJSON();
    } catch (error) {
      throw new Error(`Error updating trading account: ${error.message}`);
    }
  }

  /**
   * Set default trading account
   */
  async setDefaultAccount(userId, accountId) {
    try {
      const account = await TradingAccount.findOne({ _id: accountId, userId });
      if (!account) {
        throw new Error('Trading account not found');
      }

      account.isDefault = true;
      await account.save();
      return account.toJSON();
    } catch (error) {
      throw new Error(`Error setting default account: ${error.message}`);
    }
  }

  /**
   * Delete a trading account
   */
  async deleteAccount(userId, accountId) {
    try {
      const account = await TradingAccount.findOne({ _id: accountId, userId });
      if (!account) {
        throw new Error('Trading account not found');
      }

      // If deleting default account, make another one default
      if (account.isDefault) {
        const nextAccount = await TradingAccount.findOne({ 
          userId, 
          _id: { $ne: accountId },
          status: 'active'
        });
        
        if (nextAccount) {
          nextAccount.isDefault = true;
          await nextAccount.save();
        }
      }

      await account.remove();
      return true;
    } catch (error) {
      throw new Error(`Error deleting trading account: ${error.message}`);
    }
  }

  /**
   * Get default trading account for a user
   */
  async getDefaultAccount(userId) {
    try {
      const account = await TradingAccount.findOne({ 
        userId, 
        isDefault: true,
        status: 'active'
      });
      return account ? account.toJSON() : null;
    } catch (error) {
      throw new Error(`Error fetching default account: ${error.message}`);
    }
  }
}

export default new TradingAccountService(); 