import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

// Initialize store with accounts from user_settings
const getInitialState = () => {
  try {
    console.log('Initializing account store...');
    // Try to get accounts from both storage locations
    const settings = JSON.parse(localStorage.getItem('user_settings')) || {};
    const accountStorage = JSON.parse(localStorage.getItem('account-storage')) || {};
    console.log('Settings from user_settings:', settings);
    console.log('Settings from account-storage:', accountStorage);
    
    // Combine accounts from both sources
    const accounts = [
      ...(settings.accounts || []),
      ...(accountStorage.state?.accounts || [])
    ].filter(Boolean);
    
    console.log('Combined accounts:', accounts);
    
    const validAccounts = accounts.filter(account => 
      account && 
      account.id && 
      account.name && 
      typeof account.initialCapital === 'number' &&
      !isNaN(account.initialCapital)
    );
    console.log('Valid accounts:', validAccounts);
    
    // Remove duplicates based on ID
    const uniqueAccounts = validAccounts.reduce((acc, current) => {
      const x = acc.find(item => item.id === current.id);
      if (!x) {
        return acc.concat([current]);
      }
      return acc;
    }, []);
    
    console.log('Unique accounts:', uniqueAccounts);
    
    const defaultAccount = uniqueAccounts.find(acc => acc.isDefault) || uniqueAccounts[0] || null;
    console.log('Default account:', defaultAccount);
    
    // Save the combined accounts back to both storages
    if (uniqueAccounts.length > 0) {
      try {
        settings.accounts = uniqueAccounts;
        localStorage.setItem('user_settings', JSON.stringify(settings));
        localStorage.setItem('account-storage', JSON.stringify({
          state: { accounts: uniqueAccounts, currentAccount: defaultAccount }
        }));
      } catch (e) {
        console.error('Error saving combined accounts:', e);
      }
    }
    
    return {
      accounts: uniqueAccounts,
      currentAccount: defaultAccount,
      isLoading: false,
      error: null
    };
  } catch (error) {
    console.error('Error initializing account store:', error);
    return {
      accounts: [],
      currentAccount: null,
      isLoading: false,
      error: null
    };
  }
};

const useAccountStore = create(
  persist(
    (set, get) => ({
      ...getInitialState(),

      setAccounts: (accounts) => {
        console.log('Setting accounts:', accounts);
        set({ accounts });
        // Also update user_settings
        try {
          const settings = JSON.parse(localStorage.getItem('user_settings')) || {};
          settings.accounts = accounts;
          localStorage.setItem('user_settings', JSON.stringify(settings));
        } catch (e) {
          console.error('Error saving accounts to user_settings:', e);
        }
      },
      
      setCurrentAccount: (accountId) => {
        console.log('Setting current account:', accountId);
        const account = get().accounts.find(a => a.id === accountId);
        console.log('Found account:', account);
        set({ currentAccount: account || null });
      },

      loadAccounts: async () => {
        console.log('Loading accounts...');
        set({ isLoading: true, error: null });
        try {
          // Try to get accounts from both storage locations
          const settings = JSON.parse(localStorage.getItem('user_settings')) || {};
          const accountStorage = JSON.parse(localStorage.getItem('account-storage')) || {};
          console.log('Settings from user_settings:', settings);
          console.log('Settings from account-storage:', accountStorage);
          
          // Combine accounts from both sources
          const accounts = [
            ...(settings.accounts || []),
            ...(accountStorage.state?.accounts || [])
          ].filter(Boolean);
          
          console.log('Combined accounts:', accounts);
          
          // Validate account data
          const validAccounts = accounts.filter(account => {
            const isValid = account && 
              account.id && 
              account.name && 
              typeof account.initialCapital === 'number' &&
              !isNaN(account.initialCapital);
            
            if (!isValid) {
              console.warn('Invalid account data found:', account);
            }
            return isValid;
          });

          console.log('Valid accounts:', validAccounts);
          
          // Remove duplicates based on ID
          const uniqueAccounts = validAccounts.reduce((acc, current) => {
            const x = acc.find(item => item.id === current.id);
            if (!x) {
              return acc.concat([current]);
            }
            return acc;
          }, []);
          
          console.log('Unique accounts:', uniqueAccounts);
          
          if (uniqueAccounts.length > 0) {
            set({ accounts: uniqueAccounts, isLoading: false });
            
            // Set current account to default or first available
            const defaultAccount = uniqueAccounts.find(acc => acc.isDefault) || uniqueAccounts[0];
            if (defaultAccount) {
              console.log('Setting current account:', defaultAccount);
              get().setCurrentAccount(defaultAccount.id);
            }
            
            // Save the combined accounts back to both storages
            try {
              settings.accounts = uniqueAccounts;
              localStorage.setItem('user_settings', JSON.stringify(settings));
              localStorage.setItem('account-storage', JSON.stringify({
                state: { accounts: uniqueAccounts, currentAccount: defaultAccount }
              }));
            } catch (e) {
              console.error('Error saving combined accounts:', e);
            }
          } else {
            set({ accounts: [], currentAccount: null, isLoading: false });
          }
          
          return uniqueAccounts;
        } catch (error) {
          console.error('Error loading accounts:', error);
          set({ error: 'Failed to load accounts', isLoading: false });
          return [];
        }
      },

      addAccount: async (accountData) => {
        set({ isLoading: true, error: null });
        try {
          // Validate required fields
          if (!accountData.name || typeof accountData.name !== 'string') {
            throw new Error('Account name is required');
          }
          
          if (typeof accountData.initialCapital !== 'number' || isNaN(accountData.initialCapital)) {
            throw new Error('Valid initial capital is required');
          }

          const settings = JSON.parse(localStorage.getItem('user_settings')) || { accounts: [] };
          const accounts = settings.accounts || [];
          
          // Check for duplicate names
          if (accounts.some(acc => acc.name.toLowerCase() === accountData.name.toLowerCase())) {
            throw new Error('An account with this name already exists');
          }
          
          const newAccount = {
            ...accountData,
            id: Date.now().toString(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            isDefault: accounts.length === 0
          };
          
          const updatedAccounts = [...accounts, newAccount];
          settings.accounts = updatedAccounts;
          
          localStorage.setItem('user_settings', JSON.stringify(settings));
          set({ accounts: updatedAccounts, isLoading: false });
          
          if (newAccount.isDefault) {
            get().setCurrentAccount(newAccount.id);
          }
          
          return newAccount;
        } catch (error) {
          console.error('Error adding account:', error);
          set({ error: 'Failed to add account', isLoading: false });
          throw error;
        }
      },

      updateAccount: async (accountId, updateData) => {
        set({ isLoading: true, error: null });
        try {
          const settings = JSON.parse(localStorage.getItem('user_settings')) || { accounts: [] };
          const accounts = settings.accounts || [];
          
          const updatedAccounts = accounts.map(account => {
            if (account.id === accountId) {
              return {
                ...account,
                ...updateData,
                updatedAt: new Date().toISOString()
              };
            }
            return account;
          });
          
          settings.accounts = updatedAccounts;
          localStorage.setItem('user_settings', JSON.stringify(settings));
          set({ accounts: updatedAccounts, isLoading: false });
          
          // Update current account if needed
          const current = get().currentAccount;
          if (current && current.id === accountId) {
            get().setCurrentAccount(accountId);
          }
          
          return updatedAccounts.find(a => a.id === accountId);
        } catch (error) {
          console.error('Error updating account:', error);
          set({ error: 'Failed to update account', isLoading: false });
          throw error;
        }
      },

      setDefaultAccount: async (accountId) => {
        set({ isLoading: true, error: null });
        try {
          const settings = JSON.parse(localStorage.getItem('user_settings')) || { accounts: [] };
          const accounts = settings.accounts || [];
          
          const updatedAccounts = accounts.map(account => ({
            ...account,
            isDefault: account.id === accountId,
            updatedAt: account.id === accountId ? new Date().toISOString() : account.updatedAt
          }));
          
          settings.accounts = updatedAccounts;
          localStorage.setItem('user_settings', JSON.stringify(settings));
          set({ accounts: updatedAccounts, isLoading: false });
          get().setCurrentAccount(accountId);
          
          return updatedAccounts.find(a => a.id === accountId);
        } catch (error) {
          console.error('Error setting default account:', error);
          set({ error: 'Failed to set default account', isLoading: false });
          throw error;
        }
      }
    }),
    {
      name: 'account-storage',
      getStorage: () => localStorage
    }
  )
);

export default useAccountStore; 