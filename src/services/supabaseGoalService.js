import { supabase } from '../lib/supabase';
import { format } from 'date-fns';

/**
 * Supabase Goal History Service
 * Replaces IndexedDB goal history with cloud-based storage
 */

export class SupabaseGoalService {
  /**
   * Save current goal progress to history
   * @param {string} userId - User ID
   * @param {Object} goalData - Goal data to save
   * @returns {Promise<Object>} Saved goal data
   */
  static async saveGoalHistory(userId, goalData) {
    try {
      const today = new Date();
      const date = format(today, 'yyyy-MM-dd');
      
      console.log('Saving goal history for date:', date, 'with data:', goalData);
      
      // Check if data exists for this date
      const { data: existingData, error: fetchError } = await supabase
        .from('goal_history')
        .select('*')
        .eq('user_id', userId)
        .eq('date', date)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') {
        throw fetchError;
      }

      const dataToSave = {
        user_id: userId,
        date,
        daily_pnl: goalData.dailyPnL || 0,
        total_trades: goalData.totalTrades || 0,
        winning_trades: goalData.winningTrades || 0,
        losing_trades: goalData.losingTrades || 0,
        win_rate: goalData.winRate || 0,
        profit_factor: goalData.profitFactor || 0,
        largest_win: goalData.largestWin || 0,
        largest_loss: goalData.largestLoss || 0,
        average_win: goalData.averageWin || 0,
        average_loss: goalData.averageLoss || 0,
        max_drawdown: goalData.maxDrawdown || 0,
        goal_data: goalData,
        updated_at: new Date().toISOString()
      };

      let result;
      if (existingData) {
        // Update existing record
        dataToSave.created_at = existingData.created_at; // Preserve original creation date
        const { data, error } = await supabase
          .from('goal_history')
          .update(dataToSave)
          .eq('user_id', userId)
          .eq('date', date)
          .select()
          .single();

        if (error) throw error;
        result = data;
        console.log('Updated existing goal history:', result);
      } else {
        // Insert new record
        dataToSave.created_at = dataToSave.updated_at;
        const { data, error } = await supabase
          .from('goal_history')
          .insert(dataToSave)
          .select()
          .single();

        if (error) throw error;
        result = data;
        console.log('Created new goal history:', result);
      }
      
      return result;
    } catch (error) {
      console.error('Error in saveGoalHistory:', error);
      throw error;
    }
  }

  /**
   * Get goal progress for a specific date
   * @param {string} userId - User ID
   * @param {string} date - Date in YYYY-MM-DD format
   * @returns {Promise<Object|null>} Goal data or null
   */
  static async getGoalProgress(userId, date) {
    try {
      const { data, error } = await supabase
        .from('goal_history')
        .select('*')
        .eq('user_id', userId)
        .eq('date', date)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // No data found
        }
        throw error;
      }
      
      return data;
    } catch (error) {
      console.error('Error in getGoalProgress:', error);
      return null;
    }
  }

  /**
   * Get goal progress for a date range
   * @param {string} userId - User ID
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Promise<Array>} Array of goal history records
   */
  static async getGoalProgressRange(userId, startDate, endDate) {
    try {
      const startDateStr = format(startDate, 'yyyy-MM-dd');
      const endDateStr = format(endDate, 'yyyy-MM-dd');

      const { data, error } = await supabase
        .from('goal_history')
        .select('*')
        .eq('user_id', userId)
        .gte('date', startDateStr)
        .lte('date', endDateStr)
        .order('date', { ascending: true });

      if (error) throw error;
      
      return data || [];
    } catch (error) {
      console.error('Error in getGoalProgressRange:', error);
      return [];
    }
  }

  /**
   * Get all goal history for a user
   * @param {string} userId - User ID
   * @returns {Promise<Array>} Array of all goal history records
   */
  static async getAllGoalHistory(userId) {
    try {
      const { data, error } = await supabase
        .from('goal_history')
        .select('*')
        .eq('user_id', userId)
        .order('date', { ascending: false });

      if (error) throw error;
      
      return data || [];
    } catch (error) {
      console.error('Error in getAllGoalHistory:', error);
      return [];
    }
  }

  /**
   * Delete goal history for a specific date
   * @param {string} userId - User ID
   * @param {string} date - Date in YYYY-MM-DD format
   * @returns {Promise<boolean>} Success status
   */
  static async deleteGoalHistory(userId, date) {
    try {
      const { error } = await supabase
        .from('goal_history')
        .delete()
        .eq('user_id', userId)
        .eq('date', date);

      if (error) throw error;
      
      console.log('Successfully deleted goal history for date:', date);
      return true;
    } catch (error) {
      console.error('Error in deleteGoalHistory:', error);
      throw error;
    }
  }

  /**
   * Migrate IndexedDB goal history to Supabase
   * @param {string} userId - User ID
   * @param {Array} indexedDBData - Array of IndexedDB goal history records
   * @returns {Promise<Object>} Migration result
   */
  static async migrateFromIndexedDB(userId, indexedDBData) {
    try {
      console.log('🔄 Migrating', indexedDBData.length, 'goal history records to Supabase...');
      
      const migratedRecords = [];
      const errors = [];

      for (const record of indexedDBData) {
        try {
          const supabaseRecord = {
            user_id: userId,
            date: record.date,
            daily_pnl: record.dailyPnL || 0,
            total_trades: record.totalTrades || 0,
            winning_trades: record.winningTrades || 0,
            losing_trades: record.losingTrades || 0,
            win_rate: record.winRate || 0,
            profit_factor: record.profitFactor || 0,
            largest_win: record.largestWin || 0,
            largest_loss: record.largestLoss || 0,
            average_win: record.averageWin || 0,
            average_loss: record.averageLoss || 0,
            max_drawdown: record.maxDrawdown || 0,
            goal_data: record,
            created_at: record.createdAt || record.updatedAt || new Date().toISOString(),
            updated_at: record.updatedAt || new Date().toISOString()
          };

          const { data, error } = await supabase
            .from('goal_history')
            .upsert(supabaseRecord, { onConflict: 'user_id,date' })
            .select()
            .single();

          if (error) throw error;
          
          migratedRecords.push(data);
          console.log('✅ Migrated goal history for date:', record.date);
        } catch (error) {
          console.error('❌ Error migrating record for date:', record.date, error);
          errors.push({ date: record.date, error: error.message });
        }
      }

      const result = {
        success: true,
        migrated: migratedRecords.length,
        errors: errors.length,
        errorDetails: errors
      };

      console.log('🎉 Goal history migration completed:', result);
      return result;
    } catch (error) {
      console.error('❌ Error in goal history migration:', error);
      throw error;
    }
  }
}

export default SupabaseGoalService;
