import { supabase } from '../lib/supabase';
import SupabaseGoalService from './supabaseGoalService';

/**
 * Data Service - Supabase Implementation
 * Replaces IndexedDB with cloud-based storage
 * Maintains backward compatibility with existing API
 */

// Helper function to get current user ID
async function getCurrentUserId() {
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new Error('User not authenticated');
  }
  return user.id;
}

// Legacy compatibility functions
export async function getDB() {
  console.log('getDB called - no longer needed with Supabase');
  return null;
}

export function closeDB() {
  console.log('closeDB called - no longer needed with Supabase');
}

// Trade-related functions
// Import centralized P&L calculation utility
import { calculatePnL, standardizeTradeObject } from '../utils/pnlCalculator';

export async function addTrade(trade) {
  try {
    const userId = await getCurrentUserId();

    // Use predefined P&L if available, otherwise let database calculate it
    // Database trigger will calculate P&L automatically for closed trades
    const calculatedPnL = trade.profit_loss !== undefined && trade.profit_loss !== null
      ? parseFloat(trade.profit_loss)
      : null; // Let database trigger calculate P&L

    const supabaseTrade = {
      user_id: userId,
      symbol: trade.instrument || trade.symbol,
      trade_type: (trade.position_type || trade.side || 'LONG').toUpperCase(),
      entry_price: parseFloat(trade.entry_price || trade.entryPrice) || 0,
      exit_price: parseFloat(trade.exit_price || trade.exitPrice) || 0,
      quantity: parseInt(trade.size || trade.quantity) || 1,
      entry_date: trade.timestamp || new Date().toISOString(),
      exit_date: trade.exitTimestamp || trade.exit_date,
      fees: parseFloat(trade.commission || trade.fees) || 0,
      status: trade.status || 'CLOSED',
      notes: trade.notes || '',
      strategy: trade.strategy || '',
      market_conditions: trade.market_conditions || '',
      pnl: calculatedPnL
    };

    console.log('📊 Adding trade with calculated P&L:', {
      symbol: supabaseTrade.symbol,
      pnl: supabaseTrade.pnl,
      entry_price: supabaseTrade.entry_price,
      exit_price: supabaseTrade.exit_price,
      quantity: supabaseTrade.quantity
    });

    const { data, error } = await supabase
      .from('trades')
      .insert(supabaseTrade)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error adding trade:', error);
    throw error;
  }
}

export async function addTrades(trades) {
  try {
    const userId = await getCurrentUserId();

    const supabaseTrades = trades.map(trade => {
      // Calculate P&L if not provided
      const calculatedPnL = trade.profit_loss !== undefined && trade.profit_loss !== null
        ? parseFloat(trade.profit_loss)
        : calculatePnL(trade);

      return {
        user_id: userId,
        symbol: trade.instrument || trade.symbol,
        trade_type: (trade.position_type || trade.side || 'LONG').toUpperCase(),
        entry_price: parseFloat(trade.entry_price || trade.entryPrice) || 0,
        exit_price: parseFloat(trade.exit_price || trade.exitPrice) || 0,
        quantity: parseInt(trade.size || trade.quantity) || 1,
        entry_date: trade.timestamp || new Date().toISOString(),
        exit_date: trade.exitTimestamp || trade.exit_date,
        fees: parseFloat(trade.commission || trade.fees) || 0,
        status: trade.status || 'CLOSED',
        notes: trade.notes || '',
        strategy: trade.strategy || '',
        market_conditions: trade.market_conditions || '',
        pnl: calculatedPnL
      };
    });

    console.log('📊 Adding batch trades with calculated P&L:', supabaseTrades.length, 'trades');
    console.log('Sample trade P&L calculation:', {
      symbol: supabaseTrades[0]?.symbol,
      pnl: supabaseTrades[0]?.pnl,
      entry_price: supabaseTrades[0]?.entry_price,
      exit_price: supabaseTrades[0]?.exit_price,
      quantity: supabaseTrades[0]?.quantity
    });

    const { data, error } = await supabase
      .from('trades')
      .insert(supabaseTrades)
      .select();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error adding trades:', error);
    throw error;
  }
}

export async function getAllTrades() {
  try {
    const userId = await getCurrentUserId();
    
    const { data, error } = await supabase
      .from('trades')
      .select('*')
      .eq('user_id', userId)
      .order('entry_date', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting all trades:', error);
    return [];
  }
}

export async function getTradesByAccount(accountId) {
  try {
    const userId = await getCurrentUserId();
    
    const { data, error } = await supabase
      .from('trades')
      .select('*')
      .eq('user_id', userId)
      .eq('account_id', accountId)
      .order('entry_date', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting trades by account:', error);
    return [];
  }
}

export async function getTradesByDateRange(startDate, endDate) {
  try {
    const userId = await getCurrentUserId();
    
    const { data, error } = await supabase
      .from('trades')
      .select('*')
      .eq('user_id', userId)
      .gte('entry_date', startDate.toISOString())
      .lte('entry_date', endDate.toISOString())
      .order('entry_date', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting trades by date range:', error);
    return [];
  }
}

export async function updateTrade(tradeId, updates) {
  try {
    const { data, error } = await supabase
      .from('trades')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', tradeId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating trade:', error);
    throw error;
  }
}

export async function deleteTrade(tradeId) {
  try {
    const { error } = await supabase
      .from('trades')
      .delete()
      .eq('id', tradeId);

    if (error) throw error;
    return { success: true };
  } catch (error) {
    console.error('Error deleting trade:', error);
    throw error;
  }
}

export async function clearAllTrades() {
  try {
    const userId = await getCurrentUserId();
    
    const { error } = await supabase
      .from('trades')
      .delete()
      .eq('user_id', userId);

    if (error) throw error;
    return { success: true };
  } catch (error) {
    console.error('Error clearing all trades:', error);
    throw error;
  }
}

// Goal history functions (delegate to SupabaseGoalService)
export async function saveGoalHistory(goalData) {
  try {
    const userId = await getCurrentUserId();
    return await SupabaseGoalService.saveGoalHistory(userId, goalData);
  } catch (error) {
    console.error('Error saving goal history:', error);
    throw error;
  }
}

export async function getGoalProgress(date) {
  try {
    const userId = await getCurrentUserId();
    return await SupabaseGoalService.getGoalProgress(userId, date);
  } catch (error) {
    console.error('Error getting goal progress:', error);
    return null;
  }
}

export async function getGoalProgressRange(startDate, endDate) {
  try {
    const userId = await getCurrentUserId();
    return await SupabaseGoalService.getGoalProgressRange(userId, startDate, endDate);
  } catch (error) {
    console.error('Error getting goal progress range:', error);
    return [];
  }
}

export async function getAllGoalHistory() {
  try {
    const userId = await getCurrentUserId();
    return await SupabaseGoalService.getAllGoalHistory(userId);
  } catch (error) {
    console.error('Error getting all goal history:', error);
    return [];
  }
}

// Settings functions (delegate to Supabase user_settings table)
export async function saveSetting(key, value) {
  try {
    const userId = await getCurrentUserId();
    
    const { data, error } = await supabase
      .from('user_settings')
      .upsert({
        user_id: userId,
        setting_key: key,
        setting_value: value,
        updated_at: new Date().toISOString()
      }, { onConflict: 'user_id,setting_key' })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error saving setting:', error);
    throw error;
  }
}

export async function getSetting(key) {
  try {
    const userId = await getCurrentUserId();
    
    const { data, error } = await supabase
      .from('user_settings')
      .select('setting_value')
      .eq('user_id', userId)
      .eq('setting_key', key)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Setting not found
      }
      throw error;
    }
    
    return data?.setting_value || null;
  } catch (error) {
    console.error('Error getting setting:', error);
    return null;
  }
}

export async function getAllSettings() {
  try {
    const userId = await getCurrentUserId();
    
    const { data, error } = await supabase
      .from('user_settings')
      .select('*')
      .eq('user_id', userId);

    if (error) throw error;
    
    // Convert to key-value object for backward compatibility
    const settings = {};
    (data || []).forEach(setting => {
      settings[setting.setting_key] = setting.setting_value;
    });
    
    return settings;
  } catch (error) {
    console.error('Error getting all settings:', error);
    return {};
  }
}

// Clear all data (equivalent to clearAllData)
export async function clearAllData() {
  try {
    const userId = await getCurrentUserId();

    // Clear all user data from Supabase
    const promises = [
      // Clear trades
      supabase.from('trades').delete().eq('user_id', userId),
      // Clear goal history
      supabase.from('goal_history').delete().eq('user_id', userId),
      // Clear settings
      supabase.from('user_settings').delete().eq('user_id', userId)
    ];

    const results = await Promise.allSettled(promises);

    // Check for errors
    const errors = results.filter(result => result.status === 'rejected');
    if (errors.length > 0) {
      console.error('Some data clearing operations failed:', errors);
      throw new Error(`Failed to clear some data: ${errors.map(e => e.reason?.message).join(', ')}`);
    }

    return { success: true, message: 'All data cleared successfully' };
  } catch (error) {
    console.error('Error clearing all data:', error);
    throw error;
  }
}

// Delete trades by date range
export async function deleteTradesByDate(startDate, endDate) {
  try {
    const userId = await getCurrentUserId();

    const { error: tradesError } = await supabase
      .from('trades')
      .delete()
      .eq('user_id', userId)
      .gte('entry_date', startDate.toISOString())
      .lte('entry_date', endDate.toISOString());

    if (tradesError) throw tradesError;

    // Also delete goal history in the same date range
    const startDateStr = startDate.toISOString().split('T')[0];
    const endDateStr = endDate.toISOString().split('T')[0];

    const { error: goalError } = await supabase
      .from('goal_history')
      .delete()
      .eq('user_id', userId)
      .gte('date', startDateStr)
      .lte('date', endDateStr);

    if (goalError) throw goalError;

    return { success: true, message: 'Data deleted successfully' };
  } catch (error) {
    console.error('Error in deleteTradesByDate:', error);
    throw error;
  }
}

// Export data in JSON or CSV format
export async function exportData(format = 'json') {
  try {
    const userId = await getCurrentUserId();

    // Get all user data
    const [tradesResult, goalsResult] = await Promise.all([
      supabase.from('trades').select('*').eq('user_id', userId).order('entry_date', { ascending: false }),
      supabase.from('goal_history').select('*').eq('user_id', userId).order('date', { ascending: false })
    ]);

    if (tradesResult.error) throw tradesResult.error;
    if (goalsResult.error) throw goalsResult.error;

    const trades = tradesResult.data || [];
    const goals = goalsResult.data || [];

    const data = {
      trades,
      goalHistory: goals,
      exportDate: new Date().toISOString()
    };

    if (format === 'csv') {
      // Convert to CSV format
      const csvRows = [];

      // Headers
      csvRows.push(['Trade ID', 'Date', 'Symbol', 'Type', 'Quantity', 'Entry Price', 'Exit Price', 'PnL', 'Status']);

      // Data rows
      trades.forEach(trade => {
        csvRows.push([
          trade.id,
          trade.entry_date,
          trade.symbol,
          trade.trade_type,
          trade.quantity,
          trade.entry_price,
          trade.exit_price,
          trade.pnl,
          trade.status
        ]);
      });

      const csvContent = csvRows.map(row => row.join(',')).join('\n');
      return new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    }

    // Default to JSON format
    return new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
  } catch (error) {
    console.error('Error in exportData:', error);
    throw error;
  }
}

// Migration utilities
export async function migrateFromIndexedDB() {
  console.log('Migration from IndexedDB to Supabase not yet implemented');
  console.log('Please use the migration utility in the admin panel');
  return { success: false, message: 'Migration utility needed' };
}

export default {
  getDB,
  closeDB,
  addTrade,
  addTrades,
  getAllTrades,
  getTradesByAccount,
  getTradesByDateRange,
  updateTrade,
  deleteTrade,
  clearAllTrades,
  clearAllData,
  deleteTradesByDate,
  exportData,
  saveGoalHistory,
  getGoalProgress,
  getGoalProgressRange,
  getAllGoalHistory,
  saveSetting,
  getSetting,
  getAllSettings,
  migrateFromIndexedDB
};
