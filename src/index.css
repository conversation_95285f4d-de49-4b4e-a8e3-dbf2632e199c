@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html, body {
    @apply overflow-x-hidden bg-slate-50;
  }
}

@layer components {
  .dashboard-card {
    @apply bg-white rounded-xl shadow-sm p-6 w-full overflow-hidden;
  }
  
  .card-title {
    @apply text-sm font-bold text-slate-800;
  }

  .chart-period-btn {
    @apply px-3 py-1 text-sm font-medium rounded-md text-slate-600 hover:bg-slate-100 transition-colors;
  }

  .chart-period-btn.active {
    @apply bg-blue-50 text-blue-600;
  }

  .drag-handle {
    @apply flex gap-1.5 items-center p-1.5 rounded-md transition-all duration-200 cursor-grab hover:bg-slate-100;
  }

  .drag-handle.dragging {
    @apply cursor-grabbing bg-blue-100/50;
  }

  .drag-handle-dot {
    @apply w-1.5 h-1.5 rounded-full transition-colors duration-200 bg-slate-300;
  }

  .drag-handle-dot.dragging {
    @apply bg-blue-400;
  }

  .dashboard-layout {
    @apply flex min-h-screen;
  }

  .dashboard-content {
    @apply flex-1 ml-64 relative;
  }

  .dashboard-sidebar {
    @apply fixed left-0 top-0 h-full w-64 bg-slate-900 text-white z-50;
  }

  .dashboard-footer {
    @apply bg-slate-900 text-slate-400 py-6 px-8 mt-auto;
  }
} 