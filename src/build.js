const execSync = require('child_process').execSync;

console.log('Building Tailwind CSS...');

try {
    // Install required packages
    execSync('npm install -D tailwindcss');
    
    // Create tailwind config if it doesn't exist
    if (!require('fs').existsSync('tailwind.config.js')) {
        execSync('npx tailwindcss init');
    }
    
    // Build the CSS
    execSync('npx tailwindcss -i src/dashboard/styles.css -o src/lib/tailwind.min.css --minify');
    
    console.log('Build completed successfully!');
} catch (error) {
    console.error('Build failed:', error);
    process.exit(1);
}
