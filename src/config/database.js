import { Sequelize } from 'sequelize';

const env = process.env.NODE_ENV || 'development';
const config = {
  development: {
    username: 'postgres',
    password: 'postgres',
    database: 'tradedge_dev',
    host: '127.0.0.1',
    port: 5432,
    dialect: 'postgres',
    logging: console.log
  },
  test: {
    username: 'postgres',
    password: 'postgres',
    database: 'tradedge_test',
    host: '127.0.0.1',
    port: 5432,
    dialect: 'postgres',
    logging: false
  },
  production: {
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'postgres',
    logging: false,
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000
    },
    dialectOptions: {
      ssl: {
        require: true,
        rejectUnauthorized: false
      }
    }
  }
};

// Create Sequelize instance
const sequelize = new Sequelize(
  config[env].database,
  config[env].username,
  config[env].password,
  {
    host: config[env].host,
    port: config[env].port,
    dialect: config[env].dialect,
    logging: config[env].logging,
    pool: config[env].pool,
    dialectOptions: config[env].dialectOptions
  }
);

// Connection function
const connectDB = async () => {
  if (typeof window !== 'undefined') {
    console.log('Running in browser environment - skipping direct database connection');
    return;
  }

  try {
    await sequelize.authenticate();
    console.log('Database connection established successfully.');
  } catch (error) {
    console.error('Unable to connect to the database:', error);
    throw error;
  }
};

export { sequelize, connectDB }; 