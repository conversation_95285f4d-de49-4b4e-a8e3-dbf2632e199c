/* Tooltip Styles */
.tooltip-trigger {
  position: relative;
}

.tooltip {
  visibility: hidden;
  position: absolute;
  top: -4px;
  left: 50%;
  transform: translateX(-50%) translateY(-100%);
  background-color: rgb(15 23 42 / 0.95);
  color: white;
  text-align: center;
  padding: 3px 6px;
  border-radius: 6px;
  font-size: 9px;
  white-space: nowrap;
  opacity: 0;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: bottom;
  backdrop-filter: blur(8px);
  box-shadow: 0 2px 4px -1px rgb(0 0 0 / 0.05);
  min-width: 0;
  text-align: center;
  pointer-events: none;
  letter-spacing: 0.02em;
  font-weight: 450;
}

.tooltip::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -4px;
  border-width: 4px;
  border-style: solid;
  border-color: rgb(15 23 42 / 0.95) transparent transparent transparent;
}

.tooltip-trigger:hover .tooltip {
  visibility: visible;
  opacity: 1;
  transform: translateX(-50%) translateY(-100%);
}

.tooltip-trigger .tooltip {
  transform: translateX(-50%) translateY(-95%) scale(0.95);
}

/* Prevent tooltip from showing on card hover */
.group-hover/card .tooltip {
  display: none;
}

/* Only show tooltip on button hover, not card hover */
.group-hover .tooltip-trigger .tooltip {
  visibility: hidden;
  opacity: 0;
} 