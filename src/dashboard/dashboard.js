class TradeAnalytics {
    constructor() {
        this.trades = [];
        this.charts = {};
        this.initializeEventListeners();
        this.DateTime = luxon.DateTime;
    }

    initializeEventListeners() {
        document.getElementById('importBtn').addEventListener('click', () => {
            document.getElementById('csvFile').click();
        });

        document.getElementById('csvFile').addEventListener('change', (event) => {
            const file = event.target.files[0];
            if (file) {
                this.processCSV(file);
            }
        });

        document.getElementById('searchInput').addEventListener('input', (e) => {
            this.filterTrades(e.target.value);
        });

        document.getElementById('dateFilter').addEventListener('change', (e) => {
            this.filterTradesByDate(e.target.value);
        });
    }

    async processCSV(file) {
        const text = await file.text();
        const rows = text.split('\n').map(row => row.split(','));
        const headers = rows[0];
        
        // Process trades
        let trades = [];
        let currentTrade = null;
        let commission = 0;

        for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            if (row.length < headers.length) continue;

            const transaction = {
                account: row[0],
                transactionId: row[1],
                timestamp: this.DateTime.fromFormat(row[2], "MM/dd/yyyy HH:mm:ss"),
                date: row[3],
                delta: parseFloat(row[4]),
                amount: parseFloat(row[5].replace(',', '')),
                type: row[6].trim(),
                currency: row[7],
                contract: row[8].trim()
            };

            if (transaction.type === 'Commission') {
                commission += Math.abs(transaction.delta);
            } else if (transaction.type === 'Trade Paired') {
                transaction.commission = commission;
                commission = 0;
                trades.push(transaction);
            }
        }

        this.trades = trades;
        this.updateDashboard();
    }

    updateDashboard() {
        const stats = this.calculateStats();
        
        // Update statistics with animations
        this.animateValue('totalPnL', stats.totalPnL, this.formatCurrency);
        this.animateValue('winRate', stats.winRate * 100, (value) => `${value.toFixed(1)}%`);
        this.animateValue('totalTrades', stats.totalTrades, (value) => value);
        this.animateValue('avgTrade', stats.avgTrade, this.formatCurrency);

        // Update additional stats
        document.getElementById('totalPnLPercent').textContent = 
            `${((stats.totalPnL / Math.abs(stats.startingBalance)) * 100).toFixed(2)}% return`;
        document.getElementById('winCount').textContent = 
            `${stats.winningTrades} wins / ${stats.totalTrades - stats.winningTrades} losses`;
        document.getElementById('avgTradesPerDay').textContent = 
            `${(stats.totalTrades / stats.tradingDays).toFixed(1)} trades/day`;
        document.getElementById('largestWin').textContent = 
            `Largest win: ${this.formatCurrency(stats.largestWin)}`;

        // Update charts
        this.updateCharts(stats);

        // Update trade table
        this.updateTradeTable();
    }

    animateValue(elementId, value, formatter) {
        const element = document.getElementById(elementId);
        const duration = 1000;
        const steps = 20;
        const increment = value / steps;
        let current = 0;
        
        const animate = () => {
            current += increment;
            if (current >= value) {
                element.textContent = formatter(value);
                return;
            }
            element.textContent = formatter(current);
            requestAnimationFrame(animate);
        };
        
        animate();
    }

    calculateStats() {
        const stats = {
            totalPnL: 0,
            winningTrades: 0,
            totalTrades: this.trades.length,
            avgTrade: 0,
            largestWin: 0,
            startingBalance: this.trades[0]?.amount || 0,
            tradingDays: new Set(this.trades.map(t => t.date)).size
        };

        this.trades.forEach(trade => {
            stats.totalPnL += trade.delta;
            if (trade.delta > 0) {
                stats.winningTrades++;
                stats.largestWin = Math.max(stats.largestWin, trade.delta);
            }
        });

        stats.avgTrade = stats.totalPnL / stats.totalTrades;
        stats.winRate = stats.winningTrades / stats.totalTrades;

        return stats;
    }

    updateCharts(stats) {
        this.updatePnLChart();
        this.updateDailyPnLChart();
    }

    updatePnLChart() {
        const ctx = document.getElementById('pnlChart').getContext('2d');
        
        let cumulativePnL = 0;
        const data = this.trades.map(trade => {
            cumulativePnL += trade.delta;
            return {
                x: trade.timestamp.toJSDate(),
                y: cumulativePnL
            };
        });

        if (this.charts.pnl) {
            this.charts.pnl.destroy();
        }

        this.charts.pnl = new Chart(ctx, {
            type: 'line',
            data: {
                datasets: [{
                    label: 'Cumulative P&L',
                    data: data,
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    fill: true,
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'day'
                        }
                    },
                    y: {
                        ticks: {
                            callback: (value) => this.formatCurrency(value)
                        }
                    }
                }
            }
        });
    }

    updateDailyPnLChart() {
        const ctx = document.getElementById('dailyPnLChart').getContext('2d');
        
        // Group trades by date
        const dailyPnL = {};
        this.trades.forEach(trade => {
            const date = trade.date;
            dailyPnL[date] = (dailyPnL[date] || 0) + trade.delta;
        });

        if (this.charts.daily) {
            this.charts.daily.destroy();
        }

        this.charts.daily = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: Object.keys(dailyPnL),
                datasets: [{
                    label: 'Daily P&L',
                    data: Object.values(dailyPnL),
                    backgroundColor: Object.values(dailyPnL).map(value => 
                        value >= 0 ? 'rgba(16, 185, 129, 0.2)' : 'rgba(239, 68, 68, 0.2)'
                    ),
                    borderColor: Object.values(dailyPnL).map(value =>
                        value >= 0 ? 'rgb(16, 185, 129)' : 'rgb(239, 68, 68)'
                    ),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: (value) => this.formatCurrency(value)
                        }
                    }
                }
            }
        });
    }

    filterTrades(searchTerm) {
        const tbody = document.getElementById('tradeTableBody');
        const rows = tbody.getElementsByTagName('tr');
        
        for (let row of rows) {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm.toLowerCase()) ? '' : 'none';
        }
    }

    filterTradesByDate(date) {
        if (!date) {
            this.updateDashboard();
            return;
        }

        const filteredTrades = this.trades.filter(trade => 
            trade.date === date
        );

        this.updateTradeTable(filteredTrades);
    }

    updateTradeTable(tradesToShow = this.trades) {
        const tbody = document.getElementById('tradeTableBody');
        tbody.innerHTML = '';

        tradesToShow.forEach(trade => {
            const row = document.createElement('tr');
            row.className = 'hover:bg-gray-50 transition-colors duration-150 ease-in-out';
            row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${trade.timestamp.toFormat('yyyy-MM-dd HH:mm:ss')}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${trade.contract}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm ${trade.delta >= 0 ? 'text-green-600' : 'text-red-600'}">
                    ${this.formatCurrency(trade.delta)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${this.formatCurrency(trade.commission)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${this.formatCurrency(trade.amount)}
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    }
}

// Initialize the dashboard
document.addEventListener('DOMContentLoaded', () => {
    new TradeAnalytics();
}); 