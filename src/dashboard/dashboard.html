<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trade Analytics Dashboard</title>
    <link href="../lib/tailwind.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    <script src="../lib/chart.min.js"></script>
    <script src="../lib/luxon.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .stat-card {
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .positive { color: #10B981; }
        .negative { color: #EF4444; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen">
        <!-- Navigation -->
        <nav class="bg-white shadow-lg border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <span class="text-xl font-bold text-gray-800">Trade Analytics</span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="relative">
                            <input type="date" id="dateFilter" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                        <button id="importBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition duration-150 ease-in-out">
                            Import CSV
                        </button>
                        <input type="file" id="csvFile" accept=".csv" class="hidden">
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-6 px-4">
            <!-- Stats Overview -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="stat-card bg-white p-6 rounded-lg shadow hover:shadow-md">
                    <h3 class="text-sm font-medium text-gray-500">Total P&L</h3>
                    <p id="totalPnL" class="text-2xl font-bold"></p>
                    <p id="totalPnLPercent" class="text-sm text-gray-500"></p>
                </div>
                <div class="stat-card bg-white p-6 rounded-lg shadow hover:shadow-md">
                    <h3 class="text-sm font-medium text-gray-500">Win Rate</h3>
                    <p id="winRate" class="text-2xl font-bold text-gray-900">0%</p>
                    <p id="winCount" class="text-sm text-gray-500"></p>
                </div>
                <div class="stat-card bg-white p-6 rounded-lg shadow hover:shadow-md">
                    <h3 class="text-sm font-medium text-gray-500">Total Trades</h3>
                    <p id="totalTrades" class="text-2xl font-bold text-gray-900">0</p>
                    <p id="avgTradesPerDay" class="text-sm text-gray-500"></p>
                </div>
                <div class="stat-card bg-white p-6 rounded-lg shadow hover:shadow-md">
                    <h3 class="text-sm font-medium text-gray-500">Average Trade</h3>
                    <p id="avgTrade" class="text-2xl font-bold text-gray-900">$0.00</p>
                    <p id="largestWin" class="text-sm text-gray-500"></p>
                </div>
            </div>

            <!-- Charts -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-white p-6 rounded-lg shadow">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Cumulative P&L</h3>
                    <canvas id="pnlChart"></canvas>
                </div>
                <div class="bg-white p-6 rounded-lg shadow">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Daily P&L</h3>
                    <canvas id="dailyPnLChart"></canvas>
                </div>
            </div>

            <!-- Trade Table -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Trade History</h3>
                        <div class="flex space-x-2">
                            <input type="text" id="searchInput" placeholder="Search trades..." 
                                   class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer">
                                        Date ↕
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contract</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">P&L</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Commission</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Balance</th>
                                </tr>
                            </thead>
                            <tbody id="tradeTableBody" class="bg-white divide-y divide-gray-200">
                                <!-- Trade rows will be inserted here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
    <script src="dashboard.js"></script>
</body>
</html> 