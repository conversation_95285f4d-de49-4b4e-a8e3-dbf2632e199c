const fs = require('fs');

// Base64 encoded 48x48 blue square with "T"
const icon48Base64 = `iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAACXBIWXMAAAsTAAALEwEAmpwYAAABZklEQVR4nO2ZQU7DMBBFv1m1N2DPCbp3wR3YcIK2J2l7A7ohEifgBOwQK05A2bCAM7CKKkWqaFTXiWd+4vgjRYrSRPbzxB5PbCCTyWQymYwATwAOAN4AXAC4AnhxP2cyELnFZ7J4RwXgfWDxjTeXI5OA3AJYDSy+seKuSUZuAZz/WLzj7K5NQm4BvE0s3vHqcqPJTQXgY2bxjg+XQ01OKrfQ5fqTQ01OKgBnD4t3nFyuMORWHhbvWLpcYcgtPS3esXS5wpBbT+zKXZcrDLm1x67cdbnCkFt77MpdlysEuV0EWLxj4XKFILeLAIt3LFyuEOR2HWDxjrXLFYLcPgMs3vHpcoUgt58BFu/4dLlCkNt9gMU77l2uEOT2GGDxjqPLFYbcngMs3nF2ucKQW0vu6F25uVxhyK0ld/Su3FyuMOTWkjt6V24uVxhya8kdvSs3l8sTmUwmk8lk/jd+AO1/RKQXwGSAAAAAAElFTkSuQmCC`;

// Base64 encoded 128x128 blue square with "T"
const icon128Base64 = `iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAACXBIWXMAAAsTAAALEwEAmpwYAAABZklEQVR4nO3dQU7DMBBFv1m1N2DPCbp3wR3YcIK2J2l7A7ohEifgBOwQK05A2bCAM7CKKkWqaFTXiWd+4vgjRYrSRPbzxB5PbCCTyWQymYwATwAOAN4AXAC4AnhxP2cyELnFZ7J4RwXgfWDxjTeXI5OA3AJYDSy+seKuSUZuAZz/WLzj7K5NQm4BvE0s3vHqcqPJTQXgY2bxjg+XQ01OKrfQ5fqTQ01OKgBnD4t3nFyuMORWHhbvWLpcYcgtPS3esXS5wpBbT+zKXZcrDLm1x67cdbnCkFt77MpdlysEuV0EWLxj4XKFILeLAIt3LFyuEOR2HWDxjrXLFYLcPgMs3vHpcoUgt58BFu/4dLlCkNt9gMU77l2uEOT2GGDxjqPLFYbcngMs3nF2ucKQW0vu6F25uVxhyK0ld/Su3FyuMOTWkjt6V24uVxhya8kdvSs3l8sTmUwmk8lk/jd+AO1/RKQXwGSAAAAAAElFTkSuQmCC`;

// Remove any whitespace from base64 strings
const cleanIcon48 = icon48Base64.trim();
const cleanIcon128 = icon128Base64.trim();

try {
    // Write the base64 data to PNG files
    fs.writeFileSync('icon48.png', Buffer.from(cleanIcon48, 'base64'));
    fs.writeFileSync('icon128.png', Buffer.from(cleanIcon128, 'base64'));
    console.log('Icons created successfully!');
} catch (error) {
    console.error('Error creating icons:', error);
} 