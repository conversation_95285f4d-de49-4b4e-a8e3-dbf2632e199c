const fs = require('fs');

// Simple T icon in white on blue background
const iconSvg = `
<svg width="128" height="128" xmlns="http://www.w3.org/2000/svg">
    <rect width="128" height="128" fill="#2563eb"/>
    <text x="64" y="90" font-family="Arial" font-size="90" fill="white" text-anchor="middle">T</text>
</svg>
`;

// Convert SVG to base64 data URL
const svgBuffer = Buffer.from(iconSvg);
fs.writeFileSync('icon128.svg', svgBuffer);
fs.writeFileSync('icon48.svg', svgBuffer); 