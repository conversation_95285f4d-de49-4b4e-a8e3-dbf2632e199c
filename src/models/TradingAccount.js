import { Model, DataTypes, Op } from 'sequelize';
import { sequelize } from '../config/database.js';
import User from './User.js';

class TradingAccount extends Model {
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      initialCapital: this.initialCapital,
      type: this.type,
      broker: this.broker,
      isDefault: this.isDefault,
      status: this.status,
      metadata: this.metadata,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

TradingAccount.init({
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true
    }
  },
  initialCapital: {
    type: DataTypes.DECIMAL(20, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  type: {
    type: DataTypes.ENUM('live', 'demo', 'paper'),
    allowNull: false
  },
  broker: {
    type: DataTypes.STRING
  },
  isDefault: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'closed'),
    defaultValue: 'active'
  },
  metadata: {
    type: DataTypes.JSONB,
    defaultValue: {
      platform: null,
      accountNumber: null,
      currency: 'USD'
    }
  }
}, {
  sequelize,
  modelName: 'TradingAccount',
  timestamps: true,
  indexes: [
    {
      unique: true,
      fields: ['userId', 'name']
    },
    {
      fields: ['userId', 'isDefault']
    }
  ],
  hooks: {
    beforeSave: async (account) => {
      if (account.isDefault) {
        // Unset any other default accounts for this user
        await TradingAccount.update(
          { isDefault: false },
          {
            where: {
              userId: account.userId,
              id: { [Op.ne]: account.id || 0 },
              isDefault: true
            }
          }
        );
      }
    }
  }
});

// Define associations
TradingAccount.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
});

export default TradingAccount; 