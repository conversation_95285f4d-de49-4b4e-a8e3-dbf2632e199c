import { Model, DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';

class User extends Model {
  toProfileJSON() {
    return {
      id: this.id,
      name: this.name,
      email: this.email,
      title: this.title,
      bio: this.bio,
      tradingFirm: this.tradingFirm,
      tradingSince: this.tradingSince,
      tradingRegion: this.tradingRegion,
      tradingStyle: this.tradingStyle
    };
  }

  toSettingsJSON() {
    return {
      id: this.id,
      settings: this.settings
    };
  }
}

User.init({
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true
    }
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  title: {
    type: DataTypes.STRING,
    defaultValue: 'Trader'
  },
  bio: {
    type: DataTypes.TEXT
  },
  tradingFirm: {
    type: DataTypes.STRING
  },
  tradingSince: {
    type: DataTypes.DATE
  },
  tradingRegion: {
    type: DataTypes.STRING
  },
  tradingStyle: {
    type: DataTypes.JSONB,
    defaultValue: {
      preferredMarkets: [],
      style: null,
      riskManagement: [],
      tradingHours: null
    }
  },
  settings: {
    type: DataTypes.JSONB,
    defaultValue: {
      timeZone: 'America/New_York',
      notifications: {
        email: true,
        desktop: true,
        tradeAlerts: true
      },
      privacy: {
        profileVisibility: 'private',
        showTradingStats: false
      }
    }
  }
}, {
  sequelize,
  modelName: 'User',
  timestamps: true,
  indexes: [
    {
      unique: true,
      fields: ['email']
    }
  ]
});

export default User; 