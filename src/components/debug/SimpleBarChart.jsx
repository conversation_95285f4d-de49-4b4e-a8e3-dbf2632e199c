import React from 'react';

/**
 * Professional bar chart using pure CSS/HTML
 * Replaces Chart.js for better reliability and performance
 */
export default function SimpleBarChart({ data, title = "P&L Chart" }) {
  if (!data || !data.datasets || !data.datasets[0] || !data.datasets[0].data) {
    return (
      <div className="p-4 bg-gray-100 rounded">
        <p className="text-gray-600">No chart data available</p>
      </div>
    );
  }

  const values = data.datasets[0].data;
  const labels = data.labels || [];
  
  if (values.length === 0) {
    return (
      <div className="p-4 bg-gray-100 rounded">
        <p className="text-gray-600">No data points to display</p>
      </div>
    );
  }

  const maxValue = Math.max(...values.map(Math.abs));
  const minValue = Math.min(...values);
  const hasNegative = minValue < 0;

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  return (
    <div className="h-full flex flex-col">
      {/* Chart area */}
      <div className="flex-1 min-h-0 relative">
        {/* Y-axis labels */}
        <div className="absolute left-0 top-0 bottom-0 w-16 flex flex-col justify-between text-xs text-slate-500 py-2">
          <span>{formatCurrency(Math.max(...values))}</span>
          {hasNegative && <span>$0</span>}
          <span>{formatCurrency(Math.min(...values))}</span>
        </div>

        {/* Chart bars */}
        <div className="ml-16 mr-4 h-full flex items-end justify-center space-x-1 py-2">
          {values.map((value, index) => {
            const percentage = maxValue > 0 ? (Math.abs(value) / maxValue) * 80 : 0; // 80% max height
            const isPositive = value >= 0;
            const barHeight = Math.max(percentage, 2); // Minimum 2% height for visibility

            return (
              <div key={index} className="flex-1 max-w-12 flex flex-col items-center group relative">
                {/* Tooltip */}
                <div className="absolute bottom-full mb-2 hidden group-hover:block bg-slate-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap z-10">
                  <div>{formatCurrency(value)}</div>
                  <div className="text-slate-300">{labels[index]?.split(',')[0] || `Trade ${index + 1}`}</div>
                </div>

                {/* Bar container */}
                <div className="w-full h-full flex flex-col justify-end">
                  {hasNegative && (
                    <div className="flex-1 flex items-end justify-center">
                      {isPositive && (
                        <div
                          className="w-full bg-green-500 hover:bg-green-600 transition-colors rounded-t border border-green-600"
                          style={{ height: `${barHeight}%` }}
                        />
                      )}
                    </div>
                  )}

                  {hasNegative && <div className="h-px bg-slate-300 w-full" />}

                  {hasNegative && (
                    <div className="flex-1 flex items-start justify-center">
                      {!isPositive && (
                        <div
                          className="w-full bg-red-500 hover:bg-red-600 transition-colors rounded-b border border-red-600"
                          style={{ height: `${barHeight}%` }}
                        />
                      )}
                    </div>
                  )}

                  {!hasNegative && (
                    <div
                      className={`w-full transition-colors rounded border ${
                        isPositive
                          ? 'bg-green-500 hover:bg-green-600 border-green-600'
                          : 'bg-red-500 hover:bg-red-600 border-red-600'
                      }`}
                      style={{ height: `${barHeight}%` }}
                    />
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* X-axis labels */}
      <div className="ml-16 mr-4 flex justify-between text-xs text-slate-500 mt-2 overflow-hidden">
        {labels.slice(0, Math.min(labels.length, 8)).map((label, index) => {
          const step = Math.ceil(labels.length / 8);
          const shouldShow = index % step === 0;
          return shouldShow ? (
            <span key={index} className="truncate max-w-16 text-center">
              {label.split(',')[0]}
            </span>
          ) : null;
        })}
      </div>
    </div>
  );
}
