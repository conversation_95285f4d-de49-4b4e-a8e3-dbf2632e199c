import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import useSupabaseTradeStore from '../../stores/supabaseTradeStore';

/**
 * Debug component to help diagnose dashboard 0 values issue
 */
const TradeDebugger = () => {
  const { user } = useAuth();
  const { trades, loadTrades, isLoading } = useSupabaseTradeStore();
  const [debugInfo, setDebugInfo] = useState(null);

  const runDiagnostics = async () => {
    console.log('🔍 Running trade diagnostics...');
    
    const info = {
      timestamp: new Date().toISOString(),
      user: {
        authenticated: !!user,
        id: user?.id?.substring(0, 8) || 'none',
        email: user?.email || 'none'
      },
      trades: {
        total: trades?.length || 0,
        withPnL: trades?.filter(t => t.profit_loss !== null && t.profit_loss !== undefined).length || 0,
        withoutPnL: trades?.filter(t => t.profit_loss === null || t.profit_loss === undefined).length || 0,
        sample: trades?.length > 0 ? {
          id: trades[0].id?.substring(0, 8),
          symbol: trades[0].instrument || trades[0].symbol,
          profit_loss: trades[0].profit_loss,
          pnl: trades[0].pnl,
          entry_price: trades[0].entry_price,
          exit_price: trades[0].exit_price
        } : null
      },
      metrics: {
        totalPnL: trades?.reduce((sum, t) => sum + (t.profit_loss || 0), 0) || 0,
        winningTrades: trades?.filter(t => (t.profit_loss || 0) > 0).length || 0,
        losingTrades: trades?.filter(t => (t.profit_loss || 0) < 0).length || 0,
        winRate: trades?.length > 0 ?
          (trades.filter(t => (t.profit_loss || 0) > 0).length / trades.length * 100) : 0
      },
      allTrades: trades?.slice(0, 10).map(t => ({
        id: t.id?.substring(0, 8),
        symbol: t.instrument || t.symbol,
        profit_loss: t.profit_loss,
        entry_price: t.entry_price,
        exit_price: t.exit_price,
        status: t.status
      })) || []
    };

    setDebugInfo(info);
    console.log('🔍 Debug info:', info);
  };

  const refreshTrades = async () => {
    console.log('🔄 Refreshing trades...');
    await loadTrades();
    await runDiagnostics();
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 mb-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Trade Debugger</h3>
        <div className="space-x-2">
          <button
            onClick={runDiagnostics}
            className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Run Diagnostics
          </button>
          <button
            onClick={refreshTrades}
            disabled={isLoading}
            className="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            {isLoading ? 'Loading...' : 'Refresh Trades'}
          </button>
        </div>
      </div>

      {debugInfo && (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gray-50 p-3 rounded">
              <h4 className="font-medium text-gray-700 mb-2">Authentication</h4>
              <p className="text-sm">
                Status: <span className={debugInfo.user.authenticated ? 'text-green-600' : 'text-red-600'}>
                  {debugInfo.user.authenticated ? 'Authenticated' : 'Not Authenticated'}
                </span>
              </p>
              <p className="text-sm">Email: {debugInfo.user.email}</p>
              <p className="text-sm">ID: {debugInfo.user.id}</p>
            </div>

            <div className="bg-gray-50 p-3 rounded">
              <h4 className="font-medium text-gray-700 mb-2">Trades</h4>
              <p className="text-sm">Total: {debugInfo.trades.total}</p>
              <p className="text-sm">With P&L: {debugInfo.trades.withPnL}</p>
              <p className="text-sm">Without P&L: {debugInfo.trades.withoutPnL}</p>
            </div>

            <div className="bg-gray-50 p-3 rounded">
              <h4 className="font-medium text-gray-700 mb-2">Metrics</h4>
              <p className="text-sm">Total P&L: ${debugInfo.metrics.totalPnL.toFixed(2)}</p>
              <p className="text-sm">Win Rate: {debugInfo.metrics.winRate.toFixed(2)}%</p>
              <p className="text-sm">Winning: {debugInfo.metrics.winningTrades}</p>
              <p className="text-sm">Losing: {debugInfo.metrics.losingTrades}</p>
            </div>
          </div>

          {debugInfo.trades.sample && (
            <div className="bg-gray-50 p-3 rounded">
              <h4 className="font-medium text-gray-700 mb-2">Sample Trade</h4>
              <pre className="text-xs text-gray-600 overflow-x-auto">
                {JSON.stringify(debugInfo.trades.sample, null, 2)}
              </pre>
            </div>
          )}

          {debugInfo.allTrades && debugInfo.allTrades.length > 0 && (
            <div className="bg-gray-50 p-3 rounded">
              <h4 className="font-medium text-gray-700 mb-2">All Trades (First 10)</h4>
              <div className="overflow-x-auto">
                <table className="min-w-full text-xs">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-1">ID</th>
                      <th className="text-left p-1">Symbol</th>
                      <th className="text-left p-1">Entry</th>
                      <th className="text-left p-1">Exit</th>
                      <th className="text-left p-1">P&L</th>
                      <th className="text-left p-1">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {debugInfo.allTrades.map((trade, i) => (
                      <tr key={i} className="border-b">
                        <td className="p-1">{trade.id}</td>
                        <td className="p-1">{trade.symbol}</td>
                        <td className="p-1">{trade.entry_price}</td>
                        <td className="p-1">{trade.exit_price}</td>
                        <td className={`p-1 ${trade.profit_loss > 0 ? 'text-green-600' : trade.profit_loss < 0 ? 'text-red-600' : 'text-gray-600'}`}>
                          ${trade.profit_loss?.toFixed(2) || '0.00'}
                        </td>
                        <td className="p-1">{trade.status}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          <div className="bg-yellow-50 border border-yellow-200 p-3 rounded">
            <h4 className="font-medium text-yellow-800 mb-2">Diagnosis</h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              {!debugInfo.user.authenticated && (
                <li>❌ User not authenticated - this will cause 0 values</li>
              )}
              {debugInfo.trades.total === 0 && (
                <li>❌ No trades loaded - import trades or check database</li>
              )}
              {debugInfo.trades.total > 0 && debugInfo.trades.withPnL === 0 && (
                <li>❌ Trades missing P&L values - check import process</li>
              )}
              {debugInfo.trades.withPnL > 0 && debugInfo.metrics.totalPnL === 0 && (
                <li>⚠️ Trades have P&L but total is 0 - check calculation logic</li>
              )}
              {debugInfo.trades.withPnL > 0 && debugInfo.metrics.totalPnL !== 0 && (
                <li>✅ Trades and P&L look good - dashboard should show values</li>
              )}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default TradeDebugger;
