import React, { useState } from 'react';
import { ChevronDownIcon, ChevronRightIcon } from '@heroicons/react/24/outline';

/**
 * Debug component to help diagnose Journal screen data issues
 * Shows the actual trade data structure and P&L values
 */
export default function JournalDataDebugger({ trades, dateRange }) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedTrade, setSelectedTrade] = useState(null);

  if (!trades || trades.length === 0) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
        <h3 className="text-sm font-medium text-yellow-800 mb-2">🐛 Journal Data Debug</h3>
        <p className="text-sm text-yellow-700">No trades data available</p>
      </div>
    );
  }

  const sampleTrade = trades[0];
  const tradesWithPnL = trades.filter(t => (t.profit_loss ?? t.pnl ?? 0) !== 0);
  const tradesWithoutPnL = trades.filter(t => (t.profit_loss ?? t.pnl ?? 0) === 0);

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
      <div 
        className="flex items-center cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        {isExpanded ? (
          <ChevronDownIcon className="h-4 w-4 text-blue-600 mr-2" />
        ) : (
          <ChevronRightIcon className="h-4 w-4 text-blue-600 mr-2" />
        )}
        <h3 className="text-sm font-medium text-blue-800">
          🐛 Journal Data Debug ({trades.length} trades)
        </h3>
      </div>

      {isExpanded && (
        <div className="mt-3 space-y-3">
          {/* Summary */}
          <div className="grid grid-cols-2 gap-4 text-xs">
            <div>
              <span className="font-medium text-blue-700">Total Trades:</span>
              <span className="ml-2 text-blue-600">{trades.length}</span>
            </div>
            <div>
              <span className="font-medium text-blue-700">With P&L:</span>
              <span className="ml-2 text-blue-600">{tradesWithPnL.length}</span>
            </div>
            <div>
              <span className="font-medium text-blue-700">Without P&L:</span>
              <span className="ml-2 text-blue-600">{tradesWithoutPnL.length}</span>
            </div>
            <div>
              <span className="font-medium text-blue-700">Date Range:</span>
              <span className="ml-2 text-blue-600">
                {dateRange?.start ? 'Set' : 'Not set'}
              </span>
            </div>
          </div>

          {/* Sample Trade Structure */}
          <div>
            <h4 className="text-xs font-medium text-blue-700 mb-2">Sample Trade Structure:</h4>
            <div className="bg-white rounded border p-2 text-xs font-mono">
              <div>ID: {sampleTrade.id}</div>
              <div>Instrument: {sampleTrade.instrument}</div>
              <div>Timestamp: {sampleTrade.timestamp}</div>
              <div className="text-green-600">
                profit_loss: {sampleTrade.profit_loss ?? 'undefined'}
              </div>
              <div className="text-green-600">
                pnl: {sampleTrade.pnl ?? 'undefined'}
              </div>
              <div>Entry Price: {sampleTrade.entry_price ?? sampleTrade.entryPrice}</div>
              <div>Exit Price: {sampleTrade.exit_price ?? sampleTrade.exitPrice}</div>
              <div>Size: {sampleTrade.size ?? sampleTrade.quantity}</div>
            </div>
          </div>

          {/* P&L Values */}
          <div>
            <h4 className="text-xs font-medium text-blue-700 mb-2">P&L Values (first 5 trades):</h4>
            <div className="space-y-1">
              {trades.slice(0, 5).map((trade, index) => {
                const pnl = trade.profit_loss ?? trade.pnl ?? 0;
                return (
                  <div key={index} className="flex items-center justify-between text-xs bg-white rounded px-2 py-1">
                    <span>{trade.instrument}</span>
                    <span className={pnl >= 0 ? 'text-green-600' : 'text-red-600'}>
                      ${pnl.toFixed(2)}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Chart Data Preview */}
          <div>
            <h4 className="text-xs font-medium text-blue-700 mb-2">Chart Data Preview:</h4>
            <div className="bg-white rounded border p-2 text-xs">
              <div>Labels: {trades.length} items</div>
              <div>Data points: {trades.map(t => t.profit_loss ?? t.pnl ?? 0).filter(p => p !== 0).length} non-zero</div>
              <div>Data range: ${Math.min(...trades.map(t => t.profit_loss ?? t.pnl ?? 0)).toFixed(2)} to ${Math.max(...trades.map(t => t.profit_loss ?? t.pnl ?? 0)).toFixed(2)}</div>
            </div>
          </div>

          {/* Problematic Trades */}
          {tradesWithoutPnL.length > 0 && (
            <div>
              <h4 className="text-xs font-medium text-red-700 mb-2">
                ⚠️ Trades without P&L ({tradesWithoutPnL.length}):
              </h4>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {tradesWithoutPnL.slice(0, 10).map((trade, index) => (
                  <div key={index} className="flex items-center justify-between text-xs bg-red-50 rounded px-2 py-1">
                    <span>{trade.instrument}</span>
                    <span className="text-red-600">No P&L data</span>
                  </div>
                ))}
                {tradesWithoutPnL.length > 10 && (
                  <div className="text-xs text-red-600 px-2">
                    ... and {tradesWithoutPnL.length - 10} more
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Quick Actions */}
          <div className="flex space-x-2 pt-2 border-t border-blue-200">
            <button
              onClick={() => console.log('All trades data:', trades)}
              className="text-xs bg-blue-100 hover:bg-blue-200 px-2 py-1 rounded"
            >
              Log All Trades
            </button>
            <button
              onClick={() => console.log('Sample trade:', sampleTrade)}
              className="text-xs bg-blue-100 hover:bg-blue-200 px-2 py-1 rounded"
            >
              Log Sample Trade
            </button>
            <button
              onClick={() => console.log('Chart data:', trades.map(t => t.profit_loss ?? t.pnl ?? 0))}
              className="text-xs bg-blue-100 hover:bg-blue-200 px-2 py-1 rounded"
            >
              Log Chart Data
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
