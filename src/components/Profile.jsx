import React, { useState } from 'react';
import { UserCircleIcon, CameraIcon } from '@heroicons/react/24/outline';
import Sidebar from './dashboard/Sidebar';
import { useNavigate } from 'react-router-dom';
import { useProfile } from '../contexts/ProfileContext';

export default function Profile() {
  const navigate = useNavigate();
  const [currentView, setCurrentView] = useState('profile');
  const { profile, isLoading, error, updateProfile } = useProfile();
  const [isEditing, setIsEditing] = useState(false);
  const [editedUser, setEditedUser] = useState(profile);
  const [isSaving, setIsSaving] = useState(false);

  const handleViewChange = (view) => {
    if (view === 'overview' || view === 'analysis' || view === 'journal') {
      navigate('/', { state: { view } });
    }
    setCurrentView(view);
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      await updateProfile(editedUser);
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving profile:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setEditedUser(profile);
    setIsEditing(false);
  };

  // Show loading state only on initial load
  if (isLoading && !profile.name) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-slate-600">Loading profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <Sidebar 
        currentView={currentView} 
        onViewChange={handleViewChange}
        refreshing={false}
      />
      
      <div className="pt-20">
        <div className="max-w-[1920px] mx-auto px-4 sm:px-6 lg:px-8">
          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}
          
          {/* Profile Header */}
          <div className="bg-white rounded-2xl shadow-sm p-4 sm:p-6 mb-6">
            <div className="flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6">
              <div className="relative">
                <div className="w-20 sm:w-24 h-20 sm:h-24 rounded-full bg-blue-500/10 flex items-center justify-center">
                  {profile.avatar ? (
                    <img src={profile.avatar} alt={profile.name} className="w-full h-full rounded-full object-cover" />
                  ) : (
                    <UserCircleIcon className="w-12 sm:w-16 h-12 sm:h-16 text-blue-500" />
                  )}
                </div>
                {isEditing && (
                  <button className="absolute bottom-0 right-0 p-1.5 bg-white rounded-full shadow-sm border border-slate-200 text-slate-600 hover:text-blue-500 transition-colors">
                    <CameraIcon className="w-4 sm:w-5 h-4 sm:h-5" />
                  </button>
                )}
              </div>
              <div className="flex-1">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                  <div>
                    <h1 className="text-xl sm:text-2xl font-semibold text-slate-900">{profile.name || 'Unnamed User'}</h1>
                    <p className="text-sm text-slate-500">{profile.email || 'No email set'}</p>
                  </div>
                  {!isEditing ? (
                    <button
                      onClick={() => {
                        setEditedUser(profile);
                        setIsEditing(true);
                      }}
                      className="w-full sm:w-auto px-4 py-2 text-sm font-medium text-slate-600 hover:text-slate-900 bg-slate-100 hover:bg-slate-200 rounded-lg transition-colors"
                    >
                      Edit Profile
                    </button>
                  ) : (
                    <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
                      <button
                        onClick={handleCancel}
                        className="w-full sm:w-auto px-4 py-2 text-sm font-medium text-slate-600 hover:text-slate-900 bg-slate-100 hover:bg-slate-200 rounded-lg transition-colors"
                        disabled={isSaving}
                      >
                        Cancel
                      </button>
                      <button
                        onClick={handleSave}
                        className="w-full sm:w-auto px-4 py-2 text-sm font-medium text-white bg-blue-500 hover:bg-blue-600 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled={isSaving}
                      >
                        {isSaving ? 'Saving...' : 'Save Changes'}
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Profile Content */}
          <div className="bg-white rounded-2xl shadow-sm">
            <div className="p-4 sm:p-6">
              <h2 className="text-lg font-semibold text-slate-900 mb-6">Profile Information</h2>
              <div className="space-y-6">
                {/* Basic Information */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">Full Name</label>
                    <input
                      type="text"
                      value={isEditing ? editedUser.name : profile.name}
                      onChange={(e) => setEditedUser({ ...editedUser, name: e.target.value })}
                      disabled={!isEditing || isSaving}
                      className="w-full px-3 py-2 bg-white border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow disabled:bg-slate-50 disabled:text-slate-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">Email</label>
                    <input
                      type="email"
                      value={isEditing ? editedUser.email : profile.email}
                      onChange={(e) => setEditedUser({ ...editedUser, email: e.target.value })}
                      disabled={!isEditing || isSaving}
                      className="w-full px-3 py-2 bg-white border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow disabled:bg-slate-50 disabled:text-slate-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">Time Zone</label>
                    <select
                      value={isEditing ? editedUser.timezone : profile.timezone}
                      onChange={(e) => setEditedUser({ ...editedUser, timezone: e.target.value })}
                      disabled={!isEditing || isSaving}
                      className="w-full px-3 py-2 bg-white border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow disabled:bg-slate-50 disabled:text-slate-500"
                    >
                      <option value="America/New_York">Eastern Time (ET)</option>
                      <option value="America/Chicago">Central Time (CT)</option>
                      <option value="America/Denver">Mountain Time (MT)</option>
                      <option value="America/Los_Angeles">Pacific Time (PT)</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">Trading Experience</label>
                    <select
                      value={isEditing ? editedUser.tradingExperience : profile.tradingExperience}
                      onChange={(e) => setEditedUser({ ...editedUser, tradingExperience: e.target.value })}
                      disabled={!isEditing || isSaving}
                      className="w-full px-3 py-2 bg-white border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow disabled:bg-slate-50 disabled:text-slate-500"
                    >
                      <option value="< 1 year">Less than 1 year</option>
                      <option value="1-3 years">1-3 years</option>
                      <option value="3-5 years">3-5 years</option>
                      <option value="5+ years">5+ years</option>
                    </select>
                  </div>
                </div>

                {/* Bio */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Bio</label>
                  <textarea
                    value={isEditing ? editedUser.bio : profile.bio}
                    onChange={(e) => setEditedUser({ ...editedUser, bio: e.target.value })}
                    disabled={!isEditing || isSaving}
                    rows={4}
                    className="w-full px-3 py-2 bg-white border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow disabled:bg-slate-50 disabled:text-slate-500"
                  />
                </div>

                {/* Preferred Markets */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">Preferred Markets</label>
                  <div className="flex flex-wrap gap-2">
                    {['Stocks', 'Options', 'Futures', 'Forex', 'Crypto'].map((market) => (
                      <label
                        key={market}
                        className={`inline-flex items-center px-3 py-1.5 rounded-lg text-sm font-medium cursor-pointer transition-colors
                          ${isEditing && !isSaving ? 'hover:bg-slate-100' : 'cursor-default'}
                          ${(isEditing ? editedUser.preferredMarkets : profile.preferredMarkets).includes(market)
                            ? 'bg-blue-500/10 text-blue-600 border border-blue-500/20'
                            : 'bg-slate-100 text-slate-600 border border-transparent'
                          }`}
                      >
                        <input
                          type="checkbox"
                          className="sr-only"
                          checked={(isEditing ? editedUser.preferredMarkets : profile.preferredMarkets).includes(market)}
                          onChange={() => {
                            if (!isEditing || isSaving) return;
                            const markets = editedUser.preferredMarkets.includes(market)
                              ? editedUser.preferredMarkets.filter(m => m !== market)
                              : [...editedUser.preferredMarkets, market];
                            setEditedUser({ ...editedUser, preferredMarkets: markets });
                          }}
                          disabled={!isEditing || isSaving}
                        />
                        {market}
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 