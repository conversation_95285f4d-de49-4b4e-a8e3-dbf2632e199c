import React from 'react';
import { formatDecimal } from '../../utils/formatters';

const GoalCard = ({ title, value, goal, color, icon: Icon }) => {
  const progress = Math.min(100, (value / goal) * 100);
  
  return (
    <div className="bg-white rounded-2xl shadow-sm p-6 border border-slate-200">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className={`p-2.5 rounded-lg bg-${color}-50`}>
            <Icon className={`w-6 h-6 text-${color}-600`} />
          </div>
          <h3 className="text-lg font-semibold text-slate-900">{title}</h3>
        </div>
        <div className="text-right">
          <p className="text-2xl font-bold text-slate-900">
            {formatDecimal(value)}/{formatDecimal(goal)}
          </p>
          <p className="text-sm text-slate-500">
            {formatDecimal(progress)}% Complete
          </p>
        </div>
      </div>
      <div className="relative h-3 bg-slate-100 rounded-full overflow-hidden">
        <div
          className={`absolute left-0 top-0 h-full bg-${color}-500 rounded-full transition-all duration-500 ease-out`}
          style={{ width: `${progress}%` }}
        />
      </div>
    </div>
  );
};

export default GoalCard; 