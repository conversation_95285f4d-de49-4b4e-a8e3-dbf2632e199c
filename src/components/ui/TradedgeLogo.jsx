import React from 'react';

/**
 * TRADEDGE Logo Component
 * Matches the Flutter design with custom painted icon and gradient text
 */
const TradedgeLogo = ({ 
  size = 48, 
  showText = true, 
  variant = 'default' // 'default', 'compact', 'icon-only'
}) => {
  const iconSize = size;
  const textSize = size * 0.14;

  // Custom SVG icon that matches the Flutter CustomPainter design
  const LogoIcon = ({ size }) => (
    <svg 
      width={size} 
      height={size} 
      viewBox="0 0 100 100" 
      className="drop-shadow-lg"
    >
      {/* Background circle */}
      <circle
        cx="50"
        cy="50"
        r="35"
        fill="url(#logoGradient)"
        stroke="url(#strokeGradient)"
        strokeWidth="2"
      />
      
      {/* Trading chart line */}
      <path
        d="M25 60 L35 45 L45 50 L55 35 L65 40 L75 25"
        stroke="#ffffff"
        strokeWidth="3"
        strokeLinecap="round"
        strokeLinejoin="round"
        fill="none"
        opacity="0.9"
      />
      
      {/* Profit arrow */}
      <path
        d="M70 30 L75 25 L75 35 Z"
        fill="#22C55E"
        opacity="0.9"
      />
      
      {/* Data points */}
      <circle cx="35" cy="45" r="2.5" fill="#ffffff" opacity="0.8" />
      <circle cx="45" cy="50" r="2.5" fill="#ffffff" opacity="0.8" />
      <circle cx="55" cy="35" r="2.5" fill="#ffffff" opacity="0.8" />
      <circle cx="65" cy="40" r="2.5" fill="#ffffff" opacity="0.8" />
      
      {/* Gradients */}
      <defs>
        <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#3B82F6" stopOpacity="0.95" />
          <stop offset="50%" stopColor="#60A5FA" stopOpacity="0.95" />
          <stop offset="100%" stopColor="#93C5FD" stopOpacity="0.95" />
        </linearGradient>
        <linearGradient id="strokeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#2563EB" stopOpacity="0.8" />
          <stop offset="100%" stopColor="#3B82F6" stopOpacity="0.8" />
        </linearGradient>
      </defs>
    </svg>
  );

  if (variant === 'icon-only') {
    return (
      <div className="flex items-center justify-center">
        <LogoIcon size={iconSize} />
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-3">
      {/* Logo Icon */}
      <LogoIcon size={iconSize} />
      
      {/* Logo Text */}
      {showText && (
        <div className="flex items-center">
          {variant === 'compact' ? (
            // Compact version - single gradient text
            <h1 
              className="font-black tracking-tight bg-gradient-to-r from-blue-500 via-blue-400 to-blue-300 text-transparent bg-clip-text"
              style={{ fontSize: `${textSize * 7}px` }}
            >
              TRADEDGE
            </h1>
          ) : (
            // Full version - split TRADE/EDGE styling
            <div className="flex items-baseline">
              <span 
                className="font-extrabold text-slate-700 dark:text-slate-300"
                style={{ 
                  fontSize: `${textSize * 7}px`,
                  letterSpacing: '0.8px'
                }}
              >
                TRADE
              </span>
              <span 
                className="font-normal bg-gradient-to-r from-blue-500 to-blue-400 text-transparent bg-clip-text"
                style={{ 
                  fontSize: `${textSize * 7}px`,
                  letterSpacing: '0.8px'
                }}
              >
                EDGE
              </span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

/**
 * Logo variants for different use cases
 */
export const TradedgeLogoVariants = {
  // Full logo with icon and split text
  Full: (props) => <TradedgeLogo {...props} variant="default" showText={true} />,
  
  // Compact logo with gradient text
  Compact: (props) => <TradedgeLogo {...props} variant="compact" showText={true} />,
  
  // Icon only for small spaces
  Icon: (props) => <TradedgeLogo {...props} variant="icon-only" showText={false} />,
  
  // Large hero version
  Hero: (props) => <TradedgeLogo {...props} size={64} variant="default" showText={true} />,
  
  // Small navbar version
  Navbar: (props) => <TradedgeLogo {...props} size={32} variant="compact" showText={true} />
};

export default TradedgeLogo;
