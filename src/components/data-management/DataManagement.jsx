import React, { useState } from 'react';
import {
  ArrowDownTrayIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/solid';
import { clearAllData, deleteTradesByDate, exportData } from '../../services/dataService';
import useSupabaseTradeStore from '../../stores/supabaseTradeStore';

export default function DataManagement({ onClose }) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [confirmDelete, setConfirmDelete] = useState(false);
  const [dateRange, setDateRange] = useState({ start: '', end: '' });
  const [feedback, setFeedback] = useState({ type: '', message: '' });

  // Get the trade store to refresh data after operations
  const { loadTrades } = useSupabaseTradeStore();

  const showFeedback = (type, message, duration = 3000) => {
    setFeedback({ type, message });
    setTimeout(() => setFeedback({ type: '', message: '' }), duration);
  };

  const handleClearAll = async () => {
    try {
      setIsDeleting(true);
      console.log('🗑️ Clearing all data...');

      await clearAllData();

      // Refresh the trade store to reflect the cleared data
      console.log('🔄 Refreshing trade store after clear...');
      await loadTrades();

      setConfirmDelete(false);
      showFeedback('success', 'All data has been cleared successfully');

      if (onClose) {
        setTimeout(onClose, 1500);
      }
    } catch (error) {
      console.error('❌ Error clearing data:', error);
      showFeedback('error', `Failed to clear data: ${error.message}`);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleDeleteRange = async () => {
    try {
      setIsDeleting(true);
      const startDate = new Date(dateRange.start);
      const endDate = new Date(dateRange.end);

      console.log('🗑️ Deleting trades in date range:', startDate, 'to', endDate);
      await deleteTradesByDate(startDate, endDate);

      // Refresh the trade store to reflect the deleted data
      console.log('🔄 Refreshing trade store after delete...');
      await loadTrades();

      showFeedback('success', 'Selected data range has been deleted');
      setDateRange({ start: '', end: '' });
    } catch (error) {
      console.error('❌ Error deleting trades:', error);
      showFeedback('error', `Failed to delete data range: ${error.message}`);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleExport = async (format) => {
    try {
      const blob = await exportData(format);
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `trades_export.${format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      showFeedback('success', `Data exported successfully as ${format.toUpperCase()}`);
    } catch (error) {
      console.error('Error exporting data:', error);
      showFeedback('error', 'Failed to export data. Please try again.');
    }
  };

  return (
    <div className="space-y-6">
      {/* Feedback Message */}
      {feedback.message && (
        <div className={`p-4 rounded-lg ${
          feedback.type === 'success' ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
        } flex items-center`}>
          {feedback.type === 'success' ? (
            <CheckCircleIcon className="w-5 h-5 mr-2 text-green-400" />
          ) : (
            <XCircleIcon className="w-5 h-5 mr-2 text-red-400" />
          )}
          {feedback.message}
        </div>
      )}

      {/* Export Section */}
      <div className="bg-white rounded-lg border border-slate-200 p-6">
        <h3 className="text-lg font-medium text-slate-900 mb-4">Export Data</h3>
        <div className="flex space-x-3">
          <button
            onClick={() => handleExport('csv')}
            className="flex items-center px-4 py-2 text-sm font-medium rounded-lg text-slate-600 hover:bg-slate-100 transition-colors"
          >
            <ArrowDownTrayIcon className="w-4 h-4 mr-2" />
            Export CSV
          </button>
          <button
            onClick={() => handleExport('json')}
            className="flex items-center px-4 py-2 text-sm font-medium rounded-lg text-slate-600 hover:bg-slate-100 transition-colors"
          >
            <ArrowDownTrayIcon className="w-4 h-4 mr-2" />
            Export JSON
          </button>
        </div>
      </div>

      {/* Delete Range Section */}
      <div className="bg-white rounded-lg border border-slate-200 p-6">
        <h3 className="text-lg font-medium text-slate-900 mb-4">Delete Date Range</h3>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">Start Date</label>
            <input
              type="date"
              value={dateRange.start}
              onChange={(e) => setDateRange(prev => ({
                ...prev,
                start: e.target.value
              }))}
              className="w-full px-3 py-2 bg-white border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">End Date</label>
            <input
              type="date"
              value={dateRange.end}
              onChange={(e) => setDateRange(prev => ({
                ...prev,
                end: e.target.value
              }))}
              className="w-full px-3 py-2 bg-white border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow"
            />
          </div>
        </div>
        <button
          onClick={handleDeleteRange}
          disabled={isDeleting || !dateRange.start || !dateRange.end}
          className="w-full flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg text-white bg-red-500 hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {isDeleting ? 'Deleting...' : 'Delete Range'}
        </button>
      </div>

      {/* Clear All Section */}
      <div className="bg-white rounded-lg border border-slate-200 p-6">
        <h3 className="text-lg font-medium text-slate-900 mb-4">Clear All Data</h3>
        {!confirmDelete ? (
          <button
            onClick={() => setConfirmDelete(true)}
            className="w-full flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg text-white bg-red-500 hover:bg-red-600 transition-colors"
          >
            <TrashIcon className="w-4 h-4 mr-2" />
            Clear All Data
          </button>
        ) : (
          <div className="space-y-3">
            <div className="flex items-center text-red-600">
              <ExclamationTriangleIcon className="w-5 h-5 mr-2" />
              <span className="text-sm font-medium">
                This action cannot be undone. All trades and goal history will be permanently deleted.
              </span>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={handleClearAll}
                disabled={isDeleting}
                className="flex-1 px-4 py-2 text-sm font-medium rounded-lg text-white bg-red-500 hover:bg-red-600 disabled:opacity-50 transition-colors"
              >
                {isDeleting ? 'Clearing...' : 'Confirm Clear'}
              </button>
              <button
                onClick={() => setConfirmDelete(false)}
                className="flex-1 px-4 py-2 text-sm font-medium rounded-lg text-slate-600 bg-slate-100 hover:bg-slate-200 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 