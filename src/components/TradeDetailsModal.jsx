import React from 'react';
import { Dialog } from '@headlessui/react';
import { format } from 'date-fns';
import {
  ArrowTrendingUpIcon,
  ClockIcon,
  CurrencyDollarIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

export function TradeDetailsModal({ trade, onClose }) {
  if (!trade) return null;

  const metrics = calculateTradeMetrics(trade);

  return (
    <Dialog open={true} onClose={onClose}>
      <React.Fragment>
        <div className="fixed inset-0 bg-black/30 z-40" aria-hidden="true" />
        
        <div className="fixed inset-0 flex items-center justify-center p-4 z-50">
          <Dialog.Panel className="bg-white rounded-xl max-w-2xl w-full">
            <div className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <Dialog.Title className="text-lg font-semibold">
                    Trade Details
                  </Dialog.Title>
                  <p className="text-sm text-slate-500">
                    {format(trade.timestamp, 'PPpp')}
                  </p>
                </div>
                <button
                  onClick={onClose}
                  className="text-slate-400 hover:text-slate-600"
                >
                  <XMarkIcon className="w-6 h-6" />
                </button>
              </div>

              {/* Trade Summary */}
              <div className="mt-6 grid grid-cols-3 gap-4">
                <MetricCard
                  title="P&L"
                  value={formatCurrency(trade.profit_loss)}
                  icon={<CurrencyDollarIcon className="w-5 h-5" />}
                  type={trade.profit_loss >= 0 ? 'positive' : 'negative'}
                />
                <MetricCard
                  title="R Multiple"
                  value={metrics.rMultiple.toFixed(2)}
                  icon={<ArrowTrendingUpIcon className="w-5 h-5" />}
                  type={metrics.rMultiple >= 0 ? 'positive' : 'negative'}
                />
                <MetricCard
                  title="Duration"
                  value={metrics.duration}
                  icon={<ClockIcon className="w-5 h-5" />}
                />
              </div>

              {/* Trade Details */}
              <div className="mt-6 grid grid-cols-2 gap-4">
                <DetailItem label="Instrument" value={trade.instrument} />
                <DetailItem label="Position" value={trade.position_type} />
                <DetailItem 
                  label="Entry Price" 
                  value={formatCurrency(trade.entry_price)} 
                />
                <DetailItem 
                  label="Exit Price" 
                  value={formatCurrency(trade.exit_price)} 
                />
                <DetailItem label="Size" value={trade.size} />
                <DetailItem 
                  label="Commission" 
                  value={formatCurrency(trade.commission)} 
                />
              </div>

              {/* Trade Analysis */}
              <div className="mt-6 pt-6 border-t border-slate-200">
                <h4 className="text-sm font-medium text-slate-700 mb-3">
                  Trade Analysis
                </h4>
                <div className="space-y-2">
                  {metrics.analysis.map((item, index) => (
                    <div
                      key={index}
                      className={`p-2 rounded-lg ${
                        item.type === 'positive' 
                          ? 'bg-green-50 text-green-700'
                          : item.type === 'negative'
                          ? 'bg-red-50 text-red-700'
                          : 'bg-slate-50 text-slate-700'
                      }`}
                    >
                      {item.message}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </Dialog.Panel>
        </div>
      </React.Fragment>
    </Dialog>
  );
}

function DetailItem({ label, value, className = '' }) {
  return (
    <div>
      <div className="text-sm font-medium text-slate-500">{label}</div>
      <div className={`mt-1 text-sm ${className}`}>{value}</div>
    </div>
  );
}

function MetricCard({ title, value, icon, type }) {
  return (
    <div className="flex items-center justify-between p-3 rounded-lg">
      <div>
        <h5 className="text-sm font-medium text-slate-700">{title}</h5>
        <p className="text-sm text-slate-500">{value}</p>
      </div>
      <div className={`p-2 rounded-full ${
        type === 'positive' 
          ? 'bg-green-50 text-green-700'
          : type === 'negative'
          ? 'bg-red-50 text-red-700'
          : 'bg-slate-50 text-slate-700'
      }`}>
        {icon}
      </div>
    </div>
  );
}

function formatCurrency(value) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(value);
} 