import React, { useState, useEffect, useMemo, useContext, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import useSupabaseTradeStore from '../stores/supabaseTradeStore';
import { useAuth } from '../contexts/AuthContext';
import { TradeAnalytics } from '../utils/analytics';
import ChartSection from './dashboard/ChartSection';
import TradesTable from './dashboard/TradesTable';
import { DataManagementModal } from './DataManagementModal';
import { ImportContext } from '../contexts/ImportContext';
import GoalGaugeCard from './GoalGaugeCard';
import Settings from './Settings';

import { 
  ArrowPathIcon,
  Cog6ToothIcon,
  ArrowUturnLeftIcon,
  ViewColumnsIcon,
  ArrowUturnRightIcon,
  ChartBarSquareIcon,
  DocumentChartBarIcon,
  TableCellsIcon
} from '@heroicons/react/24/outline';
import AnalyticsOverview from './dashboard/AnalyticsOverview';
import RiskHeatmap from './dashboard/RiskHeatmap';
import PerformanceDistribution from './dashboard/PerformanceDistribution';
import AreasForImprovement from './dashboard/AreasForImprovement';
import TradePatternAnalysis from './dashboard/TradePatternAnalysis';
import { TradeDetailsModal } from './TradeDetailsModal';
import PerformanceVisualizations from './dashboard/PerformanceVisualizations';
import ConfirmDialog from './common/ConfirmDialog';
import Sidebar from './dashboard/Sidebar';
import Footer from './dashboard/Footer';
import ProgressOverview from './dashboard/ProgressOverview';
import TopBar from './common/TopBar';
import DateRangeSelector from './dashboard/DateRangeSelector';
import { startOfDay, endOfDay, subDays, isWithinInterval, parseISO, isValid } from 'date-fns';
import TradeJournal from './dashboard/TradeJournal';
import TradeAnalysis from './dashboard/TradeAnalysis';
import AnalysisEmptyState from './dashboard/AnalysisEmptyState';
import JournalEmptyState from './dashboard/JournalEmptyState';
import OverviewEmptyState from './dashboard/OverviewEmptyState';
import Profile from './Profile';

const STORAGE_KEYS = {
  DATE_RANGE: 'tradereviewer_date_range',
  DATE_PRESET: 'tradereviewer_date_preset'
};

const loadSavedDatePreferences = () => {
  try {
    const savedRange = localStorage.getItem(STORAGE_KEYS.DATE_RANGE);
    const savedPreset = localStorage.getItem(STORAGE_KEYS.DATE_PRESET);
    
    if (savedRange) {
      const { start, end } = JSON.parse(savedRange);
      return {
        dateRange: { 
          start: parseISO(start), 
          end: parseISO(end) 
        },
        preset: savedPreset || '30D'
      };
    }
  } catch (error) {
    console.error('Error loading saved date preferences:', error);
  }
  
  // Default to last 30 days if no saved preferences
  const today = new Date();
  return {
    dateRange: {
      start: startOfDay(subDays(today, 29)),
      end: endOfDay(today)
    },
    preset: '30D'
  };
};

export default function Dashboard() {
  const location = useLocation();
  const { user } = useAuth();
  const {
    trades,
    loadTrades,
    isLoading,
    getTradesByDateRange,
    initialize,
    cleanup
  } = useSupabaseTradeStore();
  const [refreshing, setRefreshing] = useState(false);
  const [showDataManagement, setShowDataManagement] = useState(false);
  const [selectedTrade, setSelectedTrade] = useState(null);
  const [showResetConfirm, setShowResetConfirm] = useState(false);
  const [currentView, setCurrentView] = useState(
    location.state?.view || 'overview'
  );
  const [dateRange, setDateRange] = useState(() => loadSavedDatePreferences().dateRange);
  const [selectedPreset, setSelectedPreset] = useState(() => loadSavedDatePreferences().preset);
  const [showAddTrade, setShowAddTrade] = useState(false);
  const [notification, setNotification] = useState(null);

  // Use ImportContext instead of local state
  const { showImportModal, registerImportHandler } = useContext(ImportContext);

  // Get the earliest and latest trade dates
  const tradeDateRange = useMemo(() => {
    if (!trades || trades.length === 0) return null;
    const dates = trades.map(trade => new Date(trade.timestamp));
    return {
      start: startOfDay(new Date(Math.min(...dates))),
      end: endOfDay(new Date(Math.max(...dates)))
    };
  }, [trades]);

  // Filter trades based on date range
  const filteredTrades = useMemo(() => {
    if (!trades) return [];
    if (selectedPreset === 'All') {
      return trades;
    }
    if (!dateRange?.start || !dateRange?.end) return [];
    
    return trades.filter(trade => {
      try {
        const tradeDate = parseISO(trade.timestamp);
        if (!isValid(tradeDate)) return false;
        
        return isWithinInterval(tradeDate, {
          start: startOfDay(dateRange.start),
          end: endOfDay(dateRange.end)
        });
      } catch (error) {
        console.error('Error filtering trade:', error);
        return false;
      }
    });
  }, [trades, dateRange, selectedPreset]);

  // Handle date range change
  const handleDateRangeChange = (start, end) => {
    const newRange = { start, end };
    
    // Save to localStorage
    try {
      localStorage.setItem(STORAGE_KEYS.DATE_RANGE, JSON.stringify({
        start: newRange.start.toISOString(),
        end: newRange.end.toISOString()
      }));
    } catch (error) {
      console.error('Error saving date preferences:', error);
    }
    
    setDateRange(newRange);
  };

  // Handle preset change
  const handlePresetChange = (preset) => {
    try {
      localStorage.setItem(STORAGE_KEYS.DATE_PRESET, preset);
    } catch (error) {
      console.error('Error saving date preset:', error);
    }
    setSelectedPreset(preset);
  };

  const handleUpdateTrade = async (tradeId, updates) => {
    try {
      const { updateTrade } = useSupabaseTradeStore.getState();
      await updateTrade(tradeId, updates);
      console.log('✅ Trade updated in Supabase');
    } catch (error) {
      console.error('❌ Error updating trade:', error);
    }
  };

  const handleDeleteTrade = async (tradeId) => {
    try {
      const { deleteTrade } = useSupabaseTradeStore.getState();
      await deleteTrade(tradeId);
      console.log('✅ Trade deleted from Supabase');
    } catch (error) {
      console.error('❌ Error deleting trade:', error);
    }
  };

  const metrics = useMemo(() => {
    try {
      // ALWAYS use all trades when 'All' is selected, otherwise use filtered
      const tradesForAnalysis = selectedPreset === 'All' ? trades : filteredTrades;

      console.log('📊 Dashboard Metrics - selectedPreset:', selectedPreset);
      console.log('📊 Dashboard Metrics - using trades:', tradesForAnalysis === trades ? 'ALL TRADES' : 'FILTERED TRADES');
      console.log('📊 Dashboard Metrics - trades count:', {
        allTrades: trades?.length || 0,
        filteredTrades: filteredTrades?.length || 0,
        forAnalysis: tradesForAnalysis?.length || 0
      });

      console.log('📊 Calculating metrics for:', {
        selectedPreset,
        totalTrades: trades?.length || 0,
        filteredTrades: filteredTrades?.length || 0,
        tradesForAnalysis: tradesForAnalysis?.length || 0
      });

      // Log detailed sample trade data
      if (tradesForAnalysis && tradesForAnalysis.length > 0) {
        const sampleTrade = tradesForAnalysis[0];
        console.log('📊 Sample trade structure:', {
          id: sampleTrade.id,
          instrument: sampleTrade.instrument,
          profit_loss: sampleTrade.profit_loss,
          pnl: sampleTrade.pnl,
          entry_price: sampleTrade.entry_price,
          exit_price: sampleTrade.exit_price,
          timestamp: sampleTrade.timestamp,
          allFields: Object.keys(sampleTrade)
        });
      }

      // Return default values if no trades
      if (!tradesForAnalysis || tradesForAnalysis.length === 0) {
        console.log('📊 No trades for analysis, returning default metrics');
        return {
          winRate: 0,
          profitFactor: 0,
          expectancy: 0,
          sharpeRatio: 0,
          maxDrawdown: 0,
          avgWinSize: 0,
          avgLossSize: 0,
          winningStreak: 0,
          netPnL: 0,
          avgTrade: 0,
          totalTrades: 0,
          winningTrades: 0,
          losingTrades: 0
        };
      }

      // Debug trade data
      console.log('🔍 Debug - Total trades for analysis:', tradesForAnalysis.length);
      console.log('🔍 Debug - User authenticated:', !!user?.id);
      console.log('🔍 Debug - Selected preset:', selectedPreset);

      if (tradesForAnalysis.length > 0) {
        console.log('🔍 Debug - Sample trade:', {
          id: tradesForAnalysis[0].id?.substring(0, 8),
          symbol: tradesForAnalysis[0].instrument || tradesForAnalysis[0].symbol,
          profit_loss: tradesForAnalysis[0].profit_loss,
          pnl: tradesForAnalysis[0].pnl,
          entry_price: tradesForAnalysis[0].entry_price,
          exit_price: tradesForAnalysis[0].exit_price,
          timestamp: tradesForAnalysis[0].timestamp
        });
      }

      // Validate that trades have profit_loss field
      const tradesWithPnL = tradesForAnalysis.filter(t => t.profit_loss !== undefined && t.profit_loss !== null);
      console.log('📊 Trades with P&L:', tradesWithPnL.length, 'out of', tradesForAnalysis.length);

      if (tradesWithPnL.length === 0) {
        console.warn('📊 No trades have profit_loss field!');
        console.log('🔍 Debug - Reasons for 0 trades:');
        console.log('   - User not authenticated:', !user?.id);
        console.log('   - No trades loaded:', tradesForAnalysis.length === 0);
        console.log('   - Trades missing profit_loss field:', tradesForAnalysis.length > 0 && tradesWithPnL.length === 0);

        return {
          winRate: 0,
          profitFactor: 0,
          expectancy: 0,
          sharpeRatio: 0,
          maxDrawdown: 0,
          avgWinSize: 0,
          avgLossSize: 0,
          winningStreak: 0,
          netPnL: 0,
          avgTrade: 0,
          totalTrades: tradesForAnalysis.length,
          winningTrades: 0,
          losingTrades: 0
        };
      }

      const analytics = new TradeAnalytics(tradesWithPnL);
      const calculatedMetrics = {
        ...analytics.summary,
        netPnL: analytics.summary.netPnL || 0,
        avgTrade: analytics.summary.avgTrade || 0
      };

      console.log('📊 Final calculated metrics:', calculatedMetrics);
      return calculatedMetrics;
    } catch (error) {
      console.error('📊 Error calculating metrics:', error);
      return {
        winRate: 0,
        profitFactor: 0,
        expectancy: 0,
        sharpeRatio: 0,
        maxDrawdown: 0,
        avgWinSize: 0,
        avgLossSize: 0,
        winningStreak: 0,
        netPnL: 0,
        avgTrade: 0,
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0
      };
    }
  }, [trades, filteredTrades, selectedPreset]);

  // Initialize Supabase trade store when user is available
  useEffect(() => {
    if (user?.id) {
      console.log('🔄 Initializing Supabase trade store for user:', user.email);
      console.log('👤 User ID:', user.id);
      initialize(user.id);
    } else {
      console.warn('⚠️ No user available for trade store initialization');
      console.log('🔍 Current user state:', user);
    }

    // Cleanup on unmount
    return () => {
      cleanup();
    };
  }, [user?.id, initialize, cleanup]);

  // Register import handler with ImportContext
  useEffect(() => {
    registerImportHandler(handleImportCSVComplete);
  }, [registerImportHandler]);

  // Load trades when date range changes (but not on initial mount to prevent loops)
  const initialLoadDone = useRef(false);

  useEffect(() => {
    // Skip initial load to prevent infinite loops
    if (!initialLoadDone.current) {
      initialLoadDone.current = true;
      return;
    }

    const loadTradesWithDateRange = async () => {
      if (dateRange.start && dateRange.end && selectedPreset !== 'All') {
        try {
          // Ensure start date is not after end date
          const start = new Date(dateRange.start);
          const end = new Date(dateRange.end);

          if (start > end) {
            console.error('Invalid date range:', { start, end });
            return;
          }

          console.log('📅 Loading trades for date range:', { start, end });
          setRefreshing(true);
          await getTradesByDateRange(start, end);
        } finally {
          setRefreshing(false);
        }
      }
      // Don't load trades when 'All' is selected - use existing trades
    };

    loadTradesWithDateRange();
  }, [dateRange.start, dateRange.end, selectedPreset]); // Remove function dependencies

  // Update view when location state changes
  useEffect(() => {
    if (location.state?.view) {
      setCurrentView(location.state.view);
    }
  }, [location.state]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadTrades(true);
    } finally {
      setRefreshing(false);
    }
  };

  const handleReset = () => {
    setShowResetConfirm(true);
  };

  const handleConfirmReset = () => {
    setShowResetConfirm(false);
  };

  // This function will be called by the ImportContext
  const handleImportCSVComplete = async (response) => {
    if (!response || !response.success) {
      console.error('Import failed:', response?.message);
      return;
    }

    console.log('CSV Import completed successfully:', response);

    try {
      setRefreshing(true);

      // First load the trades and wait for completion
      console.log('🔄 Loading trades after CSV import...');
      const freshTrades = await loadTrades();
      console.log('✅ Loaded fresh trades:', freshTrades?.length);

      // Log sample trade data for debugging
      if (freshTrades && freshTrades.length > 0) {
        console.log('📊 Sample imported trade:', {
          id: freshTrades[0].id,
          instrument: freshTrades[0].instrument,
          profit_loss: freshTrades[0].profit_loss,
          pnl: freshTrades[0].pnl,
          entry_price: freshTrades[0].entry_price,
          exit_price: freshTrades[0].exit_price,
          timestamp: freshTrades[0].timestamp
        });

        // Quick metrics test
        const totalPnL = freshTrades.reduce((sum, t) => sum + (t.profit_loss || 0), 0);
        const winningTrades = freshTrades.filter(t => (t.profit_loss || 0) > 0).length;
        const winRate = (winningTrades / freshTrades.length) * 100;

        console.log('📊 Quick metrics test:', {
          totalTrades: freshTrades.length,
          totalPnL,
          winningTrades,
          winRate: winRate.toFixed(2) + '%'
        });

        // Test TradeAnalytics directly
        try {
          const testAnalytics = new TradeAnalytics(freshTrades);
          console.log('📊 TradeAnalytics test result:', {
            winRate: testAnalytics.summary.winRate,
            profitFactor: testAnalytics.summary.profitFactor,
            netPnL: testAnalytics.summary.netPnL,
            totalTrades: testAnalytics.summary.totalTrades
          });
        } catch (analyticsError) {
          console.error('📊 TradeAnalytics error:', analyticsError);
        }
      }

      // Show success notification
      setNotification({
        type: 'success',
        message: `Successfully imported ${response.tradesCount} trades with total P&L of ${response.totalPnL?.toFixed(2) || 'N/A'}`
      });

      // Auto-hide notification after 5 seconds
      setTimeout(() => {
        setNotification(null);
      }, 5000);

      // Force 'All' preset to show all trades immediately
      console.log('📅 Setting preset to "All" to show all imported trades');
      setSelectedPreset('All');

      // Force component re-render to ensure metrics update
      setTimeout(() => {
        console.log('🔄 Checking trades state after import...');
        console.log('📊 Dashboard trades state:', trades?.length || 0);
        console.log('📊 Fresh trades loaded:', freshTrades?.length || 0);

        // Force a re-render by toggling refreshing state
        setRefreshing(true);
        setTimeout(() => setRefreshing(false), 100);
      }, 200);

      // Force a state update to ensure metrics recalculate
      setTimeout(() => {
        console.log('🔄 Forcing metrics recalculation...');
        console.log('📊 Current trades in state:', trades?.length || 0);
        console.log('📊 Fresh trades loaded:', freshTrades?.length || 0);

        // If state trades don't match fresh trades, there's a timing issue
        if ((trades?.length || 0) !== (freshTrades?.length || 0)) {
          console.warn('⚠️ State trades count mismatch! Forcing state update...');
          // Force a re-render by updating a dummy state
          setRefreshing(false);
        }
      }, 100);

      // Also update date range to include all trades after a short delay
      setTimeout(async () => {
        // Reload trades and get the returned data
        const reloadedTrades = await loadTrades();
        console.log('📅 Reloaded trades from store:', reloadedTrades?.length);

        // Use the returned trades directly
        if (reloadedTrades && reloadedTrades.length > 0) {
          const dates = reloadedTrades.map(trade => new Date(trade.timestamp));
          const latestDate = new Date(Math.max(...dates));
          const earliestDate = new Date(Math.min(...dates));

          console.log('📅 Date range calculated from reloaded trades:', { earliestDate, latestDate });

          // Set date range to include all trades
          const start = startOfDay(earliestDate);
          const end = endOfDay(latestDate);

          if (start <= end) {
            console.log('📅 Setting comprehensive date range:', { start, end });
            setDateRange({ start, end });
          }
        }
      }, 500); // Longer delay to ensure trades are fully loaded
    } catch (error) {
      console.error('Error importing CSV:', error);
      setNotification({
        type: 'error',
        message: 'Import completed but failed to refresh data. Please refresh the page.'
      });
    } finally {
      setRefreshing(false);
    }

    // ImportContext will handle modal closing automatically
  };

  // Use ImportContext instead of local handlers
  const handleImportClick = () => {
    showImportModal();
  };

  const handleAddTrade = () => {
    setShowAddTrade(true);
  };

  const handleAdjustDateRange = () => {
    // Scroll to date range selector
    const dateRangeElement = document.querySelector('.date-range-selector');
    if (dateRangeElement) {
      dateRangeElement.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Add check for empty states
  const hasTradesForDateRange = useMemo(() => {
    return filteredTrades && filteredTrades.length > 0;
  }, [filteredTrades]);

  // Add a check for whether user has any trades at all
  const hasAnyTrades = useMemo(() => {
    return trades && trades.length > 0;
  }, [trades]);

  // Render empty state based on current view
  const renderEmptyState = () => {
    const emptyStateProps = {
      onImport: handleImportClick,
      onAddTrade: () => setShowAddTrade(true),
      hasData: hasAnyTrades,
      onAdjustDateRange: handleAdjustDateRange
    };

    switch (currentView) {
      case 'overview':
        return <OverviewEmptyState {...emptyStateProps} />;
      case 'analysis':
        return <AnalysisEmptyState {...emptyStateProps} />;
      case 'journal':
        return <JournalEmptyState {...emptyStateProps} />;
      default:
        return null;
    }
  };

  // Removed problematic useEffect that was causing infinite loop
  // Date range is now managed manually in import handler

  if (currentView === 'settings') {
    return <Settings />;
  }

  if (currentView === 'profile') {
    return <Profile />;
  }

  if (isLoading && !trades?.length) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <Sidebar 
        currentView={currentView} 
        onViewChange={setCurrentView}
        refreshing={refreshing}
      />
      
      <div className="pt-16 sm:pt-20">
        <TopBar 
          title={
            currentView === 'overview' ? 'Progress Overview' :
            currentView === 'analysis' ? 'Trade Analysis' :
            currentView.charAt(0).toUpperCase() + currentView.slice(1)
          }
          showRefresh={true}
          showReset={true}
          showImport={true}
          showWidgets={true}
          onRefresh={handleRefresh}
          onReset={handleReset}
          onImport={handleImportClick}
          refreshing={refreshing}
        />

        <div className="min-h-screen dashboard-container pb-16 sm:pb-24 relative">
          <div className="max-w-[1920px] mx-auto px-4 sm:px-6 lg:px-8">
            <div className="py-4 sm:py-6">
              {/* Import Modal is now handled by ImportContext in App.jsx */}

              {/* Date Range Section */}
              <div className="mb-4 sm:mb-6 mt-8 sm:mt-12">
                <DateRangeSelector
                  startDate={dateRange.start}
                  endDate={dateRange.end}
                  onRangeChange={handleDateRangeChange}
                  selectedPreset={selectedPreset}
                  onPresetChange={handlePresetChange}
                />
              </div>

              {/* Content based on currentView */}
              {!hasTradesForDateRange ? (
                <div className="relative">
                  <div className="absolute inset-0 backdrop-blur-sm bg-white/50 z-10"></div>
                  <div className="relative z-20">
                    {renderEmptyState()}
                  </div>
                  <div className="opacity-25">
                    {currentView === 'overview' && (
                      <>
                        <GoalGaugeCard {...metrics} />
                        <ProgressOverview trades={selectedPreset === 'All' ? trades : filteredTrades} dateRange={dateRange} />
                      </>
                    )}
                    {currentView === 'analysis' && (
                      <TradeAnalysis trades={filteredTrades} />
                    )}
                    {currentView === 'journal' && (
                      <TradeJournal 
                        trades={filteredTrades}
                        dateRange={dateRange}
                        onUpdateTrade={handleUpdateTrade}
                        onDeleteTrade={handleDeleteTrade}
                      />
                    )}
                  </div>
                </div>
              ) : (
                <>
                  {currentView === 'overview' && (
                    <div className="space-y-4 sm:space-y-6">
                      <GoalGaugeCard {...metrics} />
                      <ProgressOverview trades={selectedPreset === 'All' ? trades : filteredTrades} dateRange={dateRange} />
                    </div>
                  )}
                  {currentView === 'analysis' && (
                    <TradeAnalysis trades={filteredTrades} />
                  )}
                  {currentView === 'journal' && (
                    <TradeJournal 
                      trades={filteredTrades}
                      dateRange={dateRange}
                      onUpdateTrade={handleUpdateTrade}
                      onDeleteTrade={handleDeleteTrade}
                    />
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Notification */}
      {notification && (
        <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-md ${
          notification.type === 'success'
            ? 'bg-green-100 border border-green-400 text-green-700'
            : 'bg-red-100 border border-red-400 text-red-700'
        }`}>
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">{notification.message}</span>
            <button
              onClick={() => setNotification(null)}
              className="ml-4 text-gray-400 hover:text-gray-600"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Modals */}
      <DataManagementModal
        isOpen={showDataManagement}
        onClose={() => setShowDataManagement(false)}
      />
      {selectedTrade && (
        <TradeDetailsModal
          trade={selectedTrade}
          onClose={() => setSelectedTrade(null)}
        />
      )}
      <ConfirmDialog
        isOpen={showResetConfirm}
        onClose={() => setShowResetConfirm(false)}
        onConfirm={handleConfirmReset}
        title="Reset Dashboard Layout"
        message="This will reset all widgets to their default positions and show all hidden widgets. This action cannot be undone."
      />
    </div>
  );
} 