import React from 'react';
import { Link } from 'react-router-dom';
import { ChevronRightIcon } from '@heroicons/react/24/solid';

export default function Breadcrumbs({ items = [] }) {
  if (!items || items.length === 0) {
    items = [{ label: 'Dashboard', href: '/' }];
  }

  return (
    <nav className="flex items-center gap-2 text-sm">
      {items.map((item, index) => {
        const isLast = index === items.length - 1;
        return (
          <React.Fragment key={item.href}>
            <Link
              to={item.href}
              className={`text-slate-500 hover:text-slate-700 transition-colors ${
                isLast ? 'pointer-events-none text-slate-700' : ''
              }`}
            >
              {item.label}
            </Link>
            {!isLast && (
              <ChevronRightIcon className="w-4 h-4 text-slate-400" />
            )}
          </React.Fragment>
        );
      })}
    </nav>
  );
} 