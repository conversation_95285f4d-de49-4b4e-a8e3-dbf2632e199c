import { useMemo } from 'react';
import { Bar } from 'react-chartjs-2';
import CardTemplate from './CardTemplate';

export default function BehavioralPatterns({ trades }) {
  const patternData = useMemo(() => {
    if (!trades?.length) return null;

    // Analyze trade patterns
    const patterns = trades.reduce((acc, trade) => {
      // Time of day analysis
      const hour = new Date(trade.timestamp).getHours();
      const timeKey = `${hour}:00`;
      if (!acc.timeOfDay[timeKey]) {
        acc.timeOfDay[timeKey] = { trades: 0, pnl: 0, winners: 0 };
      }
      acc.timeOfDay[timeKey].trades++;
      acc.timeOfDay[timeKey].pnl += trade.netPnL;
      if (trade.netPnL > 0) acc.timeOfDay[timeKey].winners++;

      // Trade size analysis
      const sizeKey = Math.floor(trade.size / 5) * 5;
      if (!acc.sizes[sizeKey]) {
        acc.sizes[sizeKey] = { trades: 0, pnl: 0, winners: 0 };
      }
      acc.sizes[sizeKey].trades++;
      acc.sizes[sizeKey].pnl += trade.netPnL;
      if (trade.netPnL > 0) acc.sizes[sizeKey].winners++;

      return acc;
    }, {
      timeOfDay: {},
      sizes: {}
    });

    // Process time of day data
    const timeData = Object.entries(patterns.timeOfDay)
      .map(([time, stats]) => ({
        time,
        trades: stats.trades,
        pnl: stats.pnl,
        winRate: (stats.winners / stats.trades) * 100
      }))
      .sort((a, b) => {
        const timeA = parseInt(a.time.split(':')[0]);
        const timeB = parseInt(b.time.split(':')[0]);
        return timeA - timeB;
      });

    // Process size data
    const sizeData = Object.entries(patterns.sizes)
      .map(([size, stats]) => ({
        size: parseInt(size),
        trades: stats.trades,
        pnl: stats.pnl,
        winRate: (stats.winners / stats.trades) * 100
      }))
      .sort((a, b) => a.size - b.size);

    return { timeData, sizeData };
  }, [trades]);

  if (!patternData) {
    return (
      <CardTemplate title="Trading Patterns">
        <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
          <p className="text-gray-500">No pattern data available</p>
        </div>
      </CardTemplate>
    );
  }

  const timeChartData = {
    labels: patternData.timeData.map(d => d.time),
    datasets: [
      {
        label: 'Win Rate',
        data: patternData.timeData.map(d => d.winRate),
        backgroundColor: 'rgba(59, 130, 246, 0.5)',
        borderColor: 'rgb(59, 130, 246)',
        borderWidth: 1,
        yAxisID: 'y'
      },
      {
        label: 'Number of Trades',
        data: patternData.timeData.map(d => d.trades),
        backgroundColor: 'rgba(16, 185, 129, 0.5)',
        borderColor: 'rgb(16, 185, 129)',
        borderWidth: 1,
        yAxisID: 'y1'
      }
    ]
  };

  const options = {
    responsive: true,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    plugins: {
      legend: {
        position: 'top',
      }
    },
    scales: {
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        ticks: {
          callback: value => `${value}%`
        }
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        grid: {
          drawOnChartArea: false,
        }
      },
    }
  };

  return (
    <CardTemplate title="Trading Patterns">
      <div className="h-64">
        <Bar data={timeChartData} options={options} />
      </div>
    </CardTemplate>
  );
} 