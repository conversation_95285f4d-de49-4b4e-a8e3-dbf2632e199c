export default function RiskMetric({ title, value, details }) {
  return (
    <div className="bg-slate-50 rounded-lg p-4">
      <div className="mb-4">
        <h4 className="text-sm text-slate-500">{title}</h4>
        <p className="text-2xl font-semibold">{value}</p>
      </div>
      <div className="space-y-2">
        {details.map(({ label, value }) => (
          <div key={label} className="flex justify-between text-sm">
            <span className="text-slate-500">{label}</span>
            <span className="font-medium">{value}</span>
          </div>
        ))}
      </div>
    </div>
  );
} 