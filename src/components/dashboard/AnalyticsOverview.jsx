import { useMemo } from 'react';
import {
  ChartBarIcon,
  ArrowTrendingUpIcon,
  BanknotesIcon,
  ScaleIcon,
  ExclamationTriangleIcon,
  ChartPieIcon,
  LightBulbIcon
} from '@heroicons/react/24/outline';
import { TradeAnalytics } from '../../utils/analytics';
import RecommendationCard from './RecommendationCard';
import TradeDistribution from './TradeDistribution';
import TimeAnalysis from './TimeAnalysis';

export default function AnalyticsOverview({ trades }) {
  // Calculate metrics using TradeAnalytics
  const analytics = useMemo(() => {
    try {
      return new TradeAnalytics(trades || []);
    } catch (error) {
      console.error('Error calculating metrics:', error);
      return null;
    }
  }, [trades]);

  // Early return if no metrics
  if (!analytics || !trades?.length) {
    return (
      <div className="absolute inset-x-0 top-[220px] bottom-0 z-40 flex items-start justify-center bg-slate-50/95 backdrop-blur-sm">
        <div className="w-full max-w-2xl mx-auto pt-12">
          <div className="text-center">
            <div className="mb-8">
              <div className="w-24 h-24 mx-auto mb-6 relative">
                <div className="absolute inset-0 bg-blue-100 rounded-full animate-pulse"></div>
                <div className="absolute inset-2 bg-blue-50 rounded-full flex items-center justify-center">
                  <ChartPieIcon className="w-12 h-12 text-blue-500" />
                </div>
              </div>
              <h2 className="text-2xl font-bold text-slate-900 mb-3">Track Your Trading Performance</h2>
              <p className="text-slate-500 mb-8 max-w-lg mx-auto">
                Get a comprehensive overview of your trading performance with key metrics and insights.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-6 mb-12">
              <div className="bg-white rounded-xl p-6 text-left shadow-sm border border-slate-200">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                  <ChartBarIcon className="w-6 h-6 text-blue-600" />
                </div>
                <h3 className="text-sm font-semibold text-slate-900 mb-2">Performance Stats</h3>
                <p className="text-sm text-slate-500">
                  Monitor win rates, profit factors, and key performance indicators.
                </p>
              </div>

              <div className="bg-white rounded-xl p-6 text-left shadow-sm border border-slate-200">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                  <ArrowTrendingUpIcon className="w-6 h-6 text-blue-600" />
                </div>
                <h3 className="text-sm font-semibold text-slate-900 mb-2">Growth Tracking</h3>
                <p className="text-sm text-slate-500">
                  Track your progress and equity curve over time.
                </p>
              </div>

              <div className="bg-white rounded-xl p-6 text-left shadow-sm border border-slate-200">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                  <LightBulbIcon className="w-6 h-6 text-blue-600" />
                </div>
                <h3 className="text-sm font-semibold text-slate-900 mb-2">Performance Score</h3>
                <p className="text-sm text-slate-500">
                  Get an overall rating of your trading performance.
                </p>
              </div>
            </div>

            <div className="max-w-lg mx-auto">
              <h3 className="text-sm font-semibold text-slate-900 mb-4">Key Metrics Available</h3>
              <div className="space-y-3 text-left">
                <div className="flex items-center gap-3 p-3 bg-white rounded-lg border border-slate-200 shadow-sm">
                  <div className="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center text-blue-600 font-medium">
                    <span className="text-blue-600 text-lg">✓</span>
                  </div>
                  <p className="text-sm text-slate-600">Win rate and profit factor analysis</p>
                </div>
                <div className="flex items-center gap-3 p-3 bg-white rounded-lg border border-slate-200 shadow-sm">
                  <div className="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center text-blue-600 font-medium">
                    <span className="text-blue-600 text-lg">✓</span>
                  </div>
                  <p className="text-sm text-slate-600">Risk-adjusted return metrics</p>
                </div>
                <div className="flex items-center gap-3 p-3 bg-white rounded-lg border border-slate-200 shadow-sm">
                  <div className="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center text-blue-600 font-medium">
                    <span className="text-blue-600 text-lg">✓</span>
                  </div>
                  <p className="text-sm text-slate-600">Performance scoring and benchmarking</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Get performance score directly from analytics
  const performanceScore = analytics.getPerformanceScore();
  const recommendations = analytics.getRecommendations();

  return (
    <div className="space-y-6">
      {/* Performance Score Section */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-blue-500/10 to-blue-500/5 rounded-lg">
              <ChartPieIcon className="w-6 h-6 text-blue-500" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-slate-900 tracking-wide">PERFORMANCE SCORE</h3>
              <p className="text-sm text-slate-500">Overall trading performance rating</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div className="text-2xl font-bold text-green-600">{performanceScore}</div>
            <div className="text-sm text-slate-500">/100</div>
          </div>
        </div>

        <div className="grid grid-cols-2 lg:grid-cols-5 gap-4">
          <div className="p-4 rounded-lg bg-gradient-to-br from-slate-50 to-white border border-slate-200/75 shadow-sm">
            <div className="flex items-center space-x-2 mb-2">
              <div className="p-2 rounded-lg bg-gradient-to-br from-slate-100 to-white">
                <ChartBarIcon className="w-5 h-5 text-green-500" />
              </div>
              <div className="text-sm font-medium text-slate-700">Win Rate</div>
            </div>
            <div className="text-lg font-semibold text-slate-900">
              {isFinite(analytics.summary.winRate) && analytics.summary.winRate !== null ? 
                `${(analytics.summary.winRate).toFixed(1)}%` : 
                '0.0%'}
            </div>
            <div className="text-xs text-slate-500 mt-1">Target: 55%</div>
          </div>

          <div className="p-4 rounded-lg bg-gradient-to-br from-slate-50 to-white border border-slate-200/75 shadow-sm">
            <div className="flex items-center space-x-2 mb-2">
              <div className="p-2 rounded-lg bg-gradient-to-br from-slate-100 to-white">
                <ArrowTrendingUpIcon className="w-5 h-5 text-indigo-500" />
              </div>
              <div className="text-sm font-medium text-slate-700">Profit Factor</div>
            </div>
            <div className="text-lg font-semibold text-slate-900">
              {isFinite(analytics.summary.profitFactor) && analytics.summary.profitFactor !== null ? 
                (analytics.summary.profitFactor).toFixed(2) : 
                '0.00'}
            </div>
            <div className="text-xs text-slate-500 mt-1">Target: 1.5</div>
          </div>

          <div className="p-4 rounded-lg bg-gradient-to-br from-slate-50 to-white border border-slate-200/75 shadow-sm">
            <div className="flex items-center space-x-2 mb-2">
              <div className="p-2 rounded-lg bg-gradient-to-br from-slate-100 to-white">
                <BanknotesIcon className="w-5 h-5 text-emerald-500" />
              </div>
              <div className="text-sm font-medium text-slate-700">Expectancy</div>
            </div>
            <div className="text-lg font-semibold text-slate-900">
              {isFinite(analytics.summary.expectancy) && analytics.summary.expectancy !== null ? 
                `$${Math.abs(analytics.summary.expectancy).toFixed(2)}` : 
                '$0.00'}
            </div>
            <div className="text-xs text-slate-500 mt-1">Target: $75</div>
          </div>

          <div className="p-4 rounded-lg bg-gradient-to-br from-slate-50 to-white border border-slate-200/75 shadow-sm">
            <div className="flex items-center space-x-2 mb-2">
              <div className="p-2 rounded-lg bg-gradient-to-br from-slate-100 to-white">
                <ScaleIcon className="w-5 h-5 text-blue-500" />
              </div>
              <div className="text-sm font-medium text-slate-700">Sharpe Ratio</div>
            </div>
            <div className="text-lg font-semibold text-slate-900">
              {isFinite(analytics.volatility.sharpeRatio) && analytics.volatility.sharpeRatio !== null ? 
                (analytics.volatility.sharpeRatio).toFixed(2) : 
                '0.00'}
            </div>
            <div className="text-xs text-slate-500 mt-1">Target: 1.5</div>
          </div>

          <div className="p-4 rounded-lg bg-gradient-to-br from-slate-50 to-white border border-slate-200/75 shadow-sm">
            <div className="flex items-center space-x-2 mb-2">
              <div className="p-2 rounded-lg bg-gradient-to-br from-slate-100 to-white">
                <ExclamationTriangleIcon className="w-5 h-5 text-amber-500" />
              </div>
              <div className="text-sm font-medium text-slate-700">Max Drawdown</div>
            </div>
            <div className="text-lg font-semibold text-slate-900">
              {isFinite(analytics.summary.maxDrawdown) && analytics.summary.maxDrawdown !== null ? 
                `${(analytics.summary.maxDrawdown).toFixed(2)}%` : 
                '0.00%'}
            </div>
            <div className="text-xs text-slate-500 mt-1">Target: 10%</div>
          </div>
        </div>
      </div>

      {/* Recommendations */}
      {recommendations.length > 0 && (
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-semibold mb-4">Recommendations</h3>
          <div className="space-y-3">
            {recommendations.map((rec, index) => (
              <RecommendationCard key={index} {...rec} />
            ))}
          </div>
        </div>
      )}

      {/* Trade Distribution and Time Analysis */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <TradeDistribution trades={trades} analytics={analytics} />
          <TimeAnalysis trades={trades} analytics={analytics} />
        </div>
      </div>
    </div>
  );
} 