import { useMemo } from 'react';
import { Bar } from 'react-chartjs-2';

export default function PerformanceDistribution({ trades }) {
  const distributionData = useMemo(() => calculateDistribution(trades), [trades]);
  const stats = useMemo(() => calculateStats(trades), [trades]);

  return (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <div className="h-64">
        <Bar data={distributionData} options={getChartOptions()} />
      </div>
    </div>
  );
}

function calculateDistribution(trades) {
  const bins = {
    'Excellent (>2R)': 0,
    'Good (1-2R)': 0,
    'Break Even (±0.5R)': 0,
    'Loss (-1-0R)': 0,
    'Large Loss (<-1R)': 0
  };

  trades.forEach(trade => {
    const rValue = calculateRMultiple(trade);
    
    if (rValue > 2) bins['Excellent (>2R)']++;
    else if (rValue > 1) bins['Good (1-2R)']++;
    else if (rValue >= -0.5 && rValue <= 0.5) bins['Break Even (±0.5R)']++;
    else if (rValue >= -1) bins['Loss (-1-0R)']++;
    else bins['Large Loss (<-1R)']++;
  });

  const colors = {
    'Excellent (>2R)': 'rgba(34, 197, 94, 0.5)',
    'Good (1-2R)': 'rgba(34, 197, 94, 0.3)',
    'Break Even (±0.5R)': 'rgba(59, 130, 246, 0.5)',
    'Loss (-1-0R)': 'rgba(239, 68, 68, 0.3)',
    'Large Loss (<-1R)': 'rgba(239, 68, 68, 0.5)'
  };

  return {
    labels: Object.keys(bins),
    datasets: [{
      data: Object.values(bins),
      backgroundColor: Object.keys(bins).map(key => colors[key]),
      borderColor: Object.keys(bins).map(key => colors[key].replace('0.5', '1')),
      borderWidth: 1
    }]
  };
}

function calculateRMultiple(trade) {
  const risk = Math.abs(trade.entry_price - trade.stop_loss);
  if (!risk) return 0;
  return trade.profit_loss / (risk * trade.size);
}

function getChartOptions() {
  return {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = ((context.raw / total) * 100).toFixed(1);
            return `${context.raw} trades (${percentage}%)`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Number of Trades'
        }
      }
    }
  };
}

function calculateStats(trades) {
  const rMultiples = trades.map(calculateRMultiple);
  const winningTrades = rMultiples.filter(r => r > 0);
  const losingTrades = rMultiples.filter(r => r < 0);

  return {
    averageR: rMultiples.reduce((sum, r) => sum + r, 0) / rMultiples.length,
    largestWin: Math.max(...rMultiples),
    largestLoss: Math.min(...rMultiples),
    winRate: (winningTrades.length / rMultiples.length) * 100,
    profitFactor: Math.abs(
      winningTrades.reduce((sum, r) => sum + r, 0) /
      losingTrades.reduce((sum, r) => sum + r, 0)
    )
  };
} 