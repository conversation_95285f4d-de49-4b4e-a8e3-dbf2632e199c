import { useMemo, useState } from 'react';
import { 
  CheckCircleIcon, 
  XCircleIcon,
  ChevronDownIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon
} from '@heroicons/react/24/outline';
import { formatCurrency } from '../../utils/formatters';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';
import { Bar } from 'react-chartjs-2';
import CardTemplate from './CardTemplate';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

export default function TradePatternAnalysis({ trades }) {
  const [activeTab, setActiveTab] = useState('winning');
  const [showAll, setShowAll] = useState(false);
  
  const patterns = useMemo(() => ({
    winning: analyzePatterns(trades, true),
    losing: analyzePatterns(trades, false)
  }), [trades]);

  const chartData = useMemo(() => {
    if (!trades?.length) return null;

    const winningPatterns = patterns.winning;
    const losingPatterns = patterns.losing;

    return {
      labels: ['1-3', '4-6', '7-9', '10+'],
      datasets: [
        {
          label: 'Winning Trades',
          data: winningPatterns.map(p => p.trades),
          backgroundColor: 'rgba(34, 197, 94, 0.5)',
          borderColor: 'rgb(34, 197, 94)',
          borderWidth: 1
        },
        {
          label: 'Losing Trades',
          data: losingPatterns.map(p => p.trades),
          backgroundColor: 'rgba(239, 68, 68, 0.5)',
          borderColor: 'rgb(239, 68, 68)',
          borderWidth: 1
        }
      ]
    };
  }, [trades, patterns]);

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: false
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        }
      },
      x: {
        grid: {
          display: false
        }
      }
    }
  };

  if (!trades?.length) {
    return (
      <CardTemplate title="Trade Patterns">
        <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
          <p className="text-gray-500">No pattern data available</p>
        </div>
      </CardTemplate>
    );
  }

  const activePatterns = patterns[activeTab];
  const displayPatterns = showAll ? activePatterns : activePatterns.slice(0, 5);

  return (
    <CardTemplate title="Trade Patterns">
      <div className="h-64">
        {chartData && <Bar data={chartData} options={options} />}
      </div>
    </CardTemplate>
  );
}

function TabButton({ active, onClick, icon, label, count, color }) {
  return (
    <button
      onClick={onClick}
      className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
        active 
          ? `bg-${color}-50 text-${color}-600` 
          : 'text-slate-600 hover:bg-slate-50'
      }`}
    >
      {icon}
      <span className="ml-2">{label}</span>
      <span className={`ml-2 px-2 py-0.5 rounded-full text-xs ${
        active 
          ? `bg-${color}-100 text-${color}-700`
          : 'bg-slate-100 text-slate-600'
      }`}>
        {count}
      </span>
    </button>
  );
}

function PatternCard({ pattern, type }) {
  const isWinning = type === 'winning';
  const Icon = isWinning ? CheckCircleIcon : XCircleIcon;
  const colorClass = isWinning ? 'text-green-500' : 'text-red-500';

  return (
    <div className="p-4 hover:bg-slate-50 transition-colors">
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0 mt-1">
          <Icon className={`w-5 h-5 ${colorClass}`} />
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-slate-900">
              {isWinning ? 'Winning' : 'Losing'} Pattern
            </span>
            <div className="flex items-center space-x-2">
              <span className="text-xs text-slate-500">
                {pattern.trades} trades
              </span>
              <span className="text-xs text-slate-500">
                {pattern.duration} avg duration
              </span>
            </div>
          </div>
          
          <div className="grid grid-cols-3 gap-4">
            <MetricCard 
              label="Total P&L"
              value={formatCurrency(pattern.totalPnL)}
              valueClass={colorClass}
            />
            <MetricCard 
              label="Avg Trade"
              value={formatCurrency(pattern.avgTrade)}
              valueClass="text-slate-900"
            />
            <MetricCard 
              label="Win Rate"
              value={`${(pattern.winRate * 100).toFixed(1)}%`}
              valueClass="text-slate-900"
            />
          </div>
          
          {pattern.notes && (
            <div className="mt-2 text-xs text-slate-600">
              {pattern.notes}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

function MetricCard({ label, value, valueClass }) {
  return (
    <div>
      <div className="text-xs text-slate-500">{label}</div>
      <div className={`text-sm font-medium ${valueClass}`}>
        {value}
      </div>
    </div>
  );
}

function analyzePatterns(trades, isWinning) {
  if (!trades?.length) return [];

  const patterns = [];
  let currentPattern = null;

  trades.forEach((trade) => {
    const isWinningTrade = trade.profit_loss > 0;
    
    if (isWinningTrade === isWinning) {
      if (!currentPattern) {
        currentPattern = {
          trades: 1,
          totalPnL: trade.profit_loss,
          avgTrade: trade.profit_loss,
          winRate: isWinning ? 1 : 0,
          duration: trade.duration || '00:00:00',
          startDate: trade.timestamp,
          notes: ''
        };
      } else {
        currentPattern.trades++;
        currentPattern.totalPnL += trade.profit_loss;
        currentPattern.avgTrade = currentPattern.totalPnL / currentPattern.trades;
        // Add more pattern analysis logic here
      }
    } else if (currentPattern) {
      if (currentPattern.trades >= 3) {
        patterns.push({ ...currentPattern });
      }
      currentPattern = null;
    }
  });

  if (currentPattern?.trades >= 3) {
    patterns.push(currentPattern);
  }

  return patterns.sort((a, b) => Math.abs(b.totalPnL) - Math.abs(a.totalPnL));
} 