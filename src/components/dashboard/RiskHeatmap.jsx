import React, { useMemo } from 'react';
import { format } from 'date-fns';
import CardTemplate from './CardTemplate';

function generateHeatmapColors(value, max) {
  const ratio = value / max;
  const hue = (1 - ratio) * 120; // 120 is green, 0 is red
  return `hsl(${hue}, 70%, 50%)`;
}

export default function RiskHeatmap({ trades }) {
  const heatmapData = useMemo(() => {
    if (!trades?.length) return null;

    const data = trades.reduce((acc, trade) => {
      const date = format(new Date(trade.timestamp), 'yyyy-MM-dd');
      const hour = new Date(trade.timestamp).getHours();

      if (!acc[date]) {
        acc[date] = Array(24).fill(0);
      }

      acc[date][hour] += Math.abs(trade.netPnL);
      return acc;
    }, {});

    // Find max value for color scaling
    let maxValue = 0;
    Object.values(data).forEach(hours => {
      const dayMax = Math.max(...hours);
      maxValue = Math.max(maxValue, dayMax);
    });

    return { data, maxValue };
  }, [trades]);

  if (!heatmapData) {
    return (
      <CardTemplate title="Risk Analysis">
        <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
          <p className="text-gray-500">No heatmap data available</p>
        </div>
      </CardTemplate>
    );
  }

  const hours = Array.from({ length: 24 }, (_, i) => i);
  const dates = Object.keys(heatmapData.data).sort();

  return (
    <CardTemplate title="Risk Analysis">
      <div className="h-64 overflow-x-auto">
        <div className="min-w-max">
          <div className="grid grid-cols-[auto_repeat(24,1fr)] gap-1">
            {/* Header */}
            <div className="h-8" /> {/* Empty corner */}
            {hours.map(hour => (
              <div
                key={hour}
                className="text-xs text-center text-slate-500 font-medium"
              >
                {hour}h
              </div>
            ))}

            {/* Rows */}
            {dates.map(date => (
              <React.Fragment key={date}>
                <div className="text-xs text-slate-500 font-medium pr-2 flex items-center">
                  {format(new Date(date), 'MMM dd')}
                </div>
                {hours.map(hour => {
                  const value = heatmapData.data[date][hour];
                  return (
                    <div
                      key={`${date}-${hour}`}
                      className="aspect-square rounded-sm"
                      style={{
                        backgroundColor: value
                          ? generateHeatmapColors(value, heatmapData.maxValue)
                          : '#f1f5f9'
                      }}
                    />
                  );
                })}
              </React.Fragment>
            ))}
          </div>
        </div>
      </div>
    </CardTemplate>
  );
} 