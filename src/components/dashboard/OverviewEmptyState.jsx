import React from 'react';
import { PresentationChartLineIcon, ArrowPathIcon, PencilIcon, CalendarIcon } from '@heroicons/react/24/outline';

export default function OverviewEmptyState({ onImport, onAddTrade, hasData, onAdjustDateRange }) {
  return (
    <div className="text-center">
      <div className="mb-6">
        <div className="w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-4 relative">
          <div className="absolute inset-0 bg-blue-100 rounded-full animate-pulse"></div>
          <div className="absolute inset-2 bg-blue-50 rounded-full flex items-center justify-center">
            {hasData ? (
              <CalendarIcon className="w-8 h-8 sm:w-10 sm:h-10 text-blue-500" />
            ) : (
              <PresentationChartLineIcon className="w-8 h-8 sm:w-10 sm:h-10 text-blue-500" />
            )}
          </div>
        </div>
        {hasData ? (
          <>
            <h2 className="text-lg sm:text-xl font-bold text-slate-900 mb-2">No Trades Today</h2>
            <p className="text-sm text-slate-500 mb-6 max-w-lg mx-auto">
              You have trades recorded, but none for the selected date range.
              Try adjusting the date filter to view your trading history.
            </p>
            <button
              onClick={onAdjustDateRange}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-lg hover:shadow-xl transform transition-all duration-200 hover:-translate-y-0.5"
            >
              <CalendarIcon className="w-4 h-4 mr-2" />
              Adjust Date Range
            </button>
          </>
        ) : (
          <>
            <h2 className="text-lg sm:text-xl font-bold text-slate-900 mb-2">Welcome to Your Trading Dashboard</h2>
            <p className="text-sm text-slate-500 mb-6 max-w-lg mx-auto">
              Start tracking your trades to get a comprehensive overview of your trading performance.
            </p>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mb-6 sm:mb-8 max-w-2xl mx-auto">
              <button
                onClick={onImport}
                className="flex items-center justify-center gap-2 px-4 py-3 bg-white rounded-xl shadow-sm border border-slate-200 hover:border-blue-200 hover:bg-blue-50 transition-all group"
              >
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                  <ArrowPathIcon className="w-5 h-5 text-blue-600" />
                </div>
                <div className="text-left">
                  <h3 className="font-semibold text-slate-900 text-sm">Import Trades</h3>
                  <p className="text-xs text-slate-500">Upload your trading history</p>
                </div>
              </button>

              <button
                onClick={onAddTrade}
                className="flex items-center justify-center gap-2 px-4 py-3 bg-white rounded-xl shadow-sm border border-slate-200 hover:border-blue-200 hover:bg-blue-50 transition-all group"
              >
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                  <PencilIcon className="w-5 h-5 text-blue-600" />
                </div>
                <div className="text-left">
                  <h3 className="font-semibold text-slate-900 text-sm">Add Trade</h3>
                  <p className="text-xs text-slate-500">Record a new trade manually</p>
                </div>
              </button>
            </div>

            <div className="bg-white rounded-xl p-4 shadow-sm border border-slate-200 max-w-2xl mx-auto">
              <h3 className="text-sm font-semibold text-slate-900 mb-3">Dashboard Features</h3>
              <div className="grid grid-cols-2 gap-3">
                <div className="p-3 bg-slate-50 rounded-lg">
                  <div className="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center mb-2">
                    <svg className="w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <h4 className="font-medium text-slate-900 text-sm mb-1">Performance Overview</h4>
                  <p className="text-xs text-slate-500">Track key metrics</p>
                </div>

                <div className="p-3 bg-slate-50 rounded-lg">
                  <div className="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center mb-2">
                    <svg className="w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h4 className="font-medium text-slate-900 text-sm mb-1">Trade Statistics</h4>
                  <p className="text-xs text-slate-500">View your stats</p>
                </div>

                <div className="p-3 bg-slate-50 rounded-lg">
                  <div className="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center mb-2">
                    <svg className="w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h4 className="font-medium text-slate-900 text-sm mb-1">Goal Tracking</h4>
                  <p className="text-xs text-slate-500">Monitor progress</p>
                </div>

                <div className="p-3 bg-slate-50 rounded-lg">
                  <div className="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center mb-2">
                    <svg className="w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  </div>
                  <h4 className="font-medium text-slate-900 text-sm mb-1">Recent Activity</h4>
                  <p className="text-xs text-slate-500">Latest trades</p>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
} 