import React, { useState } from 'react';
import { InformationCircleIcon } from '@heroicons/react/24/outline';

export default function MetricCard({ 
  title, 
  icon, 
  metrics = [], 
  className = '',
  showDividers = true,
  variant = 'default' // 'default' | 'compact' | 'minimal'
}) {
  const variants = {
    default: 'p-6 bg-white/60 backdrop-blur-sm rounded-xl border border-white/60 shadow-lg shadow-slate-200/20',
    compact: 'p-4 bg-white/50 backdrop-blur-sm rounded-xl border border-white/60',
    minimal: 'p-5 rounded-xl bg-white/60 backdrop-blur-sm border border-white/60'
  };

  return (
    <div className={`${variants[variant]} ${className} transition-all duration-300 hover:transform hover:-translate-y-1 hover:shadow-xl`}>
      {/* Header with title and optional icon */}
      {title && (
        <div className={`${showDividers ? 'mb-5 pb-3 border-b border-slate-200/50' : 'mb-4'}`}>
          <h4 className="text-base font-bold text-slate-700 flex items-center gap-2 tracking-wide uppercase">
            {icon && React.cloneElement(icon, { className: 'w-5 h-5' })}
            {title}
          </h4>
        </div>
      )}

      {/* Metrics */}
      <div className="space-y-4">
        {metrics.map((metric, index) => (
          <div key={index} className="flex items-center justify-between py-2 group transition-all duration-200 hover:translate-x-1">
            <div className="flex items-center gap-2">
              <span className="text-[0.95rem] font-semibold text-slate-600 group-hover:text-slate-800 transition-colors duration-200">
                {metric.label}
              </span>
              {metric.tooltip && (
                <InfoTooltip content={metric.tooltip} />
              )}
            </div>
            <div className="flex items-center gap-3">
              <span className={`text-base font-bold tracking-tight ${metric.valueColor || 'text-slate-700'}`}>
                {metric.value}
              </span>
              {metric.suffix && (
                <span className="text-sm font-medium text-slate-500">{metric.suffix}</span>
              )}
              {metric.indicator}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Interactive tooltip component for metric information
function InfoTooltip({ content }) {
  const [show, setShow] = useState(false);

  return (
    <div className="relative inline-block">
      <div
        onMouseEnter={() => setShow(true)}
        onMouseLeave={() => setShow(false)}
        className="text-slate-400 hover:text-slate-600 cursor-help transition-colors duration-200"
      >
        <InformationCircleIcon className="w-4 h-4" />
      </div>
      {show && (
        <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 w-48 p-3 bg-slate-800 text-white text-xs rounded-lg shadow-lg z-50">
          <div className="text-center">{content}</div>
          {/* Tooltip arrow */}
          <div className="absolute top-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-slate-800" />
        </div>
      )}
    </div>
  );
}