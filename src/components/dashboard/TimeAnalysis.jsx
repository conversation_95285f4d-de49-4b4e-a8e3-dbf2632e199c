import { Scatter } from 'react-chartjs-2';
import { formatCurrency } from '../../utils/formatters';
import {
  Chart as ChartJS,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

export default function TimeAnalysis({ trades }) {
  // Handle empty trades array
  if (!trades?.length) {
    return null;
  }

  function getTimeAnalysisData() {
    const validTrades = trades.filter(trade => 
      trade?.timestamp && 
      typeof trade.profit_loss === 'number' &&
      !isNaN(new Date(trade.timestamp).getTime())
    );

    const maxAbsPnL = Math.max(...validTrades.map(t => Math.abs(t.profit_loss)));
    const scaleFactor = maxAbsPnL > 0 ? 20 / maxAbsPnL : 1;

    return {
      datasets: [{
        data: validTrades.map((trade) => ({
          x: new Date(trade.timestamp).getHours(),
          y: Number(trade.profit_loss) || 0,
          r: Math.max(Math.abs(trade.profit_loss) * scaleFactor, 5)
        })),
        backgroundColor: validTrades.map((trade) => 
          (trade.profit_loss >= 0 ? '#22c55e80' : '#ef444480')
        )
      }]
    };
  }

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        callbacks: {
          label: (context) => {
            const hour = context.raw.x;
            const pnl = formatCurrency(context.raw.y);
            return `Time: ${hour}:00 - P&L: ${pnl}`;
          }
        }
      }
    },
    scales: {
      x: {
        min: 0,
        max: 24,
        title: {
          display: true,
          text: 'Hour of Day'
        }
      },
      y: {
        grid: {
          color: '#f3f4f6'
        },
        title: {
          display: true,
          text: 'P&L'
        }
      }
    }
  };

  return (
    <div className="h-[200px] sm:h-[250px] lg:h-[300px] w-full">
      <h3 className="text-sm font-medium text-slate-700 mb-2 sm:mb-4">Time Analysis</h3>
      <div className="h-[calc(100%-2rem)] sm:h-[calc(100%-2.5rem)]">
        <Scatter data={getTimeAnalysisData()} options={{
          ...options,
          maintainAspectRatio: false,
          responsive: true,
          plugins: {
            ...options.plugins,
            legend: {
              ...options.plugins.legend,
              labels: {
                ...options.plugins.legend?.labels,
                boxWidth: window.innerWidth < 640 ? 8 : 12,
                padding: window.innerWidth < 640 ? 10 : 15,
                font: {
                  size: window.innerWidth < 640 ? 10 : 12
                }
              }
            }
          },
          scales: {
            ...options.scales,
            x: {
              ...options.scales.x,
              ticks: {
                ...options.scales.x?.ticks,
                font: {
                  size: window.innerWidth < 640 ? 10 : 12
                }
              }
            },
            y: {
              ...options.scales.y,
              ticks: {
                ...options.scales.y?.ticks,
                font: {
                  size: window.innerWidth < 640 ? 10 : 12
                }
              }
            }
          }
        }} />
      </div>
    </div>
  );
} 