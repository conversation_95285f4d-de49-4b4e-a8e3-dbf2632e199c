import { useMemo } from 'react';
import {
  LightBulbIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

export default function TradePatternRecognition({ trades }) {
  const patterns = useMemo(() => analyzeTradePatterns(trades), [trades]);

  return (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold">Pattern Recognition</h3>
        <LightBulbIcon className="w-6 h-6 text-yellow-500" />
      </div>

      <div className="space-y-6">
        {/* Time-Based Patterns */}
        <PatternSection
          title="Time-Based Patterns"
          icon={<ClockIcon className="w-5 h-5" />}
          patterns={patterns.timePatterns}
        />

        {/* Momentum Patterns */}
        <PatternSection
          title="Momentum Patterns"
          icon={<TrendingUpIcon className="w-5 h-5" />}
          patterns={patterns.momentumPatterns}
        />

        {/* Reversal Patterns */}
        <PatternSection
          title="Reversal Patterns"
          icon={<TrendingDownIcon className="w-5 h-5" />}
          patterns={patterns.reversalPatterns}
        />
      </div>
    </div>
  );
}

function PatternSection({ title, icon, patterns }) {
  if (!patterns?.length) return null;

  return (
    <div>
      <div className="flex items-center space-x-2 mb-4">
        <div className="p-2 rounded-lg bg-blue-50 text-blue-600">
          {icon}
        </div>
        <h4 className="font-medium">{title}</h4>
      </div>

      <div className="space-y-3">
        {patterns.map((pattern, index) => (
          <PatternCard key={index} pattern={pattern} />
        ))}
      </div>
    </div>
  );
}

function PatternCard({ pattern }) {
  const { type, confidence, description, metrics } = pattern;

  return (
    <div className="p-4 bg-slate-50 rounded-lg">
      <div className="flex items-center justify-between mb-2">
        <h5 className="font-medium">{type}</h5>
        <div className={`px-2 py-1 rounded-full text-sm ${
          confidence >= 80 ? 'bg-green-100 text-green-700' :
          confidence >= 60 ? 'bg-yellow-100 text-yellow-700' :
          'bg-red-100 text-red-700'
        }`}>
          {confidence}% confidence
        </div>
      </div>

      <p className="text-sm text-slate-600 mb-3">{description}</p>

      {metrics && (
        <div className="grid grid-cols-2 gap-4">
          {Object.entries(metrics).map(([key, value]) => (
            <div key={key}>
              <p className="text-sm text-slate-500">{key}</p>
              <p className="text-sm font-medium">{value}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

function analyzeTradePatterns(trades) {
  if (!trades?.length) {
    return {
      timePatterns: [],
      momentumPatterns: [],
      reversalPatterns: []
    };
  }

  return {
    timePatterns: analyzeTimePatterns(trades),
    momentumPatterns: analyzeMomentumPatterns(trades),
    reversalPatterns: analyzeReversalPatterns(trades)
  };
}

function analyzeTimePatterns(trades) {
  const patterns = [];
  const hourlyStats = new Array(24).fill(0).map(() => ({
    trades: [],
    wins: 0,
    losses: 0
  }));

  // Group trades by hour
  trades.forEach(trade => {
    const hour = new Date(trade.timestamp).getHours();
    hourlyStats[hour].trades.push(trade);
    if (trade.profit_loss > 0) {
      hourlyStats[hour].wins++;
    } else {
      hourlyStats[hour].losses++;
    }
  });

  // Identify strong time patterns
  hourlyStats.forEach((stats, hour) => {
    if (stats.trades.length < 5) return;

    const winRate = (stats.wins / stats.trades.length) * 100;
    if (winRate >= 65) {
      patterns.push({
        type: 'Strong Hour',
        confidence: Math.min(winRate, 95),
        description: `High win rate during ${formatHour(hour)}`,
        metrics: {
          'Win Rate': `${winRate.toFixed(1)}%`,
          'Sample Size': stats.trades.length
        }
      });
    }
  });

  return patterns;
}

function analyzeMomentumPatterns(trades) {
  const patterns = [];
  let consecutiveWins = 0;
  let consecutiveLosses = 0;

  trades.forEach((trade, index) => {
    if (trade.profit_loss > 0) {
      consecutiveWins++;
      consecutiveLosses = 0;
    } else {
      consecutiveLosses++;
      consecutiveWins = 0;
    }

    // Check for momentum patterns
    if (consecutiveWins >= 3) {
      const avgSize = trades
        .slice(index - consecutiveWins + 1, index + 1)
        .reduce((sum, t) => sum + t.size, 0) / consecutiveWins;

      patterns.push({
        type: 'Winning Momentum',
        confidence: Math.min(70 + consecutiveWins * 5, 95),
        description: `${consecutiveWins} consecutive winning trades`,
        metrics: {
          'Streak': consecutiveWins,
          'Avg Size': avgSize.toFixed(2)
        }
      });
    }
  });

  return patterns;
}

function analyzeReversalPatterns(trades) {
  const patterns = [];
  const lookback = 5;

  trades.forEach((trade, index) => {
    if (index < lookback) return;

    const previousTrades = trades.slice(index - lookback, index);
    const currentTrade = trade;

    // Check for trend reversal
    const prevTrend = previousTrades.reduce((sum, t) => sum + t.profit_loss, 0);
    const currentPnL = currentTrade.profit_loss;

    if (Math.sign(prevTrend) !== Math.sign(currentPnL) && Math.abs(currentPnL) > Math.abs(prevTrend / lookback)) {
      patterns.push({
        type: 'Trend Reversal',
        confidence: 75,
        description: `Strong reversal after ${lookback} trades trend`,
        metrics: {
          'Prev Trend': formatCurrency(prevTrend),
          'Reversal Size': formatCurrency(currentPnL)
        }
      });
    }
  });

  return patterns;
}

function formatHour(hour) {
  return new Date(2000, 0, 1, hour).toLocaleTimeString([], {
    hour: 'numeric',
    hour12: true
  });
}

function formatCurrency(value) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0
  }).format(value);
} 