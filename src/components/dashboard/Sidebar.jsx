import React, { useState, useRef, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import {
  ChartBarSquareIcon,
  DocumentChartBarIcon,
  TableCellsIcon,
  Cog6ToothIcon,
  QuestionMarkCircleIcon,
  UserCircleIcon,
  ArrowRightOnRectangleIcon,
  XMarkIcon,
  Bars3Icon
} from '@heroicons/react/24/outline';

export default function Sidebar({ 
  currentView, 
  onViewChange,
  refreshing
}) {
  const location = useLocation();
  const navigate = useNavigate();
  const [showAccountMenu, setShowAccountMenu] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const menuRef = useRef(null);
  
  const navigationItems = [
    { id: 'overview', label: 'Overview', icon: ChartBarSquareIcon },
    { id: 'analysis', label: 'Analysis', icon: DocumentChartBarIcon },
    { id: 'journal', label: 'Journal', icon: TableCellsIcon }
  ];

  // Close menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setShowAccountMenu(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleViewChange = (view) => {
    navigate('/', { state: { view } });
    onViewChange(view);
    setIsMobileMenuOpen(false);
  };

  const { signOut } = useAuth();
  const [isSigningOut, setIsSigningOut] = useState(false);

  const handleLogout = async () => {
    try {
      setIsSigningOut(true);
      setShowAccountMenu(false);
      setIsMobileMenuOpen(false);

      console.log('🔄 Signing out user...');
      const result = await signOut();

      if (result.success) {
        console.log('✅ Successfully signed out');
        navigate('/');
      } else {
        console.error('❌ Sign out failed:', result.error);
        alert('Failed to sign out. Please try again.');
      }
    } catch (error) {
      console.error('❌ Sign out error:', error);
      alert('Failed to sign out. Please try again.');
    } finally {
      setIsSigningOut(false);
    }
  };

  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-slate-900 text-white shadow-md">
      <div className="max-w-[1920px] mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo Section */}
          <button
            onClick={() => handleViewChange('overview')}
            className="flex items-center"
          >
            <div className="relative">
              <h1 className="text-2xl sm:text-3xl font-black tracking-tight bg-gradient-to-r from-blue-500 via-blue-400 to-blue-300 
                text-transparent bg-clip-text relative z-10">
                TRADEDGE
              </h1>
              <div className="absolute -inset-1 bg-gradient-to-r from-blue-500/20 via-blue-400/20 to-blue-300/20 
                blur-lg rounded-lg opacity-75"></div>
            </div>
            <div className="hidden sm:flex items-center gap-1.5 ml-3">
              <span className="px-2 py-1 rounded-md bg-blue-500/10 text-blue-400 text-xs font-semibold border border-blue-400/20">
                BETA
              </span>
              <span className="text-xs font-medium text-slate-400">v1.0.0</span>
            </div>
          </button>
          
          {/* Mobile Menu Button */}
          <button
            className="sm:hidden p-2 rounded-lg text-slate-400 hover:text-slate-200 hover:bg-slate-800/50"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            {isMobileMenuOpen ? (
              <XMarkIcon className="w-6 h-6" />
            ) : (
              <Bars3Icon className="w-6 h-6" />
            )}
          </button>

          {/* Menu Items Section - Right Aligned */}
          <div className="hidden sm:flex items-center ml-auto">
            {/* Main Navigation */}
            <div className="flex items-center border-r border-slate-700 pr-4">
              {navigationItems.map(({ id, label, icon: Icon }) => (
                <button
                  key={id}
                  onClick={() => handleViewChange(id)}
                  className={`flex items-center gap-1.5 px-3 py-2 rounded-lg mx-0.5
                    transition-all duration-200 ${
                    currentView === id && location.pathname === '/'
                      ? 'bg-blue-500/10 text-blue-400 border border-blue-400/20'
                      : 'text-slate-400 hover:text-slate-200 hover:bg-slate-800/50'
                    }`}
                  title={label}
                >
                  <Icon className="w-5 h-5" />
                  <span className="font-medium text-sm">{label}</span>
                </button>
              ))}
            </div>

            {/* Account Section */}
            <div className="relative pl-4" ref={menuRef}>
              <button
                onClick={() => setShowAccountMenu(!showAccountMenu)}
                className={`flex items-center gap-2 px-3 py-2 rounded-lg
                  transition-all duration-200 ${
                  showAccountMenu
                    ? 'bg-blue-500/10 text-blue-400 border border-blue-400/20'
                    : 'text-slate-400 hover:text-slate-200 hover:bg-slate-800/50'
                  }`}
              >
                <UserCircleIcon className="w-5 h-5" />
                <span className="font-medium text-sm">Account</span>
              </button>

              {showAccountMenu && (
                <div className="absolute right-0 mt-2 w-56 bg-slate-800 rounded-lg shadow-lg border border-slate-700 overflow-hidden">
                  {/* Settings Section */}
                  <div className="py-1">
                    <Link
                      to="/settings/general"
                      className="flex items-center gap-2.5 px-4 py-3 text-sm text-slate-300 hover:bg-slate-700/50 transition-colors"
                      onClick={() => setShowAccountMenu(false)}
                    >
                      <Cog6ToothIcon className="w-5 h-5" />
                      Settings
                    </Link>
                  </div>

                  {/* Help Section */}
                  <div className="border-t border-slate-700/50 py-1">
                    <button
                      onClick={() => {}}
                      className="w-full flex items-center gap-2.5 px-4 py-3 text-sm text-slate-300 hover:bg-slate-700/50 transition-colors"
                    >
                      <QuestionMarkCircleIcon className="w-5 h-5" />
                      Help & Support
                    </button>
                  </div>

                  {/* Logout Section */}
                  <div className="border-t border-slate-700/50 py-1">
                    <button
                      onClick={handleLogout}
                      className="w-full flex items-center gap-2.5 px-4 py-3 text-sm text-red-400 hover:bg-red-500/10 transition-colors"
                    >
                      <ArrowRightOnRectangleIcon className="w-5 h-5" />
                      Log Out
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="sm:hidden bg-slate-900 border-t border-slate-800">
          <div className="px-4 py-3 space-y-1">
            {navigationItems.map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => handleViewChange(id)}
                className={`w-full flex items-center gap-2 px-3 py-3 rounded-lg
                  transition-all duration-200 ${
                  currentView === id && location.pathname === '/'
                    ? 'bg-blue-500/10 text-blue-400 border border-blue-400/20'
                    : 'text-slate-400 hover:text-slate-200 hover:bg-slate-800/50'
                  }`}
              >
                <Icon className="w-5 h-5" />
                <span className="font-medium text-sm">{label}</span>
              </button>
            ))}
            <Link
              to="/settings/general"
              className="w-full flex items-center gap-2 px-3 py-3 rounded-lg text-slate-400 hover:text-slate-200 hover:bg-slate-800/50"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              <Cog6ToothIcon className="w-5 h-5" />
              <span className="font-medium text-sm">Settings</span>
            </Link>
          </div>
        </div>
      )}
    </div>
  );
} 