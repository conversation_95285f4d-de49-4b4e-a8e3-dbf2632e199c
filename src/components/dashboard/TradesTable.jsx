import React, { useState } from 'react';
import { format, isValid, isWithinInterval, startOfDay, endOfDay } from 'date-fns';
import { 
  MagnifyingGlassIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  FunnelIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';

function formatTimestamp(timestamp) {
  try {
    if (!timestamp) return 'Invalid date';
    const date = new Date(timestamp);
    if (!isValid(date)) return 'Invalid date';
    return format(date, 'MMM d, yyyy HH:mm');
  } catch (err) {
    console.error('Error formatting timestamp:', timestamp, err);
    return 'Invalid date';
  }
}

export default function TradesTable({ trades = [] }) {
  const [sortConfig, setSortConfig] = useState({ key: 'timestamp', direction: 'desc' });
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [dateRange, setDateRange] = useState({ start: '', end: '' });
  const [showDateFilter, setShowDateFilter] = useState(false);
  const itemsPerPage = 10;

  // Sort and filter trades with null checks and date range
  const filteredTrades = trades
    .filter(trade => {
      if (!trade) return false;
      
      // Text search filter
      const searchLower = searchTerm.toLowerCase();
      const instrumentMatch = trade.instrument?.toLowerCase()?.includes(searchLower) || false;
      const positionMatch = trade.position_type?.toLowerCase()?.includes(searchLower) || false;
      const textMatch = instrumentMatch || positionMatch;
      
      // Date range filter
      let dateMatch = true;
      if (dateRange.start || dateRange.end) {
        const tradeDate = new Date(trade.timestamp);
        if (!isValid(tradeDate)) return false;
        
        const start = dateRange.start ? startOfDay(new Date(dateRange.start)) : new Date(0);
        const end = dateRange.end ? endOfDay(new Date(dateRange.end)) : new Date();
        
        dateMatch = isWithinInterval(tradeDate, { start, end });
      }
      
      return textMatch && dateMatch;
    })
    .sort((a, b) => {
      if (!a || !b) return 0;
      
      if (sortConfig.key === 'timestamp') {
        const dateA = new Date(a.timestamp);
        const dateB = new Date(b.timestamp);
        if (!isValid(dateA) || !isValid(dateB)) return 0;
        return sortConfig.direction === 'asc' 
          ? dateA.getTime() - dateB.getTime()
          : dateB.getTime() - dateA.getTime();
      }
      
      const aValue = a[sortConfig.key] ?? 0;
      const bValue = b[sortConfig.key] ?? 0;
      
      return sortConfig.direction === 'asc'
        ? aValue - bValue
        : bValue - aValue;
    });

  // Pagination
  const totalPages = Math.ceil(filteredTrades.length / itemsPerPage);
  const currentTrades = filteredTrades.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handleSort = (key) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleDateRangeChange = (field, value) => {
    setDateRange(prev => ({ ...prev, [field]: value }));
    setCurrentPage(1); // Reset to first page when date range changes
  };

  // Render empty state if no trades
  if (!trades.length) {
    return (
      <div className="dashboard-card">
        <div className="text-center py-12">
          <p className="text-slate-500">No trades available. Import trades to get started.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard-card">
      <div className="flex justify-between items-center mb-4">
        <h3 className="card-title">Recent Trades</h3>
        <div className="flex items-center space-x-2">
          <div className="relative">
            <MagnifyingGlassIcon className="w-5 h-5 text-slate-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
            <input
              type="text"
              placeholder="Search trades..."
              className="pl-10 pr-4 py-2 border border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          {/* Date Range Filter */}
          <div className="relative">
            <button 
              onClick={() => setShowDateFilter(!showDateFilter)}
              className="p-2 text-slate-600 hover:bg-slate-100 rounded-lg transition-colors flex items-center space-x-1"
            >
              <CalendarIcon className="w-5 h-5" />
              {(dateRange.start || dateRange.end) && (
                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
              )}
            </button>
            
            {showDateFilter && (
              <div className="absolute right-0 mt-2 p-4 bg-white rounded-lg shadow-lg border border-slate-200 z-10 min-w-[300px]">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">Start Date</label>
                    <input
                      type="date"
                      value={dateRange.start}
                      onChange={(e) => handleDateRangeChange('start', e.target.value)}
                      className="w-full px-3 py-2 border border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">End Date</label>
                    <input
                      type="date"
                      value={dateRange.end}
                      onChange={(e) => handleDateRangeChange('end', e.target.value)}
                      className="w-full px-3 py-2 border border-slate-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="flex justify-end space-x-2">
                    <button
                      onClick={() => {
                        setDateRange({ start: '', end: '' });
                        setShowDateFilter(false);
                      }}
                      className="px-3 py-1 text-sm font-medium text-slate-600 hover:bg-slate-100 rounded-md"
                    >
                      Clear
                    </button>
                    <button
                      onClick={() => setShowDateFilter(false)}
                      className="px-3 py-1 text-sm font-medium bg-blue-500 text-white rounded-md hover:bg-blue-600"
                    >
                      Apply
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
          
          <button className="p-2 text-slate-600 hover:bg-slate-100 rounded-lg transition-colors">
            <FunnelIcon className="w-5 h-5" />
          </button>
        </div>
      </div>

      <div className="overflow-x-auto">
        <div className="max-h-[500px] overflow-y-auto">
          <table className="min-w-full divide-y divide-slate-200">
            <thead>
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                  Instrument
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                  Entry
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                  Exit
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                  P&L
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-slate-200">
              {currentTrades.map((trade, index) => trade && (
                <tr key={index} className="hover:bg-slate-50 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900">
                    {formatTimestamp(trade.timestamp)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-900">
                    {trade.instrument}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      trade.position_type?.toUpperCase() === 'LONG' 
                        ? 'bg-green-100 text-green-800'
                        : trade.position_type?.toUpperCase() === 'SHORT'
                        ? 'bg-red-100 text-red-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {trade.position_type?.toUpperCase() || 'UNKNOWN'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900">
                    {formatCurrency(trade.entry_price)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900">
                    {formatCurrency(trade.exit_price)}
                  </td>
                  <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${
                    trade.profit_loss >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {formatCurrency(trade.profit_loss)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between px-4 py-3 border-t border-slate-200">
        <div className="flex items-center">
          <span className="text-sm text-slate-700">
            Showing {(currentPage - 1) * itemsPerPage + 1} to {Math.min(currentPage * itemsPerPage, filteredTrades.length)} of{' '}
            {filteredTrades.length} trades
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
            disabled={currentPage === 1}
            className="px-3 py-1 text-sm font-medium rounded-md text-slate-600 hover:bg-slate-100 disabled:opacity-50"
          >
            Previous
          </button>
          <button
            onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
            disabled={currentPage === totalPages}
            className="px-3 py-1 text-sm font-medium rounded-md text-slate-600 hover:bg-slate-100 disabled:opacity-50"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
}

function formatCurrency(value) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(value || 0);
} 