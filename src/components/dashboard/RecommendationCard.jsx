import React from 'react';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';

export default function RecommendationCard({ type, message, metric, value }) {
  return (
    <div className={`p-4 rounded-lg ${
      type === 'warning' ? 'bg-yellow-50' : 'bg-red-50'
    }`}>
      <div className="flex items-start space-x-3">
        <div className={`p-1 rounded-full ${
          type === 'warning' ? 'bg-yellow-100' : 'bg-red-100'
        }`}>
          <ExclamationTriangleIcon className={`w-5 h-5 ${
            type === 'warning' ? 'text-yellow-600' : 'text-red-600'
          }`} />
        </div>
        <div>
          <p className={`text-sm font-medium ${
            type === 'warning' ? 'text-yellow-800' : 'text-red-800'
          }`}>
            {message}
          </p>
          <p className="text-sm mt-1">
            <span className="text-slate-500">{metric}:</span>
            <span className="font-medium ml-1">{value}</span>
          </p>
        </div>
      </div>
    </div>
  );
} 