import React, { useState, useMemo } from 'react';
import { format } from 'date-fns';
import { <PERSON>, Bar, Scatter } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { ChartBarIcon } from '@heroicons/react/24/outline';
import TimeAnalysis from './TimeAnalysis';
import TradePatternAnalysis from './TradePatternAnalysis';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

// Add formatCurrency function
function formatCurrency(value) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
}

// Add calculation functions
function calculateMaxDrawdown(trades) {
  if (!trades?.length) return 0;
  
  let peak = 0;
  let maxDrawdown = 0;
  let runningPnL = 0;

  trades.forEach(trade => {
    runningPnL += trade.profit_loss;
    if (runningPnL > peak) {
      peak = runningPnL;
    }
    const drawdown = peak > 0 ? ((peak - runningPnL) / peak) * 100 : 0;
    maxDrawdown = Math.max(maxDrawdown, drawdown);
  });

  return maxDrawdown;
}

function calculateDailyVolatility(trades) {
  if (!trades?.length) return 0;

  const dailyReturns = trades.reduce((acc, trade) => {
    const date = format(new Date(trade.timestamp), 'yyyy-MM-dd');
    if (!acc[date]) acc[date] = 0;
    acc[date] += trade.profit_loss;
    return acc;
  }, {});

  const returns = Object.values(dailyReturns);
  const mean = returns.reduce((sum, val) => sum + val, 0) / returns.length;
  const variance = returns.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / returns.length;
  
  return Math.sqrt(variance);
}

function calculateAverageExposure(trades) {
  if (!trades?.length) return 0;

  const totalExposure = trades.reduce((sum, trade) => {
    const exposure = Math.abs(trade.size * trade.entry_price);
    return sum + exposure;
  }, 0);

  return (totalExposure / trades.length) / 100000; // Convert to percentage of $100k
}

// Add MetricCard component
function MetricCard({ title, value, format, subtitle }) {
  const formattedValue = useMemo(() => {
    switch (format) {
      case 'percentage':
        return `${value.toFixed(1)}%`;
      case 'currency':
        return formatCurrency(value);
      case 'decimal':
        return value.toFixed(2);
      default:
        return value.toString();
    }
  }, [value, format]);

  return (
    <div className="p-4 bg-slate-50 rounded-lg border border-slate-200">
      <h5 className="text-sm font-medium text-slate-700 mb-2">{title}</h5>
      <div className="text-2xl font-semibold text-slate-900">
        {formattedValue}
      </div>
      {subtitle && (
        <p className="mt-1 text-sm text-slate-500">{subtitle}</p>
      )}
    </div>
  );
}

// Add ComplianceCard component
function ComplianceCard({ title, value, violations, totalValid, details }) {
  const percentage = (totalValid / (totalValid + violations)) * 100;
  
  return (
    <div className="p-4 bg-slate-50 rounded-lg border border-slate-200">
      <h5 className="text-sm font-medium text-slate-700 mb-2">{title}</h5>
      <div className="text-2xl font-semibold text-slate-900">
        {percentage.toFixed(1)}%
      </div>
      <p className="mt-1 text-sm text-slate-500">
        {violations} violations • {details}
      </p>
    </div>
  );
}

export default function TradeAnalysis({ trades }) {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedTimeframe, setSelectedTimeframe] = useState('daily');

  // Calculate performance metrics
  const metrics = useMemo(() => {
    if (!trades?.length) return null;

    const totalTrades = trades.length;
    const winningTrades = trades.filter(t => t.profit_loss > 0).length;
    const losingTrades = trades.filter(t => t.profit_loss < 0).length;
    const breakEvenTrades = trades.filter(t => t.profit_loss === 0).length;
    const rulesFollowed = trades.filter(t => t.rules_followed).length;
    const emotionalControl = trades.filter(t => !t.emotional_mistakes).length;
    const strategyAdherence = trades.filter(t => t.strategy_followed).length;

    // Calculate profit metrics
    const totalProfit = trades.reduce((sum, t) => sum + (t.profit_loss > 0 ? t.profit_loss : 0), 0);
    const totalLoss = Math.abs(trades.reduce((sum, t) => sum + (t.profit_loss < 0 ? t.profit_loss : 0), 0));
    const profitFactor = totalLoss === 0 ? totalProfit : totalProfit / totalLoss;
    const avgWinSize = winningTrades === 0 ? 0 : totalProfit / winningTrades;
    const avgLossSize = losingTrades === 0 ? 0 : totalLoss / losingTrades;
    const netPnL = trades.reduce((sum, t) => sum + t.profit_loss, 0);

    // Calculate win rate and streaks
    const winRate = (winningTrades / totalTrades) * 100;
    let currentStreak = 0;
    let winningStreak = 0;
    let losingStreak = 0;
    let maxWinStreak = 0;
    let maxLossStreak = 0;

    trades.forEach(trade => {
      if (trade.profit_loss > 0) {
        if (currentStreak >= 0) {
          currentStreak++;
        } else {
          currentStreak = 1;
        }
        maxWinStreak = Math.max(maxWinStreak, currentStreak);
      } else {
        if (currentStreak <= 0) {
          currentStreak--;
        } else {
          currentStreak = -1;
        }
        maxLossStreak = Math.min(maxLossStreak, currentStreak);
      }
    });

    winningStreak = maxWinStreak;
    losingStreak = Math.abs(maxLossStreak);

    // Calculate risk metrics
    const maxDrawdown = calculateMaxDrawdown(trades);
    const dailyVolatility = calculateDailyVolatility(trades);
    const avgExposure = calculateAverageExposure(trades);

    // Calculate compliance metrics
    const positionSizeViolations = trades.filter(t => t.size > 100000).length;
    const stopLossViolations = trades.filter(t => !t.stop_loss || t.stop_loss === 0).length;
    const riskRewardViolations = trades.filter(t => (t.target_price - t.entry_price) / (t.entry_price - t.stop_loss) < 1.5).length;

    return {
      totalTrades,
      winningTrades,
      losingTrades,
      breakEvenTrades,
      winRate,
      profitFactor,
      avgWinSize,
      avgLossSize,
      netPnL,
      winningStreak,
      losingStreak,
      maxDrawdown,
      dailyVolatility,
      avgExposure,
      positionSize: {
        violations: positionSizeViolations,
        totalValid: totalTrades - positionSizeViolations,
        compliance: ((totalTrades - positionSizeViolations) / totalTrades) * 100
      },
      stopLoss: {
        violations: stopLossViolations,
        totalValid: totalTrades - stopLossViolations,
        compliance: ((totalTrades - stopLossViolations) / totalTrades) * 100
      },
      riskReward: {
        violations: riskRewardViolations,
        totalValid: totalTrades - riskRewardViolations,
        compliance: ((totalTrades - riskRewardViolations) / totalTrades) * 100
      }
    };
  }, [trades]);

  // Performance trend data with timeframe selection
  const trendData = useMemo(() => {
    if (!trades?.length) return null;

    // Sort trades by date
    const sortedTrades = [...trades].sort((a, b) => 
      new Date(a.timestamp) - new Date(b.timestamp)
    );

    // Format based on selected timeframe
    const getDateFormat = () => {
      switch (selectedTimeframe) {
        case 'hourly':
          return 'MMM d, HH:mm';
        case 'daily':
          return 'MMM d';
        case 'weekly':
          return 'MMM d, yyyy';
        case 'monthly':
          return 'MMM yyyy';
        default:
          return 'MMM d';
      }
    };

    // Group trades by timeframe
    const tradesByPeriod = sortedTrades.reduce((acc, trade) => {
      const date = format(new Date(trade.timestamp), getDateFormat());
      if (!acc[date]) {
        acc[date] = {
          total: 0,
          wins: 0,
          pnl: 0,
          volume: 0
        };
      }
      acc[date].total++;
      if (trade.profit_loss > 0) acc[date].wins++;
      acc[date].pnl += trade.profit_loss;
      acc[date].volume += trade.volume || Math.abs(trade.size * trade.entry_price);
      return acc;
    }, {});

    const periods = Object.keys(tradesByPeriod);
    const winRates = periods.map(period => 
      (tradesByPeriod[period].wins / tradesByPeriod[period].total) * 100
    );
    const pnls = periods.map(period => tradesByPeriod[period].pnl);
    const volumes = periods.map(period => tradesByPeriod[period].volume);

    // Calculate cumulative values
    const cumulativePnL = pnls.reduce((acc, pnl) => {
      const lastValue = acc[acc.length - 1] || 0;
      acc.push(lastValue + pnl);
      return acc;
    }, []);

    return {
      labels: periods,
      datasets: [
        {
          label: 'Win Rate',
          data: winRates,
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          fill: true,
          tension: 0.4,
          yAxisID: 'y'
        },
        {
          label: 'Cumulative P&L',
          data: cumulativePnL,
          borderColor: 'rgb(16, 185, 129)',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          fill: true,
          tension: 0.4,
          yAxisID: 'y1'
        },
        {
          label: 'Volume',
          data: volumes,
          borderColor: 'rgb(249, 115, 22)',
          backgroundColor: 'rgba(249, 115, 22, 0.1)',
          fill: true,
          tension: 0.4,
          yAxisID: 'y2',
          hidden: true
        }
      ]
    };
  }, [trades, selectedTimeframe]);

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    plugins: {
      legend: {
        display: true,
        position: 'top',
        labels: {
          usePointStyle: true,
          padding: 15,
          color: '#64748b'
        }
      },
      tooltip: {
        mode: 'index',
        intersect: false,
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        titleColor: '#1e293b',
        bodyColor: '#475569',
        borderColor: '#e2e8f0',
        borderWidth: 1,
        padding: 12,
        boxPadding: 6,
        callbacks: {
          label: function(context) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              switch (context.dataset.label) {
                case 'Win Rate':
                  label += context.parsed.y.toFixed(1) + '%';
                  break;
                case 'Cumulative P&L':
                  label += formatCurrency(context.parsed.y);
                  break;
                case 'Volume':
                  label += formatCurrency(context.parsed.y);
                  break;
                default:
                  label += context.parsed.y;
              }
            }
            return label;
          }
        }
      }
    },
    scales: {
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        beginAtZero: true,
        max: 100,
        grid: {
          color: '#f1f5f9'
        },
        ticks: {
          callback: value => `${value}%`,
          color: '#64748b'
        },
        title: {
          display: true,
          text: 'Win Rate',
          color: '#64748b',
          font: {
            size: 12,
            weight: 'normal'
          }
        }
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        grid: {
          drawOnChartArea: false
        },
        ticks: {
          callback: value => formatCurrency(value),
          color: '#64748b'
        },
        title: {
          display: true,
          text: 'Cumulative P&L',
          color: '#64748b',
          font: {
            size: 12,
            weight: 'normal'
          }
        }
      },
      y2: {
        type: 'linear',
        display: true,
        position: 'right',
        grid: {
          drawOnChartArea: false
        },
        ticks: {
          callback: value => formatCurrency(value),
          color: '#64748b'
        },
        title: {
          display: true,
          text: 'Volume',
          color: '#64748b',
          font: {
            size: 12,
            weight: 'normal'
          }
        }
      },
      x: {
        grid: {
          display: false
        },
        ticks: {
          color: '#64748b',
          maxRotation: 45,
          minRotation: 45
        }
      }
    }
  };

  if (!trades || trades.length === 0) {
    return (
      <div className="absolute inset-x-0 top-[220px] bottom-0 z-40 flex items-start justify-center bg-slate-50/95 backdrop-blur-sm">
        <div className="w-full max-w-2xl mx-auto pt-12">
          <div className="text-center">
            <div className="mb-8">
              <div className="w-24 h-24 mx-auto mb-6 relative">
                <div className="absolute inset-0 bg-violet-100 rounded-full animate-pulse"></div>
                <div className="absolute inset-2 bg-violet-50 rounded-full flex items-center justify-center">
                  <ChartBarIcon className="w-12 h-12 text-violet-500" />
                </div>
              </div>
              <h2 className="text-2xl font-bold text-slate-900 mb-3">Unlock Trading Analytics</h2>
              <p className="text-slate-500 mb-8 max-w-lg mx-auto">
                Add trades to access in-depth analysis and discover insights about your trading performance.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!metrics) return null;

  return (
    <div className="bg-white rounded-lg shadow-sm">
      <div className="p-4 sm:p-6 border-b border-slate-200">
        <h2 className="text-lg font-semibold text-slate-900">
          Trading Analysis & Insights
        </h2>
      </div>
      <div className="h-full flex flex-col">
        {/* Tabs */}
        <div className="border-b border-slate-200 flex-shrink-0 overflow-x-auto">
          <div className="flex justify-between items-center p-2 sm:px-6">
            <div className="flex space-x-4 sm:space-x-8">
              <button
                onClick={() => setActiveTab('overview')}
                className={`
                  py-3 sm:py-4 text-sm font-medium border-b-2 outline-none transition-colors whitespace-nowrap
                  ${activeTab === 'overview' 
                    ? 'text-blue-600 border-blue-600'
                    : 'text-slate-500 border-transparent hover:text-slate-700 hover:border-slate-300'
                  }
                `}
              >
                Overview
              </button>
              <button
                onClick={() => setActiveTab('detailed')}
                className={`
                  py-3 sm:py-4 text-sm font-medium border-b-2 outline-none transition-colors whitespace-nowrap
                  ${activeTab === 'detailed'
                    ? 'text-blue-600 border-blue-600'
                    : 'text-slate-500 border-transparent hover:text-slate-700 hover:border-slate-300'
                  }
                `}
              >
                Detailed Analysis
              </button>
              <button
                onClick={() => setActiveTab('trends')}
                className={`
                  py-3 sm:py-4 text-sm font-medium border-b-2 outline-none transition-colors whitespace-nowrap
                  ${activeTab === 'trends'
                    ? 'text-blue-600 border-blue-600'
                    : 'text-slate-500 border-transparent hover:text-slate-700 hover:border-slate-300'
                  }
                `}
              >
                Trends
              </button>
            </div>

            {/* Timeframe Selection */}
            {activeTab === 'trends' && (
              <div className="flex items-center space-x-2">
                <label className="text-sm text-slate-600 hidden sm:inline">Timeframe:</label>
                <select
                  value={selectedTimeframe}
                  onChange={(e) => setSelectedTimeframe(e.target.value)}
                  className="text-sm border border-slate-300 rounded-md py-1 px-2 text-slate-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="hourly">Hourly</option>
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                </select>
              </div>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="flex-grow overflow-auto">
          {activeTab === 'overview' && (
            <div className="p-4 sm:p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                {/* Performance Summary */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-slate-900">Performance Summary</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                    <MetricCard
                      title="Win Rate"
                      value={metrics.winRate}
                      format="percentage"
                      subtitle={`${metrics.winningTrades}W / ${metrics.losingTrades}L / ${metrics.breakEvenTrades}BE`}
                    />
                    <MetricCard
                      title="Profit Factor"
                      value={metrics.profitFactor}
                      format="decimal"
                      subtitle={`${formatCurrency(metrics.netPnL)} Net P&L`}
                    />
                    <MetricCard
                      title="Avg Win"
                      value={metrics.avgWinSize}
                      format="currency"
                      subtitle="Per Winning Trade"
                    />
                    <MetricCard
                      title="Avg Loss"
                      value={metrics.avgLossSize}
                      format="currency"
                      subtitle="Per Losing Trade"
                    />
                  </div>
                </div>

                {/* Time Analysis */}
                <div className="mt-6 lg:mt-0">
                  <TimeAnalysis trades={trades} />
                </div>

                {/* Pattern Analysis */}
                <div className="mt-6">
                  <TradePatternAnalysis trades={trades} />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'detailed' && (
            <div className="p-4 sm:p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                {/* Left Column */}
                <div className="space-y-6">
                  {/* Performance Metrics */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-slate-900">Performance Metrics</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                      <MetricCard
                        title="Win Rate"
                        value={metrics.winRate}
                        format="percentage"
                        subtitle={`${metrics.winningTrades}W / ${metrics.losingTrades}L / ${metrics.breakEvenTrades}BE`}
                      />
                      <MetricCard
                        title="Profit Factor"
                        value={metrics.profitFactor}
                        format="decimal"
                        subtitle={`${formatCurrency(metrics.netPnL)} Net P&L`}
                      />
                      <MetricCard
                        title="Avg Win"
                        value={metrics.avgWinSize}
                        format="currency"
                        subtitle="Per Winning Trade"
                      />
                      <MetricCard
                        title="Avg Loss"
                        value={metrics.avgLossSize}
                        format="currency"
                        subtitle="Per Losing Trade"
                      />
                      <MetricCard
                        title="Win Streak"
                        value={metrics.winningStreak}
                        format="number"
                        subtitle="Consecutive Wins"
                      />
                      <MetricCard
                        title="Loss Streak"
                        value={metrics.losingStreak}
                        format="number"
                        subtitle="Consecutive Losses"
                      />
                    </div>
                  </div>

                  {/* Performance Trends */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-slate-900">Performance Trends</h3>
                    <div className="h-[300px] bg-white rounded-lg p-4 border border-slate-200">
                      {trendData && <Line data={trendData} options={chartOptions} />}
                    </div>
                  </div>
                </div>

                {/* Right Column */}
                <div className="space-y-6">
                  {/* Compliance Analysis */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-slate-900">Compliance Analysis</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                      <ComplianceCard
                        title="Position Size"
                        value={metrics.positionSize.compliance}
                        violations={metrics.positionSize.violations}
                        totalValid={metrics.positionSize.totalValid}
                        details="Max size: $100k"
                      />
                      <ComplianceCard
                        title="Stop Loss"
                        value={metrics.stopLoss.compliance}
                        violations={metrics.stopLoss.violations}
                        totalValid={metrics.stopLoss.totalValid}
                        details="Max risk: 2%"
                      />
                      <ComplianceCard
                        title="Risk/Reward"
                        value={metrics.riskReward.compliance}
                        violations={metrics.riskReward.violations}
                        totalValid={metrics.riskReward.totalValid}
                        details="Min ratio: 1.5:1"
                      />
                    </div>
                  </div>

                  {/* Time Analysis */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-slate-900">Time Analysis</h3>
                    <TimeAnalysis trades={trades} />
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'trends' && (
            <div className="p-4 sm:p-6">
              <div className="space-y-6">
                {/* Win Rate Trend */}
                <div>
                  <h3 className="text-lg font-semibold text-slate-900 mb-4">Performance Trends</h3>
                  <div className="h-[300px] bg-white rounded-lg p-4 border border-slate-200">
                    {trendData && <Line data={trendData} options={chartOptions} />}
                  </div>
                </div>

                {/* Risk Metrics Trend */}
                <div>
                  <h3 className="text-lg font-semibold text-slate-900 mb-4">Risk Metrics</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div className="p-4 bg-slate-50 rounded-lg border border-slate-200">
                      <h5 className="text-sm font-medium text-slate-700 mb-2">Maximum Drawdown</h5>
                      <div className="text-xl sm:text-2xl font-semibold text-slate-900">
                        {metrics.maxDrawdown.toFixed(2)}%
                      </div>
                      <p className="mt-1 text-sm text-slate-500">
                        Peak to trough decline
                      </p>
                    </div>
                    <div className="p-4 bg-slate-50 rounded-lg border border-slate-200">
                      <h5 className="text-sm font-medium text-slate-700 mb-2">Daily Volatility</h5>
                      <div className="text-xl sm:text-2xl font-semibold text-slate-900">
                        {metrics.dailyVolatility.toFixed(2)}%
                      </div>
                      <p className="mt-1 text-sm text-slate-500">
                        Average daily fluctuation
                      </p>
                    </div>
                    <div className="p-4 bg-slate-50 rounded-lg border border-slate-200">
                      <h5 className="text-sm font-medium text-slate-700 mb-2">Average Exposure</h5>
                      <div className="text-xl sm:text-2xl font-semibold text-slate-900">
                        {metrics.avgExposure.toFixed(2)}%
                      </div>
                      <p className="mt-1 text-sm text-slate-500">
                        Typical position size
                      </p>
                    </div>
                  </div>
                </div>

                {/* Pattern Analysis */}
                <div>
                  <h3 className="text-lg font-semibold text-slate-900 mb-4">Trading Patterns</h3>
                  <TradePatternAnalysis trades={trades} />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
 