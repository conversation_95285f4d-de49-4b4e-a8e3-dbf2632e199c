import { useState, useEffect } from 'react';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import Draggable from 'react-draggable';

export default function DraggableCard({ title, children }) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  const handleDragStart = () => {
    setIsDragging(true);
    document.body.style.cursor = 'grabbing';
  };

  const handleDragStop = () => {
    setIsDragging(false);
    document.body.style.cursor = 'default';
  };

  const handleDrag = (e, data) => {
    setPosition({ x: data.x, y: data.y });
  };

  return (
    <Draggable
      handle=".drag-handle"
      bounds="parent"
      position={position}
      onStart={handleDragStart}
      onStop={handleDragStop}
      onDrag={handleDrag}
    >
      <div className={`dashboard-card relative h-full flex flex-col border transition-all duration-200 transform
        ${isDragging 
          ? 'border-blue-200 shadow-lg scale-[1.02] rotate-[0.5deg] z-50' 
          : 'border-slate-100/75 hover:shadow-md hover:border-slate-200/75'
        }`}>
        
        {/* Card Header */}
        <div className="flex items-center justify-between px-4 py-3 border-b border-slate-100">
          <h3 className="card-title">{title}</h3>
          
          {/* Drag Handle - Right Side */}
          <div className="flex items-center gap-2">
            {isDragging && (
              <div className="text-xs font-medium text-blue-500">
                Dragging...
              </div>
            )}
            <div className={`drag-handle ${isDragging ? 'dragging' : ''}`}>
              <div className={`drag-handle-dot ${isDragging ? 'dragging' : ''}`} />
              <div className={`drag-handle-dot ${isDragging ? 'dragging' : ''}`} />
              <div className={`drag-handle-dot ${isDragging ? 'dragging' : ''}`} />
            </div>
          </div>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-20">
            <div className="flex flex-col items-center gap-2">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
              <span className="text-sm text-slate-500">Loading widget...</span>
            </div>
          </div>
        )}

        {/* Error State */}
        {hasError && (
          <div className="absolute inset-0 bg-white/90 backdrop-blur-sm flex items-center justify-center z-20">
            <div className="flex flex-col items-center gap-2 p-4 text-center">
              <div className="rounded-full bg-red-100 p-3">
                <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
              </div>
              <span className="text-sm font-medium text-slate-900">Widget Error</span>
              <p className="text-sm text-slate-500">There was a problem loading this widget.</p>
              <button 
                onClick={() => setHasError(false)}
                className="mt-2 px-3 py-1.5 text-sm font-medium text-white bg-red-600 
                  hover:bg-red-500 rounded-md transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        )}

        {/* Content */}
        <div className="flex-1 p-6">
          {children}
        </div>
      </div>
    </Draggable>
  );
} 