import { useMemo } from 'react';
import { Line } from 'react-chartjs-2';
import { formatCurrency } from '../../utils/formatters';

export default function PerformanceTrends({ trades }) {
  const trendData = useMemo(() => {
    if (!trades?.length) return null;

    // Calculate rolling metrics
    const windowSize = 20;
    const metrics = trades.reduce((acc, trade, index) => {
      const window = trades.slice(Math.max(0, index - windowSize), index + 1);
      const date = new Date(trade.timestamp).toISOString().split('T')[0];
      
      const winRate = (window.filter(t => t.profit_loss > 0).length / window.length) * 100;
      const avgWin = window.filter(t => t.profit_loss > 0).reduce((sum, t) => sum + t.profit_loss, 0) / 
                    window.filter(t => t.profit_loss > 0).length || 0;
      const avgLoss = Math.abs(window.filter(t => t.profit_loss < 0).reduce((sum, t) => sum + t.profit_loss, 0) / 
                    window.filter(t => t.profit_loss < 0).length || 0);
      
      acc[date] = {
        winRate,
        avgWin,
        avgLoss,
        profitFactor: avgLoss ? avgWin / avgLoss : avgWin ? Infinity : 1
      };
      return acc;
    }, {});

    return {
      dates: Object.keys(metrics).sort(),
      metrics
    };
  }, [trades]);

  if (!trendData) return null;

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <TrendChart
          title="Win Rate & Profit Factor Trend"
          data={{
            labels: trendData.dates,
            datasets: [
              {
                label: 'Win Rate',
                data: trendData.dates.map(date => trendData.metrics[date].winRate),
                borderColor: 'rgb(34, 197, 94)',
                yAxisID: 'y',
                tension: 0.4
              },
              {
                label: 'Profit Factor',
                data: trendData.dates.map(date => trendData.metrics[date].profitFactor),
                borderColor: 'rgb(59, 130, 246)',
                yAxisID: 'y1',
                tension: 0.4
              }
            ]
          }}
          options={getDualAxisOptions('Win Rate (%)', 'Profit Factor')}
        />
        <TrendChart
          title="Average Win/Loss Trend"
          data={{
            labels: trendData.dates,
            datasets: [
              {
                label: 'Avg Win',
                data: trendData.dates.map(date => trendData.metrics[date].avgWin),
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                fill: true,
                tension: 0.4
              },
              {
                label: 'Avg Loss',
                data: trendData.dates.map(date => trendData.metrics[date].avgLoss),
                borderColor: 'rgb(239, 68, 68)',
                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                fill: true,
                tension: 0.4
              }
            ]
          }}
          options={getSingleAxisOptions('Amount ($)')}
        />
      </div>
    </div>
  );
}

function TrendChart({ title, data, options }) {
  return (
    <div className="bg-white rounded-lg p-4">
      <h4 className="text-sm font-medium text-slate-700 mb-4">{title}</h4>
      <div className="h-[300px]">
        <Line data={data} options={options} />
      </div>
    </div>
  );
}

function getDualAxisOptions(yLabel, y2Label) {
  return {
    responsive: true,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.dataset.label;
            const value = context.raw.toFixed(2);
            return `${label}: ${value}${context.dataset.yAxisID === 'y' ? '%' : ''}`;
          }
        }
      }
    },
    scales: {
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: yLabel
        },
        min: 0,
        max: 100
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        title: {
          display: true,
          text: y2Label
        },
        grid: {
          drawOnChartArea: false
        },
        min: 0,
        max: Math.max(...data.datasets[1].data) * 1.1
      }
    }
  };
}

function getSingleAxisOptions(yLabel) {
  return {
    responsive: true,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return `${context.dataset.label}: ${formatCurrency(context.raw)}`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: yLabel
        }
      }
    }
  };
} 