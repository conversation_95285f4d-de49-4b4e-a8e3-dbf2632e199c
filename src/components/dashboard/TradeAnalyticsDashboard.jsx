import { useState, useMemo } from 'react';
import { format, subDays, differenceInDays } from 'date-fns';
import {
  ChartBarSquareIcon,
  TableCellsIcon,
  DocumentChartBarIcon,
  AdjustmentsHorizontalIcon,
  ArrowPathIcon,
  ChartBarIcon,
  PresentationChartBarIcon,
  ExclamationTriangleIcon,
  CurrencyDollarIcon,
  ScaleIcon,
  ChartPieIcon
} from '@heroicons/react/24/outline';
import { useTradeFilters } from '../../hooks/useTradeFilters';
import PerformanceMetrics from './PerformanceMetrics';
import TradePatternAnalysis from './TradePatternAnalysis';
import PerformanceComparison from './PerformanceComparison';
import AdvancedFilters from './AdvancedFilters';
import TradeJournal from './TradeJournal';
import ChartSection from './ChartSection';
import PerformanceVisualizations from './PerformanceVisualizations';
import RiskHeatmap from './RiskHeatmap';
import SessionAnalysis from './SessionAnalysis';
import BehavioralPatterns from './BehavioralPatterns';
import { parseCSVTrades } from '../../utils/csvImport';
import DraggableCard from './DraggableCard';

const VIEWS = {
  OVERVIEW: 'overview',
  ANALYSIS: 'analysis',
  JOURNAL: 'journal'
};

const MENU_ITEMS = [
  { id: 'overview', label: 'Overview', icon: ChartBarSquareIcon },
  { id: 'analysis', label: 'Analysis', icon: DocumentChartBarIcon },
  { id: 'journal', label: 'Journal', icon: TableCellsIcon }
];

export default function TradeAnalyticsDashboard({ trades, dateRange }) {
  const [selectedMetrics, setSelectedMetrics] = useState(['winRate', 'profitFactor', 'maxDrawdown']);
  const [selectedTimeframe, setSelectedTimeframe] = useState('daily');
  const [selectedView, setSelectedView] = useState('chart');
  const [selectedChartType, setSelectedChartType] = useState('line');
  const [isLoading, setIsLoading] = useState(false);

  // Use the passed dateRange prop for filtering
  const filteredData = useMemo(() => {
    return trades.filter(trade => {
      const tradeDate = new Date(trade.timestamp);
      return isWithinInterval(tradeDate, {
        start: dateRange.start,
        end: dateRange.end
      });
    });
  }, [trades, dateRange]);

  const {
    filteredTrades,
    activeFilters,
    handleFilterChange,
    clearFilters
  } = useTradeFilters(trades);

  const stats = useMemo(() => calculateStats(filteredTrades), [filteredTrades]);

  const handleImportCSV = async (file) => {
    try {
      setIsImporting(true);
      setImportError(null);
      const newTrades = await parseCSVTrades(file);
      
      // Merge new trades with existing trades, avoiding duplicates
      const mergedTrades = [...trades];
      newTrades.forEach(newTrade => {
        const existingIndex = mergedTrades.findIndex(
          existing => existing.timestamp === newTrade.timestamp && 
                     existing.netPnL === newTrade.netPnL &&
                     existing.size === newTrade.size
        );
        if (existingIndex === -1) {
          mergedTrades.push(newTrade);
        }
      });

      // Sort trades by timestamp
      mergedTrades.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
      
      setTrades(mergedTrades);
    } catch (error) {
      console.error('Failed to import trades:', error);
      setImportError(error.message);
    } finally {
      setIsImporting(false);
    }
  };

  const handleTradeUpdate = (updatedTrade) => {
    setTrades(current => 
      current.map(trade => 
        trade.id === updatedTrade.id ? updatedTrade : trade
      )
    );
  };

  const handleTradeDelete = (tradeId) => {
    setTrades(current => current.filter(trade => trade.id !== tradeId));
  };

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Navigation */}
      <nav className="bg-gradient-to-r from-slate-900 via-slate-800 to-slate-900 border-b border-slate-700 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            {/* Logo */}
            <div className="flex items-center flex-shrink-0">
              <div className="flex items-center gap-3">
                <div className="relative">
                  <h1 className="text-3xl font-black tracking-tight bg-gradient-to-r from-blue-500 via-blue-400 to-blue-300 
                    text-transparent bg-clip-text relative z-10">
                    TRADEDGE
                  </h1>
                  <div className="absolute -inset-1 bg-gradient-to-r from-blue-500/20 via-blue-400/20 to-blue-300/20 
                    blur-lg rounded-lg opacity-75"></div>
                </div>
                <div className="flex items-center gap-1.5">
                  <span className="px-2 py-1 rounded-md bg-blue-500/10 text-blue-400 text-xs font-semibold border border-blue-400/20">
                    BETA
                  </span>
                  <span className="text-xs font-medium text-slate-400">v1.0.0</span>
                </div>
              </div>
            </div>

            {/* Menu Items */}
            <div className="flex items-center">
              <div className="flex mr-8">
                {MENU_ITEMS.map(({ id, label, icon: Icon }) => (
                  <button
                    key={id}
                    onClick={() => setCurrentView(id)}
                    className={`px-4 inline-flex items-center space-x-2 h-16 border-b-2
                      transition-all duration-200 ${
                      currentView === id
                        ? 'border-blue-500 text-blue-400 bg-blue-500/5'
                        : 'border-transparent text-slate-400 hover:text-slate-200 hover:border-slate-600 hover:bg-slate-800/50'
                      }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span className="font-medium">{label}</span>
                  </button>
                ))}
              </div>

              {/* Right Section */}
              <div className="flex items-center space-x-4">
                <DateRangeSelector
                  range={dateRange}
                  onChange={setDateRange}
                />
                <div className="h-8 w-px bg-slate-700"></div>
                <button
                  onClick={() => document.getElementById('csvInput').click()}
                  className="inline-flex items-center px-3 py-1.5 
                    text-sm font-medium rounded-lg text-blue-300 
                    hover:text-blue-200 hover:bg-blue-500/10 
                    border border-blue-400/20 transition-all duration-200
                    focus:outline-none focus:ring-2 focus:ring-blue-500/40"
                >
                  <ArrowPathIcon className="w-4 h-4 mr-1.5" />
                  Import CSV
                </button>
                <input
                  id="csvInput"
                  type="file"
                  accept=".csv"
                  className="hidden"
                  onChange={(e) => {
                    if (e.target.files?.[0]) {
                      handleImportCSV(e.target.files[0]);
                    }
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Import Status */}
      {(isImporting || importError) && (
        <div className={`fixed top-20 right-4 p-4 rounded-lg shadow-lg z-50 transition-all duration-300 transform
          ${isImporting ? 'bg-blue-500' : 'bg-red-500'}
          ${isImporting || importError ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}`}>
          <div className="flex items-center gap-3">
            {isImporting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                <p className="text-white text-sm font-medium">Importing trades...</p>
              </>
            ) : (
              <>
                <ExclamationTriangleIcon className="h-4 w-4 text-white" />
                <p className="text-white text-sm font-medium">{importError}</p>
              </>
            )}
          </div>
        </div>
      )}

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pb-24">
        <div className="space-y-6">
          {/* Page Title */}
          <div className="flex items-center justify-between bg-white rounded-xl p-6 shadow-sm border border-slate-200">
            <div>
              <h2 className="text-2xl font-bold text-slate-800">
                {currentView === VIEWS.OVERVIEW && 'Trading Overview'}
                {currentView === VIEWS.ANALYSIS && 'Performance Analysis'}
                {currentView === VIEWS.JOURNAL && 'Trade Journal'}
              </h2>
              <p className="text-sm text-slate-500 mt-1">
                {trades.length} trades • {format(dateRange.start, 'MMM dd, yyyy')} - {format(dateRange.end, 'MMM dd, yyyy')}
              </p>
            </div>
            
            {/* Quick Actions */}
            <div className="flex items-center gap-4">
              <button
                onClick={() => clearFilters()}
                className="inline-flex items-center px-3 py-1.5 text-sm font-medium 
                  text-slate-600 hover:text-slate-800 transition-colors
                  hover:bg-slate-50 rounded-lg"
              >
                <AdjustmentsHorizontalIcon className="w-4 h-4 mr-1.5" />
                Clear Filters
              </button>
              <button
                onClick={() => document.getElementById('csvInput').click()}
                className="inline-flex items-center px-3 py-1.5 
                  text-sm font-medium rounded-lg text-white
                  bg-gradient-to-r from-blue-600 to-blue-500 
                  hover:from-blue-500 hover:to-blue-400 
                  transition-all duration-200 shadow-sm"
              >
                <ArrowPathIcon className="w-4 h-4 mr-1.5" />
                Import Trades
              </button>
            </div>
          </div>

          {/* Filters */}
          <DraggableCard title="Filters">
            <AdvancedFilters
              trades={trades}
              activeFilters={activeFilters}
              onFilterChange={handleFilterChange}
            />
          </DraggableCard>

          {/* Grid Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {/* Performance Metrics */}
            <div className="col-span-full">
              <DraggableCard title="Performance Metrics">
                <PerformanceMetrics trades={filteredTrades} />
              </DraggableCard>
            </div>

            {/* Performance Chart */}
            <div className="col-span-full">
              <DraggableCard title="Performance Chart">
                <PerformanceVisualizations trades={filteredTrades} />
              </DraggableCard>
            </div>

            {/* Risk Heatmap */}
            <div>
              <DraggableCard title="Risk Heatmap">
                <RiskHeatmap trades={filteredTrades} />
              </DraggableCard>
            </div>

            {/* Session Analysis */}
            <div>
              <DraggableCard title="Session Analysis">
                <SessionAnalysis trades={filteredTrades} />
              </DraggableCard>
            </div>

            {/* Behavioral Patterns */}
            <div>
              <DraggableCard title="Behavioral Patterns">
                <BehavioralPatterns trades={filteredTrades} />
              </DraggableCard>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

const DateRangeSelector = ({ range, onChange }) => {
  const presets = [
    { label: '7D', days: 7 },
    { label: '30D', days: 30 },
    { label: '90D', days: 90 },
    { label: 'YTD', days: 365 }
  ];

  return (
    <div className="flex items-center space-x-4">
      <div className="flex items-center space-x-2">
        {presets.map(preset => (
          <button
            key={preset.label}
            onClick={() => onChange({
              start: subDays(new Date(), preset.days),
              end: new Date()
            })}
            className={`px-3 py-1.5 text-sm font-medium rounded-lg transition-all duration-200 ${
              preset.days === getDaysDifference(range.start, range.end)
                ? 'bg-blue-500/10 text-blue-300 border border-blue-400/20'
                : 'text-slate-400 hover:text-slate-300 hover:bg-slate-700/50'
            }`}
          >
            {preset.label}
          </button>
        ))}
      </div>
      <div className="flex items-center space-x-2">
        <input
          type="date"
          value={format(range.start, 'yyyy-MM-dd')}
          onChange={(e) => onChange({
            ...range,
            start: new Date(e.target.value)
          })}
          className="rounded-lg bg-slate-800 border-slate-700 text-slate-300 text-sm
            focus:ring-blue-500 focus:border-blue-500"
        />
        <span className="text-slate-400">to</span>
        <input
          type="date"
          value={format(range.end, 'yyyy-MM-dd')}
          onChange={(e) => onChange({
            ...range,
            end: new Date(e.target.value)
          })}
          className="rounded-lg bg-slate-800 border-slate-700 text-slate-300 text-sm
            focus:ring-blue-500 focus:border-blue-500"
        />
      </div>
    </div>
  );
};

function StatsSummary({ stats }) {
  return (
    <div className="grid grid-cols-4 gap-8">
      <StatItem
        label="Net P&L"
        value={formatCurrency(stats.netPnL)}
        change={stats.pnlChange}
        icon={<CurrencyDollarIcon className="w-5 h-5" />}
      />
      <StatItem
        label="Win Rate"
        value={`${stats.winRate.toFixed(1)}%`}
        change={stats.winRateChange}
        icon={<ChartBarIcon className="w-5 h-5" />}
      />
      <StatItem
        label="Avg Trade"
        value={formatCurrency(stats.avgTrade)}
        change={stats.avgTradeChange}
        icon={<ScaleIcon className="w-5 h-5" />}
      />
      <StatItem
        label="Profit Factor"
        value={stats.profitFactor.toFixed(2)}
        change={stats.profitFactorChange}
        icon={<ChartPieIcon className="w-5 h-5" />}
      />
    </div>
  );
}

function StatItem({ label, value, change, icon }) {
  const isPositive = change >= 0;
  const changeColor = isPositive ? 'text-green-400' : 'text-red-400';
  const bgColor = isPositive ? 'bg-green-400/10' : 'bg-red-400/10';

  return (
    <div className="flex items-center space-x-4">
      <div className={`p-2 rounded-lg ${isPositive ? 'bg-green-400/10' : 'bg-red-400/10'}`}>
        <div className={`${isPositive ? 'text-green-400' : 'text-red-400'}`}>
          {icon}
        </div>
      </div>
      <div>
        <p className="text-sm font-medium text-slate-400">{label}</p>
        <div className="flex items-baseline gap-2">
          <p className="text-lg font-bold text-slate-100">{value}</p>
          {change != null && (
            <span className={`text-sm font-medium ${changeColor}`}>
              {change >= 0 ? '↑' : '↓'} {Math.abs(change).toFixed(1)}%
            </span>
          )}
        </div>
      </div>
    </div>
  );
}

function OverviewView({ trades, stats, onImportCSV, isImporting }) {
  return (
    <div className="space-y-6">
      {/* Performance Overview */}
      <div className="grid grid-cols-1 gap-6">
        <PerformanceVisualizations 
          trades={trades}
          metrics={stats}
          onImportCSV={onImportCSV}
          isImporting={isImporting}
        />
      </div>

      {/* Detailed Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <PerformanceMetrics trades={trades} />
        <TradePatternAnalysis trades={trades} />
      </div>

      {/* Risk Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <RiskHeatmap trades={trades} />
        <SessionAnalysis trades={trades} />
        <BehavioralPatterns trades={trades} />
      </div>
    </div>
  );
}

function AnalysisView({ trades, dateRange }) {
  const [selectedMetrics, setSelectedMetrics] = useState(['performance', 'risk', 'behavioral']);
  const [comparisonPeriod, setComparisonPeriod] = useState('1M');

  return (
    <div className="space-y-6">
      {/* Filters and Controls */}
      <div className="flex justify-between items-center">
        <MetricsFilter
          selectedMetrics={selectedMetrics}
          onChange={(metric) => {
            setSelectedMetrics(prev => 
              prev.includes(metric)
                ? prev.filter(m => m !== metric)
                : [...prev, metric]
            );
          }}
        />
        <PeriodSelector value={comparisonPeriod} onChange={setComparisonPeriod} />
      </div>

      {/* Performance Comparison */}
      {selectedMetrics.includes('performance') && (
        <PerformanceComparison 
          trades={trades}
          period={comparisonPeriod}
          dateRange={dateRange}
        />
      )}

      {/* Advanced Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {selectedMetrics.includes('behavioral') && (
          <>
            <InstrumentAnalysis trades={trades} />
            <TimeOfDayAnalysis trades={trades} />
          </>
        )}
      </div>

      {/* Risk Analysis */}
      {selectedMetrics.includes('risk') && (
        <RiskAnalysis trades={trades} />
      )}

      {/* Behavioral Patterns */}
      {selectedMetrics.includes('behavioral') && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <EmotionalAnalysis trades={trades} />
          <StrategyCompliance trades={trades} />
        </div>
      )}
    </div>
  );
}

function JournalView({ trades, onUpdateTrade, onDeleteTrade }) {
  return (
    <div>
      <TradeJournal trades={trades} onUpdateTrade={onUpdateTrade} onDeleteTrade={onDeleteTrade} />
    </div>
  );
}

function InstrumentAnalysis({ trades }) {
  const stats = useMemo(() => calculateInstrumentStats(trades), [trades]);

  return (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <h3 className="text-lg font-semibold mb-4">Instrument Analysis</h3>
      <div className="space-y-4">
        {stats.map(({ instrument, trades: count, winRate, pnl, avgTrade }) => (
          <div
            key={instrument}
            className="flex items-center justify-between p-4 bg-slate-50 rounded-lg"
          >
            <div>
              <p className="font-medium">{instrument}</p>
              <p className="text-sm text-slate-500">{count} trades</p>
            </div>
            <div className="grid grid-cols-3 gap-4 text-right">
              <Metric label="Win Rate" value={`${winRate.toFixed(1)}%`} />
              <Metric label="P&L" value={formatCurrency(pnl)} />
              <Metric label="Avg Trade" value={formatCurrency(avgTrade)} />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

function TimeOfDayAnalysis({ trades }) {
  const stats = useMemo(() => calculateTimeStats(trades), [trades]);

  return (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <h3 className="text-lg font-semibold mb-4">Time Analysis</h3>
      <div className="space-y-4">
        {stats.map(({ hour, trades: count, winRate, pnl }) => (
          <div
            key={hour}
            className="flex items-center justify-between p-4 bg-slate-50 rounded-lg"
          >
            <div>
              <p className="font-medium">{formatHour(hour)}</p>
              <p className="text-sm text-slate-500">{count} trades</p>
            </div>
            <div className="grid grid-cols-2 gap-4 text-right">
              <Metric label="Win Rate" value={`${winRate.toFixed(1)}%`} />
              <Metric label="P&L" value={formatCurrency(pnl)} />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

function RiskAnalysis({ trades }) {
  const stats = useMemo(() => calculateRiskStats(trades), [trades]);

  return (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <h3 className="text-lg font-semibold mb-4">Risk Analysis</h3>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <RiskMetric
          title="Max Drawdown"
          value={`${stats.maxDrawdown.toFixed(1)}%`}
          details={[
            { label: 'Peak Equity', value: formatCurrency(stats.peakEquity) },
            { label: 'Valley Equity', value: formatCurrency(stats.valleyEquity) }
          ]}
        />
        <RiskMetric
          title="Risk per Trade"
          value={formatCurrency(stats.avgRisk)}
          details={[
            { label: 'Max Risk', value: formatCurrency(stats.maxRisk) },
            { label: 'Min Risk', value: formatCurrency(stats.minRisk) }
          ]}
        />
        <RiskMetric
          title="Risk/Reward"
          value={stats.riskRewardRatio.toFixed(2)}
          details={[
            { label: 'Avg Win', value: formatCurrency(stats.avgWin) },
            { label: 'Avg Loss', value: formatCurrency(stats.avgLoss) }
          ]}
        />
      </div>
    </div>
  );
}

function Metric({ label, value }) {
  return (
    <div>
      <p className="text-sm text-slate-500">{label}</p>
      <p className="font-medium">{value}</p>
    </div>
  );
}

function RiskMetric({ title, value, details }) {
  return (
    <div className="p-4 bg-slate-50 rounded-lg">
      <h4 className="font-medium mb-2">{title}</h4>
      <p className="text-2xl font-semibold mb-4">{value}</p>
      <div className="grid grid-cols-2 gap-4">
        {details.map(({ label, value }) => (
          <div key={label}>
            <p className="text-sm text-slate-500">{label}</p>
            <p className="text-sm font-medium">{value}</p>
          </div>
        ))}
      </div>
    </div>
  );
}

// Helper functions
function calculateStats(trades) {
  if (!trades?.length) {
    return {
      netPnL: 0,
      winRate: 0,
      avgTrade: 0,
      profitFactor: 0,
      pnlChange: 0,
      winRateChange: 0,
      avgTradeChange: 0,
      profitFactorChange: 0
    };
  }

  const winningTrades = trades.filter(t => t.profit_loss > 0);
  const netPnL = trades.reduce((sum, t) => sum + t.profit_loss, 0);
  const winRate = (winningTrades.length / trades.length) * 100;
  const avgTrade = netPnL / trades.length;

  const grossProfit = winningTrades.reduce((sum, t) => sum + t.profit_loss, 0);
  const grossLoss = Math.abs(trades.filter(t => t.profit_loss < 0).reduce((sum, t) => sum + t.profit_loss, 0));
  const profitFactor = grossLoss ? grossProfit / grossLoss : 0;

  // Calculate changes (would normally compare with previous period)
  return {
    netPnL,
    winRate,
    avgTrade,
    profitFactor,
    pnlChange: 0,
    winRateChange: 0,
    avgTradeChange: 0,
    profitFactorChange: 0
  };
}

function getDaysDifference(start, end) {
  return differenceInDays(end, start);
}

function formatHour(hour) {
  return new Date(2000, 0, 1, hour).toLocaleTimeString([], {
    hour: 'numeric',
    hour12: true
  });
}

const formatCurrency = (value) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
};

function calculateInstrumentStats(trades) {
  if (!trades?.length) return [];

  const instrumentMap = trades.reduce((acc, trade) => {
    const instrument = trade.instrument || 'Unknown';
    if (!acc[instrument]) {
      acc[instrument] = {
        instrument,
        trades: 0,
        winners: 0,
        pnl: 0,
        volume: 0
      };
    }
    acc[instrument].trades++;
    acc[instrument].pnl += trade.netPnL;
    acc[instrument].volume += Math.abs(trade.size || 0);
    if (trade.netPnL > 0) acc[instrument].winners++;
    return acc;
  }, {});

  return Object.values(instrumentMap)
    .map(stats => ({
      ...stats,
      winRate: (stats.winners / stats.trades) * 100,
      avgTrade: stats.pnl / stats.trades
    }))
    .sort((a, b) => b.pnl - a.pnl);
}

function calculateTimeStats(trades) {
  if (!trades?.length) return [];

  const hourMap = trades.reduce((acc, trade) => {
    const hour = new Date(trade.timestamp).getHours();
    if (!acc[hour]) {
      acc[hour] = {
        hour,
        trades: 0,
        winners: 0,
        pnl: 0
      };
    }
    acc[hour].trades++;
    acc[hour].pnl += trade.netPnL;
    if (trade.netPnL > 0) acc[hour].winners++;
    return acc;
  }, {});

  return Object.values(hourMap)
    .map(stats => ({
      ...stats,
      winRate: (stats.winners / stats.trades) * 100
    }))
    .sort((a, b) => a.hour - b.hour);
}

function calculateRiskStats(trades) {
  if (!trades?.length) {
    return {
      maxDrawdown: 0,
      peakEquity: 0,
      valleyEquity: 0,
      avgRisk: 0,
      maxRisk: 0,
      minRisk: 0,
      riskRewardRatio: 0,
      avgWin: 0,
      avgLoss: 0
    };
  }

  let runningTotal = 0;
  let peak = 0;
  let maxDrawdown = 0;
  let peakEquity = 0;
  let valleyEquity = runningTotal;

  const winners = trades.filter(t => t.netPnL > 0);
  const losers = trades.filter(t => t.netPnL < 0);

  trades.forEach(trade => {
    runningTotal += trade.netPnL;
    if (runningTotal > peak) {
      peak = runningTotal;
      peakEquity = runningTotal;
    } else {
      const drawdown = ((peak - runningTotal) / peak) * 100;
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
        valleyEquity = runningTotal;
      }
    }
  });

  const avgWin = winners.length ? winners.reduce((sum, t) => sum + t.netPnL, 0) / winners.length : 0;
  const avgLoss = losers.length ? Math.abs(losers.reduce((sum, t) => sum + t.netPnL, 0)) / losers.length : 0;

  return {
    maxDrawdown,
    peakEquity,
    valleyEquity,
    avgRisk: avgLoss,
    maxRisk: losers.length ? Math.abs(Math.min(...losers.map(t => t.netPnL))) : 0,
    minRisk: losers.length ? Math.abs(Math.max(...losers.map(t => t.netPnL))) : 0,
    riskRewardRatio: avgLoss ? avgWin / avgLoss : 0,
    avgWin,
    avgLoss
  };
}

// Add new components for enhanced analysis
function EmotionalAnalysis({ trades }) {
  const stats = useMemo(() => {
    if (!trades?.length) return null;

    const emotionalTrades = trades.filter(t => t.emotional_mistakes);
    const totalLoss = emotionalTrades.reduce((sum, t) => sum + (t.profit_loss < 0 ? Math.abs(t.profit_loss) : 0), 0);
    
    return {
      count: emotionalTrades.length,
      percentage: (emotionalTrades.length / trades.length) * 100,
      totalLoss,
      patterns: analyzeEmotionalPatterns(trades)
    };
  }, [trades]);

  if (!stats) return null;

  return (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <h3 className="text-lg font-semibold mb-4">Emotional Analysis</h3>
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <Metric
            label="Emotional Trades"
            value={`${stats.percentage.toFixed(1)}%`}
            subtext={`${stats.count} trades`}
          />
          <Metric
            label="Loss from Emotions"
            value={formatCurrency(stats.totalLoss)}
            subtext="Total impact"
          />
        </div>
        <div className="mt-6">
          <h4 className="text-sm font-medium mb-3">Common Patterns</h4>
          {stats.patterns.map((pattern, index) => (
            <div key={index} className="flex items-center justify-between py-2">
              <span className="text-sm text-slate-600">{pattern.name}</span>
              <span className="text-sm font-medium">{pattern.count} occurrences</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

function StrategyCompliance({ trades }) {
  const stats = useMemo(() => {
    if (!trades?.length) return null;

    const compliantTrades = trades.filter(t => t.strategy_followed);
    const profitableCompliant = compliantTrades.filter(t => t.profit_loss > 0);
    
    return {
      compliance: (compliantTrades.length / trades.length) * 100,
      winRate: (profitableCompliant.length / compliantTrades.length) * 100,
      avgProfit: compliantTrades.reduce((sum, t) => sum + t.profit_loss, 0) / compliantTrades.length,
      deviations: analyzeStrategyDeviations(trades)
    };
  }, [trades]);

  if (!stats) return null;

  return (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <h3 className="text-lg font-semibold mb-4">Strategy Compliance</h3>
      <div className="space-y-4">
        <div className="grid grid-cols-3 gap-4">
          <Metric
            label="Compliance Rate"
            value={`${stats.compliance.toFixed(1)}%`}
          />
          <Metric
            label="Win Rate (Compliant)"
            value={`${stats.winRate.toFixed(1)}%`}
          />
          <Metric
            label="Avg. Profit"
            value={formatCurrency(stats.avgProfit)}
          />
        </div>
        <div className="mt-6">
          <h4 className="text-sm font-medium mb-3">Common Deviations</h4>
          {stats.deviations.map((deviation, index) => (
            <div key={index} className="flex items-center justify-between py-2">
              <span className="text-sm text-slate-600">{deviation.type}</span>
              <span className="text-sm font-medium">{deviation.count} times</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Helper functions for analysis
function analyzeEmotionalPatterns(trades) {
  const patterns = trades
    .filter(t => t.emotional_mistakes)
    .reduce((acc, trade) => {
      const pattern = trade.emotional_pattern || 'Other';
      if (!acc[pattern]) acc[pattern] = 0;
      acc[pattern]++;
      return acc;
    }, {});

  return Object.entries(patterns)
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);
}

function analyzeStrategyDeviations(trades) {
  const deviations = trades
    .filter(t => !t.strategy_followed)
    .reduce((acc, trade) => {
      const type = trade.deviation_type || 'Unknown';
      if (!acc[type]) acc[type] = 0;
      acc[type]++;
      return acc;
    }, {});

  return Object.entries(deviations)
    .map(([type, count]) => ({ type, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);
}
 