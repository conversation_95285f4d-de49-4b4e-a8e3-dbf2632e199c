import { ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/outline';

export default function ComplianceMetricCard({ title, value, previousValue, details }) {
  const change = value - previousValue;
  const changePercent = (change / previousValue) * 100;

  return (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <h4 className="text-sm font-medium text-slate-700 mb-2">{title}</h4>
      <div className="flex items-baseline space-x-2 mb-4">
        <span className="text-2xl font-semibold">{value.toFixed(1)}%</span>
        {change !== 0 && (
          <span className={`flex items-center text-sm ${
            change > 0 ? 'text-green-600' : 'text-red-600'
          }`}>
            {change > 0 ? (
              <ArrowUpIcon className="w-4 h-4 mr-1" />
            ) : (
              <ArrowDownIcon className="w-4 h-4 mr-1" />
            )}
            {Math.abs(changePercent).toFixed(1)}%
          </span>
        )}
      </div>
      <div className="space-y-2">
        {details.map((detail, index) => (
          <div key={index} className="flex justify-between">
            <span className="text-sm text-slate-500">{detail.label}</span>
            <span className="text-sm font-medium">{detail.value}</span>
          </div>
        ))}
      </div>
    </div>
  );
} 