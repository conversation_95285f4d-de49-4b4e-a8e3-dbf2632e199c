import React, { useState, useMemo } from 'react';
import { <PERSON>, Bar, Scatter } from 'react-chartjs-2';
import { format, subDays, subMonths } from 'date-fns';
import { ChevronDownIcon } from '@heroicons/react/24/outline';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  TimeScale
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  TimeScale
);

// Define metrics outside the component
const METRICS = {
  pnl: { 
    label: 'P&L', 
    color: '#22c55e',
    format: (value) => formatCurrency(value),
    cumulative: true
  },
  drawdown: { 
    label: 'Drawdown', 
    color: '#ef4444',
    format: (value) => `${value.toFixed(2)}%`,
    cumulative: false
  },
  volume: { 
    label: 'Volume', 
    color: '#3b82f6',
    format: (value) => formatCurrency(value),
    cumulative: false
  }
};

const TIMEFRAMES = Object.freeze({
  D1: { days: 1, label: '1 Day' },
  W1: { days: 7, label: '1 Week' },
  M1: { days: 30, label: '1 Month' },
  M3: { days: 90, label: '3 Months' },
  YTD: { days: 365, label: 'Year to Date' },
  ALL: { days: 9999, label: 'All Time' }
});

export default function ChartSection({ trades }) {
  const [timeframe, setTimeframe] = useState('M1');
  const [selectedMetric, setSelectedMetric] = useState('pnl');
  const [showTimeframeMenu, setShowTimeframeMenu] = useState(false);

  const filteredTrades = useMemo(() => 
    trades.filter(trade => 
      trade.timestamp >= subDays(new Date(), TIMEFRAMES[timeframe].days)
    ).sort((a, b) => a.timestamp - b.timestamp)
  , [trades, timeframe]);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Main Chart */}
      <div className="lg:col-span-2 dashboard-card">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-4">
          <div className="w-full">
            <h3 className="card-title">Performance Analysis</h3>
            <div className="mt-2 overflow-x-auto pb-2 -mb-2">
              <div className="flex gap-2 min-w-max">
                {Object.entries(METRICS).map(([key, { label, color }]) => (
                  <button
                    key={key}
                    onClick={() => setSelectedMetric(key)}
                    className={`flex items-center gap-1.5 px-3 py-1.5 rounded-md transition-colors ${
                      selectedMetric === key 
                        ? 'bg-slate-100 font-medium' 
                        : 'text-slate-500 hover:bg-slate-50'
                    }`}
                  >
                    <span 
                      className="inline-block w-2 h-2 rounded-full"
                      style={{ backgroundColor: color }}
                    />
                    <span className="text-sm whitespace-nowrap">{label}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Mobile Timeframe Dropdown */}
          <div className="relative sm:hidden">
            <button
              onClick={() => setShowTimeframeMenu(!showTimeframeMenu)}
              className="w-full flex items-center justify-between px-3 py-2 bg-white border border-slate-200 rounded-lg text-sm font-medium text-slate-700 shadow-sm hover:bg-slate-50"
            >
              <span>{TIMEFRAMES[timeframe].label}</span>
              <ChevronDownIcon className={`w-4 h-4 text-slate-400 transition-transform ${showTimeframeMenu ? 'rotate-180' : ''}`} />
            </button>
            
            {showTimeframeMenu && (
              <div className="absolute z-10 mt-1 w-full bg-white rounded-lg shadow-lg border border-slate-200 py-1">
                {Object.entries(TIMEFRAMES).map(([key, { label }]) => (
                  <button
                    key={key}
                    onClick={() => {
                      setTimeframe(key);
                      setShowTimeframeMenu(false);
                    }}
                    className={`w-full px-4 py-2 text-left text-sm transition-colors ${
                      timeframe === key
                        ? 'bg-blue-50 text-blue-600'
                        : 'text-slate-600 hover:bg-slate-50'
                    }`}
                  >
                    {label}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Desktop Timeframe Buttons */}
          <div className="hidden sm:flex items-center">
            <div className="flex flex-wrap gap-1">
              {Object.entries(TIMEFRAMES).map(([key, { label }]) => (
                <button
                  key={key}
                  onClick={() => setTimeframe(key)}
                  className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${
                    timeframe === key
                      ? 'bg-blue-50 text-blue-600'
                      : 'text-slate-600 hover:bg-slate-100'
                  }`}
                >
                  {key}
                </button>
              ))}
            </div>
          </div>
        </div>

        <div className="h-[300px] sm:h-[400px]">
          <Line 
            data={getChartData(filteredTrades, selectedMetric)} 
            options={getChartOptions(selectedMetric)} 
          />
        </div>
      </div>
    </div>
  );
}

function getChartData(trades, metric) {
  // Handle empty trades array
  if (!trades?.length) {
    return {
      labels: [],
      datasets: [{
        label: METRICS[metric].label,
        data: [],
        borderColor: METRICS[metric].color,
        backgroundColor: `${METRICS[metric].color}20`,
        fill: true,
        tension: 0.4
      }]
    };
  }

  const dailyData = trades.reduce((acc, trade) => {
    if (!trade?.timestamp || typeof trade.profit_loss !== 'number') return acc;

    const date = format(new Date(trade.timestamp), 'yyyy-MM-dd');
    if (!acc[date]) {
      acc[date] = {
        pnl: 0,
        volume: 0,
        drawdown: 0,
        trades: 0
      };
    }

    // Ensure numeric values
    const profitLoss = Number(trade.profit_loss) || 0;
    const size = Number(trade.size) || 0;
    const entryPrice = Number(trade.entry_price) || 0;

    acc[date].pnl += profitLoss;
    acc[date].volume += Math.abs(size * entryPrice);
    acc[date].trades += 1;
    return acc;
  }, {});

  const dates = Object.keys(dailyData).sort();
  let runningPnL = 0;
  let maxPnL = 0;
  let maxDrawdown = 0;

  dates.forEach(date => {
    const dayPnL = dailyData[date].pnl;
    
    if (METRICS[metric].cumulative) {
      runningPnL += dayPnL;
      dailyData[date][metric] = runningPnL;
    } else {
      // For non-cumulative metrics, use the daily value
      dailyData[date][metric] = metric === 'drawdown' 
        ? dailyData[date].drawdown 
        : dayPnL;
    }
    
    // Calculate drawdown only when we have positive peak
    if (runningPnL > maxPnL) {
      maxPnL = runningPnL;
    }
    
    // Prevent division by zero and negative drawdown
    if (maxPnL > 0) {
      const currentDrawdown = ((maxPnL - runningPnL) / maxPnL) * 100;
      maxDrawdown = Math.max(maxDrawdown, currentDrawdown);
      dailyData[date].drawdown = currentDrawdown;
    } else {
      dailyData[date].drawdown = 0;
    }
  });

  return {
    labels: dates.map(date => format(new Date(date), 'MMM d')),
    datasets: [{
      label: METRICS[metric].label,
      data: dates.map(date => dailyData[date][metric] || 0),
      borderColor: METRICS[metric].color,
      backgroundColor: `${METRICS[metric].color}20`,
      fill: true,
      tension: 0.4
    }]
  };
}

function getChartOptions(metric) {
  return {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        callbacks: {
          label: (context) => {
            const value = context.raw;
            return `${context.dataset.label}: ${METRICS[metric].format(value)}`;
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        }
      },
      y: {
        grid: {
          color: '#f3f4f6'
        },
        ticks: {
          callback: (value) => METRICS[metric].format(value)
        }
      }
    }
  };
}

function formatCurrency(value) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0
  }).format(value);
}