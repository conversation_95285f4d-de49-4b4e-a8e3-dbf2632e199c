import { ArrowTrendingUpIcon, ArrowTrendingDownIcon } from '@heroicons/react/24/outline';

export default function ComparisonInsight({ title, current, previous }) {
  const metrics = getMetricsForTitle(title, current, previous);
  
  return (
    <div className="p-4 bg-slate-50 rounded-lg">
      <h4 className="font-medium mb-3">{title}</h4>
      <div className="space-y-3">
        {metrics.map((metric, index) => (
          <MetricComparison key={index} {...metric} />
        ))}
      </div>
    </div>
  );
}

function MetricComparison({ label, current, previous }) {
  const change = current - previous;
  const isPositive = change > 0;

  return (
    <div className="flex items-center justify-between">
      <span className="text-sm text-slate-600">{label}</span>
      <div className="flex items-center space-x-2">
        <span className="text-sm font-medium">{current.toFixed(1)}%</span>
        {change !== 0 && (
          <div className={`flex items-center text-sm ${
            isPositive ? 'text-green-600' : 'text-red-600'
          }`}>
            {isPositive ? (
              <ArrowTrendingUpIcon className="w-4 h-4 mr-1" />
            ) : (
              <ArrowTrendingDownIcon className="w-4 h-4 mr-1" />
            )}
            {Math.abs(change).toFixed(1)}%
          </div>
        )}
      </div>
    </div>
  );
}

function getMetricsForTitle(title, current, previous) {
  switch (title) {
    case 'Risk Management':
      return [
        { label: 'Stop Loss Usage', current: current.stopLossUsage, previous: previous.stopLossUsage },
        { label: 'Position Sizing', current: current.positionSizeCompliance, previous: previous.positionSizeCompliance }
      ];
    case 'Trading Discipline':
      return [
        { label: 'Plan Adherence', current: current.planAdherence, previous: previous.planAdherence },
        { label: 'Rule Compliance', current: current.disciplineScore, previous: previous.disciplineScore }
      ];
    case 'Overall Compliance':
      return [
        { label: 'Compliance Score', current: current.overallScore, previous: previous.overallScore },
        { label: 'Risk/Reward', current: current.rrCompliance, previous: previous.rrCompliance }
      ];
    default:
      return [];
  }
} 