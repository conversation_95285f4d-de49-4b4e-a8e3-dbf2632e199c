import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

export default function TradeDistribution({ trades }) {
  // <PERSON>le empty trades array
  if (!trades?.length) {
    return null;
  }

  function getDistributionData() {
    // Calculate dynamic ranges based on actual trade data
    const validTrades = trades.filter(t => typeof t?.profit_loss === 'number');
    const profits = validTrades.map(t => Number(t.profit_loss));
    const maxProfit = Math.max(...profits, 0);
    const maxLoss = Math.abs(Math.min(...profits, 0));
    const step = Math.max(Math.ceil((maxProfit + maxLoss) / 8 / 100) * 100, 100);

    const ranges = [
      { min: -Infinity, max: -step * 4, label: `< -$${step * 4}` },
      { min: -step * 4, max: -step * 2, label: `-$${step * 4} to -$${step * 2}` },
      { min: -step * 2, max: -step, label: `-$${step * 2} to -$${step}` },
      { min: -step, max: 0, label: `-$${step} to $0` },
      { min: 0, max: step, label: `$0 to $${step}` },
      { min: step, max: step * 2, label: `$${step} to $${step * 2}` },
      { min: step * 2, max: step * 4, label: `$${step * 2} to $${step * 4}` },
      { min: step * 4, max: Infinity, label: `> $${step * 4}` }
    ];

    const distribution = ranges.map(range => ({
      range: range.label,
      count: validTrades.filter(t => 
        t.profit_loss > range.min && 
        t.profit_loss <= range.max
      ).length
    }));

    return {
      labels: distribution.map(d => d.range),
      datasets: [{
        data: distribution.map(d => d.count),
        backgroundColor: distribution.map(d => 
          d.range.includes('-') ? '#ef444480' : '#22c55e80'
        )
      }]
    };
  }

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        }
      },
      y: {
        beginAtZero: true,
        grid: {
          color: '#f3f4f6'
        }
      }
    }
  };

  return (
    <div className="h-[200px]">
      <h3 className="text-sm font-medium text-slate-700 mb-4">Trade Distribution</h3>
      <Bar data={getDistributionData()} options={options} />
    </div>
  );
} 