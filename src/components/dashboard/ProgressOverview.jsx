import React, { useState, useMemo, useCallback } from 'react';
import { Line } from 'react-chartjs-2';
import { format, subDays, startOfDay, endOfDay, isToday, isThisWeek, isThisMonth, isThisYear, differenceInDays } from 'date-fns';
import {
  ChartBarSquareIcon,
  ArrowTrendingUpIcon,
  CurrencyDollarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ScaleIcon,
  BanknotesIcon,
  ClockIcon,
  ChartPieIcon,
  CalendarIcon,
  TagIcon,
  FireIcon,
  AdjustmentsHorizontalIcon,
  FunnelIcon,
  SparklesIcon,
  BoltIcon,
  ChartBarIcon,
  BeakerIcon,
  TrophyIcon,
  InformationCircleIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowTrendingDownIcon
} from '@heroicons/react/24/outline';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  TimeScale,
  Legend,
  Filler
} from 'chart.js';
import annotationPlugin from 'chartjs-plugin-annotation';
import 'chartjs-adapter-date-fns';
import { enUS } from 'date-fns/locale';
import { TradeAnalytics } from '../../utils/analytics';
import { formatDecimal, formatCurrency } from '../../utils/formatters';

// Register ChartJS plugins
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  TimeScale,
  Legend,
  Filler,
  annotationPlugin
);

const TIME_PERIODS = [
  { id: '1D', label: '1D', days: 1, checker: isToday },
  { id: '1W', label: '1W', days: 7, checker: isThisWeek },
  { id: '1M', label: '1M', days: 30, checker: isThisMonth },
  { id: '3M', label: '3M', days: 90 },
  { id: '1Y', label: '1Y', days: 365, checker: isThisYear },
  { id: 'ALL', label: 'ALL', days: null },
  { id: 'CUSTOM', label: 'Custom', days: null }
];

const METRICS = {
  cumulative: {
    label: 'Cumulative P&L',
    color: '#16a34a',
    formatter: (value) => formatCurrency(value)
  },
  drawdown: {
    label: 'Drawdown',
    color: '#ef4444',
    formatter: (value) => `${value.toFixed(2)}%`
  },
  equity: {
    label: 'Equity Curve',
    color: '#0ea5e9',
    formatter: (value) => formatCurrency(value)
  },
  volatility: {
    label: 'Volatility',
    color: '#8b5cf6',
    formatter: (value) => `${value.toFixed(2)}%`
  },
  riskAdjusted: {
    label: 'Risk-Adjusted Return',
    color: '#6366f1',
    formatter: (value) => `${value.toFixed(2)}%`
  },
  calmar: {
    label: 'Calmar Ratio',
    color: '#f59e0b',
    formatter: (value) => value.toFixed(2)
  }
};

// Add chart configuration constants
const CHART_COLORS = {
  cumulative: {
    line: '#16a34a',  // green-600
    fill: 'rgba(22, 163, 74, 0.08)',
    hover: '#15803d'  // green-700
  },
  drawdown: {
    line: '#ef4444',  // red-500
    fill: 'rgba(239, 68, 68, 0.08)',
    hover: '#dc2626'  // red-600
  },
  equity: {
    line: '#0ea5e9',  // sky-500
    fill: 'rgba(14, 165, 233, 0.08)',
    hover: '#0284c7'  // sky-600
  },
  volatility: {
    line: '#8b5cf6',  // violet-500
    fill: 'rgba(139, 92, 246, 0.08)',
    hover: '#7c3aed'  // violet-600
  },
  riskAdjusted: {
    line: '#6366f1',  // indigo-500
    fill: 'rgba(99, 102, 241, 0.08)',
    hover: '#4f46e5'  // indigo-600
  },
  calmar: {
    line: '#f59e0b',  // amber-500
    fill: 'rgba(245, 158, 11, 0.08)',
    hover: '#d97706'  // amber-600
  }
};

// Add getMetricIcon function
const getMetricIcon = (value) => {
  if (value > 0) return <ArrowUpIcon className="w-4 h-4 text-green-500" />;
  if (value < 0) return <ArrowDownIcon className="w-4 h-4 text-red-500" />;
  return null;
};

// Add MetricCard component
function MetricCard({ title, value, change, target, reverseIndicator = false, icon }) {
  return (
    <div className="p-4 rounded-lg bg-gradient-to-br from-slate-50 to-white border border-slate-200/75 shadow-sm">
      <div className="flex items-center space-x-2 mb-2">
        <div className="p-2 rounded-lg bg-gradient-to-br from-slate-100 to-white">
          {icon}
        </div>
        <div className="text-sm font-medium text-slate-700">{title}</div>
      </div>
      <div className="text-lg font-semibold text-slate-900">{value}</div>
      <div className="text-xs text-slate-500 mt-1">Target: {target}</div>
    </div>
  );
}

export default function ProgressOverview({ trades, dateRange }) {
  const [selectedPeriod, setSelectedPeriod] = useState('1M');
  const [selectedMetric, setSelectedMetric] = useState('cumulative');
  const [showAnnotations, setShowAnnotations] = useState(true);
  const [showAdvancedMetrics, setShowAdvancedMetrics] = useState(false);
  const [showPatterns, setShowPatterns] = useState(false);
  const [customDateRange, setCustomDateRange] = useState({ start: null, end: null });

  const analytics = useMemo(() => {
    console.log('📊 ProgressOverview - Creating TradeAnalytics with trades:', trades?.length || 0);
    if (trades && trades.length > 0) {
      console.log('📊 ProgressOverview - Sample trade:', {
        id: trades[0].id,
        instrument: trades[0].instrument,
        profit_loss: trades[0].profit_loss,
        timestamp: trades[0].timestamp
      });
    }

    try {
      const analytics = new TradeAnalytics(trades || []);
      console.log('📊 ProgressOverview - Analytics summary:', analytics.summary);
      return analytics;
    } catch (error) {
      console.error('📊 ProgressOverview - Error calculating metrics:', error);
      return null;
    }
  }, [trades]);

  // Use analytics for metrics
  const metrics = useMemo(() => {
    if (!analytics) return null;

    // Add safety checks for all metrics
    const summary = analytics.summary || {};
    const volatility = analytics.volatility || {};

    return {
      winRate: summary.winRate || 0,
      profitFactor: summary.profitFactor || 0,
      expectancy: summary.expectancy || 0,
      maxDrawdown: summary.maxDrawdown || 0,
      sharpeRatio: volatility.sharpeRatio || 0,
      dailyWinRate: summary.profitableDays && summary.tradingDays ? 
        (summary.profitableDays / summary.tradingDays * 100) : 0,
      avgTrade: summary.avgTrade || 0,
      avgWin: summary.avgWin || 0,
      avgLoss: summary.avgLoss || 0,
      netPnL: summary.netPnL || 0,
      grossProfit: summary.grossProfit || 0,
      grossLoss: summary.grossLoss || 0,
      totalTrades: summary.totalTrades || 0,
      winningTrades: summary.winningTrades || 0,
      losingTrades: summary.losingTrades || 0,
      riskMetrics: calculateRiskAdjustedMetrics(trades)
    };
  }, [analytics, trades]);

  // Prepare chart data
  const chartData = useMemo(() => {
    if (!trades?.length) return null;

    // Sort trades by timestamp to ensure proper chronological order
    const sortedTrades = [...trades].sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    console.log('📊 ProgressOverview chartData - Processing trades:', {
      totalTrades: sortedTrades.length,
      dateRangeActive: !!(dateRange?.start && dateRange?.end),
      firstTradeDate: sortedTrades[0]?.timestamp,
      lastTradeDate: sortedTrades[sortedTrades.length - 1]?.timestamp,
      originalOrder: trades.slice(0, 3).map(t => t.timestamp),
      sortedOrder: sortedTrades.slice(0, 3).map(t => t.timestamp)
    });

    let cumulativePnL = 0;
    let maxEquity = 0;
    let currentDrawdown = 0;

    const data = sortedTrades.map(trade => {
      const pnl = trade.profit_loss || 0;
      cumulativePnL += pnl;
      maxEquity = Math.max(maxEquity, cumulativePnL);
      currentDrawdown = ((maxEquity - cumulativePnL) / maxEquity) * 100;

      // Calculate volatility using a 20-period rolling window
      const recentTrades = sortedTrades.slice(Math.max(0, sortedTrades.indexOf(trade) - 19), sortedTrades.indexOf(trade) + 1);
      const returns = recentTrades.map(t => t.profit_loss || 0);
      const volatility = Math.std(returns) * Math.sqrt(252) * 100; // Annualized volatility

          return {
        timestamp: new Date(trade.timestamp),
        cumulative: cumulativePnL,
        drawdown: currentDrawdown,
        equity: cumulativePnL + (trade.initial_capital || 0),
            volatility: volatility,
        riskAdjusted: (cumulativePnL / (Math.max(currentDrawdown, 1) || 1)) * 100,
        calmar: (cumulativePnL / (Math.max(maxEquity - cumulativePnL, 1) || 1))
      };
    });

    return data;
  }, [trades]);

  // Use chart data directly since trades are already filtered by main date range
  // Only apply internal period filter if no main date range is set
  const filteredChartData = useMemo(() => {
    if (!chartData) return null;

    // If main date range is active, use the already filtered data
    if (dateRange?.start && dateRange?.end) {
      console.log('📊 ProgressOverview - Using main date filter, chart points:', chartData.length);
      return chartData;
    }

    // Otherwise, apply internal period filtering as fallback
    const now = new Date();
    const period = TIME_PERIODS.find(p => p.id === selectedPeriod);

    if (period?.days) {
      const startDate = subDays(now, period.days);
      const filtered = chartData.filter(d => d.timestamp >= startDate);
      console.log('📊 ProgressOverview - Using internal period filter:', {
        period: selectedPeriod,
        originalPoints: chartData.length,
        filteredPoints: filtered.length
      });
      return filtered;
    }

    console.log('📊 ProgressOverview - Using all chart data:', chartData.length);
    return chartData;
  }, [chartData, selectedPeriod, dateRange]);

  // Early return if no metrics
  if (!metrics) {
    return null;
  }

    return (
      <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-br from-blue-500/10 to-blue-500/5 rounded-lg">
            <ChartBarIcon className="w-6 h-6 text-blue-500" />
        </div>
              <div>
            <h3 className="text-lg font-semibold text-slate-900 tracking-wide">OVERALL PERFORMANCE</h3>
            <p className="text-sm text-slate-500">Key performance metrics and trends</p>
      </div>
          </div>
          
          <div className="flex items-center gap-2">
            {/* Show date filter status */}
            {dateRange?.start && dateRange?.end && (
              <div className="px-3 py-1.5 text-sm font-medium bg-blue-100 text-blue-800 rounded-lg border border-blue-200">
                📅 Date Filtered
              </div>
            )}

            {/* Only show period buttons if no main date filter is active */}
            {(!dateRange?.start || !dateRange?.end) && TIME_PERIODS.map(period => (
              <button
                key={period.id}
                onClick={() => setSelectedPeriod(period.id)}
                className={`px-3 py-1.5 text-sm font-medium rounded-lg transition-colors
                  ${selectedPeriod === period.id
                  ? 'bg-slate-100 text-slate-900'
                    : 'text-slate-600 hover:bg-slate-50'
                  }`}
              >
                {period.label}
              </button>
            ))}
          </div>
        </div>

      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <MetricCard
          title="Win Rate"
          value={`${formatDecimal(metrics.winRate)}%`}
          change={getMetricIcon(metrics.winRate - 55)}
          target="55%"
        />
        <MetricCard
          title="Profit Factor"
          value={formatDecimal(metrics.profitFactor)}
          change={getMetricIcon(metrics.profitFactor - 1.5)}
          target="1.5"
        />
        <MetricCard
          title="Max Drawdown"
          value={`${formatDecimal(metrics.maxDrawdown)}%`}
          change={getMetricIcon(10 - metrics.maxDrawdown)}
          target="10%"
          reverseIndicator
        />
        <MetricCard
          title="Sharpe Ratio"
          value={formatDecimal(metrics.sharpeRatio)}
          change={getMetricIcon(metrics.sharpeRatio - 1.5)}
          target="1.5"
        />
          </div>
          
        <div className="grid grid-cols-5 gap-4 mb-6">
        <div className="p-3 rounded-lg bg-gradient-to-br from-slate-50/50 to-white border border-slate-100">
          <div className="flex items-center gap-1.5 text-xs font-medium text-slate-600">
            <BanknotesIcon className="w-3.5 h-3.5 text-emerald-500" />
              Avg Win/Loss
            </div>
          <div className="mt-2 flex items-center justify-between">
            <span className="text-sm font-medium text-green-500">
              {metrics.avgWin ? formatCurrency(metrics.avgWin) : '$0'}
              </span>
              <span className="text-xs text-slate-400">/</span>
              <span className="text-sm font-medium text-red-600">
              {metrics.avgLoss ? formatCurrency(Math.abs(metrics.avgLoss)) : '$0'}
              </span>
            </div>
          </div>

        <div className="p-3 rounded-lg bg-gradient-to-br from-slate-50/50 to-white border border-slate-100">
          <div className="flex items-center gap-1.5 text-xs font-medium text-slate-600">
            <ChartPieIcon className="w-3.5 h-3.5 text-purple-500" />
            Trade Count
            </div>
          <div className="mt-2">
            <span className="text-sm font-medium text-slate-800">
              {metrics.totalTrades.toLocaleString()}
              </span>
            <span className="ml-1 text-xs text-slate-500">trades</span>
            </div>
          </div>

        <div className="p-3 rounded-lg bg-gradient-to-br from-slate-50/50 to-white border border-slate-100">
          <div className="flex items-center gap-1.5 text-xs font-medium text-slate-600">
            <ArrowTrendingUpIcon className="w-3.5 h-3.5 text-indigo-500" />
            Net P&L
            </div>
          <div className="mt-2">
            <span className={`text-sm font-medium ${metrics.netPnL >= 0 ? 'text-green-500' : 'text-red-600'}`}>
              {formatCurrency(metrics.netPnL)}
              </span>
            </div>
          </div>

        <div className="p-3 rounded-lg bg-gradient-to-br from-slate-50/50 to-white border border-slate-100">
          <div className="flex items-center gap-1.5 text-xs font-medium text-slate-600">
            <ScaleIcon className="w-3.5 h-3.5 text-blue-500" />
            Daily Win Rate
            </div>
          <div className="mt-2">
            <span className="text-sm font-medium text-slate-800">
              {metrics.dailyWinRate ? metrics.dailyWinRate.toFixed(1) : '0.0'}%
              </span>
            </div>
          </div>

        <div className="p-3 rounded-lg bg-gradient-to-br from-slate-50/50 to-white border border-slate-100">
          <div className="flex items-center gap-1.5 text-xs font-medium text-slate-600">
            <ArrowTrendingUpIcon className="w-3.5 h-3.5 text-indigo-500" />
            Average Trade
            </div>
          <div className="mt-2">
            <span className={`text-sm font-medium ${metrics.avgTrade >= 0 ? 'text-green-500' : 'text-red-600'}`}>
              {metrics.avgTrade ? formatCurrency(metrics.avgTrade) : '$0'}
              </span>
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between gap-2 mb-4">
          <div className="flex items-center gap-2">
            {Object.entries(METRICS).map(([key, { label, color }]) => (
              <button
                key={key}
                onClick={() => setSelectedMetric(key)}
                className={`inline-flex items-center gap-2 px-3 py-1.5 rounded-lg text-sm font-medium transition-colors
                  ${selectedMetric === key
                    ? 'bg-slate-100 text-slate-900'
                    : 'text-slate-600 hover:bg-slate-50'
                  }`}
              >
                <span className="w-2 h-2 rounded-full" style={{ backgroundColor: color }} />
                {label}
              </button>
            ))}
          </div>

          <button
            onClick={() => setShowAnnotations(!showAnnotations)}
            className={`px-3 py-1.5 text-sm font-medium rounded-lg transition-colors
              ${showAnnotations
              ? 'bg-slate-100 text-slate-900'
                : 'text-slate-600 hover:bg-slate-50'
              }`}
          >
            Annotations
          </button>
        </div>

        <div className="h-[400px]">
        {filteredChartData && (
          <Line
            data={{
              labels: filteredChartData.map(d => d.timestamp),
              datasets: [{
                label: METRICS[selectedMetric].label,
                data: filteredChartData.map(d => d[selectedMetric]),
                borderColor: CHART_COLORS[selectedMetric].line,
                backgroundColor: context => {
                  const chart = context.chart;
                  const { ctx, chartArea } = chart;
                  if (!chartArea) return null;
                  
                  const gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
                  gradient.addColorStop(0, 'rgba(255, 255, 255, 0)');
                  gradient.addColorStop(1, CHART_COLORS[selectedMetric].fill);
                  return gradient;
                },
                fill: true,
                tension: 0.35,
                borderWidth: 2.5,
                pointRadius: 0,
                pointHitRadius: 20,
                pointHoverRadius: 6,
                pointHoverBackgroundColor: CHART_COLORS[selectedMetric].hover,
                pointHoverBorderColor: 'white',
                pointHoverBorderWidth: 2
              }]
            }}
            options={{
              responsive: true,
              maintainAspectRatio: false,
              interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
              },
              plugins: {
                legend: {
                  display: false
                },
                tooltip: {
                  backgroundColor: 'white',
                  titleColor: '#1f2937',
                  bodyColor: '#1f2937',
                  borderColor: '#e5e7eb',
                  borderWidth: 1,
                  padding: 10,
                  displayColors: false,
                  callbacks: {
                    label: function(context) {
                      return METRICS[selectedMetric].formatter(context.raw);
                    }
                  }
                },
                ...(showAnnotations && filteredChartData?.length ? {
                  annotation: {
                    annotations: {
                      breakeven: {
                        type: 'line',
                        yMin: 0,
                        yMax: 0,
                        borderColor: '#94a3b8',
                        borderWidth: 1,
                        borderDash: [4, 4],
                        label: {
                          content: 'Break Even',
                          display: true,
                          position: 'start',
                          backgroundColor: 'white',
                          color: '#64748b',
                          padding: 4,
                          font: {
                            size: 11
                          }
                        }
                      },
                      ...(filteredChartData?.length > 0 && filteredChartData.some(d => d[selectedMetric] !== undefined) && {
                        peak: {
                          type: 'point',
                          xValue: (() => {
                            const maxValue = Math.max(...filteredChartData.map(d => d[selectedMetric] || -Infinity));
                            const maxPoint = filteredChartData.find(d => d[selectedMetric] === maxValue);
                            return maxPoint?.timestamp;
                          })(),
                          yValue: Math.max(...filteredChartData.map(d => d[selectedMetric] || -Infinity)),
                          backgroundColor: '#22c55e',
                          radius: 4,
                          label: {
                            content: 'Peak',
                            display: true,
                            position: 'top',
                            backgroundColor: 'white',
                            color: '#64748b',
                            padding: 4,
                            font: {
                              size: 11
                            }
                          }
                        },
                        bottom: {
                          type: 'point',
                          xValue: (() => {
                            const minValue = Math.min(...filteredChartData.map(d => d[selectedMetric] || Infinity));
                            const minPoint = filteredChartData.find(d => d[selectedMetric] === minValue);
                            return minPoint?.timestamp;
                          })(),
                          yValue: Math.min(...filteredChartData.map(d => d[selectedMetric] || Infinity)),
                          backgroundColor: '#ef4444',
                          radius: 4,
                          label: {
                            content: 'Bottom',
                            display: true,
                            position: 'bottom',
                            backgroundColor: 'white',
                            color: '#64748b',
                            padding: 4,
                            font: {
                              size: 11
                            }
                          }
                        }
                      })
                    }
                  }
                } : {})
              },
              scales: {
                x: {
                  type: 'time',
                  time: {
                    unit: selectedPeriod === '1D' ? 'hour' : 'day',
                    displayFormats: {
                      hour: 'HH:mm',
                      day: 'MMM d'
                    }
                  },
                  grid: {
                    display: false
                  },
                  ticks: {
                    maxRotation: 0,
                    color: '#64748b',
                    font: {
                      size: 11
                    }
                  }
                },
                y: {
                  grid: {
                    color: 'rgba(226, 232, 240, 0.5)'
                  },
                  ticks: {
                    callback: value => METRICS[selectedMetric].formatter(value),
                    color: '#64748b',
                    font: {
                      size: 11
                    }
                  }
                }
              }
            }}
          />
        )}
        </div>

      {/* Advanced Metrics Section */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setShowAdvancedMetrics(!showAdvancedMetrics)}
            className={`flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-lg transition-colors
              ${showAdvancedMetrics ? 'bg-blue-50 text-blue-600' : 'hover:bg-slate-50 text-slate-600'}`}
          >
            <AdjustmentsHorizontalIcon className="w-4 h-4" />
            Advanced Metrics
          </button>
          <button
            onClick={() => setShowPatterns(!showPatterns)}
            className={`flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-lg transition-colors
              ${showPatterns ? 'bg-blue-50 text-blue-600' : 'hover:bg-slate-50 text-slate-600'}`}
          >
            <SparklesIcon className="w-4 h-4" />
            Patterns
          </button>
        </div>
      </div>

      {/* Advanced Metrics Content */}
      {showAdvancedMetrics && (
        <div className="mb-6 space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <MetricCard
              title="Risk-Adjusted Return"
              value={formatDecimal(metrics?.riskMetrics?.riskAdjustedReturn || 0)}
              target="2.0+"
              icon={<ScaleIcon className="w-5 h-5 text-indigo-500" />}
            />
            <MetricCard
              title="Calmar Ratio"
              value={formatDecimal(metrics?.riskMetrics?.calmarRatio || 0)}
              target="3.0+"
              icon={<ChartBarSquareIcon className="w-5 h-5 text-amber-500" />}
            />
            <MetricCard
              title="Volatility"
              value={`${formatDecimal(metrics?.riskMetrics?.volatility || 0)}%`}
              target="< 15%"
              reverseIndicator
              icon={<BoltIcon className="w-5 h-5 text-purple-500" />}
            />
          </div>
        </div>
      )}

      {/* Patterns Content */}
      {showPatterns && (
        <div className="mb-6 space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Momentum Pattern */}
            <div className="p-4 rounded-lg bg-gradient-to-br from-slate-50 to-white border border-slate-200/75 shadow-sm">
              <div className="flex items-center gap-2 mb-3">
                <div className="p-2 rounded-lg bg-gradient-to-br from-green-50 to-white">
                  <ArrowTrendingUpIcon className="w-5 h-5 text-green-500" />
                </div>
                <h3 className="text-sm font-medium text-slate-700">Momentum</h3>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Win Streak</span>
                  <span className="text-sm font-medium text-slate-700">{metrics?.winStreak || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Consistency</span>
                  <span className="text-sm font-medium text-slate-700">{formatDecimal(metrics?.riskMetrics?.consistency || 0)}%</span>
                </div>
              </div>
            </div>

            {/* Reversal Pattern */}
            <div className="p-4 rounded-lg bg-gradient-to-br from-slate-50 to-white border border-slate-200/75 shadow-sm">
              <div className="flex items-center gap-2 mb-3">
                <div className="p-2 rounded-lg bg-gradient-to-br from-amber-50 to-white">
                  <ArrowTrendingDownIcon className="w-5 h-5 text-amber-500" />
                </div>
                <h3 className="text-sm font-medium text-slate-700">Reversals</h3>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Recovery Rate</span>
                  <span className="text-sm font-medium text-slate-700">{formatDecimal(metrics?.riskMetrics?.recoveryRate || 0)}%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Bounce Back</span>
                  <span className="text-sm font-medium text-slate-700">{formatDecimal(metrics?.riskMetrics?.bounceBack || 0)}%</span>
                </div>
              </div>
            </div>

            {/* Volatility Pattern */}
            <div className="p-4 rounded-lg bg-gradient-to-br from-slate-50 to-white border border-slate-200/75 shadow-sm">
              <div className="flex items-center gap-2 mb-3">
                <div className="p-2 rounded-lg bg-gradient-to-br from-purple-50 to-white">
                  <BoltIcon className="w-5 h-5 text-purple-500" />
                </div>
                <h3 className="text-sm font-medium text-slate-700">Volatility</h3>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Daily Range</span>
                  <span className="text-sm font-medium text-slate-700">{formatDecimal(metrics?.riskMetrics?.dailyRange || 0)}%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Stability</span>
                  <span className="text-sm font-medium text-slate-700">{formatDecimal(metrics?.riskMetrics?.stability || 0)}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Add Math helper functions
Math.std = function(arr) {
  if (!arr?.length) return 0;
  const mean = arr.reduce((a, b) => a + b, 0) / arr.length;
  return Math.sqrt(
    arr.map(x => Math.pow(x - mean, 2)).reduce((a, b) => a + b, 0) / arr.length
  );
};

Math.mean = function(arr) {
  if (!arr?.length) return 0;
  return arr.reduce((a, b) => a + b, 0) / arr.length;
};

// Helper functions
function calculateStreak(trades) {
  if (!trades.length) return 0;
  
  let streak = 0;
  const lastTrade = trades[trades.length - 1];
  const isWinning = lastTrade.profit_loss > 0;

  for (let i = trades.length - 1; i >= 0; i--) {
    if ((trades[i].profit_loss > 0) === isWinning) {
      streak += isWinning ? 1 : -1;
    } else {
      break;
    }
  }
  
  return streak;
}

function calculateRiskAdjustedMetrics(trades) {
  if (!trades?.length) {
    return {
      sortinoRatio: 0,
      calmarRatio: 0,
      riskOfRuin: 0,
      profitFactor: 0
    };
  }

  const returns = trades.map(t => t.profit_loss || 0);
  const downside = returns.filter(r => r < 0);
  const upside = returns.filter(r => r > 0);
  
  const avgReturn = Math.mean(returns);
  const downsideDeviation = Math.std(downside);
  const upsideDeviation = Math.std(upside);
  
  const sortinoRatio = downsideDeviation === 0 ? 0 : avgReturn / downsideDeviation;
  const calmarRatio = Math.abs(avgReturn / (Math.min(...returns) || 1));
  
  const winRate = upside.length / returns.length;
  const avgWin = Math.mean(upside);
  const avgLoss = Math.abs(Math.mean(downside));
  const riskOfRuin = avgLoss === 0 ? 0 : Math.pow(1 - winRate, Math.ceil(1 / (avgLoss / (avgWin || 1)))) * 100;
  
  const profitFactor = downside.length === 0 
    ? upside.reduce((a, b) => a + b, 0) 
    : Math.abs(upside.reduce((a, b) => a + b, 0) / downside.reduce((a, b) => a + b, -1));

  return {
    sortinoRatio,
    calmarRatio,
    riskOfRuin,
    profitFactor
  };
}

function calculateSMA(data, period) {
  if (!data?.length || period <= 0) return [];
  const sma = [];
  for (let i = period - 1; i < data.length; i++) {
    const avg = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period;
    sma.push(avg);
  }
  return sma;
}

function calculateMACD(prices, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {
  if (!prices?.length) return { signal: 0, histogram: 0 };

  const fastEMA = calculateEMA(prices, fastPeriod);
  const slowEMA = calculateEMA(prices, slowPeriod);
  const macdLine = fastEMA - slowEMA;
  const signalLine = calculateEMA([macdLine], signalPeriod);
  
    return {
    signal: signalLine,
    histogram: macdLine - signalLine
  };
}

function calculateEMA(data, period) {
  if (!data?.length) return 0;
  
  const multiplier = 2 / (period + 1);
  let ema = data[0];
  
  for (let i = 1; i < data.length; i++) {
    ema = (data[i] - ema) * multiplier + ema;
  }
  
  return ema;
}

function calculateTimeInMarket(trades) {
  if (!trades?.length) return 0;
  const totalTime = trades.reduce((total, trade) => {
    const entryTime = new Date(trade.entryTime);
    const exitTime = new Date(trade.exitTime);
    return total + (exitTime - entryTime);
  }, 0);
  return (totalTime / (1000 * 60 * 60)).toFixed(1); // Convert to hours
}

function calculateMostProfitableInstruments(trades) {
  if (!trades?.length) return [];
  const instrumentPnL = trades.reduce((acc, trade) => {
    const symbol = trade.symbol;
    if (!acc[symbol]) {
      acc[symbol] = {
        totalPnL: 0,
        tradeCount: 0,
        winCount: 0
      };
    }
    acc[symbol].totalPnL += trade.profit_loss || 0;
    acc[symbol].tradeCount++;
    if (trade.profit_loss > 0) acc[symbol].winCount++;
    return acc;
  }, {});

  return Object.entries(instrumentPnL)
    .map(([symbol, stats]) => ({
      symbol,
      totalPnL: stats.totalPnL,
      tradeCount: stats.tradeCount,
      winRate: (stats.winCount / stats.tradeCount * 100).toFixed(1)
    }))
    .sort((a, b) => b.totalPnL - a.totalPnL)
    .slice(0, 5);
}

function calculateBestTimeOfDay(trades) {
  if (!trades?.length) return null;
  const hourlyPnL = trades.reduce((acc, trade) => {
    const hour = new Date(trade.timestamp).getHours();
    if (!acc[hour]) acc[hour] = 0;
    acc[hour] += trade.profit_loss || 0;
    return acc;
  }, {});

  const bestHour = Object.entries(hourlyPnL)
    .sort(([,a], [,b]) => b - a)[0][0];
  
  return `${bestHour.padStart(2, '0')}:00`;
}

function calculatePeriodComparison(trades, comparisonPeriod) {
  if (!trades?.length || !comparisonPeriod) return null;

  const currentPeriod = trades.slice(-Math.floor(trades.length / 2));
  const previousPeriod = trades.slice(0, Math.floor(trades.length / 2));

  const currentStats = {
    pnl: currentPeriod.reduce((sum, t) => sum + (t.profit_loss || 0), 0),
    winRate: (currentPeriod.filter(t => t.profit_loss > 0).length / currentPeriod.length * 100),
    volume: currentPeriod.length
  };

  const previousStats = {
    pnl: previousPeriod.reduce((sum, t) => sum + (t.profit_loss || 0), 0),
    winRate: (previousPeriod.filter(t => t.profit_loss > 0).length / previousPeriod.length * 100),
    volume: previousPeriod.length
  };

  return {
    pnlDiff: currentStats.pnl - previousStats.pnl,
    winRateChange: currentStats.winRate - previousStats.winRate,
    volumeChange: ((currentStats.volume - previousStats.volume) / previousStats.volume * 100)
  };
}

function detectTradingPatterns(trades) {
  if (!trades?.length) return [];

  const patterns = [];
  const returns = trades.map(t => t.profit_loss || 0);
  const prices = trades.map(t => t.exitPrice);
  const volumes = trades.map(t => t.size || 0);
  
  // Trend patterns
  const sma20 = calculateSMA(prices, 20);
  const sma50 = calculateSMA(prices, 50);
  if (sma20[sma20.length - 1] > sma50[sma50.length - 1]) {
    patterns.push({
      type: 'trend',
      name: 'Uptrend',
      sentiment: 'bullish',
      confidence: 75
    });
  }

  // Volume patterns
  const avgVolume = Math.mean(volumes);
  const recentVolume = Math.mean(volumes.slice(-5));
  if (recentVolume > avgVolume * 1.5) {
    patterns.push({
      type: 'volume',
      name: 'Volume Surge',
      sentiment: 'bullish',
      confidence: 65
    });
  }
  
  // Momentum patterns
  const rsi = calculateRSI(returns);
  if (rsi < 30) {
    patterns.push({
      type: 'momentum',
      name: 'Oversold',
      sentiment: 'bullish',
      confidence: 70
    });
  } else if (rsi > 70) {
    patterns.push({
      type: 'momentum',
      name: 'Overbought',
      sentiment: 'bearish',
      confidence: 70
    });
  }

  return patterns;
}

function calculateRSI(returns, period = 14) {
  if (returns.length < period) return 50;

  let gains = 0;
  let losses = 0;

  for (let i = returns.length - period; i < returns.length; i++) {
    if (returns[i] >= 0) {
      gains += returns[i];
    } else {
      losses -= returns[i];
    }
  }

  const avgGain = gains / period;
  const avgLoss = losses / period;
  
  return avgLoss === 0 ? 100 : 100 - (100 / (1 + avgGain / avgLoss));
} 