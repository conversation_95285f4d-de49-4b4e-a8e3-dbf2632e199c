import { useMemo, useState } from 'react';
import { Line, Bar } from 'react-chartjs-2';
import {
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';

export default function ComplianceTracking({ trades }) {
  const analytics = useMemo(() => new TradeAnalytics(trades), [trades]);
  const compliance = analytics.compliance;
  const [comparisonPeriod, setComparisonPeriod] = useState('1M');

  const historicalComparison = useMemo(() => 
    calculateHistoricalComparison(trades, comparisonPeriod),
    [trades, comparisonPeriod]
  );

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold">Compliance Tracking</h3>
            <p className="text-sm text-slate-500">Overall Compliance Score: {compliance.overallCompliance.toFixed(1)}%</p>
          </div>
          <ShieldCheckIcon className="w-6 h-6 text-slate-400" />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Daily Loss Limit Violations */}
          <div>
            <h4 className="text-sm font-medium text-slate-700 mb-4">Daily Loss Limit Violations</h4>
            <div className="space-y-3">
              {compliance.dailyLossLimitViolations.map((violation, index) => (
                <ViolationCard
                  key={index}
                  date={new Date(violation.date).toLocaleDateString()}
                  pnl={violation.pnl}
                  type="loss"
                />
              ))}
              {!compliance.dailyLossLimitViolations.length && (
                <p className="text-sm text-green-600">No daily loss limit violations</p>
              )}
            </div>
          </div>

          {/* Position Size Violations */}
          <div>
            <h4 className="text-sm font-medium text-slate-700 mb-4">Position Size Violations</h4>
            <div className="space-y-3">
              {compliance.positionSizeViolations.map((violation, index) => (
                <ViolationCard
                  key={index}
                  date={new Date(violation.timestamp).toLocaleDateString()}
                  size={violation.size}
                  instrument={violation.instrument}
                  type="size"
                />
              ))}
              {!compliance.positionSizeViolations.length && (
                <p className="text-sm text-green-600">No position size violations</p>
              )}
            </div>
          </div>
        </div>

        {/* Compliance Trend */}
        <div className="mt-6">
          <h4 className="text-sm font-medium text-slate-700 mb-4">Compliance Trend</h4>
          <div className="h-[200px]">
            <Line data={getComplianceTrendData(trades)} options={getChartOptions()} />
          </div>
        </div>
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <ComplianceMetricCard
          title="Risk Management Score"
          value={historicalComparison.current.riskScore}
          previousValue={historicalComparison.previous.riskScore}
          details={[
            { label: 'Stop Loss Usage', value: `${historicalComparison.current.stopLossUsage}%` },
            { label: 'Position Size Compliance', value: `${historicalComparison.current.positionSizeCompliance}%` }
          ]}
        />
        <ComplianceMetricCard
          title="Trading Discipline"
          value={historicalComparison.current.disciplineScore}
          previousValue={historicalComparison.previous.disciplineScore}
          details={[
            { label: 'Plan Adherence', value: `${historicalComparison.current.planAdherence}%` },
            { label: 'Rule Violations', value: historicalComparison.current.ruleViolations }
          ]}
        />
        <ComplianceMetricCard
          title="Risk/Reward Compliance"
          value={historicalComparison.current.rrCompliance}
          previousValue={historicalComparison.previous.rrCompliance}
          details={[
            { label: 'Target Achievement', value: `${historicalComparison.current.targetAchievement}%` },
            { label: 'Stop Violations', value: historicalComparison.current.stopViolations }
          ]}
        />
      </div>

      {/* Historical Comparison */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold">Historical Comparison</h3>
          <select
            value={comparisonPeriod}
            onChange={(e) => setComparisonPeriod(e.target.value)}
            className="rounded-md border-slate-200 text-sm"
          >
            <option value="1M">Last Month</option>
            <option value="3M">Last 3 Months</option>
            <option value="6M">Last 6 Months</option>
            <option value="1Y">Last Year</option>
          </select>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="h-[300px]">
            <Bar 
              data={getComparisonChartData(historicalComparison)} 
              options={getComparisonChartOptions()} 
            />
          </div>
          <div className="space-y-4">
            <ComparisonInsight
              title="Risk Management"
              current={historicalComparison.current}
              previous={historicalComparison.previous}
            />
            <ComparisonInsight
              title="Trading Discipline"
              current={historicalComparison.current}
              previous={historicalComparison.previous}
            />
            <ComparisonInsight
              title="Overall Compliance"
              current={historicalComparison.current}
              previous={historicalComparison.previous}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

function ViolationCard({ date, pnl, size, instrument, type }) {
  return (
    <div className="p-3 bg-red-50 rounded-lg">
      <div className="flex items-start space-x-3">
        <div className="p-1 rounded-full bg-red-100">
          <ExclamationTriangleIcon className="w-4 h-4 text-red-600" />
        </div>
        <div>
          <p className="text-sm font-medium text-red-900">{date}</p>
          {type === 'loss' && (
            <p className="text-sm text-red-700">
              Daily Loss: {formatCurrency(pnl)}
            </p>
          )}
          {type === 'size' && (
            <p className="text-sm text-red-700">
              Oversized Position: {size} units ({instrument})
            </p>
          )}
        </div>
      </div>
    </div>
  );
}

function getComplianceTrendData(trades) {
  // Group trades by day and calculate daily compliance
  const dailyCompliance = trades.reduce((acc, trade) => {
    const date = new Date(trade.timestamp).toISOString().split('T')[0];
    if (!acc[date]) {
      acc[date] = {
        violations: 0,
        trades: 0
      };
    }
    acc[date].trades++;
    
    // Check for violations
    if (trade.size > 100) acc[date].violations++; // Position size violation
    if (trade.profit_loss < -5000) acc[date].violations++; // Daily loss violation
    
    return acc;
  }, {});

  const dates = Object.keys(dailyCompliance).sort();
  const complianceScores = dates.map(date => {
    const { violations, trades } = dailyCompliance[date];
    return Math.max(0, 100 - (violations / trades) * 100);
  });

  return {
    labels: dates.map(date => new Date(date).toLocaleDateString()),
    datasets: [{
      label: 'Daily Compliance Score',
      data: complianceScores,
      borderColor: 'rgb(59, 130, 246)',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      fill: true,
      tension: 0.4
    }]
  };
}

function getChartOptions() {
  return {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        title: {
          display: true,
          text: 'Compliance Score (%)'
        }
      }
    }
  };
}

function formatCurrency(value) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0
  }).format(value);
} 