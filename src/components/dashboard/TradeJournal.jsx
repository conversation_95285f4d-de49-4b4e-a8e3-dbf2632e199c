import { useState, useMemo, useEffect } from 'react';
import { format, isWithinInterval, startOfDay, endOfDay, parseISO } from 'date-fns';
import {
  PencilIcon,
  TagIcon,
  PhotoIcon,
  XMarkIcon,
  PlusIcon,
  TrashIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  FunnelIcon,
  AdjustmentsHorizontalIcon,
  ChartBarIcon,
  QuestionMarkCircleIcon,
  ChevronRightIcon,
  MagnifyingGlassIcon as SearchIcon,
  ChartPieIcon,
  TableCellsIcon,
  ArrowsUpDownIcon,
  Bars4Icon,
  ClockIcon,
  ChevronLeftIcon,
  CalendarIcon,
  PencilSquareIcon,
  DocumentTextIcon,
  ChatBubbleLeftIcon,
  ChevronDoubleLeftIcon,
  ChevronDoubleRightIcon,
} from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';
import DatePicker from 'react-datepicker';
import "react-datepicker/dist/react-datepicker.css";
import SimpleBarChart from '../charts/SimpleBarChart';

// Chart implementation using custom SimpleBarChart component

// Add session analysis helper
function analyzeSession(trades) {
  const sessions = trades.reduce((acc, trade) => {
    const date = format(new Date(trade.timestamp), 'yyyy-MM-dd');
    if (!acc[date]) {
      acc[date] = {
        trades: [],
        totalPnL: 0,
        winners: 0,
        losers: 0,
        largestWin: 0,
        largestLoss: 0,
        avgWinSize: 0,
        avgLossSize: 0,
        profitFactor: 0,
        winRate: 0,
        drawdown: 0,
        maxDrawdown: 0,
        runningBalance: []
      };
    }

    // Get P&L with fallback handling
    const pnl = trade.profit_loss ?? trade.pnl ?? 0;

    acc[date].trades.push(trade);
    acc[date].totalPnL += pnl;

    if (pnl > 0) {
      acc[date].winners++;
      acc[date].largestWin = Math.max(acc[date].largestWin, pnl);
      acc[date].avgWinSize = (acc[date].avgWinSize * (acc[date].winners - 1) + pnl) / acc[date].winners;
    } else {
      acc[date].losers++;
      acc[date].largestLoss = Math.min(acc[date].largestLoss, pnl);
      acc[date].avgLossSize = (acc[date].avgLossSize * (acc[date].losers - 1) + pnl) / acc[date].losers;
    }
    
    acc[date].winRate = (acc[date].winners / acc[date].trades.length) * 100;
    acc[date].profitFactor = Math.abs(acc[date].avgWinSize / acc[date].avgLossSize);
    
    // Calculate running balance and drawdown
    const runningPnL = acc[date].trades.reduce((sum, t) => {
      const newSum = sum + t.profit_loss;
      const drawdown = Math.min(0, newSum - acc[date].maxBalance || 0);
      acc[date].maxDrawdown = Math.min(acc[date].maxDrawdown, drawdown);
      acc[date].maxBalance = Math.max(acc[date].maxBalance || 0, newSum);
      acc[date].runningBalance.push(newSum);
      return newSum;
    }, 0);
    
    return acc;
  }, {});

  return sessions;
}

// Add performance chart options
const performanceChartOptions = {
  responsive: true,
  interaction: {
    mode: 'index',
    intersect: false,
  },
  plugins: {
    legend: {
      position: 'top',
    },
    tooltip: {
      callbacks: {
        label: (context) => {
          const label = context.dataset.label || '';
          if (label === 'Balance') {
            return `Balance: ${formatCurrency(context.raw)}`;
          }
          if (label === 'Drawdown') {
            return `Drawdown: ${formatCurrency(context.raw)}`;
          }
          return `${label}: ${context.raw}%`;
        }
      }
    }
  },
  scales: {
    y: {
      type: 'linear',
      display: true,
      position: 'left',
      ticks: {
        callback: (value) => formatCurrency(value)
      }
    },
    y1: {
      type: 'linear',
      display: true,
      position: 'right',
      grid: {
        drawOnChartArea: false,
      },
      ticks: {
        callback: (value) => `${value}%`
      }
    }
  }
};

// Add trade comparison helper
function compareTradePerformance(trade1, trade2) {
  const metrics = {
    profitLoss: {
      value: trade1.profit_loss - trade2.profit_loss,
      label: 'P&L Difference',
      format: formatCurrency
    },
    duration: {
      value: parseDuration(trade1.duration) - parseDuration(trade2.duration),
      label: 'Duration Difference',
      format: (v) => `${Math.abs(Math.round(v / 60))}m`
    },
    efficiency: {
      value: (trade1.profit_loss / parseDuration(trade1.duration)) - 
             (trade2.profit_loss / parseDuration(trade2.duration)),
      label: 'Efficiency Difference',
      format: (v) => `${formatCurrency(v)}/min`
    }
  };

  return metrics;
}

// Add advanced metrics calculation
function calculateAdvancedMetrics(trades) {
  if (!trades.length) return null;

  const metrics = {
    profitFactor: 0,
    sharpeRatio: 0,
    maxDrawdown: 0,
    averageWinningTrade: 0,
    averageLosingTrade: 0,
    largestWinningTrade: 0,
    largestLosingTrade: 0,
    winningTradeCount: 0,
    losingTradeCount: 0,
    consecutiveWins: 0,
    consecutiveLosses: 0,
    profitByTimeOfDay: {},
    profitByDayOfWeek: {},
    averageHoldingTime: 0,
    riskRewardRatio: 0,
    dailyVolatility: 0
  };

  let runningBalance = 0;
  let peakBalance = 0;
  let currentDrawdown = 0;
  let returns = [];
  let totalHoldingTime = 0;

  trades.forEach((trade, index) => {
    const pnl = trade.profit_loss ?? trade.pnl ?? 0;
    runningBalance += pnl;
    returns.push(pnl);

    // Update peak balance and drawdown
    if (runningBalance > peakBalance) {
      peakBalance = runningBalance;
      currentDrawdown = 0;
    } else {
      currentDrawdown = peakBalance - runningBalance;
      metrics.maxDrawdown = Math.max(metrics.maxDrawdown, currentDrawdown);
    }

    // Time-based analysis
    const tradeDate = new Date(trade.timestamp);
    const hour = tradeDate.getHours();
    const dayOfWeek = tradeDate.getDay();

    // Update time of day metrics
    const timeKey = `${hour.toString().padStart(2, '0')}:00`;
    if (!metrics.profitByTimeOfDay[timeKey]) {
      metrics.profitByTimeOfDay[timeKey] = { total: 0, count: 0 };
    }
    metrics.profitByTimeOfDay[timeKey].total += pnl;
    metrics.profitByTimeOfDay[timeKey].count++;

    // Update day of week metrics
    const dayKey = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][dayOfWeek];
    if (!metrics.profitByDayOfWeek[dayKey]) {
      metrics.profitByDayOfWeek[dayKey] = { total: 0, count: 0 };
    }
    metrics.profitByDayOfWeek[dayKey].total += pnl;
    metrics.profitByDayOfWeek[dayKey].count++;

    // Update win/loss metrics
    if (pnl > 0) {
      metrics.winningTradeCount++;
      metrics.averageWinningTrade += pnl;
      metrics.largestWinningTrade = Math.max(metrics.largestWinningTrade, pnl);
    } else {
      metrics.losingTradeCount++;
      metrics.averageLosingTrade += pnl;
      metrics.largestLosingTrade = Math.min(metrics.largestLosingTrade, pnl);
    }

    // Update holding time metrics
    if (trade.duration) {
      totalHoldingTime += parseDuration(trade.duration);
    }
  });

  // Calculate averages and ratios
  metrics.averageWinningTrade /= metrics.winningTradeCount || 1;
  metrics.averageLosingTrade /= metrics.losingTradeCount || 1;
  metrics.profitFactor = Math.abs(metrics.averageWinningTrade / metrics.averageLosingTrade);
  metrics.averageHoldingTime = totalHoldingTime / trades.length;

  // Calculate Sharpe Ratio (simplified)
  const meanReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
  const stdDev = Math.sqrt(
    returns.reduce((sum, r) => sum + Math.pow(r - meanReturn, 2), 0) / returns.length
  );
  metrics.sharpeRatio = meanReturn / (stdDev || 1);

  // Calculate daily volatility
  metrics.dailyVolatility = Math.sqrt(
    returns.reduce((sum, r) => sum + Math.pow(r - meanReturn, 2), 0) / returns.length
  );

  return metrics;
}

// Move EmptyState component definition before the main TradeJournal component
function EmptyState({ hasDateRange }) {
  return (
    <div className="text-center py-12 px-4">
      <div className="max-w-md mx-auto">
        <div className="flex flex-col items-center">
          {hasDateRange ? (
            <>
              <div className="w-16 h-16 bg-slate-50 rounded-full flex items-center justify-center mb-4">
                <CalendarIcon className="w-8 h-8 text-slate-300" />
              </div>
              <h3 className="text-lg font-medium text-slate-900 mb-2">No Trades in Selected Period</h3>
              <p className="text-sm text-slate-500 mb-4">
                Try adjusting your date range or filters to see your documented trades.
              </p>
            </>
          ) : (
            <>
              <div className="w-16 h-16 bg-emerald-50 rounded-full flex items-center justify-center mb-4">
                <PencilSquareIcon className="w-8 h-8 text-emerald-400" />
              </div>
              <h3 className="text-lg font-medium text-slate-900 mb-2">Start Your Trading Journal</h3>
              <p className="text-sm text-slate-500 mb-4">
                Document your trades to build a comprehensive trading history and track your progress.
              </p>
            </>
          )}

          <div className="bg-white rounded-lg p-4 shadow-sm border border-slate-200 w-full">
            <h4 className="text-sm font-medium text-slate-800 mb-3">Journaling Best Practices:</h4>
            <ul className="text-sm text-slate-600 space-y-2.5">
              <li className="flex items-start">
                <div className="flex-shrink-0 w-5 h-5 rounded-full bg-emerald-50 flex items-center justify-center mr-2">
                  <span className="text-emerald-500 text-xs">✓</span>
                </div>
                Document your trade setup and entry/exit points
              </li>
              <li className="flex items-start">
                <div className="flex-shrink-0 w-5 h-5 rounded-full bg-emerald-50 flex items-center justify-center mr-2">
                  <span className="text-emerald-500 text-xs">✓</span>
                </div>
                Note market conditions and key observations
              </li>
              <li className="flex items-start">
                <div className="flex-shrink-0 w-5 h-5 rounded-full bg-emerald-50 flex items-center justify-center mr-2">
                  <span className="text-emerald-500 text-xs">✓</span>
                </div>
                Track your emotions and decision-making process
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function TradeJournal({ trades, dateRange, onUpdateTrade, onDeleteTrade }) {
  // Load initial preferences from localStorage
  const [showStats, setShowStats] = useState(() => {
    const saved = localStorage.getItem('tradeJournal.showStats');
    return saved !== null ? JSON.parse(saved) : true;
  });
  const [showChart, setShowChart] = useState(() => {
    const saved = localStorage.getItem('tradeJournal.showChart');
    return saved !== null ? JSON.parse(saved) : true;
  });
  const [showAdvancedMetrics, setShowAdvancedMetrics] = useState(() => {
    const saved = localStorage.getItem('tradeJournal.showAdvancedMetrics');
    return saved !== null ? JSON.parse(saved) : false;
  });

  // Save preferences when they change
  useEffect(() => {
    localStorage.setItem('tradeJournal.showStats', JSON.stringify(showStats));
  }, [showStats]);

  useEffect(() => {
    localStorage.setItem('tradeJournal.showChart', JSON.stringify(showChart));
  }, [showChart]);

  useEffect(() => {
    localStorage.setItem('tradeJournal.showAdvancedMetrics', JSON.stringify(showAdvancedMetrics));
  }, [showAdvancedMetrics]);

  const [selectedTrade, setSelectedTrade] = useState(null);
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('date');
  const [sortOrder, setSortOrder] = useState('desc');
  const [groupBy, setGroupBy] = useState('none'); // none, day, week, instrument
  const [selectedTag, setSelectedTag] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedTradeId, setSelectedTradeId] = useState(null);
  const [expandedGroups, setExpandedGroups] = useState(new Set());
  const [comparisonMode, setComparisonMode] = useState(false);
  const [selectedTradesForComparison, setSelectedTradesForComparison] = useState([]);
  const [showGroupOptions, setShowGroupOptions] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const tradesPerPage = 10;
  const [showDetails, setShowDetails] = useState(false);

  // Add animation variants for trade entries
  const tradeEntryVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  // Get unique tags from all trades
  const allTags = useMemo(() => {
    const tags = new Set();
    trades.forEach(trade => {
      trade.tags?.forEach(tag => tags.add(tag));
    });
    return Array.from(tags);
  }, [trades]);

  // Filter and sort trades
  const filteredTrades = useMemo(() => {
    if (!trades || !dateRange?.start || !dateRange?.end) return [];
    
    return trades
      .filter(trade => {
        // Date range filter using passed prop
        const tradeDate = new Date(trade.timestamp);
        if (!isWithinInterval(tradeDate, { start: dateRange.start, end: dateRange.end })) {
          return false;
        }
        
        // Existing filters with robust P&L handling
        const pnl = trade.profit_loss ?? trade.pnl ?? 0;
        if (filter === 'winners') return pnl > 0;
        if (filter === 'losers') return pnl < 0;
        if (filter === 'tagged') return trade.tags?.length > 0;
        if (filter === 'untagged') return !trade.tags?.length;
        return true;
      })
      .filter(trade => {
        if (selectedTag) return trade.tags?.includes(selectedTag);
        return true;
      })
      .filter(trade => 
        trade.notes?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trade.tags?.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())) ||
        trade.instrument?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trade.lessons_learned?.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .sort((a, b) => {
        if (sortBy === 'date') {
          return sortOrder === 'desc' 
            ? new Date(b.timestamp) - new Date(a.timestamp)
            : new Date(a.timestamp) - new Date(b.timestamp);
        }
        if (sortBy === 'pnl') {
          return sortOrder === 'desc'
            ? b.profit_loss - a.profit_loss
            : a.profit_loss - b.profit_loss;
        }
        if (sortBy === 'instrument') {
          return sortOrder === 'desc'
            ? b.instrument.localeCompare(a.instrument)
            : a.instrument.localeCompare(b.instrument);
        }
        return 0;
      });
  }, [trades, dateRange, filter, selectedTag, searchTerm, sortBy, sortOrder]);

  // Calculate pagination
  const totalPages = Math.ceil(filteredTrades.length / tradesPerPage);
  const indexOfLastTrade = currentPage * tradesPerPage;
  const indexOfFirstTrade = indexOfLastTrade - tradesPerPage;
  const currentTrades = filteredTrades.slice(indexOfFirstTrade, indexOfLastTrade);

  // Handle page change
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
    // Scroll to top of trade list
    document.querySelector('.trade-list')?.scrollIntoView({ behavior: 'smooth' });
  };

  // Group trades if needed
  const groupedTrades = useMemo(() => {
    if (groupBy === 'none') return { ungrouped: filteredTrades };

    return filteredTrades.reduce((groups, trade) => {
      let key;
      if (groupBy === 'day') {
        key = format(new Date(trade.timestamp), 'yyyy-MM-dd');
      } else if (groupBy === 'week') {
        key = format(new Date(trade.timestamp), 'yyyy-[W]ww');
      } else if (groupBy === 'instrument') {
        key = trade.instrument;
      }

      if (!groups[key]) groups[key] = [];
      groups[key].push(trade);
      return groups;
    }, {});
  }, [filteredTrades, groupBy]);

  // Calculate summary statistics
  const stats = useMemo(() => {
    if (!filteredTrades?.length) {
      return {
        totalTrades: 0,
        winners: 0,
        losers: 0,
        winRate: 0,
        totalPnL: 0,
        avgPnL: 0,
        avgWin: 0,
        avgLoss: 0,
        profitFactor: 0,
        largestWin: 0,
        largestLoss: 0,
        averageSize: 0
      };
    }

    // Helper function to get P&L value with fallback
    const getPnL = (trade) => {
      const pnl = trade.profit_loss ?? trade.pnl ?? 0;
      return typeof pnl === 'number' && !isNaN(pnl) ? pnl : 0;
    };

    const winners = filteredTrades.filter(t => getPnL(t) > 0);
    const losers = filteredTrades.filter(t => getPnL(t) < 0);
    const totalPnL = filteredTrades.reduce((sum, t) => sum + getPnL(t), 0);
    const avgWin = winners.length ? winners.reduce((sum, t) => sum + getPnL(t), 0) / winners.length : 0;
    const avgLoss = losers.length ? Math.abs(losers.reduce((sum, t) => sum + getPnL(t), 0)) / losers.length : 0;
    const winRate = filteredTrades.length ? (winners.length / filteredTrades.length) * 100 : 0;

    // Calculate additional metrics
    const profitFactor = avgLoss ? avgWin / avgLoss : 0;
    const largestWin = winners.length ? Math.max(...winners.map(t => getPnL(t))) : 0;
    const largestLoss = losers.length ? Math.min(...losers.map(t => getPnL(t))) : 0;
    const averageSize = filteredTrades.length 
      ? filteredTrades.reduce((sum, t) => sum + (t.size || 0), 0) / filteredTrades.length 
      : 0;
    
    return {
      totalTrades: filteredTrades.length,
      winners: winners.length,
      losers: losers.length,
      winRate,
      totalPnL,
      avgPnL: totalPnL / filteredTrades.length,
      avgWin,
      avgLoss,
      profitFactor,
      largestWin,
      largestLoss,
      averageSize
    };
  }, [filteredTrades]);

  // Chart data for SimpleBarChart component
  const chartData = useMemo(() => {
    // Sort trades chronologically for proper chart display
    const sortedTrades = [...filteredTrades].sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    return {
      labels: sortedTrades.map(trade => format(new Date(trade.timestamp), 'MMM d, h:mm a')),
      datasets: [
        {
          label: 'P&L',
          data: sortedTrades.map(trade => {
            // Handle multiple P&L property names and ensure we have a valid number
            const pnl = trade.profit_loss ?? trade.pnl ?? 0;
            return typeof pnl === 'number' && !isNaN(pnl) ? pnl : 0;
          })
        }
      ]
    };
  }, [filteredTrades]);

  // Chart data is now handled by SimpleBarChart component

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  // Add performance metrics calculation
  const performanceMetrics = useMemo(() => {
    if (!filteredTrades.length) return null;
    
    const consecutiveWins = filteredTrades.reduce((max, trade, i, arr) => {
      const pnl = trade.profit_loss ?? trade.pnl ?? 0;
      const prevPnl = i > 0 ? (arr[i-1].profit_loss ?? arr[i-1].pnl ?? 0) : 0;
      if (i === 0) return pnl > 0 ? 1 : 0;
      const streak = pnl > 0 ? (prevPnl > 0 ? max + 1 : 1) : 0;
      return Math.max(max, streak);
    }, 0);

    const consecutiveLosses = filteredTrades.reduce((max, trade, i, arr) => {
      const pnl = trade.profit_loss ?? trade.pnl ?? 0;
      const prevPnl = i > 0 ? (arr[i-1].profit_loss ?? arr[i-1].pnl ?? 0) : 0;
      if (i === 0) return pnl < 0 ? 1 : 0;
      const streak = pnl < 0 ? (prevPnl < 0 ? max + 1 : 1) : 0;
      return Math.max(max, streak);
    }, 0);

    const timeOfDayPerformance = filteredTrades.reduce((acc, trade) => {
      const hour = new Date(trade.timestamp).getHours();
      const period = Math.floor(hour / 4) * 4;
      const key = `${period.toString().padStart(2, '0')}:00-${(period + 4).toString().padStart(2, '0')}:00`;
      
      if (!acc[key]) acc[key] = { trades: 0, pnl: 0, wins: 0 };
      acc[key].trades++;
      const pnl = trade.profit_loss ?? trade.pnl ?? 0;
      acc[key].pnl += pnl;
      if (pnl > 0) acc[key].wins++;
      
      return acc;
    }, {});

    return {
      consecutiveWins,
      consecutiveLosses,
      timeOfDayPerformance
    };
  }, [filteredTrades]);

  // Calculate advanced metrics
  const advancedMetrics = useMemo(() => calculateAdvancedMetrics(filteredTrades), [filteredTrades]);

  // Handle trade comparison
  const handleTradeComparison = (trade) => {
    if (comparisonMode) {
      if (selectedTradesForComparison.length < 2) {
        setSelectedTradesForComparison([...selectedTradesForComparison, trade]);
      }
      if (selectedTradesForComparison.length === 1) {
        // Show comparison modal or section
        const comparison = compareTradePerformance(selectedTradesForComparison[0], trade);
        // Handle showing comparison results
      }
    }
  };

  // Add keyboard shortcut handling
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (e.key === 'f' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        document.querySelector('input[type="text"]')?.focus();
      }
      if (e.key === 'Escape' && selectedTrade) {
        setSelectedTrade(null);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [selectedTrade]);

  // Add smooth scroll into view for selected trade
  useEffect(() => {
    if (selectedTradeId) {
      const element = document.getElementById(`trade-${selectedTradeId}`);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  }, [selectedTradeId]);

  // Add keyboard navigation for trades
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!selectedTradeId) return;
      
      const currentIndex = filteredTrades.findIndex(t => t.id === selectedTradeId);
      if (currentIndex === -1) return;

      if (e.key === 'ArrowUp' && currentIndex > 0) {
        setSelectedTradeId(filteredTrades[currentIndex - 1].id);
      } else if (e.key === 'ArrowDown' && currentIndex < filteredTrades.length - 1) {
        setSelectedTradeId(filteredTrades[currentIndex + 1].id);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedTradeId, filteredTrades]);

  // Add group toggle handler
  const toggleGroup = (groupId) => {
    setExpandedGroups(prev => {
      const next = new Set(prev);
      if (next.has(groupId)) {
        next.delete(groupId);
      } else {
        next.add(groupId);
      }
      return next;
    });
  };

  const handleSelectTrade = (trade) => {
    setSelectedTrade(trade);
    setSelectedTradeId(trade.id);
  };

  if (!trades || trades.length === 0) {
    return (
      <div className="absolute inset-x-0 top-[220px] bottom-0 z-40 flex items-start justify-center bg-slate-50/95 backdrop-blur-sm">
        <div className="w-full max-w-2xl mx-auto pt-12">
          <div className="text-center">
            <div className="mb-8">
              <div className="w-24 h-24 mx-auto mb-6 relative">
                <div className="absolute inset-0 bg-emerald-100 rounded-full animate-pulse"></div>
                <div className="absolute inset-2 bg-emerald-50 rounded-full flex items-center justify-center">
                  <PencilIcon className="w-12 h-12 text-emerald-500" />
                </div>
              </div>
              <h2 className="text-2xl font-bold text-slate-900 mb-3">Start Your Trading Journal</h2>
              <p className="text-slate-500 mb-8 max-w-lg mx-auto">
                Record your trades to track your progress and build a comprehensive trading history.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-6 mb-12">
              <div className="bg-white rounded-xl p-6 text-left shadow-sm border border-slate-200">
                <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center mb-4">
                  <DocumentTextIcon className="w-6 h-6 text-emerald-600" />
                </div>
                <h3 className="text-sm font-semibold text-slate-900 mb-2">Trade Details</h3>
                <p className="text-sm text-slate-500">
                  Log entry/exit points, position size, and setup type.
                </p>
              </div>

              <div className="bg-white rounded-xl p-6 text-left shadow-sm border border-slate-200">
                <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center mb-4">
                  <ChatBubbleLeftIcon className="w-6 h-6 text-emerald-600" />
                </div>
                <h3 className="text-sm font-semibold text-slate-900 mb-2">Trade Notes</h3>
                <p className="text-sm text-slate-500">
                  Document your thoughts and lessons learned.
                </p>
              </div>

              <div className="bg-white rounded-xl p-6 text-left shadow-sm border border-slate-200">
                <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center mb-4">
                  <PhotoIcon className="w-6 h-6 text-emerald-600" />
                </div>
                <h3 className="text-sm font-semibold text-slate-900 mb-2">Trade Screenshots</h3>
                <p className="text-sm text-slate-500">
                  Capture and annotate your trade setups.
                </p>
              </div>
            </div>

            <div className="max-w-lg mx-auto">
              <h3 className="text-sm font-semibold text-slate-900 mb-4">Journal Features</h3>
              <div className="space-y-3 text-left">
                <div className="flex items-center gap-3 p-3 bg-white rounded-lg border border-slate-200 shadow-sm">
                  <div className="w-8 h-8 bg-emerald-50 rounded-full flex items-center justify-center">
                    <span className="text-emerald-600 text-lg">✓</span>
                  </div>
                  <p className="text-sm text-slate-600">Detailed trade entry and management tracking</p>
                </div>
                <div className="flex items-center gap-3 p-3 bg-white rounded-lg border border-slate-200 shadow-sm">
                  <div className="w-8 h-8 bg-emerald-50 rounded-full flex items-center justify-center">
                    <span className="text-emerald-600 text-lg">✓</span>
                  </div>
                  <p className="text-sm text-slate-600">Screenshot and chart annotation tools</p>
                </div>
                <div className="flex items-center gap-3 p-3 bg-white rounded-lg border border-slate-200 shadow-sm">
                  <div className="w-8 h-8 bg-emerald-50 rounded-full flex items-center justify-center">
                    <span className="text-emerald-600 text-lg">✓</span>
                  </div>
                  <p className="text-sm text-slate-600">Trade reflection and learning documentation</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Controls Bar */}
      <div className="bg-white rounded-xl shadow-sm p-3 sm:p-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
          {/* Left: View Toggles */}
          <div className="flex items-center space-x-2">
            <CustomTooltip content="Toggle Statistics">
              <button
                onClick={() => setShowStats(!showStats)}
                className={`p-1.5 sm:p-2 rounded-lg transition-all transform hover:scale-105 border ${
                  showStats 
                    ? 'bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100' 
                    : 'bg-white text-slate-400 border-slate-200 hover:border-slate-300 hover:text-slate-600'
                }`}
              >
                <ChartPieIcon className="w-4 h-4 sm:w-5 sm:h-5" />
              </button>
            </CustomTooltip>
            <CustomTooltip content="Toggle Chart">
              <button
                onClick={() => setShowChart(!showChart)}
                className={`p-1.5 sm:p-2 rounded-lg transition-all transform hover:scale-105 border ${
                  showChart 
                    ? 'bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100' 
                    : 'bg-white text-slate-400 border-slate-200 hover:border-slate-300 hover:text-slate-600'
                }`}
              >
                <ChartBarIcon className="w-4 h-4 sm:w-5 sm:h-5" />
              </button>
            </CustomTooltip>
            <CustomTooltip content="Toggle Advanced Metrics">
              <button
                onClick={() => setShowAdvancedMetrics(!showAdvancedMetrics)}
                className={`p-1.5 sm:p-2 rounded-lg transition-all transform hover:scale-105 border ${
                  showAdvancedMetrics 
                    ? 'bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100' 
                    : 'bg-white text-slate-400 border-slate-200 hover:border-slate-300 hover:text-slate-600'
                }`}
              >
                <AdjustmentsHorizontalIcon className="w-4 h-4 sm:w-5 sm:h-5" />
              </button>
            </CustomTooltip>
          </div>

          {/* Right: Search */}
          <div className="relative w-full sm:w-64">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search trades..."
              className="w-full pl-8 pr-8 py-1.5 sm:py-2 rounded-lg text-sm
                bg-white border border-slate-200 
                placeholder-slate-400 text-slate-700
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
                hover:border-slate-300"
            />
            <SearchIcon className="absolute left-2.5 top-1/2 -translate-y-1/2 w-3.5 h-3.5 sm:w-4 sm:h-4 text-slate-400" />
            {searchTerm && (
              <button
                onClick={() => setSearchTerm('')}
                className="absolute right-2 top-1/2 -translate-y-1/2 p-0.5 rounded-full hover:bg-slate-100 transition-colors"
              >
                <XMarkIcon className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-slate-400" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Summary Stats */}
      {showStats && (
        <div className="space-y-4">
          {/* Top Stats Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
            {/* Total Trades Card */}
            <div className="bg-white rounded-xl shadow-sm border border-slate-200 hover:shadow-lg transition-shadow duration-200">
              <div className="p-3 sm:p-4">
                <div className="flex items-center justify-between">
                  <div className="text-sm font-medium text-slate-800">Total Trades</div>
                  <div className="flex items-center">
                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                      stats.winRate >= 50 ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'
                    }`}>
                      {stats.winRate.toFixed(1)}% Win
                    </span>
                  </div>
                </div>
                <div className="mt-2 flex items-center justify-between">
                  <div className="text-xl sm:text-2xl font-bold text-slate-900">{stats.totalTrades}</div>
                  <div className="text-sm font-medium">
                    <span className="text-green-600">{stats.winners}W</span>
                    <span className="mx-1 text-slate-400">/</span>
                    <span className="text-red-600">{stats.losers}L</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Net P&L Card */}
            <div className="bg-white rounded-xl shadow-sm border border-slate-200 hover:shadow-lg transition-shadow duration-200">
              <div className="p-3 sm:p-4">
                <div className="flex items-center justify-between">
                  <div className="text-sm font-medium text-slate-800">Net P&L</div>
                  <div className="flex items-center">
                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                      stats.totalPnL >= 0 ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'
                    }`}>
                      {stats.totalPnL >= 0 ? '+' : ''}{formatCurrency(stats.totalPnL)}
                    </span>
                  </div>
                </div>
                <div className="mt-2">
                  <div className={`text-xl sm:text-2xl font-bold ${stats.totalPnL >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatCurrency(stats.totalPnL)}
                  </div>
                  <div className="text-xs font-medium text-slate-500 mt-1">
                    {formatCurrency(stats.avgPnL)} avg per trade
                  </div>
                </div>
              </div>
            </div>

            {/* Best Trades Card */}
            <div className="bg-white rounded-xl shadow-sm border border-slate-200 hover:shadow-lg transition-shadow duration-200">
              <div className="p-3 sm:p-4">
                <div className="flex items-center justify-between">
                  <div className="text-sm font-medium text-slate-800">Best Trades</div>
                  <div className="flex items-center">
                    <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
                      Top 3
                    </span>
                  </div>
                </div>
                <div className="mt-2">
                  <div className="text-xl sm:text-2xl font-bold text-green-600">
                    {formatCurrency(stats.largestWin)}
                  </div>
                  <div className="text-xs font-medium text-slate-500 mt-1">
                    {formatCurrency(stats.avgWin)} avg win
                  </div>
                </div>
              </div>
            </div>

            {/* Worst Trades Card */}
            <div className="bg-white rounded-xl shadow-sm border border-slate-200 hover:shadow-lg transition-shadow duration-200">
              <div className="p-3 sm:p-4">
                <div className="flex items-center justify-between">
                  <div className="text-sm font-medium text-slate-800">Worst Trades</div>
                  <div className="flex items-center">
                    <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
                      Bottom 3
                    </span>
                  </div>
                </div>
                <div className="mt-2">
                  <div className="text-xl sm:text-2xl font-bold text-red-600">
                    {formatCurrency(stats.largestLoss)}
                  </div>
                  <div className="text-xs font-medium text-slate-500 mt-1">
                    {formatCurrency(stats.avgLoss)} avg loss
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Advanced Metrics Section */}
      {showAdvancedMetrics && advancedMetrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
          {/* Risk & Performance Card */}
          <div className="bg-white rounded-xl shadow-sm p-3 sm:p-4 border border-slate-200">
            <h3 className="text-sm font-medium text-slate-800 mb-2 sm:mb-3 flex items-center">
              <ChartPieIcon className="w-3.5 h-3.5 sm:w-4 sm:h-4 mr-2 text-slate-400" />
              Risk & Performance
            </h3>
            <div className="space-y-2 sm:space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-xs sm:text-sm text-slate-600">Sharpe Ratio</span>
                <span className={`text-xs sm:text-sm font-medium ${
                  advancedMetrics.sharpeRatio >= 1 ? 'text-green-600' : 'text-slate-900'
                }`}>
                  {advancedMetrics.sharpeRatio.toFixed(2)}
                </span>
              </div>
              {/* Add other risk metrics similarly */}
            </div>
          </div>

          {/* Time Analysis Card */}
          <div className="bg-white rounded-xl shadow-sm p-3 sm:p-4 border border-slate-200">
            <h3 className="text-sm font-medium text-slate-800 mb-2 sm:mb-3 flex items-center">
              <ClockIcon className="w-3.5 h-3.5 sm:w-4 sm:h-4 mr-2 text-slate-400" />
              Time Analysis
            </h3>
            <div className="space-y-3 sm:space-y-4">
              {/* Trading Hours Performance */}
              <div>
                <h4 className="text-xs font-medium text-slate-600 mb-2">Trading Hours Performance</h4>
                <div className="space-y-2 sm:space-y-3">
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs text-slate-500">Best Hour</span>
                      <span className="text-xs font-medium text-green-600">
                        {Object.entries(performanceMetrics.timeOfDayPerformance)
                          .sort(([,a], [,b]) => b.pnl - a.pnl)[0]?.[0] || 'N/A'}
                      </span>
                    </div>
                    <div className="w-full h-1 bg-slate-100 rounded-full overflow-hidden">
                      <div className="h-full bg-green-500 rounded-full" style={{ width: '75%' }} />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Trade Analysis Card */}
          <div className="bg-white rounded-xl shadow-sm p-3 sm:p-4 border border-slate-200">
            <h3 className="text-sm font-medium text-slate-800 mb-2 sm:mb-3 flex items-center">
              <ChartBarIcon className="w-3.5 h-3.5 sm:w-4 sm:h-4 mr-2 text-slate-400" />
              Trade Analysis
            </h3>
            <div className="space-y-3 sm:space-y-4">
              {/* Performance Overview */}
              <div>
                <h4 className="text-xs font-medium text-slate-600 mb-2">Performance Overview</h4>
                <div className="grid grid-cols-2 gap-2 sm:gap-3">
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs text-slate-500">Win Rate</span>
                      <span className="text-xs font-medium text-slate-700">{stats.winRate.toFixed(1)}%</span>
                    </div>
                    <div className="w-full h-1 bg-slate-100 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-green-500 rounded-full"
                        style={{ width: `${stats.winRate}%` }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Chart */}
      {showChart && (
        <div className="bg-white rounded-xl shadow-sm p-3 sm:p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-slate-900">P&L Chart</h3>
            <div className="text-sm text-slate-500">
              {filteredTrades.length} trades
            </div>
          </div>
          <div className="h-[250px] sm:h-[300px]">
            {filteredTrades.length > 0 ? (
              <SimpleBarChart data={chartData} title="P&L Distribution" />
            ) : (
              <div className="flex items-center justify-center h-full text-slate-500">
                <div className="text-center">
                  <ChartBarIcon className="h-12 w-12 mx-auto mb-2 text-slate-300" />
                  <p>No trades to display</p>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Enhanced Trade List */}
      <div className="space-y-3 sm:space-y-4 trade-list">
        {currentTrades.map((trade) => (
          <TradeEntry
            key={trade.id}
            trade={trade}
            isSelected={selectedTradeId === trade.id}
            onSelect={() => {
              setSelectedTrade(trade);
              setSelectedTradeId(trade.id);
            }}
            onDelete={onDeleteTrade}
            allTrades={filteredTrades}
          />
        ))}
        {filteredTrades.length === 0 && <EmptyState hasDateRange={dateRange?.start !== null && dateRange?.end !== null} />}
      </div>

      {/* Pagination */}
      {filteredTrades.length > tradesPerPage && (
        <div className="flex items-center justify-between border-t border-slate-200 bg-white px-3 py-2 sm:px-6 sm:py-3 rounded-lg shadow-sm">
          <div className="flex flex-1 justify-between sm:hidden">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="relative inline-flex items-center rounded-md border border-slate-300 bg-white px-4 py-2 text-sm font-medium text-slate-700 hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <span className="mx-2 flex items-center text-sm text-slate-700">
              Page {currentPage} of {totalPages}
            </span>
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="relative ml-3 inline-flex items-center rounded-md border border-slate-300 bg-white px-4 py-2 text-sm font-medium text-slate-700 hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-slate-700">
                Showing <span className="font-medium">{indexOfFirstTrade + 1}</span> to{' '}
                <span className="font-medium">
                  {Math.min(indexOfLastTrade, filteredTrades.length)}
                </span>{' '}
                of <span className="font-medium">{filteredTrades.length}</span> trades
              </p>
            </div>
            <div>
              <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                {/* First Page */}
                <button
                  onClick={() => handlePageChange(1)}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center rounded-l-md px-2 py-2 text-slate-400 ring-1 ring-inset ring-slate-300 hover:bg-slate-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">First</span>
                  <ChevronDoubleLeftIcon className="h-4 w-4 sm:h-5 sm:w-5" aria-hidden="true" />
                </button>
                {/* Previous */}
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-2 py-2 text-slate-400 ring-1 ring-inset ring-slate-300 hover:bg-slate-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Previous</span>
                  <ChevronLeftIcon className="h-4 w-4 sm:h-5 sm:w-5" aria-hidden="true" />
                </button>
                
                {/* Page Numbers */}
                {[...Array(totalPages)].map((_, index) => {
                  const pageNumber = index + 1;
                  // Show first page, last page, current page, and pages around current page
                  if (
                    pageNumber === 1 ||
                    pageNumber === totalPages ||
                    (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1)
                  ) {
                    return (
                      <button
                        key={pageNumber}
                        onClick={() => handlePageChange(pageNumber)}
                        className={`relative inline-flex items-center px-3 sm:px-4 py-2 text-sm font-semibold ${
                          currentPage === pageNumber
                            ? 'z-10 bg-blue-600 text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600'
                            : 'text-slate-900 ring-1 ring-inset ring-slate-300 hover:bg-slate-50 focus:z-20 focus:outline-offset-0'
                        }`}
                      >
                        {pageNumber}
                      </button>
                    );
                  }
                  // Show ellipsis
                  if (
                    (pageNumber === currentPage - 2 && pageNumber > 2) ||
                    (pageNumber === currentPage + 2 && pageNumber < totalPages - 1)
                  ) {
                    return (
                      <span
                        key={pageNumber}
                        className="relative inline-flex items-center px-3 sm:px-4 py-2 text-sm font-semibold text-slate-700 ring-1 ring-inset ring-slate-300"
                      >
                        ...
                      </span>
                    );
                  }
                  return null;
                })}
                
                {/* Next */}
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="relative inline-flex items-center px-2 py-2 text-slate-400 ring-1 ring-inset ring-slate-300 hover:bg-slate-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Next</span>
                  <ChevronRightIcon className="h-4 w-4 sm:h-5 sm:w-5" aria-hidden="true" />
                </button>
                {/* Last Page */}
                <button
                  onClick={() => handlePageChange(totalPages)}
                  disabled={currentPage === totalPages}
                  className="relative inline-flex items-center rounded-r-md px-2 py-2 text-slate-400 ring-1 ring-inset ring-slate-300 hover:bg-slate-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Last</span>
                  <ChevronDoubleRightIcon className="h-4 w-4 sm:h-5 sm:w-5" aria-hidden="true" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Trade Detail Modal */}
      {selectedTrade && (
        <TradeDetailModal
          trade={selectedTrade}
          onClose={() => {
            setSelectedTrade(null);
            setSelectedTradeId(null);
          }}
          onUpdate={onUpdateTrade}
          onDelete={onDeleteTrade}
        />
      )}
    </div>
  );
}

function TradeEntry({ trade, isSelected, onSelect, onDelete, allTrades }) {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showFullNotes, setShowFullNotes] = useState(false);

  // Get P&L with fallback handling
  const pnl = trade.profit_loss ?? trade.pnl ?? 0;
  const isProfit = pnl > 0;
  
  const tradeTypeColor = trade.position_type?.toLowerCase() === 'long' 
    ? 'bg-green-100 text-green-800'
    : trade.position_type?.toLowerCase() === 'short'
    ? 'bg-red-100 text-red-800'
    : 'bg-slate-100 text-slate-800';

  // Calculate duration percentage based on max duration for the day
  const getDurationPercentage = useMemo(() => {
    const currentDuration = parseDuration(trade.duration);
    const maxDurationForDay = getMaxDurationForDay(allTrades, trade);
    return maxDurationForDay ? (currentDuration / maxDurationForDay) * 100 : 0;
  }, [trade, allTrades]);

  // Format duration for display
  const formattedDuration = useMemo(() => {
    if (!trade.duration) return '0m';
    const parts = trade.duration.split(':');
    if (parts.length === 3) {
      const [hours, minutes, seconds] = parts;
      if (parseInt(hours) > 0) {
        return `${parseInt(hours)}h ${parseInt(minutes)}m ${parseInt(seconds)}s`;
      }
      if (parseInt(minutes) > 0) {
        return `${parseInt(minutes)}m ${parseInt(seconds)}s`;
      }
      return `${parseInt(seconds)}s`;
    }
    return trade.duration;
  }, [trade.duration]);

  return (
    <div className="flex flex-col sm:flex-row sm:items-start space-y-3 sm:space-y-0 sm:space-x-4 p-3 sm:p-4 hover:bg-slate-50 transition-colors rounded-lg">
      {/* Trade Info Section - Left Side */}
      <div className="flex-1 min-w-0">
        <div className="flex flex-wrap items-center gap-2 sm:gap-3">
          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
            isProfit ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {isProfit ? '+' : ''}{formatCurrency(pnl)}
          </span>
          <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${tradeTypeColor}`}>
            {trade.position_type?.toUpperCase() || 'UNKNOWN'}
          </span>
          <span className="text-sm font-medium text-slate-900">{trade.instrument}</span>
          <span className="text-xs text-slate-500 font-medium">
            {format(new Date(trade.timestamp), 'MMM d • h:mm a')}
          </span>
          {trade.tags?.map(tag => (
            <span
              key={tag}
              className="px-2 py-0.5 text-xs font-medium bg-blue-50 text-blue-600 rounded-full"
            >
              {tag}
            </span>
          ))}
        </div>
        
        <div className="mt-2 flex flex-wrap items-center gap-2 sm:gap-4">
          <div className="flex items-center gap-1 text-xs sm:text-sm text-slate-600">
            <span className="font-medium">Size:</span> {trade.size}
          </div>
          <div className="flex items-center gap-1 text-xs sm:text-sm text-slate-600">
            <span className="font-medium">Entry:</span> {formatCurrency(trade.entry_price)}
          </div>
          <div className="flex items-center gap-1 text-xs sm:text-sm text-slate-600">
            <span className="font-medium">Exit:</span> {formatCurrency(trade.exit_price)}
          </div>
        </div>

        {/* Trade Notes */}
        {trade.notes && (
          <div className="mt-3 text-xs sm:text-sm text-slate-600">
            <p className="line-clamp-2">{trade.notes}</p>
          </div>
        )}

        {/* Trade Screenshots */}
        {trade.screenshots?.length > 0 && (
          <div className="mt-3 flex flex-wrap gap-2">
            {trade.screenshots.map((screenshot, index) => (
              <div
                key={index}
                className="relative w-16 h-16 sm:w-20 sm:h-20 rounded-lg overflow-hidden border border-slate-200"
              >
                <img
                  src={screenshot}
                  alt={`Trade screenshot ${index + 1}`}
                  className="w-full h-full object-cover"
                />
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Actions Section - Right Side */}
      <div className="flex items-center space-x-2 sm:space-x-3">
        <button
          onClick={() => onSelect(trade)}
          className="p-1.5 sm:p-2 text-slate-400 hover:text-slate-600 rounded-lg hover:bg-slate-100 transition-colors"
        >
          <PencilIcon className="w-4 h-4 sm:w-5 sm:h-5" />
        </button>
        <button
          onClick={() => onDelete(trade.id)}
          className="p-1.5 sm:p-2 text-slate-400 hover:text-red-600 rounded-lg hover:bg-red-50 transition-colors"
        >
          <TrashIcon className="w-4 h-4 sm:w-5 sm:h-5" />
        </button>
      </div>
    </div>
  );
}

function TradeDetailModal({ trade, onClose, onUpdate, onDelete }) {
  const [notes, setNotes] = useState(trade.notes || '');
  const [newTag, setNewTag] = useState('');
  const [tags, setTags] = useState(trade.tags || []);
  const [screenshots, setScreenshots] = useState(trade.screenshots || []);
  const [marketConditions, setMarketConditions] = useState(trade.market_conditions || {
    trend: '',
    volatility: '',
    volume: '',
    key_levels: []
  });
  const [executionQuality, setExecutionQuality] = useState(trade.execution_quality || {
    entry_accuracy: 0,
    exit_accuracy: 0,
    slippage: 0,
    fill_quality: 0
  });
  const [emotions, setEmotions] = useState(trade.emotions || {
    pre_trade: '',
    during_trade: '',
    post_trade: ''
  });
  const [lessonsLearned, setLessonsLearned] = useState(trade.lessons_learned || '');
  const [followUpActions, setFollowUpActions] = useState(trade.follow_up_actions || []);
  const [newAction, setNewAction] = useState('');
  const [activeTab, setActiveTab] = useState('notes');
  const [showKeyboardShortcuts, setShowKeyboardShortcuts] = useState(false);

  useEffect(() => {
    const handleKeyPress = (e) => {
      if (e.key === '?' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        setShowKeyboardShortcuts(true);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, []);

  const tabs = [
    { id: 'notes', label: 'Notes & Tags' },
    { id: 'market', label: 'Market Context' },
    { id: 'execution', label: 'Execution' },
    { id: 'emotions', label: 'Psychology' },
    { id: 'followup', label: 'Follow-up' }
  ];

  const handleSave = () => {
    onUpdate(trade.id, {
      ...trade,
      notes,
      tags,
      screenshots,
      market_conditions: marketConditions,
      execution_quality: executionQuality,
      emotions,
      lessons_learned: lessonsLearned,
      follow_up_actions: followUpActions,
      lastUpdated: new Date().toISOString()
    });
    onClose();
  };

  const handleAddTag = () => {
    if (newTag && !tags.includes(newTag)) {
      setTags([...tags, newTag]);
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleAddAction = () => {
    if (newAction) {
      setFollowUpActions([...followUpActions, {
        id: Date.now(),
        text: newAction,
        completed: false
      }]);
      setNewAction('');
    }
  };

  const handleToggleAction = (actionId) => {
    setFollowUpActions(followUpActions.map(action =>
      action.id === actionId ? { ...action, completed: !action.completed } : action
    ));
  };

  const handleRemoveAction = (actionId) => {
    setFollowUpActions(followUpActions.filter(action => action.id !== actionId));
  };

  const handleAddScreenshot = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      const reader = new FileReader();
      reader.onloadend = () => {
        setScreenshots([
          ...screenshots,
          {
            id: Date.now(),
            url: reader.result,
            timestamp: new Date().toISOString(),
            notes: ''
          }
        ]);
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error adding screenshot:', error);
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-end justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-slate-500 bg-opacity-75 transition-opacity" onClick={onClose} />
        
        <div className="inline-block transform overflow-hidden rounded-lg bg-white text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-4xl sm:align-middle">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-slate-900">
                Edit Trade Details
              </h3>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setShowKeyboardShortcuts(true)}
                  className="p-2 text-slate-400 hover:text-slate-600"
                >
                  <QuestionMarkCircleIcon className="w-5 h-5" />
                </button>
                <button onClick={onClose} className="p-2 text-slate-400 hover:text-slate-600">
                  <XMarkIcon className="w-5 h-5" />
          </button>
              </div>
        </div>

            {/* Tabs */}
            <div className="border-b border-slate-200">
              <nav className="-mb-px flex space-x-8">
                {tabs.map(tab => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`
                      whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm
                      ${activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
                      }
                    `}
                  >
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="mt-4">
              {activeTab === 'notes' && (
                <div className="space-y-4">
            {/* Notes */}
                  <div>
                    <label className="block text-sm font-medium text-slate-700">Notes</label>
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={4}
                      className="mt-1 w-full rounded-lg border-slate-200 text-sm focus:ring-2 focus:ring-blue-500 transition-shadow"
                placeholder="Add your trade notes..."
              />
            </div>

            {/* Tags */}
                  <div>
                    <label className="block text-sm font-medium text-slate-700">Tags</label>
                    <div className="mt-2 flex flex-wrap gap-2">
                {tags.map(tag => (
                  <span
                    key={tag}
                    className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    {tag}
                    <button
                      onClick={() => handleRemoveTag(tag)}
                            className="ml-1.5 text-blue-600 hover:text-blue-800"
                    >
                            <XMarkIcon className="w-4 h-4" />
                    </button>
                  </span>
                ))}
                <div className="flex items-center">
                  <input
                    type="text"
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                          className="w-32 px-2 py-1 text-sm border rounded-lg focus:ring-2 focus:ring-blue-500 transition-shadow"
                    placeholder="Add tag..."
                  />
                  <button
                    onClick={handleAddTag}
                          className="ml-2 p-1 text-blue-600 hover:text-blue-800"
                  >
                          <PlusIcon className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>

                  {/* Screenshots */}
                  <div>
                    <label className="block text-sm font-medium text-slate-700">Screenshots</label>
                    <div className="mt-2 grid grid-cols-3 gap-4">
                      {screenshots.map(screenshot => (
                        <div
                          key={screenshot.id}
                          className="relative aspect-video bg-slate-100 rounded-lg overflow-hidden group"
                        >
                          <img
                            src={screenshot.url}
                            alt="Trade screenshot"
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity" />
                          <button
                            onClick={() => setScreenshots(screenshots.filter(s => s.id !== screenshot.id))}
                            className="absolute top-2 right-2 p-1 bg-white/80 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <XMarkIcon className="w-4 h-4 text-slate-600" />
                          </button>
                          <input
                            type="text"
                            placeholder="Add note..."
                            value={screenshot.notes || ''}
                            onChange={(e) => setScreenshots(screenshots.map(s =>
                              s.id === screenshot.id ? { ...s, notes: e.target.value } : s
                            ))}
                            className="absolute bottom-0 left-0 right-0 bg-white/80 px-2 py-1 text-sm"
                          />
                        </div>
                      ))}
                      <label className="relative aspect-video bg-slate-50 rounded-lg border-2 border-dashed border-slate-200 hover:border-slate-300 cursor-pointer flex items-center justify-center">
                        <div className="text-center">
                          <PhotoIcon className="w-8 h-8 text-slate-400 mx-auto" />
                          <span className="mt-2 block text-sm text-slate-600">
                            Add Screenshot
                          </span>
                        </div>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleAddScreenshot}
                          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        />
                      </label>
                    </div>
                  </div>
                </div>
              )}
              {activeTab === 'market' && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-700">Market Trend</label>
                <select
                  value={marketConditions.trend}
                  onChange={(e) => setMarketConditions({...marketConditions, trend: e.target.value})}
                        className="mt-1 w-full rounded-lg border-slate-200 text-sm focus:ring-2 focus:ring-blue-500 transition-shadow"
                >
                  <option value="">Select Trend</option>
                  <option value="uptrend">Uptrend</option>
                  <option value="downtrend">Downtrend</option>
                  <option value="sideways">Sideways</option>
                </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-slate-700">Volatility</label>
                <select
                  value={marketConditions.volatility}
                  onChange={(e) => setMarketConditions({...marketConditions, volatility: e.target.value})}
                        className="mt-1 w-full rounded-lg border-slate-200 text-sm focus:ring-2 focus:ring-blue-500 transition-shadow"
                >
                  <option value="">Select Volatility</option>
                  <option value="high">High</option>
                  <option value="medium">Medium</option>
                  <option value="low">Low</option>
                </select>
              </div>
            </div>
                </div>
              )}
              {activeTab === 'execution' && (
                <div className="space-y-6">
                <div>
                    <label className="block text-sm font-medium text-slate-700">Entry Accuracy</label>
                    <div className="mt-2 flex items-center space-x-4">
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={executionQuality.entry_accuracy}
                    onChange={(e) => setExecutionQuality({
                      ...executionQuality,
                      entry_accuracy: parseInt(e.target.value)
                    })}
                        className="flex-1"
                  />
                      <span className="text-sm text-slate-600 w-12 text-right">
                        {executionQuality.entry_accuracy}%
                      </span>
                    </div>
                </div>
                <div>
                    <label className="block text-sm font-medium text-slate-700">Exit Accuracy</label>
                    <div className="mt-2 flex items-center space-x-4">
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={executionQuality.exit_accuracy}
                    onChange={(e) => setExecutionQuality({
                      ...executionQuality,
                      exit_accuracy: parseInt(e.target.value)
                    })}
                        className="flex-1"
                  />
                      <span className="text-sm text-slate-600 w-12 text-right">
                        {executionQuality.exit_accuracy}%
                      </span>
                </div>
              </div>
            </div>
              )}
              {activeTab === 'emotions' && (
                <div className="space-y-4">
          <div>
                    <label className="block text-sm font-medium text-slate-700">Pre-Trade Emotions</label>
                <textarea
                  value={emotions.pre_trade}
                  onChange={(e) => setEmotions({...emotions, pre_trade: e.target.value})}
                  rows={2}
                      className="mt-1 w-full rounded-lg border-slate-200 text-sm focus:ring-2 focus:ring-blue-500 transition-shadow"
                      placeholder="How were you feeling before the trade?"
                />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700">During Trade</label>
                <textarea
                  value={emotions.during_trade}
                  onChange={(e) => setEmotions({...emotions, during_trade: e.target.value})}
                  rows={2}
                      className="mt-1 w-full rounded-lg border-slate-200 text-sm focus:ring-2 focus:ring-blue-500 transition-shadow"
                      placeholder="How did you feel during the trade?"
                />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700">Post-Trade Reflection</label>
                <textarea
                  value={emotions.post_trade}
                  onChange={(e) => setEmotions({...emotions, post_trade: e.target.value})}
                  rows={2}
                      className="mt-1 w-full rounded-lg border-slate-200 text-sm focus:ring-2 focus:ring-blue-500 transition-shadow"
                      placeholder="How did you feel after the trade?"
                />
              </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700">Lessons Learned</label>
              <textarea
                value={lessonsLearned}
                onChange={(e) => setLessonsLearned(e.target.value)}
                      rows={3}
                      className="mt-1 w-full rounded-lg border-slate-200 text-sm focus:ring-2 focus:ring-blue-500 transition-shadow"
                placeholder="What did you learn from this trade?"
              />
            </div>
                </div>
              )}
              {activeTab === 'followup' && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-700">Follow-up Actions</label>
              <div className="mt-2 space-y-2">
                <div className="flex items-center">
                  <input
                    type="text"
                    value={newAction}
                    onChange={(e) => setNewAction(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleAddAction()}
                          className="flex-1 rounded-lg border-slate-200 text-sm focus:ring-2 focus:ring-blue-500 transition-shadow"
                    placeholder="Add action item..."
                  />
                  <button
                    onClick={handleAddAction}
                          className="ml-2 p-2 text-blue-600 hover:text-blue-800"
                  >
                          <PlusIcon className="w-5 h-5" />
                  </button>
                </div>
                <ul className="space-y-2">
                  {followUpActions.map(action => (
                    <li key={action.id} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={action.completed}
                        onChange={() => handleToggleAction(action.id)}
                              className="mr-2 rounded text-blue-600 focus:ring-blue-500"
                      />
                            <span className={action.completed ? 'line-through text-slate-500' : 'text-slate-700'}>
                        {action.text}
                      </span>
                      <button
                        onClick={() => handleRemoveAction(action.id)}
                              className="ml-auto p-1 text-slate-400 hover:text-red-600"
                      >
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
                </div>
              )}
          </div>
        </div>

          {/* Footer */}
          <div className="bg-slate-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
            <button
              type="button"
              onClick={handleSave}
              className="inline-flex w-full justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 sm:ml-3 sm:w-auto"
            >
              Save Changes
            </button>
                <button
              type="button"
              onClick={onClose}
              className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-slate-900 shadow-sm ring-1 ring-inset ring-slate-300 hover:bg-slate-50 sm:mt-0 sm:w-auto"
                >
              Cancel
                </button>
              </div>
              </div>
      </div>

      {/* Keyboard Shortcuts Modal */}
      {showKeyboardShortcuts && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-screen items-end justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div 
              className="fixed inset-0 bg-slate-500 bg-opacity-75 transition-opacity" 
              onClick={() => setShowKeyboardShortcuts(false)} 
            />
            
            <div className="inline-block transform overflow-hidden rounded-lg bg-white text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:align-middle">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6">
                <h3 className="text-lg font-medium text-slate-900 mb-4">
                  Keyboard Shortcuts
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-slate-600">Search</span>
                    <kbd className="px-2 py-1 text-sm font-semibold text-slate-800 bg-slate-100 rounded">
                      ⌘/Ctrl + F
                    </kbd>
          </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Close Modal</span>
                    <kbd className="px-2 py-1 text-sm font-semibold text-slate-800 bg-slate-100 rounded">
                      Esc
                    </kbd>
        </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Show Shortcuts</span>
                    <kbd className="px-2 py-1 text-sm font-semibold text-slate-800 bg-slate-100 rounded">
                      ⌘/Ctrl + ?
                    </kbd>
                  </div>
                </div>
              </div>
              <div className="bg-slate-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
          <button
                  type="button"
                  onClick={() => setShowKeyboardShortcuts(false)}
                  className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-slate-900 shadow-sm ring-1 ring-inset ring-slate-300 hover:bg-slate-50 sm:mt-0 sm:w-auto"
          >
                  Close
          </button>
        </div>
      </div>
          </div>
        </div>
      )}
    </div>
  );
}

function formatCurrency(value) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0
  }).format(value);
} 

// Change Tooltip to CustomTooltip in the component definition
function CustomTooltip({ content, children }) {
  return (
    <div className="relative group">
      {children}
      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs font-medium text-white bg-slate-800 rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
        {content}
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 -mt-1">
          <div className="border-4 border-transparent border-t-slate-800" />
        </div>
      </div>
    </div>
  );
}

// Add helper functions
function parseDuration(duration) {
  if (!duration) return 0;
  const parts = duration.split(':').map(Number);
  if (parts.length === 3) {
    const [hours, minutes, seconds] = parts;
    return (hours * 60 * 60) + (minutes * 60) + seconds;
  } else if (parts.length === 2) {
    const [minutes, seconds] = parts;
    return (minutes * 60) + seconds;
  }
  return 0;
}

function formatDuration(duration) {
  if (!duration) return '0m';
  const parts = duration.split(':').map(Number);
  
  if (parts.length === 3) {
    const [hours, minutes, seconds] = parts;
    if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    }
    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    return `${seconds}s`;
  } else if (parts.length === 2) {
    const [minutes, seconds] = parts;
    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    return `${seconds}s`;
  }
  
  return '0m';
}

// Add trade performance indicators
function getTradePerformanceIndicator(trade) {
  if (!trade) return null;
  
  const indicators = [];
  
  // Check R:R ratio
  if (trade.risk_reward) {
    if (trade.risk_reward >= 2) {
      indicators.push({ type: 'positive', message: 'High R:R ratio' });
    } else if (trade.risk_reward < 1) {
      indicators.push({ type: 'negative', message: 'Low R:R ratio' });
    }
  }
  
  // Check execution quality
  if (trade.execution_quality) {
    const avgAccuracy = (trade.execution_quality.entry_accuracy + trade.execution_quality.exit_accuracy) / 2;
    if (avgAccuracy >= 80) {
      indicators.push({ type: 'positive', message: 'Excellent execution' });
    } else if (avgAccuracy <= 50) {
      indicators.push({ type: 'negative', message: 'Poor execution' });
    }
  }
  
  // Check trade duration vs profit
  const pnl = trade.profit_loss ?? trade.pnl ?? 0;
  if (trade.duration && pnl !== 0) {
    const durationInSeconds = parseDuration(trade.duration);
    const profitPerMinute = (pnl / (durationInSeconds / 60));
    if (profitPerMinute >= 100) {
      indicators.push({ type: 'positive', message: 'High efficiency' });
    } else if (profitPerMinute <= -100) {
      indicators.push({ type: 'negative', message: 'High loss rate' });
    }
  }
  
  return indicators;
}

function DeleteConfirmationDialog({ isOpen, trade, onConfirm, onCancel }) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-end justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-slate-500 bg-opacity-75 transition-opacity" onClick={onCancel} />
        
        <div className="inline-block transform overflow-hidden rounded-lg bg-white text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:align-middle">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                <TrashIcon className="h-6 w-6 text-red-600" />
              </div>
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                <h3 className="text-lg font-medium leading-6 text-slate-900">
                  Delete Trade
                </h3>
                <div className="mt-2">
                  <p className="text-sm text-slate-500">
                    Are you sure you want to delete this trade? This action cannot be undone.
                  </p>
                  <div className="mt-2 p-2 bg-slate-50 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-slate-700">{trade.instrument}</span>
                      <span className={`text-sm ${pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {formatCurrency(pnl)}
                      </span>
                      <span className="text-sm text-slate-500">
                        {format(new Date(trade.timestamp), 'MMM d, yyyy • h:mm a')}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="bg-slate-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
            <button
              type="button"
              onClick={onConfirm}
              className="inline-flex w-full justify-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 sm:ml-3 sm:w-auto"
            >
              Delete
            </button>
            <button
              type="button"
              onClick={onCancel}
              className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-slate-900 shadow-sm ring-1 ring-inset ring-slate-300 hover:bg-slate-50 sm:mt-0 sm:w-auto"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Add this helper function at the bottom of the file with other helpers
function getMaxDurationForDay(trades, currentTrade) {
  const tradeDate = format(new Date(currentTrade.timestamp), 'yyyy-MM-dd');
  const sameDayTrades = trades.filter(trade => 
    format(new Date(trade.timestamp), 'yyyy-MM-dd') === tradeDate
  );
  return Math.max(...sameDayTrades.map(trade => parseDuration(trade.duration)));
} 