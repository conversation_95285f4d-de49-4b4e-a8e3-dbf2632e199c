import React, { useState, useEffect } from 'react';
import { CalendarIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
import { format, subDays, startOfDay, endOfDay, parseISO } from 'date-fns';
import DatePicker from 'react-datepicker';
import "react-datepicker/dist/react-datepicker.css";

const PRESET_RANGES = [
  { label: 'Today', getValue: () => ({ start: startOfDay(new Date()), end: endOfDay(new Date()) }) },
  { label: 'Yesterday', getValue: () => ({ start: startOfDay(subDays(new Date(), 1)), end: endOfDay(subDays(new Date(), 1)) }) },
  { label: '7D', getValue: () => ({ start: startOfDay(subDays(new Date(), 6)), end: endOfDay(new Date()) }) },
  { label: '30D', getValue: () => ({ start: startOfDay(subDays(new Date(), 29)), end: endOfDay(new Date()) }) },
  { label: '90D', getValue: () => ({ start: startOfDay(subDays(new Date(), 89)), end: endOfDay(new Date()) }) },
  { label: 'YTD', getValue: () => ({ start: startOfDay(new Date(new Date().getFullYear(), 0, 1)), end: endOfDay(new Date()) }) },
  { label: 'All', getValue: () => ({ start: null, end: null }) }
];

export default function DateRangeSelector({ 
  startDate, 
  endDate, 
  onRangeChange,
  selectedPreset,
  onPresetChange
}) {
  const [showPresets, setShowPresets] = useState(false);
  const [currentPreset, setCurrentPreset] = useState('30D');

  // Initialize with 30D preset if no preset is selected
  useEffect(() => {
    if (!selectedPreset && !startDate && !endDate) {
      const thirtyDayPreset = PRESET_RANGES.find(preset => preset.label === '30D');
      if (thirtyDayPreset) {
        const range = thirtyDayPreset.getValue();
        onPresetChange('30D');
        onRangeChange(range.start, range.end);
      }
    }
  }, []);

  // Update current preset when selectedPreset changes
  useEffect(() => {
    if (selectedPreset) {
      setCurrentPreset(selectedPreset);
    }
  }, [selectedPreset]);

  const handlePresetClick = (preset) => {
    const range = preset.getValue();
    setCurrentPreset(preset.label);
    onPresetChange(preset.label);
    if (range.start && range.end && range.start > range.end) {
      // If preset has invalid range, use end date for both
      onRangeChange(range.end, range.end);
    } else {
      onRangeChange(range.start, range.end);
    }
    setShowPresets(false);
  };

  const getDisplayText = () => {
    if (currentPreset) return currentPreset;
    if (startDate && endDate) return 'Custom Range';
    return '30D';
  };

  const handleDateChange = (date, isStart) => {
    if (isStart) {
      const newStartDate = startOfDay(date);
      // If new start date is after current end date, set end date to start date
      if (endDate && newStartDate > endDate) {
        onRangeChange(newStartDate, endOfDay(date));
      } else {
        onRangeChange(newStartDate, endDate);
      }
    } else {
      const newEndDate = endOfDay(date);
      // If new end date is before current start date, set start date to end date
      if (startDate && newEndDate < startDate) {
        onRangeChange(startOfDay(date), newEndDate);
      } else {
        onRangeChange(startDate, newEndDate);
      }
    }
    setCurrentPreset('Custom Range');
  };

  return (
    <div className="relative">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between bg-white rounded-lg shadow-sm border border-slate-200 p-2 sm:p-1.5 space-y-2 sm:space-y-0">
        {/* Date Range Picker */}
        <div className="flex items-center gap-1.5">
          <div className="flex items-center gap-1 px-2 py-1.5 sm:py-1 bg-slate-50 rounded-md">
            <CalendarIcon className="w-4 h-4 sm:w-3.5 sm:h-3.5 text-slate-400" />
            <DatePicker
              selected={startDate}
              onChange={(date) => handleDateChange(date, true)}
              selectsStart
              startDate={startDate}
              endDate={endDate}
              maxDate={endDate || new Date()} // Can't select start date after end date or today
              dateFormat="MMM d"
              className="bg-transparent text-sm sm:text-xs text-slate-600 border-none focus:ring-0 w-[80px] sm:w-[70px] touch-manipulation"
              placeholderText="Start"
            />
          </div>
          <span className="text-slate-300 text-sm sm:text-xs">→</span>
          <div className="flex items-center gap-1 px-2 py-1.5 sm:py-1 bg-slate-50 rounded-md">
            <DatePicker
              selected={endDate}
              onChange={(date) => handleDateChange(date, false)}
              selectsEnd
              startDate={startDate}
              endDate={endDate}
              minDate={startDate} // Can't select end date before start date
              maxDate={new Date()} // Can't select future dates
              dateFormat="MMM d"
              className="bg-transparent text-sm sm:text-xs text-slate-600 border-none focus:ring-0 w-[80px] sm:w-[70px] touch-manipulation"
              placeholderText="End"
            />
          </div>
        </div>

        {/* Mobile Preset Button */}
        <div className="sm:hidden">
          <button
            onClick={() => setShowPresets(!showPresets)}
            className="w-full flex items-center justify-between px-3 py-1.5 bg-slate-50 rounded-md text-sm text-slate-600 font-medium"
          >
            <span>{getDisplayText()}</span>
            <ChevronDownIcon className={`w-4 h-4 text-slate-400 transition-transform ${showPresets ? 'rotate-180' : ''}`} />
          </button>
        </div>

        {/* Desktop Presets */}
        <div className="hidden sm:flex sm:items-center">
          <div className="h-5 w-px bg-slate-200 mx-2"></div>
          <div className="flex items-center gap-0.5">
            {PRESET_RANGES.map((preset) => (
              <button
                key={preset.label}
                onClick={() => handlePresetClick(preset)}
                className={`px-2 py-1 text-xs font-medium rounded-md transition-all duration-200 ${
                  currentPreset === preset.label
                    ? 'bg-blue-50 text-blue-600 border border-blue-200'
                    : 'text-slate-500 hover:bg-slate-50 hover:text-slate-700'
                }`}
              >
                {preset.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Mobile Preset Menu */}
      {showPresets && (
        <div className="absolute z-10 mt-1 w-full bg-white rounded-lg shadow-lg border border-slate-200 py-1 sm:hidden">
          {PRESET_RANGES.map((preset) => (
            <button
              key={preset.label}
              onClick={() => handlePresetClick(preset)}
              className={`w-full px-4 py-2 text-left text-sm transition-colors ${
                currentPreset === preset.label
                  ? 'bg-blue-50 text-blue-600'
                  : 'text-slate-600 hover:bg-slate-50'
              }`}
            >
              {preset.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
} 