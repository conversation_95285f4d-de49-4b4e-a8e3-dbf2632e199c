import { useState, useMemo } from 'react';
import { format, startOfDay, endOfDay } from 'date-fns';
import {
  FunnelIcon,
  XMarkIcon,
  PlusIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline';

export default function AdvancedFilters({ trades, onFilterChange }) {
  const [isOpen, setIsOpen] = useState(false);
  const [filters, setFilters] = useState({
    dateRange: {
      start: null,
      end: null
    },
    profitRange: {
      min: null,
      max: null
    },
    instruments: [],
    tags: [],
    conditions: []
  });

  const stats = useMemo(() => calculateFilterStats(trades), [trades]);

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
    const filteredTrades = applyFilters(trades, newFilters);
    onFilterChange(filteredTrades);
  };

  return (
    <div className="bg-white rounded-xl shadow-sm">
      {/* Filter Header */}
      <div className="p-4 flex items-center justify-between border-b border-slate-200">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center space-x-2 text-slate-600 hover:text-slate-800"
        >
          <FunnelIcon className="w-5 h-5" />
          <span className="text-sm font-medium">Advanced Filters</span>
        </button>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-slate-500">
            {getActiveFilterCount(filters)} active filters
          </span>
          {getActiveFilterCount(filters) > 0 && (
            <button
              onClick={() => handleFilterChange(getDefaultFilters())}
              className="text-sm text-red-600 hover:text-red-700"
            >
              Clear All
            </button>
          )}
        </div>
      </div>

      {/* Filter Content */}
      {isOpen && (
        <div className="p-4 space-y-6">
          {/* Date Range */}
          <div>
            <h4 className="text-sm font-medium text-slate-700 mb-2">Date Range</h4>
            <div className="flex items-center space-x-2">
              <input
                type="date"
                value={filters.dateRange.start || ''}
                onChange={(e) => handleFilterChange({
                  ...filters,
                  dateRange: {
                    ...filters.dateRange,
                    start: e.target.value ? startOfDay(new Date(e.target.value)) : null
                  }
                })}
                className="rounded-md border-slate-200 text-sm"
              />
              <span className="text-slate-500">to</span>
              <input
                type="date"
                value={filters.dateRange.end || ''}
                onChange={(e) => handleFilterChange({
                  ...filters,
                  dateRange: {
                    ...filters.dateRange,
                    end: e.target.value ? endOfDay(new Date(e.target.value)) : null
                  }
                })}
                className="rounded-md border-slate-200 text-sm"
              />
            </div>
          </div>

          {/* Profit Range */}
          <div>
            <h4 className="text-sm font-medium text-slate-700 mb-2">P&L Range</h4>
            <div className="flex items-center space-x-2">
              <input
                type="number"
                placeholder="Min"
                value={filters.profitRange.min || ''}
                onChange={(e) => handleFilterChange({
                  ...filters,
                  profitRange: {
                    ...filters.profitRange,
                    min: e.target.value ? Number(e.target.value) : null
                  }
                })}
                className="rounded-md border-slate-200 text-sm"
              />
              <span className="text-slate-500">to</span>
              <input
                type="number"
                placeholder="Max"
                value={filters.profitRange.max || ''}
                onChange={(e) => handleFilterChange({
                  ...filters,
                  profitRange: {
                    ...filters.profitRange,
                    max: e.target.value ? Number(e.target.value) : null
                  }
                })}
                className="rounded-md border-slate-200 text-sm"
              />
            </div>
          </div>

          {/* Instruments */}
          <div>
            <h4 className="text-sm font-medium text-slate-700 mb-2">Instruments</h4>
            <div className="flex flex-wrap gap-2">
              {stats.instruments.map(instrument => (
                <button
                  key={instrument}
                  onClick={() => handleFilterChange({
                    ...filters,
                    instruments: filters.instruments.includes(instrument)
                      ? filters.instruments.filter(i => i !== instrument)
                      : [...filters.instruments, instrument]
                  })}
                  className={`px-3 py-1 rounded-full text-sm font-medium ${
                    filters.instruments.includes(instrument)
                      ? 'bg-blue-100 text-blue-700'
                      : 'bg-slate-100 text-slate-700 hover:bg-slate-200'
                  }`}
                >
                  {instrument}
                </button>
              ))}
            </div>
          </div>

          {/* Tags */}
          <div>
            <h4 className="text-sm font-medium text-slate-700 mb-2">Tags</h4>
            <div className="flex flex-wrap gap-2">
              {stats.tags.map(tag => (
                <button
                  key={tag}
                  onClick={() => handleFilterChange({
                    ...filters,
                    tags: filters.tags.includes(tag)
                      ? filters.tags.filter(t => t !== tag)
                      : [...filters.tags, tag]
                  })}
                  className={`px-3 py-1 rounded-full text-sm font-medium ${
                    filters.tags.includes(tag)
                      ? 'bg-green-100 text-green-700'
                      : 'bg-slate-100 text-slate-700 hover:bg-slate-200'
                  }`}
                >
                  {tag}
                </button>
              ))}
            </div>
          </div>

          {/* Custom Conditions */}
          <div>
            <h4 className="text-sm font-medium text-slate-700 mb-2">Conditions</h4>
            <div className="space-y-2">
              {filters.conditions.map((condition, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <select
                    value={condition.field}
                    onChange={(e) => {
                      const newConditions = [...filters.conditions];
                      newConditions[index] = {
                        ...condition,
                        field: e.target.value
                      };
                      handleFilterChange({
                        ...filters,
                        conditions: newConditions
                      });
                    }}
                    className="rounded-md border-slate-200 text-sm"
                  >
                    <option value="profit_loss">P&L</option>
                    <option value="duration">Duration</option>
                    <option value="size">Size</option>
                  </select>
                  <select
                    value={condition.operator}
                    onChange={(e) => {
                      const newConditions = [...filters.conditions];
                      newConditions[index] = {
                        ...condition,
                        operator: e.target.value
                      };
                      handleFilterChange({
                        ...filters,
                        conditions: newConditions
                      });
                    }}
                    className="rounded-md border-slate-200 text-sm"
                  >
                    <option value="gt">Greater than</option>
                    <option value="lt">Less than</option>
                    <option value="eq">Equal to</option>
                  </select>
                  <input
                    type="number"
                    value={condition.value}
                    onChange={(e) => {
                      const newConditions = [...filters.conditions];
                      newConditions[index] = {
                        ...condition,
                        value: e.target.value
                      };
                      handleFilterChange({
                        ...filters,
                        conditions: newConditions
                      });
                    }}
                    className="rounded-md border-slate-200 text-sm"
                  />
                  <button
                    onClick={() => {
                      const newConditions = filters.conditions.filter((_, i) => i !== index);
                      handleFilterChange({
                        ...filters,
                        conditions: newConditions
                      });
                    }}
                    className="text-red-500 hover:text-red-600"
                  >
                    <XMarkIcon className="w-5 h-5" />
                  </button>
                </div>
              ))}
              <button
                onClick={() => handleFilterChange({
                  ...filters,
                  conditions: [
                    ...filters.conditions,
                    { field: 'profit_loss', operator: 'gt', value: 0 }
                  ]
                })}
                className="flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-700"
              >
                <PlusIcon className="w-4 h-4" />
                <span>Add Condition</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Filter Summary */}
      {getActiveFilterCount(filters) > 0 && (
        <div className="p-4 bg-slate-50 border-t border-slate-200">
          <div className="flex flex-wrap gap-2">
            {Object.entries(filters).map(([key, value]) => {
              if (!hasActiveFilter(key, value)) return null;
              return (
                <div
                  key={key}
                  className="px-2 py-1 bg-white rounded-md text-sm text-slate-600 border border-slate-200"
                >
                  {formatFilterSummary(key, value)}
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}

// Helper functions
function calculateFilterStats(trades) {
  if (!trades?.length) {
    return {
      instruments: [],
      tags: []
    };
  }

  const instruments = [...new Set(trades.map(t => t.instrument))];
  const tags = [...new Set(trades.flatMap(t => t.tags || []))];

  return {
    instruments,
    tags
  };
}

function getDefaultFilters() {
  return {
    dateRange: {
      start: null,
      end: null
    },
    profitRange: {
      min: null,
      max: null
    },
    instruments: [],
    tags: [],
    conditions: []
  };
}

function getActiveFilterCount(filters) {
  let count = 0;

  if (filters.dateRange.start || filters.dateRange.end) count++;
  if (filters.profitRange.min || filters.profitRange.max) count++;
  if (filters.instruments.length) count++;
  if (filters.tags.length) count++;
  count += filters.conditions.length;

  return count;
}

function hasActiveFilter(key, value) {
  if (key === 'dateRange') {
    return value.start || value.end;
  }
  if (key === 'profitRange') {
    return value.min || value.max;
  }
  if (Array.isArray(value)) {
    return value.length > 0;
  }
  return false;
}

function formatFilterSummary(key, value) {
  switch (key) {
    case 'dateRange':
      if (value.start && value.end) {
        return `Date: ${format(value.start, 'MMM d')} - ${format(value.end, 'MMM d')}`;
      }
      if (value.start) {
        return `Date: After ${format(value.start, 'MMM d')}`;
      }
      if (value.end) {
        return `Date: Before ${format(value.end, 'MMM d')}`;
      }
      return '';

    case 'profitRange':
      if (value.min && value.max) {
        return `P&L: ${formatCurrency(value.min)} to ${formatCurrency(value.max)}`;
      }
      if (value.min) {
        return `P&L: ≥ ${formatCurrency(value.min)}`;
      }
      if (value.max) {
        return `P&L: ≤ ${formatCurrency(value.max)}`;
      }
      return '';

    case 'instruments':
      return `Instruments: ${value.join(', ')}`;

    case 'tags':
      return `Tags: ${value.join(', ')}`;

    case 'conditions':
      return `${value.length} condition${value.length > 1 ? 's' : ''}`;

    default:
      return '';
  }
}

function applyFilters(trades, filters) {
  return trades.filter(trade => {
    // Date Range Filter
    if (filters.dateRange.start && new Date(trade.timestamp) < filters.dateRange.start) {
      return false;
    }
    if (filters.dateRange.end && new Date(trade.timestamp) > filters.dateRange.end) {
      return false;
    }

    // Profit Range Filter
    if (filters.profitRange.min && trade.profit_loss < filters.profitRange.min) {
      return false;
    }
    if (filters.profitRange.max && trade.profit_loss > filters.profitRange.max) {
      return false;
    }

    // Instruments Filter
    if (filters.instruments.length && !filters.instruments.includes(trade.instrument)) {
      return false;
    }

    // Tags Filter
    if (filters.tags.length) {
      const tradeTags = trade.tags || [];
      if (!filters.tags.some(tag => tradeTags.includes(tag))) {
        return false;
      }
    }

    // Custom Conditions
    for (const condition of filters.conditions) {
      const value = trade[condition.field];
      const conditionValue = Number(condition.value);

      switch (condition.operator) {
        case 'gt':
          if (!(value > conditionValue)) return false;
          break;
        case 'lt':
          if (!(value < conditionValue)) return false;
          break;
        case 'eq':
          if (!(Math.abs(value - conditionValue) < 0.01)) return false;
          break;
        default:
          break;
      }
    }

    return true;
  });
}

function formatCurrency(value) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
}