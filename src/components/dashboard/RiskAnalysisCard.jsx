import React from 'react';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';

export default function RiskAnalysisCard({ riskMetrics }) {
  const { stopLossViolations = 0 } = riskMetrics || {};
  const impactScore = 20;
  const frequency = 'Low';
  const priority = 'HIGH';

  return (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <div className="grid grid-cols-3 gap-6 mb-6">
        <div>
          <h3 className="text-sm font-medium text-slate-600 mb-1">Impact Score</h3>
          <p className="text-2xl font-bold text-slate-900">{impactScore}%</p>
          <p className="text-sm text-slate-500">Estimated impact on overall performance</p>
        </div>
        <div>
          <h3 className="text-sm font-medium text-slate-600 mb-1">Frequency</h3>
          <p className="text-2xl font-bold text-slate-900">{frequency}</p>
          <p className="text-sm text-slate-500">How often this issue occurs</p>
        </div>
        <div>
          <h3 className="text-sm font-medium text-slate-600 mb-1">Priority</h3>
          <p className="text-2xl font-bold text-red-600">{priority}</p>
          <p className="text-sm text-slate-500">Recommended focus level</p>
        </div>
      </div>

      <div className="space-y-4">
        <h4 className="text-lg font-semibold text-slate-900">Detailed Analysis</h4>
        <div className="space-y-4">
          <p className="text-slate-600">
            Analysis of your risk management shows {stopLossViolations} significant issues that need attention. 
            The overall impact of these issues on your trading performance is estimated at {impactScore}%.
          </p>
          
          <div>
            <h5 className="font-medium text-slate-900 mb-2">Key Findings:</h5>
            <ul className="list-disc pl-6 space-y-2 text-slate-600">
              <li>100% of trades lack stop losses</li>
            </ul>
          </div>

          <div>
            <h5 className="font-medium text-slate-900 mb-2">Improvement Plan:</h5>
            <ul className="list-disc pl-6 space-y-2 text-slate-600">
              <li>Always set stop losses before entering trades</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="mt-6 flex items-center space-x-2 text-yellow-600 bg-yellow-50 p-4 rounded-lg">
        <ExclamationTriangleIcon className="w-5 h-5 flex-shrink-0" />
        <p className="text-sm">
          Implementing proper stop losses can significantly reduce your risk exposure and improve overall performance.
        </p>
      </div>
    </div>
  );
} 