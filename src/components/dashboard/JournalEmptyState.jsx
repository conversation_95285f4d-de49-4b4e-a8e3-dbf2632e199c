import React from 'react';
import { DocumentTextIcon, ArrowPathIcon, PencilIcon, CalendarIcon, ChatBubbleLeftIcon, PhotoIcon } from '@heroicons/react/24/outline';

export default function JournalEmptyState({ onImport, onAddTrade, hasData, onAdjustDateRange }) {
  return (
    <div className="absolute inset-x-0 top-[220px] bottom-0 z-40 flex items-start justify-center bg-slate-50/95 backdrop-blur-sm overflow-y-auto">
      <div className="w-full max-w-2xl mx-auto px-4 sm:px-6 py-6 sm:py-8">
        <div className="text-center">
          <div className="mb-6">
            <div className="w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-4 relative">
              <div className="absolute inset-0 bg-emerald-100 rounded-full animate-pulse"></div>
              <div className="absolute inset-2 bg-emerald-50 rounded-full flex items-center justify-center">
                <PencilIcon className="w-8 h-8 sm:w-10 sm:h-10 text-emerald-500" />
              </div>
            </div>
            <h2 className="text-lg sm:text-xl font-bold text-slate-900 mb-2">Start Your Trading Journal</h2>
            <p className="text-sm text-slate-500 mb-6 max-w-lg mx-auto">
              Record your trades to track your progress and build a comprehensive trading history.
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 sm:gap-4 mb-6 sm:mb-8">
            <div className="bg-white rounded-xl p-4 text-left shadow-sm border border-slate-200">
              <div className="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center mb-3">
                <DocumentTextIcon className="w-5 h-5 text-emerald-600" />
              </div>
              <h3 className="text-sm font-semibold text-slate-900 mb-1">Trade Details</h3>
              <p className="text-xs text-slate-500">
                Log entry/exit points, position size, and setup type.
              </p>
            </div>

            <div className="bg-white rounded-xl p-4 text-left shadow-sm border border-slate-200">
              <div className="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center mb-3">
                <ChatBubbleLeftIcon className="w-5 h-5 text-emerald-600" />
              </div>
              <h3 className="text-sm font-semibold text-slate-900 mb-1">Trade Notes</h3>
              <p className="text-xs text-slate-500">
                Document your thoughts and lessons learned.
              </p>
            </div>

            <div className="bg-white rounded-xl p-4 text-left shadow-sm border border-slate-200">
              <div className="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center mb-3">
                <PhotoIcon className="w-5 h-5 text-emerald-600" />
              </div>
              <h3 className="text-sm font-semibold text-slate-900 mb-1">Trade Screenshots</h3>
              <p className="text-xs text-slate-500">
                Capture and annotate your trade setups.
              </p>
            </div>
          </div>

          <div className="max-w-lg mx-auto">
            <h3 className="text-sm font-semibold text-slate-900 mb-3">Journal Features</h3>
            <div className="space-y-2 text-left">
              <div className="flex items-center gap-2 p-3 bg-white rounded-lg border border-slate-200 shadow-sm">
                <div className="w-6 h-6 bg-emerald-50 rounded-full flex items-center justify-center">
                  <span className="text-emerald-600 text-base">✓</span>
                </div>
                <p className="text-xs text-slate-600">Detailed trade entry and management tracking</p>
              </div>
              <div className="flex items-center gap-2 p-3 bg-white rounded-lg border border-slate-200 shadow-sm">
                <div className="w-6 h-6 bg-emerald-50 rounded-full flex items-center justify-center">
                  <span className="text-emerald-600 text-base">✓</span>
                </div>
                <p className="text-xs text-slate-600">Screenshot and chart annotation tools</p>
              </div>
              <div className="flex items-center gap-2 p-3 bg-white rounded-lg border border-slate-200 shadow-sm">
                <div className="w-6 h-6 bg-emerald-50 rounded-full flex items-center justify-center">
                  <span className="text-emerald-600 text-base">✓</span>
                </div>
                <p className="text-xs text-slate-600">Trade reflection and learning documentation</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 