import React from 'react';
import { ChartBarIcon, ArrowPathIcon, PencilIcon, CalendarIcon } from '@heroicons/react/24/outline';

export default function AnalysisEmptyState({ onImport, onAddTrade, hasData, onAdjustDateRange }) {
  return (
    <div className="absolute inset-x-0 top-[220px] bottom-0 z-40 flex items-start justify-center bg-slate-50/95 backdrop-blur-sm overflow-y-auto">
      <div className="w-full max-w-2xl mx-auto px-4 sm:px-6 py-6 sm:py-8">
        <div className="text-center">
          <div className="mb-6">
            <div className="w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-4 relative">
              <div className="absolute inset-0 bg-violet-100 rounded-full animate-pulse"></div>
              <div className="absolute inset-2 bg-violet-50 rounded-full flex items-center justify-center">
                <ChartBarIcon className="w-8 h-8 sm:w-10 sm:h-10 text-violet-500" />
              </div>
            </div>
            <h2 className="text-lg sm:text-xl font-bold text-slate-900 mb-2">Unlock Trading Analytics</h2>
            <p className="text-sm text-slate-500 mb-6 max-w-lg mx-auto">
              Add trades to access in-depth analysis and discover insights about your trading performance.
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mb-6 sm:mb-8">
            <button
              onClick={onImport}
              className="flex items-center justify-center gap-2 px-4 py-3 bg-white rounded-xl shadow-sm border border-slate-200 hover:border-indigo-200 hover:bg-indigo-50 transition-all group"
            >
              <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center group-hover:bg-indigo-200 transition-colors">
                <ArrowPathIcon className="w-5 h-5 text-indigo-600" />
              </div>
              <div className="text-left">
                <h3 className="font-semibold text-slate-900 text-sm">Import Trades</h3>
                <p className="text-xs text-slate-500">Upload your trading history</p>
              </div>
            </button>

            <button
              onClick={onAddTrade}
              className="flex items-center justify-center gap-2 px-4 py-3 bg-white rounded-xl shadow-sm border border-slate-200 hover:border-indigo-200 hover:bg-indigo-50 transition-all group"
            >
              <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center group-hover:bg-indigo-200 transition-colors">
                <PencilIcon className="w-5 h-5 text-indigo-600" />
              </div>
              <div className="text-left">
                <h3 className="font-semibold text-slate-900 text-sm">Add Trade</h3>
                <p className="text-xs text-slate-500">Record a new trade manually</p>
              </div>
            </button>
          </div>

          <div className="bg-white rounded-xl p-4 shadow-sm border border-slate-200">
            <h3 className="text-sm font-semibold text-slate-900 mb-3">Analysis Features</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <div className="p-3 bg-slate-50 rounded-lg">
                <div className="w-6 h-6 bg-indigo-100 rounded-lg flex items-center justify-center mb-2">
                  <svg className="w-4 h-4 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <h4 className="font-medium text-slate-900 text-sm mb-1">Performance Metrics</h4>
                <p className="text-xs text-slate-500">Track key indicators</p>
              </div>

              <div className="p-3 bg-slate-50 rounded-lg">
                <div className="w-6 h-6 bg-indigo-100 rounded-lg flex items-center justify-center mb-2">
                  <svg className="w-4 h-4 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <h4 className="font-medium text-slate-900 text-sm mb-1">Trading Patterns</h4>
                <p className="text-xs text-slate-500">Identify trends</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 