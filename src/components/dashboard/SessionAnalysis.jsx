import { useMemo } from 'react';
import { Bar } from 'react-chartjs-2';
import {
  ClockIcon,
  ArrowTrendingUpIcon,
  BanknotesIcon
} from '@heroicons/react/24/outline';
import CardTemplate from './CardTemplate';

export default function SessionAnalysis({ trades }) {
  const analytics = useMemo(() => new TradeAnalytics(trades), [trades]);
  const sessionData = analytics.getSessionAnalysis();

  return (
    <CardTemplate title="Session Analysis">
      <div className="h-64">
        <Bar data={getChartData(sessionData)} options={getChartOptions()} />
      </div>
    </CardTemplate>
  );
}

function SessionCard({ session, trades, winRate, netPnL, avgTrade }) {
  const isProfit = netPnL >= 0;

  return (
    <div className="p-4 bg-slate-50 rounded-lg">
      <div className="flex items-center justify-between mb-2">
        <h4 className="font-medium capitalize">{session}</h4>
        <span className="text-sm text-slate-500">{trades} trades</span>
      </div>

      <div className="grid grid-cols-3 gap-4">
        <MetricItem
          label="Win Rate"
          value={`${winRate.toFixed(1)}%`}
          icon={<ArrowTrendingUpIcon className="w-4 h-4" />}
          type={winRate >= 50 ? 'positive' : 'negative'}
        />
        <MetricItem
          label="Net P&L"
          value={formatCurrency(netPnL)}
          icon={<BanknotesIcon className="w-4 h-4" />}
          type={isProfit ? 'positive' : 'negative'}
        />
        <MetricItem
          label="Avg Trade"
          value={formatCurrency(avgTrade)}
          icon={<BanknotesIcon className="w-4 h-4" />}
          type={avgTrade >= 0 ? 'positive' : 'negative'}
        />
      </div>
    </div>
  );
}

function MetricItem({ label, value, icon, type }) {
  return (
    <div>
      <div className="flex items-center space-x-1 mb-1">
        <div className={`${
          type === 'positive' ? 'text-green-500' :
          type === 'negative' ? 'text-red-500' :
          'text-slate-500'
        }`}>
          {icon}
        </div>
        <span className="text-sm text-slate-500">{label}</span>
      </div>
      <p className={`text-sm font-medium ${
        type === 'positive' ? 'text-green-600' :
        type === 'negative' ? 'text-red-600' :
        'text-slate-700'
      }`}>
        {value}
      </p>
    </div>
  );
}

function getChartData(sessionData) {
  return {
    labels: sessionData.map(s => s.session),
    datasets: [
      {
        label: 'Win Rate',
        data: sessionData.map(s => s.winRate),
        backgroundColor: sessionData.map(s => 
          s.winRate >= 50 ? 'rgba(34, 197, 94, 0.5)' : 'rgba(239, 68, 68, 0.5)'
        ),
        borderColor: sessionData.map(s => 
          s.winRate >= 50 ? 'rgb(34, 197, 94)' : 'rgb(239, 68, 68)'
        ),
        borderWidth: 1,
        yAxisID: 'y'
      },
      {
        label: 'P&L',
        data: sessionData.map(s => s.netPnL),
        type: 'line',
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        fill: true,
        yAxisID: 'y1'
      }
    ]
  };
}

function getChartOptions() {
  return {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    plugins: {
      legend: {
        position: 'top',
      }
    },
    scales: {
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: 'Win Rate (%)'
        }
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        title: {
          display: true,
          text: 'P&L ($)'
        },
        grid: {
          drawOnChartArea: false
        }
      }
    }
  };
}

function formatCurrency(value) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0
  }).format(value);
} 