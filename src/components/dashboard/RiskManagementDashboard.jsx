import React from 'react';
import {
  ExclamationTriangleIcon,
  ShieldCheckIcon,
  ScaleIcon,
  ChartBarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon
} from '@heroicons/react/24/outline';
import MetricCard from './MetricCard';
import { formatCurrency } from '../../utils/formatters';

export default function RiskManagementDashboard({ trades, riskMetrics }) {
  const getMetricIcon = (value, threshold) => {
    if (value > threshold) return <ArrowTrendingUpIcon className="w-4 h-4 text-green-500" />;
    if (value < threshold) return <ArrowTrendingDownIcon className="w-4 h-4 text-red-500" />;
    return null;
  };

  return (
    <div className="space-y-6">
      {/* Risk Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <MetricCard
          title="Position Risk"
          icon={<ShieldCheckIcon className="text-blue-500" />}
          metrics={[
            {
              label: 'Risk per Trade',
              value: formatCurrency(riskMetrics.avgRiskPerTrade),
              tooltip: 'Average risk amount per trade'
            },
            {
              label: 'Max Position Size',
              value: riskMetrics.maxPositionSize,
              suffix: 'units'
            },
            {
              label: 'Risk/Reward Ratio',
              value: riskMetrics.riskRewardRatio.toFixed(2),
              indicator: getMetricIcon(riskMetrics.riskRewardRatio, 1)
            },
            {
              label: 'Win Rate Required',
              value: `${riskMetrics.requiredWinRate.toFixed(1)}%`,
              tooltip: 'Win rate needed for profitability'
            }
          ]}
        />

        <MetricCard
          title="Capital Risk"
          icon={<ScaleIcon className="text-red-500" />}
          metrics={[
            {
              label: 'Account Risk',
              value: `${riskMetrics.accountRiskPercent.toFixed(1)}%`,
              tooltip: 'Percentage of account at risk',
              valueColor: riskMetrics.accountRiskPercent > 2 ? 'text-red-600' : 'text-green-600'
            },
            {
              label: 'Max Drawdown',
              value: `${riskMetrics.maxDrawdown.toFixed(1)}%`,
              valueColor: 'text-red-600'
            },
            {
              label: 'Risk of Ruin',
              value: `${riskMetrics.riskOfRuin.toFixed(2)}%`,
              tooltip: 'Probability of losing all capital'
            },
            {
              label: 'Capital Utilization',
              value: `${riskMetrics.capitalUtilization.toFixed(1)}%`,
              indicator: getMetricIcon(riskMetrics.capitalUtilization, 50)
            }
          ]}
        />

        <MetricCard
          title="Risk Adjusted Returns"
          icon={<ChartBarIcon className="text-green-500" />}
          metrics={[
            {
              label: 'Sharpe Ratio',
              value: riskMetrics.sharpeRatio.toFixed(2),
              indicator: getMetricIcon(riskMetrics.sharpeRatio, 1),
              tooltip: 'Risk-adjusted return metric'
            },
            {
              label: 'Sortino Ratio',
              value: riskMetrics.sortinoRatio.toFixed(2),
              indicator: getMetricIcon(riskMetrics.sortinoRatio, 1)
            },
            {
              label: 'Calmar Ratio',
              value: riskMetrics.calmarRatio.toFixed(2),
              indicator: getMetricIcon(riskMetrics.calmarRatio, 1)
            },
            {
              label: 'Recovery Factor',
              value: riskMetrics.recoveryFactor.toFixed(2),
              tooltip: 'Net profit relative to max drawdown'
            }
          ]}
        />
      </div>

      {/* Risk Analysis */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <MetricCard
          title="Size Analysis"
          icon={<ArrowTrendingUpIcon className="text-purple-500" />}
          variant="minimal"
          metrics={[
            {
              label: 'Average Size',
              value: riskMetrics.avgPositionSize.toFixed(0),
              suffix: 'units'
            },
            {
              label: 'Size Variance',
              value: `${riskMetrics.positionSizeVariance.toFixed(1)}%`,
              tooltip: 'Variation in position sizes'
            },
            {
              label: 'Optimal Size',
              value: riskMetrics.optimalPositionSize.toFixed(0),
              suffix: 'units',
              tooltip: 'Suggested position size based on analysis'
            },
            {
              label: 'Size Compliance',
              value: `${riskMetrics.sizeComplianceRate.toFixed(1)}%`,
              tooltip: 'Adherence to position size rules'
            }
          ]}
        />

        <MetricCard
          title="Risk Violations"
          icon={<ExclamationTriangleIcon className="text-red-500" />}
          variant="minimal"
          metrics={[
            {
              label: 'Size Violations',
              value: riskMetrics.sizeViolations,
              valueColor: riskMetrics.sizeViolations > 0 ? 'text-red-600' : 'text-green-600',
              suffix: 'trades'
            },
            {
              label: 'Stop Violations',
              value: riskMetrics.stopViolations,
              valueColor: riskMetrics.stopViolations > 0 ? 'text-red-600' : 'text-green-600',
              suffix: 'trades'
            },
            {
              label: 'Risk Limit Breaches',
              value: riskMetrics.riskLimitBreaches,
              valueColor: riskMetrics.riskLimitBreaches > 0 ? 'text-red-600' : 'text-green-600',
              suffix: 'times'
            },
            {
              label: 'Compliance Score',
              value: `${riskMetrics.complianceScore.toFixed(1)}%`,
              valueColor: riskMetrics.complianceScore >= 90 ? 'text-green-600' : 'text-red-600'
            }
          ]}
        />
      </div>

      {/* Improvement Areas */}
      {riskMetrics.improvementAreas?.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-slate-900">Areas for Improvement</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {riskMetrics.improvementAreas.map((area, index) => (
              <MetricCard
                key={index}
                title={area.title}
                icon={<ExclamationTriangleIcon className="text-yellow-500" />}
                variant="minimal"
                metrics={area.items.map(item => ({
                  label: item.label,
                  value: item.value,
                  valueColor: item.type === 'issue' ? 'text-red-600' : 'text-green-600',
                  indicator: item.type === 'issue' ? 
                    <ExclamationTriangleIcon className="w-4 h-4 text-red-500" /> : 
                    <ShieldCheckIcon className="w-4 h-4 text-green-500" />
                }))}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
} 