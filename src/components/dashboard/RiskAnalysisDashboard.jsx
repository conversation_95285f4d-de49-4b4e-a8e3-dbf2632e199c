import { useState, useMemo } from 'react';
import {
  ShieldExclamationIcon,
  ChartBarIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import RiskAnalysisCard from './RiskAnalysisCard';

export default function RiskAnalysisDashboard({ trades }) {
  const [selectedPeriod, setSelectedPeriod] = useState('1M');
  const [showDetails, setShowDetails] = useState(false);

  const riskMetrics = useMemo(() => calculateRiskMetrics(trades, selectedPeriod), [trades, selectedPeriod]);

  if (!riskMetrics) return null;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Risk Analysis</h3>
        <div className="flex items-center space-x-4">
          <PeriodSelector value={selectedPeriod} onChange={setSelectedPeriod} />
        </div>
      </div>

      {/* Risk Analysis Card */}
      <RiskAnalysisCard riskMetrics={riskMetrics} />

      {/* Risk Score Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <RiskScoreCard
          title="Overall Risk"
          score={riskMetrics.overallScore}
          icon={<ShieldExclamationIcon className="w-6 h-6" />}
          details={showDetails ? [
            { label: 'Position Risk', value: `${riskMetrics.positionRiskScore}%` },
            { label: 'Market Risk', value: `${riskMetrics.marketRiskScore}%` }
          ] : []}
        />
        <RiskScoreCard
          title="Volatility"
          score={riskMetrics.volatilityScore}
          icon={<ChartBarIcon className="w-6 h-6" />}
          details={showDetails ? [
            { label: 'Daily Range', value: `${riskMetrics.volatilityScore}%` },
            { label: 'Trend', value: 'Increasing' }
          ] : []}
        />
        <RiskScoreCard
          title="Exposure"
          score={riskMetrics.exposureScore}
          icon={<ExclamationTriangleIcon className="w-6 h-6" />}
          details={showDetails ? [
            { label: 'Max Position', value: formatCurrency(riskMetrics.maxExposure) },
            { label: 'Avg Position', value: formatCurrency(riskMetrics.avgExposure) }
          ] : []}
        />
      </div>

      {/* Risk Factors */}
      {showDetails && (
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h4 className="text-sm font-medium text-slate-700 mb-4">Risk Factors</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {getRiskFactors(trades).map((factor, index) => (
              <RiskFactorCard key={index} {...factor} />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// Helper Components
function PeriodSelector({ value, onChange }) {
  const periods = [
    { id: '1W', label: '1 Week' },
    { id: '1M', label: '1 Month' },
    { id: '3M', label: '3 Months' },
    { id: '6M', label: '6 Months' },
    { id: '1Y', label: 'Year' }
  ];

  return (
    <select
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className="rounded-lg border-slate-200 text-sm"
    >
      {periods.map(period => (
        <option key={period.id} value={period.id}>
          {period.label}
        </option>
      ))}
    </select>
  );
}

function RiskScoreCard({ title, score, icon, details }) {
  const getScoreColor = (score) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="text-slate-400">{icon}</div>
          <h4 className="text-sm font-medium text-slate-700">{title}</h4>
        </div>
        <div className={`text-2xl font-bold ${getScoreColor(score)}`}>
          {score}
        </div>
      </div>
      {details.length > 0 && (
        <div className="space-y-2 mt-4 pt-4 border-t">
          {details.map((detail, index) => (
            <div key={index} className="flex justify-between text-sm">
              <span className="text-slate-500">{detail.label}</span>
              <span className="font-medium">{detail.value}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

function RiskFactorCard({ title, score, impact, recommendations }) {
  const getImpactColor = (impact) => {
    switch (impact.toLowerCase()) {
      case 'high':
        return 'text-red-600';
      case 'medium':
        return 'text-yellow-600';
      default:
        return 'text-green-600';
    }
  };

  return (
    <div className="bg-white rounded-lg border border-slate-200 p-4">
      <div className="flex items-center justify-between mb-3">
        <h5 className="text-sm font-medium text-slate-900">{title}</h5>
        <span className={`text-sm font-medium ${getImpactColor(impact)}`}>
          {impact} Impact
        </span>
      </div>
      <div className="mb-3">
        <div className="h-2 bg-slate-100 rounded-full">
          <div
            className="h-2 bg-blue-600 rounded-full"
            style={{ width: `${score}%` }}
          />
        </div>
      </div>
      <ul className="space-y-1">
        {recommendations.map((rec, index) => (
          <li key={index} className="text-sm text-slate-600">
            • {rec}
          </li>
        ))}
      </ul>
    </div>
  );
}

// Utility function
function formatCurrency(value) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0
  }).format(value);
} 