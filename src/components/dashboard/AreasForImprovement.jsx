import { useMemo, useState } from 'react';
import {
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowTrendingUpIcon,
  ScaleIcon,
  ClockIcon,
  XMarkIcon,
  InformationCircleIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';
import { Tab } from '@headlessui/react';
import { Line, Bar } from 'react-chartjs-2';

// Add formatCurrency utility function
const formatCurrency = (value) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
};

export default function AreasForImprovement({ trades }) {
  const [selectedTab, setSelectedTab] = useState(0);
  const improvements = useMemo(() => analyzeImprovementAreas(trades), [trades]);
  const [selectedArea, setSelectedArea] = useState(null);
  const metrics = useMemo(() => calculateMetrics(trades), [trades]);

  if (!improvements?.length) return null;

  return (
    <div className="bg-white rounded-xl shadow-sm h-full flex flex-col">
      <Tab.Group 
        as="div"
        selectedIndex={selectedTab} 
        onChange={setSelectedTab} 
        className="h-full flex flex-col"
      >
        <div className="border-b border-slate-200 flex-shrink-0">
          <Tab.List className="flex space-x-8 px-6">
            <Tab as="button" className={({ selected }) => `
              py-4 text-sm font-medium border-b-2 outline-none
              ${selected 
                ? 'text-blue-600 border-blue-600' 
                : 'text-slate-500 border-transparent hover:text-slate-700 hover:border-slate-300'}
            `}>
              Overview
            </Tab>
            <Tab as="button" className={({ selected }) => `
              py-4 text-sm font-medium border-b-2 outline-none
              ${selected 
                ? 'text-blue-600 border-blue-600' 
                : 'text-slate-500 border-transparent hover:text-slate-700 hover:border-slate-300'}
            `}>
              Detailed Analysis
            </Tab>
            <Tab as="button" className={({ selected }) => `
              py-4 text-sm font-medium border-b-2 outline-none
              ${selected 
                ? 'text-blue-600 border-blue-600' 
                : 'text-slate-500 border-transparent hover:text-slate-700 hover:border-slate-300'}
            `}>
              Trends
            </Tab>
          </Tab.List>
        </div>

        <Tab.Panels as="div" className="flex-grow overflow-auto">
          {/* Overview Panel */}
          <Tab.Panel as="div" className="p-6 h-full">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 h-full">
              {improvements.map((area, index) => (
                <ImprovementCard
                  key={index}
                  {...area}
                  isSelected={selectedArea === index}
                  onClick={() => setSelectedArea(selectedArea === index ? null : index)}
                />
              ))}
            </div>
          </Tab.Panel>

          {/* Detailed Analysis Panel */}
          <Tab.Panel as="div" className="p-6 h-full">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
              <div className="space-y-6">
                <MetricsOverview metrics={metrics} />
                <TrendAnalysis trades={trades} />
              </div>
              <div className="space-y-6">
                <ComplianceScores trades={trades} />
                <RiskMetrics trades={trades} />
              </div>
            </div>
          </Tab.Panel>

          {/* Trends Panel */}
          <Tab.Panel as="div" className="p-6 h-full">
            <div className="space-y-6 h-full">
              <PerformanceTrends trades={trades} />
              <BehavioralPatterns trades={trades} />
            </div>
          </Tab.Panel>
        </Tab.Panels>
      </Tab.Group>

      {/* Detailed Analysis Modal */}
      {selectedArea !== null && (
        <DetailedAnalysisModal
          area={improvements[selectedArea]}
          metrics={metrics}
          onClose={() => setSelectedArea(null)}
        />
      )}
    </div>
  );
}

function ImprovementCard({ title, icon, issues, suggestions, severity, isSelected, onClick }) {
  const colors = {
    high: 'border-red-200 bg-red-50 hover:bg-red-100',
    medium: 'border-yellow-200 bg-yellow-50 hover:bg-yellow-100',
    low: 'border-blue-200 bg-blue-50 hover:bg-blue-100'
  };

  return (
    <div
      className={`p-4 rounded-lg border ${colors[severity]} transition-all cursor-pointer h-full flex flex-col
        ${isSelected ? 'ring-2 ring-blue-500 transform scale-[1.02]' : ''}`}
      onClick={onClick}
    >
      <div className="flex items-start space-x-3 flex-grow">
        <div className="flex-shrink-0 mt-1">{icon}</div>
        <div className="flex-1">
          <h4 className="font-medium mb-2">{title}</h4>
          <div className="space-y-2">
            {issues.slice(0, isSelected ? undefined : 2).map((issue, index) => (
              <div key={index} className="flex items-start space-x-2">
                <ExclamationTriangleIcon className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                <p className="text-sm text-slate-600">{issue}</p>
              </div>
            ))}
            {!isSelected && issues.length > 2 && (
              <p className="text-sm text-slate-500 italic">
                +{issues.length - 2} more issues...
              </p>
            )}
            {isSelected && suggestions.map((suggestion, index) => (
              <div key={index} className="flex items-start space-x-2">
                <CheckCircleIcon className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <p className="text-sm text-slate-600">{suggestion}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

function MetricBadge({ label, value, color }) {
  return (
    <div className="flex items-center space-x-2">
      <span className="text-sm text-slate-500">{label}</span>
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${color}`}>
        {value}
      </span>
    </div>
  );
}

function DetailedAnalysisModal({ area, metrics, onClose }) {
  return (
    <div className="fixed inset-0 bg-black/30 z-50 flex items-center justify-center">
      <div className="bg-white rounded-xl shadow-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-slate-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">{area.title} Analysis</h3>
            <button
              onClick={onClose}
              className="text-slate-400 hover:text-slate-500"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Detailed Metrics */}
          <div className="grid grid-cols-3 gap-4">
            <MetricCard
              title="Impact Score"
              value={`${calculateImpactScore(area)}%`}
              description="Estimated impact on overall performance"
            />
            <MetricCard
              title="Frequency"
              value={calculateFrequency(area)}
              description="How often this issue occurs"
            />
            <MetricCard
              title="Priority"
              value={area.severity.toUpperCase()}
              description="Recommended focus level"
            />
          </div>

          {/* Detailed Analysis */}
          <div className="space-y-4">
            <h4 className="font-medium">Detailed Analysis</h4>
            <div className="prose prose-sm max-w-none">
              {getDetailedAnalysis(area)}
            </div>
          </div>

          {/* Action Plan */}
          <div className="space-y-4">
            <h4 className="font-medium">Recommended Action Plan</h4>
            <div className="space-y-2">
              {area.suggestions.map((suggestion, index) => (
                <ActionItem
                  key={index}
                  suggestion={suggestion}
                  priority={index === 0 ? 'high' : index === 1 ? 'medium' : 'low'}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Implement the missing analysis functions
function calculateConsistencyScore(trades) {
  if (!trades?.length) return { score: 0 };

  try {
    // Calculate position size consistency
    const sizes = trades.map(t => t.size);
    const avgSize = sizes.reduce((sum, size) => sum + size, 0) / sizes.length;
    const sizeVariance = Math.sqrt(
      sizes.reduce((sum, size) => sum + Math.pow(size - avgSize, 2), 0) / sizes.length
    ) / avgSize;

    // Calculate time consistency
    const timeBetweenTrades = [];
    for (let i = 1; i < trades.length; i++) {
      const diff = new Date(trades[i].timestamp) - new Date(trades[i-1].timestamp);
      timeBetweenTrades.push(diff);
    }
    const avgTime = timeBetweenTrades.reduce((sum, diff) => sum + diff, 0) / timeBetweenTrades.length;
    const timeVariance = Math.sqrt(
      timeBetweenTrades.reduce((sum, diff) => sum + Math.pow(diff - avgTime, 2), 0) / timeBetweenTrades.length
    ) / avgTime;

    // Calculate win rate consistency
    const windowSize = 10;
    const winRates = [];
    for (let i = windowSize; i <= trades.length; i++) {
      const window = trades.slice(i - windowSize, i);
      const wins = window.filter(t => t.profit_loss > 0).length;
      winRates.push(wins / windowSize);
    }
    const avgWinRate = winRates.reduce((sum, rate) => sum + rate, 0) / winRates.length;
    const winRateVariance = Math.sqrt(
      winRates.reduce((sum, rate) => sum + Math.pow(rate - avgWinRate, 2), 0) / winRates.length
    );

    // Calculate overall consistency score
    const sizeScore = Math.max(0, 100 - sizeVariance * 100);
    const timeScore = Math.max(0, 100 - timeVariance * 50);
    const winRateScore = Math.max(0, 100 - winRateVariance * 200);

    return {
      score: Math.round((sizeScore + timeScore + winRateScore) / 3),
      details: {
        sizeConsistency: Math.round(sizeScore),
        timeConsistency: Math.round(timeScore),
        winRateConsistency: Math.round(winRateScore)
      }
    };
  } catch (error) {
    console.error('Error in calculateConsistencyScore:', error);
    return { score: 0 };
  }
}

function analyzeTradingPsychology(trades) {
  if (!trades?.length) return { score: 0 };

  try {
    // Analyze emotional control
    const consecutiveLosses = findLongestLosingStreak(trades);
    const recoveryAfterLoss = analyzeRecoveryAfterLoss(trades);
    const overtrading = analyzeOvertrading(trades);
    const profitTaking = analyzeProfitTaking(trades);

    // Calculate psychology score
    const emotionalControl = Math.max(0, 100 - (consecutiveLosses * 10));
    const recoveryScore = recoveryAfterLoss * 100;
    const overtradingScore = Math.max(0, 100 - overtrading * 20);
    const profitTakingScore = profitTaking * 100;

    return {
      score: Math.round((emotionalControl + recoveryScore + overtradingScore + profitTakingScore) / 4),
      details: {
        emotionalControl,
        recoveryScore,
        overtradingScore,
        profitTakingScore
      }
    };
  } catch (error) {
    console.error('Error in analyzeTradingPsychology:', error);
    return { score: 0 };
  }
}

function analyzeStrategyAdherence(trades) {
  if (!trades?.length) return { score: 0 };

  try {
    // Analyze strategy components
    const stopLossAdherence = calculateStopLossAdherence(trades);
    const targetAdherence = calculateTargetAdherence(trades);
    const positionSizingAdherence = calculatePositionSizingAdherence(trades);
    const timeFrameAdherence = calculateTimeFrameAdherence(trades);

    return {
      score: Math.round((stopLossAdherence + targetAdherence + positionSizingAdherence + timeFrameAdherence) / 4),
      details: {
        stopLossAdherence,
        targetAdherence,
        positionSizingAdherence,
        timeFrameAdherence
      }
    };
  } catch (error) {
    console.error('Error in analyzeStrategyAdherence:', error);
    return { score: 0 };
  }
}

// Helper functions for psychology analysis
function findLongestLosingStreak(trades) {
  let maxStreak = 0;
  let currentStreak = 0;
  trades.forEach(trade => {
    if (trade.profit_loss < 0) {
      currentStreak++;
      maxStreak = Math.max(maxStreak, currentStreak);
    } else {
      currentStreak = 0;
    }
  });
  return maxStreak;
}

function analyzeRecoveryAfterLoss(trades) {
  let recoveryCount = 0;
  let lossCount = 0;
  
  for (let i = 1; i < trades.length; i++) {
    if (trades[i-1].profit_loss < 0) {
      lossCount++;
      if (trades[i].profit_loss > 0) recoveryCount++;
    }
  }
  
  return lossCount ? recoveryCount / lossCount : 1;
}

// Add missing helper functions
function analyzeOvertrading(trades) {
  // Check for trades too close together
  const MINIMUM_TIME_BETWEEN_TRADES = 5 * 60 * 1000; // 5 minutes
  let overtradingCount = 0;

  for (let i = 1; i < trades.length; i++) {
    const timeDiff = new Date(trades[i].timestamp) - new Date(trades[i-1].timestamp);
    if (timeDiff < MINIMUM_TIME_BETWEEN_TRADES) {
      overtradingCount++;
    }
  }

  return overtradingCount / trades.length;
}

function analyzeProfitTaking(trades) {
  // Analyze if trades are being closed too early
  const earlyExits = trades.filter(trade => {
    return trade.profit_loss > 0 && trade.exit_price < trade.target_price;
  }).length;

  return 1 - (earlyExits / trades.length);
}

function calculateStopLossAdherence(trades) {
  const tradesWithStops = trades.filter(t => t.stop_loss).length;
  return (tradesWithStops / trades.length) * 100;
}

function calculateTargetAdherence(trades) {
  const tradesWithTargets = trades.filter(t => t.target_price).length;
  return (tradesWithTargets / trades.length) * 100;
}

function calculatePositionSizingAdherence(trades) {
  const IDEAL_SIZE = 100; // Example ideal position size
  const TOLERANCE = 0.2; // 20% tolerance

  const compliantTrades = trades.filter(t => {
    const deviation = Math.abs(t.size - IDEAL_SIZE) / IDEAL_SIZE;
    return deviation <= TOLERANCE;
  }).length;

  return (compliantTrades / trades.length) * 100;
}

function calculateTimeFrameAdherence(trades) {
  // Check if trades are within trading hours
  const TRADING_START_HOUR = 9;
  const TRADING_END_HOUR = 16;

  const compliantTrades = trades.filter(t => {
    const hour = new Date(t.timestamp).getHours();
    return hour >= TRADING_START_HOUR && hour < TRADING_END_HOUR;
  }).length;

  return (compliantTrades / trades.length) * 100;
}

function calculateImpactScore(area) {
  const severityWeights = {
    high: 1,
    medium: 0.7,
    low: 0.4
  };

  return Math.round(
    (area.issues.length * severityWeights[area.severity]) * 20
  );
}

function calculateFrequency(area) {
  return area.issues.length > 3 ? 'High' :
         area.issues.length > 1 ? 'Medium' : 'Low';
}

function getDetailedAnalysis(area) {
  return (
    <div className="space-y-4">
      <p>
        Analysis of your {area.title.toLowerCase()} shows {area.issues.length} 
        significant issues that need attention. The overall impact of these issues 
        on your trading performance is estimated at {calculateImpactScore(area)}%.
      </p>
      
      <div>
        <h5 className="font-medium">Key Findings:</h5>
        <ul className="list-disc pl-4 space-y-2">
          {area.issues.map((issue, index) => (
            <li key={index}>{issue}</li>
          ))}
        </ul>
      </div>

      <div>
        <h5 className="font-medium">Improvement Plan:</h5>
        <ul className="list-disc pl-4 space-y-2">
          {area.suggestions.map((suggestion, index) => (
            <li key={index}>{suggestion}</li>
          ))}
        </ul>
      </div>
    </div>
  );
}

// Add new components for enhanced interactivity
function ActionItem({ suggestion, priority }) {
  const colors = {
    high: 'border-red-200 bg-red-50',
    medium: 'border-yellow-200 bg-yellow-50',
    low: 'border-blue-200 bg-blue-50'
  };

  return (
    <div className={`p-3 rounded-lg border ${colors[priority]}`}>
      <div className="flex items-start space-x-3">
        <div className="flex-1">
          <p className="text-sm text-slate-600">{suggestion}</p>
          <div className="mt-2 flex items-center space-x-2">
            <span className={`text-xs font-medium ${
              priority === 'high' ? 'text-red-600' :
              priority === 'medium' ? 'text-yellow-600' :
              'text-blue-600'
            }`}>
              {priority.toUpperCase()} PRIORITY
            </span>
            <button className="text-xs text-blue-600 hover:text-blue-700">
              Mark as Complete
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

function MetricCard({ title, value, description }) {
  return (
    <div className="p-4 bg-slate-50 rounded-lg">
      <h5 className="text-sm font-medium text-slate-700">{title}</h5>
      <div className="mt-2 flex items-baseline space-x-2">
        <span className="text-2xl font-semibold">{value}</span>
        <span className="text-sm text-slate-500">{description}</span>
      </div>
    </div>
  );
}

// Add visualization components
function TrendChart({ data, title }) {
  const chartData = {
    labels: data.map((_, i) => `Day ${i + 1}`),
    datasets: [{
      label: title,
      data: data,
      borderColor: 'rgb(59, 130, 246)',
      tension: 0.4,
      fill: false
    }]
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      }
    },
    scales: {
      y: {
        beginAtZero: true
      }
    }
  };

  return (
    <div className="h-[200px]">
      <Line data={chartData} options={options} />
    </div>
  );
}

function getScoreColor(score) {
  if (score >= 80) return 'bg-green-100 text-green-700';
  if (score >= 60) return 'bg-yellow-100 text-yellow-700';
  return 'bg-red-100 text-red-700';
}

function analyzeImprovementAreas(trades) {
  if (!trades?.length) return [];

  const areas = [];

  // Analyze Winning Streaks
  const winningStreaks = trades.reduce((acc, trade, index) => {
    if (trade.profit_loss > 0) {
      if (!acc.current) {
        acc.current = {
          trades: 1,
          totalPnL: trade.profit_loss,
          avgTrade: trade.profit_loss
        };
      } else {
        acc.current.trades++;
        acc.current.totalPnL += trade.profit_loss;
        acc.current.avgTrade = acc.current.totalPnL / acc.current.trades;
      }
    } else if (acc.current) {
      if (acc.current.trades >= 3) {
        acc.streaks.push({ ...acc.current });
      }
      acc.current = null;
    }
    return acc;
  }, { streaks: [], current: null }).streaks;

  if (winningStreaks.length > 0) {
    const avgStreakPnL = winningStreaks.reduce((sum, streak) => sum + streak.avgTrade, 0) / winningStreaks.length;
    areas.push({
      title: 'Profit Taking Strategy',
      icon: <ArrowTrendingUpIcon className="w-5 h-5 text-green-500" />,
      issues: [
        `Average profit per trade in winning streaks: ${formatCurrency(avgStreakPnL)}`,
        'Potential for larger gains during winning streaks'
      ],
      suggestions: [
        'Consider scaling out of positions during winning streaks',
        'Let winners run with trailing stops',
        'Increase position size after confirming trend'
      ],
      severity: 'medium'
    });
  }

  // Risk Management Analysis
  const riskManagement = analyzeRiskManagement(trades);
  if (riskManagement.issues.length > 0) {
    areas.push({
      title: 'Risk Management',
      icon: <ScaleIcon className="w-5 h-5 text-red-500" />,
      issues: riskManagement.issues,
      suggestions: riskManagement.suggestions,
      severity: 'high'
    });
  }

  // Trade Execution Analysis
  const execution = analyzeTradeExecution(trades);
  if (execution.issues.length > 0) {
    areas.push({
      title: 'Trade Execution',
      icon: <ArrowTrendingUpIcon className="w-5 h-5 text-yellow-500" />,
      issues: execution.issues,
      suggestions: execution.suggestions,
      severity: 'medium'
    });
  }

  // Trade Timing Analysis
  const timing = analyzeTradeTimingIssues(trades);
  if (timing.issues.length > 0) {
    areas.push({
      title: 'Trade Timing',
      icon: <ClockIcon className="w-5 h-5 text-blue-500" />,
      issues: timing.issues,
      suggestions: timing.suggestions,
      severity: 'low'
    });
  }

  // Analyze Consecutive Trades
  const consecutiveTrades = findConsecutiveTrades(trades);
  if (consecutiveTrades.length > 0) {
    areas.push({
      title: 'Trading Frequency',
      icon: <ClockIcon className="w-5 h-5 text-yellow-500" />,
      issues: [
        `${consecutiveTrades.length} instances of rapid consecutive trading`,
        'Potential overtrading during volatile periods'
      ],
      suggestions: [
        'Add minimum time between trades',
        'Wait for clear setups before re-entering',
        'Review rapid trading periods for quality'
      ],
      severity: 'medium'
    });
  }

  return areas;
}

function findConsecutiveTrades(trades) {
  const MINIMUM_TIME_GAP = 5 * 60 * 1000; // 5 minutes in milliseconds
  const consecutiveTrades = [];
  
  for (let i = 1; i < trades.length; i++) {
    const timeDiff = new Date(trades[i].timestamp) - new Date(trades[i-1].timestamp);
    if (timeDiff < MINIMUM_TIME_GAP) {
      consecutiveTrades.push({
        trades: [trades[i-1], trades[i]],
        timeGap: timeDiff
      });
    }
  }
  
  return consecutiveTrades;
}

function analyzeRiskManagement(trades) {
  const issues = [];
  const suggestions = [];

  // Analyze stop loss usage
  const tradesWithoutStops = trades.filter(t => !t.stop_loss).length;
  const stopLossPercentage = (tradesWithoutStops / trades.length) * 100;
  if (stopLossPercentage > 20) {
    issues.push(`${Math.round(stopLossPercentage)}% of trades lack stop losses`);
    suggestions.push('Always set stop losses before entering trades');
  }

  // Analyze position sizing
  const avgPositionSize = trades.reduce((sum, t) => sum + t.size, 0) / trades.length;
  const largePositions = trades.filter(t => t.size > avgPositionSize * 1.5).length;
  if (largePositions > trades.length * 0.1) {
    issues.push('Inconsistent position sizing detected');
    suggestions.push('Maintain consistent position sizes based on account risk');
  }

  // Analyze risk-reward ratios
  const poorRRTrades = trades.filter(t => {
    if (!t.target_price || !t.stop_loss) return false;
    const reward = Math.abs(t.target_price - t.entry_price);
    const risk = Math.abs(t.entry_price - t.stop_loss);
    return (reward / risk) < 1.5;
  }).length;

  if (poorRRTrades > trades.length * 0.3) {
    issues.push('Low risk-reward ratios on multiple trades');
    suggestions.push('Aim for minimum 1.5:1 risk-reward ratio');
  }

  return { issues, suggestions };
}

function analyzeTradeExecution(trades) {
  const issues = [];
  const suggestions = [];

  // Analyze early exits on winners
  const earlyExits = trades.filter(t => 
    t.profit_loss > 0 && t.exit_price < t.target_price
  ).length;

  if (earlyExits > trades.length * 0.3) {
    issues.push('Frequent early exits on winning trades');
    suggestions.push('Let winners run to target when possible');
  }

  // Analyze late exits on losers
  const lateExits = trades.filter(t =>
    t.profit_loss < 0 && Math.abs(t.profit_loss) > Math.abs(t.entry_price - t.stop_loss)
  ).length;

  if (lateExits > trades.length * 0.2) {
    issues.push('Multiple trades exceeded planned stop loss');
    suggestions.push('Stick to predetermined stop losses');
  }

  // Analyze win rate consistency
  const winRates = [];
  const windowSize = 10;
  for (let i = windowSize; i <= trades.length; i++) {
    const window = trades.slice(i - windowSize, i);
    const wins = window.filter(t => t.profit_loss > 0).length;
    winRates.push(wins / windowSize);
  }

  const winRateVariance = calculateVariance(winRates);
  if (winRateVariance > 0.1) {
    issues.push('Inconsistent win rate across trading sessions');
    suggestions.push('Focus on maintaining consistent trading approach');
  }

  // Analyze consecutive losses
  let maxConsecutiveLosses = 0;
  let currentLosses = 0;
  trades.forEach(trade => {
    if (trade.profit_loss < 0) {
      currentLosses++;
      maxConsecutiveLosses = Math.max(maxConsecutiveLosses, currentLosses);
    } else {
      currentLosses = 0;
    }
  });

  if (maxConsecutiveLosses > 4) {
    issues.push(`Maximum losing streak of ${maxConsecutiveLosses} trades`);
    suggestions.push('Consider reducing size after consecutive losses');
  }

  return { issues, suggestions };
}

function calculateVariance(array) {
  const mean = array.reduce((a, b) => a + b, 0) / array.length;
  return array.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / array.length;
}

function analyzeTradeTimingIssues(trades) {
  const issues = [];
  const suggestions = [];

  // Analyze trade clustering
  const clusters = findTradeClusters(trades);
  if (clusters.length > trades.length * 0.1) {
    issues.push('Multiple trades opened within short periods');
    suggestions.push('Space out trades to reduce correlation risk');
  }

  // Analyze time-based performance
  const hourlyPerformance = calculateHourlyPerformance(trades);
  const worstHours = findWorstPerformingHours(hourlyPerformance);
  
  if (worstHours.length > 0) {
    issues.push(`Poor performance during ${worstHours.join(', ')}`);
    suggestions.push('Consider reducing activity during underperforming periods');
  }

  return { issues, suggestions };
}

function findTradeClusters(trades) {
  const CLUSTER_THRESHOLD_MINUTES = 5;
  const clusters = [];
  let currentCluster = [];

  trades.forEach((trade, index) => {
    if (index === 0) {
      currentCluster = [trade];
      return;
    }

    const timeDiff = (new Date(trade.timestamp) - new Date(trades[index - 1].timestamp)) / (1000 * 60);
    if (timeDiff <= CLUSTER_THRESHOLD_MINUTES) {
      currentCluster.push(trade);
    } else {
      if (currentCluster.length > 1) {
        clusters.push([...currentCluster]);
      }
      currentCluster = [trade];
    }
  });

  return clusters;
}

function calculateHourlyPerformance(trades) {
  const hourlyStats = Array(24).fill().map(() => ({
    trades: 0,
    pnl: 0
  }));

  trades.forEach(trade => {
    const hour = new Date(trade.timestamp).getHours();
    hourlyStats[hour].trades++;
    hourlyStats[hour].pnl += trade.profit_loss;
  });

  return hourlyStats;
}

function findWorstPerformingHours(hourlyStats) {
  return hourlyStats
    .map((stats, hour) => ({
      hour,
      avgPnL: stats.trades ? stats.pnl / stats.trades : 0
    }))
    .filter(stats => stats.avgPnL < 0)
    .sort((a, b) => a.avgPnL - b.avgPnL)
    .slice(0, 3)
    .map(stats => `${stats.hour}:00`);
}

// New Components
function MetricsOverview({ metrics }) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Performance Metrics</h3>
      <div className="grid grid-cols-2 gap-4">
        {Object.entries(metrics).map(([key, value]) => (
          <MetricCard
            key={key}
            title={key.charAt(0).toUpperCase() + key.slice(1)}
            value={`${value.score}%`}
            trend={value.trend}
            details={value.details}
          />
        ))}
      </div>
    </div>
  );
}

function TrendAnalysis({ trades }) {
  const trendData = useMemo(() => calculateTrends(trades), [trades]);

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Performance Trends</h3>
      <div className="h-[300px]">
        <Line data={trendData} options={getTrendOptions()} />
      </div>
    </div>
  );
}

function ComplianceScores({ trades }) {
  const scores = useMemo(() => calculateComplianceScores(trades), [trades]);

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Compliance Analysis</h3>
      <div className="grid grid-cols-2 gap-4">
        {Object.entries(scores).map(([key, value]) => (
          <ComplianceCard
            key={key}
            title={key}
            score={value.score}
            violations={value.violations}
          />
        ))}
      </div>
    </div>
  );
}

function RiskMetrics({ trades }) {
  const metrics = useMemo(() => calculateRiskMetrics(trades), [trades]);

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Risk Analysis</h3>
      <div className="grid grid-cols-1 gap-4">
        {metrics.map((metric, index) => (
          <RiskMetricCard key={index} {...metric} />
        ))}
      </div>
    </div>
  );
}

function calculateMetrics(trades) {
  if (!trades?.length) {
    return {
      consistency: { score: 0, trend: 0, details: {} },
      psychology: { score: 0, trend: 0, details: {} },
      strategy: { score: 0, trend: 0, details: {} }
    };
  }

  const midPoint = Math.floor(trades.length / 2);
  const recentTrades = trades.slice(midPoint);
  const olderTrades = trades.slice(0, midPoint);

  return {
    consistency: {
      score: calculateConsistencyScore(trades),
      trend: calculateTrend(
        calculateConsistencyScore(olderTrades),
        calculateConsistencyScore(recentTrades)
      ),
      details: analyzeConsistencyDetails(trades)
    },
    psychology: {
      score: calculatePsychologyScore(trades),
      trend: calculateTrend(
        calculatePsychologyScore(olderTrades),
        calculatePsychologyScore(recentTrades)
      ),
      details: analyzePsychologyDetails(trades)
    },
    strategy: {
      score: calculateStrategyScore(trades),
      trend: calculateTrend(
        calculateStrategyScore(olderTrades),
        calculateStrategyScore(recentTrades)
      ),
      details: analyzeStrategyDetails(trades)
    }
  };
}

function calculateTrend(oldValue, newValue) {
  if (!oldValue) return 0;
  return ((newValue - oldValue) / oldValue) * 100;
}

function calculateTrends(trades) {
  if (!trades?.length) return null;

  const dailyStats = trades.reduce((acc, trade) => {
    const date = new Date(trade.timestamp).toISOString().split('T')[0];
    if (!acc[date]) {
      acc[date] = {
        pnl: 0,
        trades: 0,
        winRate: 0,
        avgSize: 0
      };
    }
    acc[date].pnl += trade.profit_loss;
    acc[date].trades++;
    acc[date].winRate = (acc[date].winRate * (acc[date].trades - 1) + 
      (trade.profit_loss > 0 ? 100 : 0)) / acc[date].trades;
    acc[date].avgSize = (acc[date].avgSize * (acc[date].trades - 1) + 
      trade.size) / acc[date].trades;
    return acc;
  }, {});

  const dates = Object.keys(dailyStats).sort();
  return {
    labels: dates,
    datasets: [
      {
        label: 'P&L',
        data: dates.map(date => dailyStats[date].pnl),
        borderColor: 'rgb(34, 197, 94)',
        yAxisID: 'y'
      },
      {
        label: 'Win Rate',
        data: dates.map(date => dailyStats[date].winRate),
        borderColor: 'rgb(59, 130, 246)',
        yAxisID: 'y1'
      }
    ]
  };
}

function getTrendOptions() {
  return {
    responsive: true,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    scales: {
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: 'P&L ($)'
        }
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        title: {
          display: true,
          text: 'Win Rate (%)'
        },
        grid: {
          drawOnChartArea: false
        }
      }
    }
  };
}

function calculateComplianceScores(trades) {
  if (!trades?.length) return {};

  return {
    'Position Size': {
      score: calculatePositionSizingAdherence(trades),
      violations: trades.filter(t => t.size > 100).length
    },
    'Stop Loss': {
      score: calculateStopLossAdherence(trades),
      violations: trades.filter(t => !t.stop_loss).length
    },
    'Risk/Reward': {
      score: calculateRRCompliance(trades),
      violations: trades.filter(t => {
        const rr = Math.abs((t.target_price - t.entry_price) / 
                           (t.entry_price - t.stop_loss));
        return rr < 1.5;
      }).length
    }
  };
}

function calculateRiskMetrics(trades) {
  if (!trades?.length) return [];

  const drawdown = calculateMaxDrawdown(trades);
  const volatility = calculateVolatility(trades);
  const exposure = calculateExposure(trades);

  return [
    {
      title: 'Maximum Drawdown',
      value: `${drawdown.toFixed(2)}%`,
      status: drawdown > 10 ? 'high' : drawdown > 5 ? 'medium' : 'low'
    },
    {
      title: 'Daily Volatility',
      value: `${volatility.toFixed(2)}%`,
      status: volatility > 2 ? 'high' : volatility > 1 ? 'medium' : 'low'
    },
    {
      title: 'Average Exposure',
      value: `${exposure.toFixed(2)}%`,
      status: exposure > 50 ? 'high' : exposure > 30 ? 'medium' : 'low'
    }
  ];
}

function PerformanceTrends({ trades }) {
  const trendData = useMemo(() => {
    if (!trades?.length) return null;

    const dailyStats = trades.reduce((acc, trade) => {
      const date = new Date(trade.timestamp).toISOString().split('T')[0];
      if (!acc[date]) {
        acc[date] = {
          pnl: 0,
          winRate: 0,
          trades: 0,
          avgRR: 0
        };
      }
      acc[date].pnl += trade.profit_loss;
      acc[date].trades++;
      acc[date].winRate = (acc[date].winRate * (acc[date].trades - 1) + 
        (trade.profit_loss > 0 ? 100 : 0)) / acc[date].trades;
      
      const rr = calculateRiskRewardRatio(trade);
      acc[date].avgRR = (acc[date].avgRR * (acc[date].trades - 1) + rr) / acc[date].trades;
      
      return acc;
    }, {});

    return {
      dates: Object.keys(dailyStats).sort(),
      stats: dailyStats
    };
  }, [trades]);

  if (!trendData) return null;

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg p-4">
          <h4 className="text-sm font-medium text-slate-700 mb-4">P&L Trend</h4>
          <div className="h-[200px]">
            <Line
              data={{
                labels: trendData.dates,
                datasets: [{
                  label: 'Daily P&L',
                  data: trendData.dates.map(date => trendData.stats[date].pnl),
                  borderColor: 'rgb(34, 197, 94)',
                  tension: 0.4
                }]
              }}
              options={getTrendOptions('P&L ($)')}
            />
          </div>
        </div>
        <div className="bg-white rounded-lg p-4">
          <h4 className="text-sm font-medium text-slate-700 mb-4">Win Rate Trend</h4>
          <div className="h-[200px]">
            <Line
              data={{
                labels: trendData.dates,
                datasets: [{
                  label: 'Win Rate',
                  data: trendData.dates.map(date => trendData.stats[date].winRate),
                  borderColor: 'rgb(59, 130, 246)',
                  tension: 0.4
                }]
              }}
              options={getTrendOptions('Win Rate (%)')}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

function BehavioralPatterns({ trades }) {
  const patterns = useMemo(() => analyzeBehavioralPatterns(trades), [trades]);

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {patterns.map((pattern, index) => (
          <div key={index} className="bg-white rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <div className={`p-2 rounded-lg ${pattern.color}`}>
                {pattern.icon}
              </div>
              <div>
                <h4 className="text-sm font-medium text-slate-700">{pattern.title}</h4>
                <p className="text-sm text-slate-500 mt-1">{pattern.description}</p>
                {pattern.metrics && (
                  <div className="mt-3 grid grid-cols-2 gap-4">
                    {Object.entries(pattern.metrics).map(([key, value]) => (
                      <div key={key}>
                        <div className="text-sm text-slate-500">{key}</div>
                        <div className="text-sm font-medium">{value}</div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

function analyzeBehavioralPatterns(trades) {
  if (!trades?.length) return [];

  const patterns = [];

  // Analyze revenge trading
  const revengeTrades = findRevengeTrades(trades);
  if (revengeTrades.length > 0) {
    patterns.push({
      title: 'Revenge Trading Detected',
      description: 'Pattern of increasing position sizes after losses',
      icon: <ExclamationTriangleIcon className="w-5 h-5" />,
      color: 'bg-red-50 text-red-600',
      metrics: {
        'Occurrences': revengeTrades.length,
        'Avg Loss': formatCurrency(
          revengeTrades.reduce((sum, t) => sum + t.profit_loss, 0) / revengeTrades.length
        )
      }
    });
  }

  // Analyze overtrading periods
  const overtradingPeriods = findOvertradingPeriods(trades);
  if (overtradingPeriods.length > 0) {
    const avgDuration = Math.round(
      overtradingPeriods.reduce((sum, p) => sum + p.duration, 0) / overtradingPeriods.length
    );
    
    patterns.push({
      title: 'Overtrading Periods',
      description: 'High frequency trading with diminishing returns',
      icon: <ClockIcon className="w-5 h-5" />,
      color: 'bg-yellow-50 text-yellow-600',
      metrics: {
        'Periods': overtradingPeriods.length,
        'Avg Duration': `${avgDuration} min`
      }
    });
  }

  return patterns;
}

function findRevengeTrades(trades) {
  const revengeTrades = [];
  for (let i = 1; i < trades.length; i++) {
    const prevTrade = trades[i - 1];
    const currentTrade = trades[i];
    
    if (prevTrade.profit_loss < 0 && 
        currentTrade.size > prevTrade.size * 1.5 && 
        currentTrade.profit_loss < 0) {
      revengeTrades.push(currentTrade);
    }
  }
  return revengeTrades;
}

function findOvertradingPeriods(trades) {
  const periods = [];
  let currentPeriod = null;
  const THRESHOLD_MINUTES = 5;

  trades.forEach((trade, index) => {
    if (index === 0) return;

    const timeDiff = (new Date(trade.timestamp) - new Date(trades[index - 1].timestamp)) / (1000 * 60);
    
    if (timeDiff < THRESHOLD_MINUTES) {
      if (!currentPeriod) {
        currentPeriod = {
          start: trades[index - 1].timestamp,
          trades: [trades[index - 1], trade],
          duration: timeDiff
        };
      } else {
        currentPeriod.trades.push(trade);
        currentPeriod.duration += timeDiff;
      }
    } else if (currentPeriod) {
      periods.push(currentPeriod);
      currentPeriod = null;
    }
  });

  return periods;
}

function calculateRRCompliance(trades) {
  if (!trades?.length) return 0;

  const compliantTrades = trades.filter(trade => {
    if (!trade.target_price || !trade.stop_loss || !trade.entry_price) return false;
    
    const reward = Math.abs(trade.target_price - trade.entry_price);
    const risk = Math.abs(trade.entry_price - trade.stop_loss);
    
    return risk && (reward / risk >= 1.5);
  });

  return (compliantTrades.length / trades.length) * 100;
}

function calculateMaxDrawdown(trades) {
  if (!trades?.length) return 0;

  let peak = 0;
  let maxDrawdown = 0;
  let runningPnL = 0;

  trades.forEach(trade => {
    runningPnL += trade.profit_loss;
    
    if (runningPnL > peak) {
      peak = runningPnL;
    }

    const drawdown = peak > 0 ? ((peak - runningPnL) / peak) * 100 : 0;
    maxDrawdown = Math.max(maxDrawdown, drawdown);
  });

  return maxDrawdown;
}

function calculateVolatility(trades) {
  if (!trades?.length) return 0;

  // Group trades by day
  const dailyPnL = trades.reduce((acc, trade) => {
    const date = new Date(trade.timestamp).toISOString().split('T')[0];
    acc[date] = (acc[date] || 0) + trade.profit_loss;
    return acc;
  }, {});

  const pnlValues = Object.values(dailyPnL);
  const mean = pnlValues.reduce((sum, pnl) => sum + pnl, 0) / pnlValues.length;
  
  const variance = pnlValues.reduce((sum, pnl) => {
    return sum + Math.pow(pnl - mean, 2);
  }, 0) / pnlValues.length;

  return Math.sqrt(variance);
}

function calculateExposure(trades) {
  if (!trades?.length) return 0;

  // Calculate average position size relative to account size
  const avgPositionSize = trades.reduce((sum, trade) => {
    return sum + (trade.size * trade.entry_price);
  }, 0) / trades.length;

  // Estimate account size based on largest position
  const estimatedAccountSize = Math.max(...trades.map(t => t.size * t.entry_price)) * 5;

  return (avgPositionSize / estimatedAccountSize) * 100;
}

function calculateRiskRewardRatio(trade) {
  if (!trade.target_price || !trade.stop_loss || !trade.entry_price) return 0;
  
  const reward = Math.abs(trade.target_price - trade.entry_price);
  const risk = Math.abs(trade.entry_price - trade.stop_loss);
  
  return risk ? reward / risk : 0;
}

function ComplianceCard({ title, score, violations }) {
  const getColor = (score) => {
    if (score >= 80) return 'bg-green-50 border-green-200 text-green-700';
    if (score >= 60) return 'bg-yellow-50 border-yellow-200 text-yellow-700';
    return 'bg-red-50 border-red-200 text-red-700';
  };

  return (
    <div className={`p-4 rounded-lg border ${getColor(score)}`}>
      <h5 className="text-sm font-medium mb-2">{title}</h5>
      <div className="flex items-center justify-between">
        <div className="text-2xl font-semibold">{score.toFixed(1)}%</div>
        {violations > 0 && (
          <div className="text-sm">
            {violations} violation{violations !== 1 ? 's' : ''}
          </div>
        )}
      </div>
    </div>
  );
}

function RiskMetricCard({ title, value, status }) {
  const colors = {
    high: 'bg-red-50 border-red-200 text-red-700',
    medium: 'bg-yellow-50 border-yellow-200 text-yellow-700',
    low: 'bg-green-50 border-green-200 text-green-700'
  };

  return (
    <div className={`p-4 rounded-lg border ${colors[status]}`}>
      <div className="flex items-center justify-between">
        <h5 className="text-sm font-medium">{title}</h5>
        <div className="text-lg font-semibold">{value}</div>
      </div>
    </div>
  );
}

// Add interactive tooltip component
function InfoTooltip({ content }) {
  const [show, setShow] = useState(false);

  return (
    <div className="relative inline-block">
      <div
        onMouseEnter={() => setShow(true)}
        onMouseLeave={() => setShow(false)}
        className="text-slate-400 hover:text-slate-600 cursor-help"
      >
        <InformationCircleIcon className="w-4 h-4" />
      </div>
      {show && (
        <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 w-48 p-2 bg-slate-800 text-white text-xs rounded shadow-lg z-10">
          {content}
          <div className="absolute -bottom-1 left-1/2 -translate-x-1/2 w-2 h-2 bg-slate-800 transform rotate-45" />
        </div>
      )}
    </div>
  );
}

// Add expandable detail section
function ExpandableDetails({ title, children }) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="border rounded-lg overflow-hidden">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full p-4 flex items-center justify-between bg-slate-50 hover:bg-slate-100"
      >
        <span className="font-medium">{title}</span>
        <ChevronDownIcon
          className={`w-5 h-5 transform transition-transform ${
            isExpanded ? 'rotate-180' : ''
          }`}
        />
      </button>
      {isExpanded && <div className="p-4 border-t">{children}</div>}
    </div>
  );
}

// Add interactive filters
function MetricsFilter({ onChange, selectedMetrics }) {
  const metrics = [
    { id: 'risk', label: 'Risk Metrics' },
    { id: 'performance', label: 'Performance Metrics' },
    { id: 'behavioral', label: 'Behavioral Metrics' }
  ];

  return (
    <div className="flex space-x-2">
      {metrics.map(metric => (
        <button
          key={metric.id}
          onClick={() => onChange(metric.id)}
          className={`px-3 py-1 rounded-full text-sm ${
            selectedMetrics.includes(metric.id)
              ? 'bg-blue-100 text-blue-700'
              : 'bg-slate-100 text-slate-600 hover:bg-slate-200'
          }`}
        >
          {metric.label}
        </button>
      ))}
    </div>
  );
}

// Add these missing analysis functions
function analyzeConsistencyDetails(trades) {
  if (!trades?.length) return {};

  const sizeConsistency = analyzeSizeConsistency(trades);
  const timeConsistency = analyzeTimeConsistency(trades);
  const winRateConsistency = analyzeWinRateConsistency(trades);

  return {
    sizeConsistency,
    timeConsistency,
    winRateConsistency,
    recommendations: generateConsistencyRecommendations({
      sizeConsistency,
      timeConsistency,
      winRateConsistency
    })
  };
}

function analyzePsychologyDetails(trades) {
  if (!trades?.length) return {};

  const emotionalControl = analyzeEmotionalControl(trades);
  const recoveryPatterns = analyzeRecoveryPatterns(trades);
  const tradingDiscipline = analyzeTradingDiscipline(trades);

  return {
    emotionalControl,
    recoveryPatterns,
    tradingDiscipline,
    recommendations: generatePsychologyRecommendations({
      emotionalControl,
      recoveryPatterns,
      tradingDiscipline
    })
  };
}

function analyzeStrategyDetails(trades) {
  if (!trades?.length) return {};

  const setupAdherence = analyzeSetupAdherence(trades);
  const riskManagement = analyzeDetailedRiskManagement(trades);
  const marketConditions = analyzeMarketConditions(trades);

  return {
    setupAdherence,
    riskManagement,
    marketConditions,
    recommendations: generateStrategyRecommendations({
      setupAdherence,
      riskManagement,
      marketConditions
    })
  };
}

// Helper functions for detailed analysis
function analyzeSizeConsistency(trades) {
  const sizes = trades.map(t => t.size);
  const avgSize = sizes.reduce((a, b) => a + b, 0) / sizes.length;
  const variance = sizes.reduce((sum, size) => sum + Math.pow(size - avgSize, 2), 0) / sizes.length;
  const stdDev = Math.sqrt(variance);
  
  return {
    score: Math.max(0, 100 - (stdDev / avgSize) * 100),
    avgSize,
    stdDev,
    consistency: stdDev / avgSize
  };
}

function analyzeTimeConsistency(trades) {
  const intervals = [];
  for (let i = 1; i < trades.length; i++) {
    intervals.push(new Date(trades[i].timestamp) - new Date(trades[i-1].timestamp));
  }
  
  const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
  const variance = intervals.reduce((sum, int) => sum + Math.pow(int - avgInterval, 2), 0) / intervals.length;
  const stdDev = Math.sqrt(variance);

  return {
    score: Math.max(0, 100 - (stdDev / avgInterval) * 50),
    avgInterval,
    stdDev,
    consistency: stdDev / avgInterval
  };
}

function analyzeWinRateConsistency(trades) {
  const windowSize = 10;
  const winRates = [];
  
  for (let i = windowSize; i <= trades.length; i++) {
    const window = trades.slice(i - windowSize, i);
    const wins = window.filter(t => t.profit_loss > 0).length;
    winRates.push(wins / windowSize);
  }

  const avgWinRate = winRates.reduce((a, b) => a + b, 0) / winRates.length;
  const variance = winRates.reduce((sum, rate) => sum + Math.pow(rate - avgWinRate, 2), 0) / winRates.length;
  const stdDev = Math.sqrt(variance);

  return {
    score: Math.max(0, 100 - stdDev * 200),
    avgWinRate,
    stdDev,
    consistency: stdDev / avgWinRate
  };
}

function generateConsistencyRecommendations(metrics) {
  const recommendations = [];

  if (metrics.sizeConsistency.consistency > 0.3) {
    recommendations.push('Standardize position sizing based on account risk');
  }
  if (metrics.timeConsistency.consistency > 0.5) {
    recommendations.push('Develop more consistent trading schedule');
  }
  if (metrics.winRateConsistency.consistency > 0.2) {
    recommendations.push('Focus on maintaining consistent trading approach');
  }

  return recommendations;
}

// Add missing analysis functions
function analyzeEmotionalControl(trades) {
  const maxLossStreak = findLongestLosingStreak(trades);
  const recoveryRate = analyzeRecoveryAfterLoss(trades);
  
  return {
    score: Math.max(0, 100 - maxLossStreak * 10) * recoveryRate,
    maxLossStreak,
    recoveryRate,
    details: {
      streakLength: maxLossStreak,
      recoveryPercentage: recoveryRate * 100
    }
  };
}

function analyzeRecoveryPatterns(trades) {
  let recoveryTrades = 0;
  let totalLosses = 0;
  
  for (let i = 1; i < trades.length; i++) {
    if (trades[i-1].profit_loss < 0) {
      totalLosses++;
      if (trades[i].profit_loss > 0) {
        recoveryTrades++;
      }
    }
  }
  
  return {
    score: totalLosses ? (recoveryTrades / totalLosses) * 100 : 100,
    recoveryRate: totalLosses ? recoveryTrades / totalLosses : 1,
    details: {
      totalLosses,
      successfulRecoveries: recoveryTrades
    }
  };
}

function analyzeTradingDiscipline(trades) {
  const stopLossAdherence = calculateStopLossAdherence(trades);
  const targetAdherence = calculateTargetAdherence(trades);
  const timeAdherence = calculateTimeFrameAdherence(trades);
  
  return {
    score: (stopLossAdherence + targetAdherence + timeAdherence) / 3,
    details: {
      stopLossAdherence,
      targetAdherence,
      timeAdherence
    }
  };
}

function analyzeSetupAdherence(trades) {
  // Analyze if trades follow predefined setups
  const withValidSetup = trades.filter(t => 
    t.entry_price && t.stop_loss && t.target_price
  ).length;
  
  return {
    score: (withValidSetup / trades.length) * 100,
    details: {
      validSetups: withValidSetup,
      totalTrades: trades.length
    }
  };
}

function analyzeDetailedRiskManagement(trades) {
  const riskPerTrade = trades.map(t => {
    if (!t.stop_loss || !t.entry_price) return null;
    return Math.abs((t.stop_loss - t.entry_price) * t.size);
  }).filter(r => r !== null);

  const avgRisk = riskPerTrade.reduce((a, b) => a + b, 0) / riskPerTrade.length;
  const maxRisk = Math.max(...riskPerTrade);
  
  return {
    score: Math.max(0, 100 - (maxRisk / avgRisk - 1) * 50),
    details: {
      averageRisk: avgRisk,
      maxRisk,
      riskConsistency: avgRisk / maxRisk
    }
  };
}

function analyzeMarketConditions(trades) {
  // Group trades by market conditions (if available in trade data)
  // For now, use time-based analysis as a proxy
  const marketHourTrades = trades.filter(t => {
    const hour = new Date(t.timestamp).getHours();
    return hour >= 9 && hour < 16;
  }).length;
  
  return {
    score: (marketHourTrades / trades.length) * 100,
    details: {
      marketHourTrades,
      totalTrades: trades.length,
      adherenceRate: marketHourTrades / trades.length
    }
  };
}

function generatePsychologyRecommendations(metrics) {
  const recommendations = [];
  
  if (metrics.emotionalControl.score < 70) {
    recommendations.push('Implement cooling-off period after losses');
    recommendations.push('Use a trading journal to track emotions');
  }
  
  if (metrics.recoveryPatterns.recoveryRate < 0.6) {
    recommendations.push('Review recovery strategy after losses');
    recommendations.push('Consider reducing size after consecutive losses');
  }
  
  if (metrics.tradingDiscipline.score < 80) {
    recommendations.push('Stick to predefined trading plan');
    recommendations.push('Review trades that deviated from rules');
  }
  
  return recommendations;
}

function generateStrategyRecommendations(metrics) {
  const recommendations = [];
  
  if (metrics.setupAdherence.score < 80) {
    recommendations.push('Document trading setups more clearly');
    recommendations.push('Only take trades that match defined setups');
  }
  
  if (metrics.riskManagement.score < 70) {
    recommendations.push('Standardize risk per trade');
    recommendations.push('Review position sizing rules');
  }
  
  if (metrics.marketConditions.score < 90) {
    recommendations.push('Focus on trading during core market hours');
    recommendations.push('Analyze performance by market conditions');
  }
  
  return recommendations;
}

function calculatePsychologyScore(trades) {
  if (!trades?.length) return 0;

  try {
    // Calculate emotional control score
    const emotionalControl = analyzeEmotionalControl(trades);
    const recoveryPatterns = analyzeRecoveryPatterns(trades);
    const tradingDiscipline = analyzeTradingDiscipline(trades);

    // Weight the components
    const weights = {
      emotionalControl: 0.4,
      recoveryPatterns: 0.3,
      tradingDiscipline: 0.3
    };

    // Calculate weighted score
    const weightedScore = 
      emotionalControl.score * weights.emotionalControl +
      recoveryPatterns.score * weights.recoveryPatterns +
      tradingDiscipline.score * weights.tradingDiscipline;

    return Math.round(weightedScore);
  } catch (error) {
    console.error('Error calculating psychology score:', error);
    return 0;
  }
}

function calculateStrategyScore(trades) {
  if (!trades?.length) return 0;

  try {
    // Calculate strategy components
    const setupAdherence = analyzeSetupAdherence(trades);
    const riskManagement = analyzeDetailedRiskManagement(trades);
    const marketConditions = analyzeMarketConditions(trades);

    // Weight the components
    const weights = {
      setupAdherence: 0.4,
      riskManagement: 0.4,
      marketConditions: 0.2
    };

    // Calculate weighted score
    const weightedScore = 
      setupAdherence.score * weights.setupAdherence +
      riskManagement.score * weights.riskManagement +
      marketConditions.score * weights.marketConditions;

    return Math.round(weightedScore);
  } catch (error) {
    console.error('Error calculating strategy score:', error);
    return 0;
  }
}

// ... rest of the code ... 