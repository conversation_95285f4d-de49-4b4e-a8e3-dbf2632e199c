import React, { useMemo, useState } from 'react';
import {
  ChartBarIcon,
  BanknotesIcon,
  ScaleIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ClockIcon,
  TrophyIcon,
  CurrencyDollarIcon,
  ChartBarSquareIcon
} from '@heroicons/react/24/outline';
import MetricCard from './MetricCard';
import { formatCurrency, formatDecimal } from '../../utils/formatters';

const TIME_PERIODS = [
  { id: '1D', label: '1D' },
  { id: '1W', label: '1W' },
  { id: '1M', label: '1M' },
  { id: '3M', label: '3M' },
  { id: '1Y', label: '1Y' },
  { id: 'ALL', label: 'ALL' },
  { id: 'CUSTOM', label: 'Custom' }
];

export default function PerformanceMetrics({ trades }) {
  const [selectedPeriod, setSelectedPeriod] = useState('1M');
  const metrics = useMemo(() => calculateMetrics(trades), [trades]);

  const getMetricIcon = (value) => {
    if (value > 0) return <ArrowTrendingUpIcon className="w-4 h-4 text-green-500" />;
    if (value < 0) return <ArrowTrendingDownIcon className="w-4 h-4 text-red-500" />;
    return null;
  };

  return (
    <div className="bg-white rounded-xl shadow-sm">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="space-y-1">
            <h2 className="text-lg font-semibold text-slate-900">Progress Overview</h2>
            <p className="text-sm text-slate-500">Track your trading performance over time</p>
          </div>
          
          <div className="flex items-center gap-2">
            {TIME_PERIODS.map(period => (
              <button
                key={period.id}
                onClick={() => setSelectedPeriod(period.id)}
                className={`px-3 py-1.5 text-sm font-medium rounded-lg transition-colors
                  ${selectedPeriod === period.id
                    ? 'bg-blue-50 text-blue-600'
                    : 'text-slate-600 hover:bg-slate-50'
                  }`}
              >
                {period.label}
              </button>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-4 gap-4 mb-6">
          {/* Performance Score Mini Card */}
          <div className="p-4 rounded-lg bg-slate-50">
            <div className="flex items-center gap-2 text-sm font-medium text-slate-600 mb-1">
              <TrophyIcon className="w-4 h-4" />
              Performance Score
            </div>
            <div className="flex items-center gap-1">
              <span className="text-lg font-semibold text-slate-900">85</span>
              <span className="text-sm text-slate-500">/100</span>
            </div>
          </div>

          <div className="p-4 rounded-lg bg-slate-50">
            <div className="flex items-center gap-2 text-sm font-medium text-slate-600 mb-1">
              <CurrencyDollarIcon className="w-4 h-4" />
              Total P&L
            </div>
            <div className="flex items-center gap-1">
              <span className="text-lg font-semibold text-slate-900">{formatCurrency(metrics.netProfit)}</span>
              {getMetricIcon(metrics.netProfit)}
            </div>
          </div>

          <div className="p-4 rounded-lg bg-slate-50">
            <div className="flex items-center gap-2 text-sm font-medium text-slate-600 mb-1">
              <ChartBarSquareIcon className="w-4 h-4" />
              Win Rate
            </div>
            <div className="flex items-center gap-1">
              <span className="text-lg font-semibold text-slate-900">{formatDecimal(metrics.winRate)}%</span>
              {getMetricIcon(metrics.winRate - 50)}
            </div>
          </div>

          <div className="p-4 rounded-lg bg-slate-50">
            <div className="flex items-center gap-2 text-sm font-medium text-slate-600 mb-1">
              <ArrowTrendingUpIcon className="w-4 h-4" />
              Profit Factor
            </div>
            <div className="flex items-center gap-1">
              <span className="text-lg font-semibold text-slate-900">{formatDecimal(metrics.profitFactor)}</span>
              {getMetricIcon(metrics.profitFactor - 1)}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <MetricCard
            title="Win Rate"
            icon={<ChartBarIcon className="text-blue-500" />}
            metrics={[
              {
                label: 'Win Rate',
                value: `${formatDecimal(metrics.winRate)}%`,
                indicator: getMetricIcon(metrics.winRateChange),
                tooltip: 'Percentage of profitable trades'
              },
              {
                label: 'Winning Trades',
                value: metrics.winningTrades,
                suffix: 'trades',
                valueColor: 'text-green-600'
              },
              {
                label: 'Losing Trades',
                value: metrics.losingTrades,
                suffix: 'trades',
                valueColor: 'text-red-600'
              },
              {
                label: 'Win Streak',
                value: metrics.winStreak,
                suffix: 'trades',
                valueColor: 'text-green-600'
              }
            ]}
          />

          <MetricCard
            title="Average Trade"
            icon={<BanknotesIcon className="text-green-500" />}
            metrics={[
              {
                label: 'Average Trade',
                value: formatCurrency(metrics.avgTrade),
                indicator: getMetricIcon(metrics.avgTradeChange)
              },
              {
                label: 'Average Win',
                value: formatCurrency(metrics.avgWin),
                valueColor: 'text-green-600'
              },
              {
                label: 'Average Loss',
                value: formatCurrency(metrics.avgLoss),
                valueColor: 'text-red-600'
              },
              {
                label: 'Expectancy',
                value: formatCurrency(metrics.expectancy),
                tooltip: 'Expected value per trade'
              }
            ]}
          />

          <MetricCard
            title="Risk/Reward"
            icon={<ScaleIcon className="text-red-500" />}
            metrics={[
              {
                label: 'Risk/Reward Ratio',
                value: formatDecimal(metrics.riskRewardRatio),
                tooltip: 'Average win size relative to average loss'
              },
              {
                label: 'Max Win',
                value: formatCurrency(metrics.maxWin),
                valueColor: 'text-green-600'
              },
              {
                label: 'Max Loss',
                value: formatCurrency(metrics.maxLoss),
                valueColor: 'text-red-600'
              },
              {
                label: 'Profit Factor',
                value: formatDecimal(metrics.profitFactor),
                indicator: getMetricIcon(metrics.profitFactor - 1)
              }
            ]}
          />

          <MetricCard
            title="Profit Factor"
            icon={<ArrowTrendingUpIcon className="text-purple-500" />}
            metrics={[
              {
                label: 'Profit Factor',
                value: formatDecimal(metrics.profitFactor),
                indicator: getMetricIcon(metrics.profitFactor - 1),
                tooltip: 'Ratio of gross profit to gross loss'
              },
              {
                label: 'Gross Profit',
                value: formatCurrency(metrics.grossProfit),
                valueColor: 'text-green-600'
              },
              {
                label: 'Gross Loss',
                value: formatCurrency(metrics.grossLoss),
                valueColor: 'text-red-600'
              },
              {
                label: 'Net Profit',
                value: formatCurrency(metrics.netProfit),
                valueColor: metrics.netProfit >= 0 ? 'text-green-600' : 'text-red-600'
              }
            ]}
          />

          <MetricCard
            title="Drawdown"
            icon={<ArrowTrendingDownIcon className="text-red-500" />}
            metrics={[
              {
                label: 'Max Drawdown',
                value: `${formatDecimal(metrics.maxDrawdown)}%`,
                valueColor: 'text-red-600',
                tooltip: 'Largest peak-to-valley decline'
              },
              {
                label: 'Peak Equity',
                value: formatCurrency(metrics.peakEquity)
              },
              {
                label: 'Valley Equity',
                value: formatCurrency(metrics.valleyEquity)
              },
              {
                label: 'Recovery Factor',
                value: formatDecimal(metrics.recoveryFactor)
              }
            ]}
          />

          <MetricCard
            title="Time Analysis"
            icon={<ClockIcon className="text-blue-500" />}
            metrics={[
              {
                label: 'Avg Duration',
                value: metrics.avgDuration,
                tooltip: 'Average time in market per trade'
              },
              {
                label: 'Best Hour',
                value: metrics.bestHour,
                valueColor: 'text-green-600'
              },
              {
                label: 'Worst Hour',
                value: metrics.worstHour,
                valueColor: 'text-red-600'
              },
              {
                label: 'Trading Days',
                value: metrics.tradingDays,
                suffix: 'days'
              }
            ]}
          />
        </div>
      </div>
    </div>
  );
}

// Helper function to calculate metrics
function calculateMetrics(trades) {
  if (!trades?.length) {
    return {
      winRate: 0,
      winningTrades: 0,
      losingTrades: 0,
      winStreak: 0,
      avgTrade: 0,
      avgWin: 0,
      avgLoss: 0,
      expectancy: 0,
      riskRewardRatio: 0,
      maxWin: 0,
      maxLoss: 0,
      profitFactor: 0,
      grossProfit: 0,
      grossLoss: 0,
      netProfit: 0,
      maxDrawdown: 0,
      peakEquity: 0,
      valleyEquity: 0,
      recoveryFactor: 0,
      avgDuration: '0h 0m',
      bestHour: 'N/A',
      worstHour: 'N/A',
      tradingDays: 0,
      winRateChange: 0,
      avgTradeChange: 0
    };
  }

  const winningTrades = trades.filter(t => t.profit_loss > 0);
  const losingTrades = trades.filter(t => t.profit_loss < 0);
  
  const grossProfit = winningTrades.reduce((sum, t) => sum + t.profit_loss, 0);
  const grossLoss = Math.abs(losingTrades.reduce((sum, t) => sum + t.profit_loss, 0));
  const netProfit = grossProfit - grossLoss;

  // Calculate win streak
  let currentStreak = 0;
  let maxStreak = 0;
  trades.forEach(trade => {
    if (trade.profit_loss > 0) {
      currentStreak++;
      maxStreak = Math.max(maxStreak, currentStreak);
    } else {
      currentStreak = 0;
    }
  });

  // Calculate trading days
  const tradingDays = new Set(
    trades.map(t => new Date(t.timestamp).toDateString())
  ).size;

  return {
    winRate: (winningTrades.length / trades.length) * 100,
    winningTrades: winningTrades.length,
    losingTrades: losingTrades.length,
    winStreak: maxStreak,
    avgTrade: netProfit / trades.length,
    avgWin: grossProfit / winningTrades.length,
    avgLoss: grossLoss / losingTrades.length,
    expectancy: (grossProfit / winningTrades.length) * (winningTrades.length / trades.length) -
               (grossLoss / losingTrades.length) * (losingTrades.length / trades.length),
    riskRewardRatio: (grossProfit / winningTrades.length) / (grossLoss / losingTrades.length),
    maxWin: Math.max(...winningTrades.map(t => t.profit_loss)),
    maxLoss: Math.min(...losingTrades.map(t => t.profit_loss)),
    profitFactor: grossLoss === 0 ? grossProfit : grossProfit / grossLoss,
    grossProfit,
    grossLoss,
    netProfit,
    maxDrawdown: calculateMaxDrawdown(trades),
    peakEquity: calculatePeakEquity(trades),
    valleyEquity: calculateValleyEquity(trades),
    recoveryFactor: calculateRecoveryFactor(trades),
    avgDuration: calculateAverageDuration(trades),
    bestHour: calculateBestHour(trades),
    worstHour: calculateWorstHour(trades),
    tradingDays,
    winRateChange: 0, // Compare with previous period
    avgTradeChange: 0 // Compare with previous period
  };
}

// Helper functions for calculations
function calculateMaxDrawdown(trades) {
  let peak = 0;
  let maxDrawdown = 0;
  let runningPnL = 0;

  trades.forEach(trade => {
    runningPnL += trade.profit_loss;
    if (runningPnL > peak) {
      peak = runningPnL;
    }
    const drawdown = ((peak - runningPnL) / peak) * 100;
    maxDrawdown = Math.max(maxDrawdown, drawdown);
  });

  return maxDrawdown;
}

function calculatePeakEquity(trades) {
  let peak = 0;
  let runningPnL = 0;

  trades.forEach(trade => {
    runningPnL += trade.profit_loss;
    peak = Math.max(peak, runningPnL);
  });

  return peak;
}

function calculateValleyEquity(trades) {
  let valley = 0;
  let runningPnL = 0;

  trades.forEach(trade => {
    runningPnL += trade.profit_loss;
    valley = Math.min(valley, runningPnL);
  });

  return valley;
}

function calculateRecoveryFactor(trades) {
  const netProfit = trades.reduce((sum, t) => sum + t.profit_loss, 0);
  const maxDrawdown = calculateMaxDrawdown(trades);
  return maxDrawdown === 0 ? 0 : Math.abs(netProfit / maxDrawdown);
}

function calculateAverageDuration(trades) {
  if (!trades.length) return '0h 0m';
  
  const totalDuration = trades.reduce((sum, trade) => {
    const duration = new Date(trade.exit_time) - new Date(trade.entry_time);
    return sum + duration;
  }, 0);

  const avgMinutes = Math.floor(totalDuration / trades.length / 1000 / 60);
  const hours = Math.floor(avgMinutes / 60);
  const minutes = avgMinutes % 60;

  return `${hours}h ${minutes}m`;
}

function calculateBestHour(trades) {
  if (!trades.length) return 'N/A';

  const hourlyPnL = new Array(24).fill(0);
  trades.forEach(trade => {
    const hour = new Date(trade.timestamp).getHours();
    hourlyPnL[hour] += trade.profit_loss;
  });

  const bestHour = hourlyPnL.indexOf(Math.max(...hourlyPnL));
  return `${bestHour}:00`;
}

function calculateWorstHour(trades) {
  if (!trades.length) return 'N/A';

  const hourlyPnL = new Array(24).fill(0);
  trades.forEach(trade => {
    const hour = new Date(trade.timestamp).getHours();
    hourlyPnL[hour] += trade.profit_loss;
  });

  const worstHour = hourlyPnL.indexOf(Math.min(...hourlyPnL));
  return `${worstHour}:00`;
} 