import { useMemo } from 'react';
import { format, subDays, subMonths, subYears, isAfter } from 'date-fns';
import {
  ArrowTrendingUpIcon,
  BanknotesIcon,
  ScaleIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { formatDecimal, formatCurrency } from '../../utils/formatters';

export default function PerformanceComparison({ trades, period, dateRange }) {
  const stats = useMemo(() => {
    if (!trades?.length) return null;

    // Determine comparison periods
    const now = new Date();
    let periodStart;
    switch (period) {
      case '1W':
        periodStart = subDays(now, 7);
        break;
      case '1M':
        periodStart = subMonths(now, 1);
        break;
      case '3M':
        periodStart = subMonths(now, 3);
        break;
      case '6M':
        periodStart = subMonths(now, 6);
        break;
      case '1Y':
        periodStart = subYears(now, 1);
        break;
      default:
        periodStart = subMonths(now, 1);
    }

    // Split trades into current and previous periods
    const currentPeriodTrades = trades.filter(t => 
      isAfter(new Date(t.timestamp), periodStart)
    );
    const previousPeriodTrades = trades.filter(t => 
      !isAfter(new Date(t.timestamp), periodStart)
    );

    // Calculate metrics for both periods
    const currentMetrics = calculatePeriodMetrics(currentPeriodTrades);
    const previousMetrics = calculatePeriodMetrics(previousPeriodTrades);

    // Calculate changes
    return {
      current: currentMetrics,
      previous: previousMetrics,
      changes: {
        winRate: currentMetrics.winRate - previousMetrics.winRate,
        profitFactor: currentMetrics.profitFactor - previousMetrics.profitFactor,
        avgTrade: currentMetrics.avgTrade - previousMetrics.avgTrade,
        sharpeRatio: currentMetrics.sharpeRatio - previousMetrics.sharpeRatio
      }
    };
  }, [trades, period]);

  if (!stats) return null;

  return (
    <div className="bg-white rounded-xl shadow-sm">
      <div className="p-6 border-b border-slate-200">
        <h3 className="text-lg font-semibold">Performance Comparison</h3>
        <p className="text-sm text-slate-500 mt-1">
          Comparing current {period} with previous period
        </p>
      </div>
      
      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <ComparisonMetric
            title="Win Rate"
            current={`${stats.current.winRate.toFixed(1)}%`}
            previous={`${stats.previous.winRate.toFixed(1)}%`}
            change={stats.changes.winRate}
            icon={<ArrowTrendingUpIcon className="w-5 h-5" />}
          />
          
          <ComparisonMetric
            title="Profit Factor"
            current={stats.current.profitFactor.toFixed(2)}
            previous={stats.previous.profitFactor.toFixed(2)}
            change={stats.changes.profitFactor}
            icon={<BanknotesIcon className="w-5 h-5" />}
          />
          
          <ComparisonMetric
            title="Avg Trade"
            current={formatCurrency(stats.current.avgTrade)}
            previous={formatCurrency(stats.previous.avgTrade)}
            change={stats.changes.avgTrade}
            icon={<ScaleIcon className="w-5 h-5" />}
            isCurrency={true}
          />
          
          <ComparisonMetric
            title="Sharpe Ratio"
            current={stats.current.sharpeRatio.toFixed(2)}
            previous={stats.previous.sharpeRatio.toFixed(2)}
            change={stats.changes.sharpeRatio}
            icon={<ChartBarIcon className="w-5 h-5" />}
          />
        </div>
      </div>
    </div>
  );
}

function ComparisonMetric({ title, current, previous, change, icon, isCurrency = false }) {
  const isPositive = change > 0;
  const changeText = isCurrency 
    ? formatCurrency(Math.abs(change))
    : formatDecimal(Math.abs(change)) + (title === 'Win Rate' ? '%' : '');

  return (
    <div className="p-4 bg-slate-50 rounded-lg">
      <div className="flex items-center space-x-2 mb-3">
        <div className="p-2 rounded-lg bg-white text-slate-600">
          {icon}
        </div>
        <h4 className="font-medium text-slate-900">{title}</h4>
      </div>
      
      <div className="space-y-2">
        <div>
          <div className="text-2xl font-semibold text-slate-900">{current}</div>
          <div className="text-sm text-slate-500">Current Period</div>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="text-sm text-slate-500">{previous}</div>
          <div className={`text-sm font-medium ${
            isPositive ? 'text-green-600' : 'text-red-600'
          }`}>
            {isPositive ? '+' : '-'}{changeText}
          </div>
        </div>
      </div>
    </div>
  );
}

function calculatePeriodMetrics(trades) {
  if (!trades?.length) {
    return {
      winRate: 0,
      profitFactor: 0,
      avgTrade: 0,
      sharpeRatio: 0
    };
  }

  const winningTrades = trades.filter(t => t.profit_loss > 0);
  const losingTrades = trades.filter(t => t.profit_loss < 0);
  
  const totalWins = winningTrades.reduce((sum, t) => sum + t.profit_loss, 0);
  const totalLosses = Math.abs(losingTrades.reduce((sum, t) => sum + t.profit_loss, 0));
  
  const winRate = (winningTrades.length / trades.length) * 100;
  const profitFactor = totalLosses === 0 ? totalWins : totalWins / totalLosses;
  const avgTrade = trades.reduce((sum, t) => sum + t.profit_loss, 0) / trades.length;
  
  // Calculate Sharpe Ratio
  const returns = trades.map(t => t.profit_loss);
  const meanReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
  const stdDev = Math.sqrt(
    returns.reduce((sum, r) => sum + Math.pow(r - meanReturn, 2), 0) / returns.length
  );
  const sharpeRatio = stdDev === 0 ? 0 : (meanReturn / stdDev) * Math.sqrt(252); // Annualized

  return {
    winRate,
    profitFactor,
    avgTrade,
    sharpeRatio
  };
}

function formatCurrency(value) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
} 