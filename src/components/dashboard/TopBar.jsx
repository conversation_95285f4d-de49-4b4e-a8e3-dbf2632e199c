import React from 'react';
import { Menu, Transition } from '@headlessui/react';
import { 
  UserCircleIcon,
  ChevronDownIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  ChartBarIcon,
  BookOpenIcon,
  HomeIcon
} from '@heroicons/react/24/solid';
import { Link } from 'react-router-dom';

const navigationItems = [
  { key: 'overview', label: 'Overview', icon: HomeIcon, path: '/' },
  { key: 'analysis', label: 'Analysis', icon: ChartBarIcon, path: '/analysis' },
  { key: 'journal', label: 'Journal', icon: BookOpenIcon, path: '/journal' }
];

export default function TopBar({ 
  title, 
  showSettings = true,
  isAccountMenuOpen,
  onAccountMenuClick,
  onAccountMenuClose,
  userName,
  userEmail,
  currentView,
  onViewChange
}) {
  return (
    <div className="fixed top-0 left-0 right-0 h-20 bg-slate-900 text-white z-40">
      <div className="h-full flex items-center justify-between px-6">
        <div className="flex items-center gap-8">
          <h1 className="text-xl font-semibold text-white">{title}</h1>

          {/* Navigation Menu */}
          <nav className="flex items-center gap-4">
            {navigationItems.map(item => (
              <Link
                key={item.key}
                to={item.path}
                onClick={() => onViewChange(item.key)}
                className={`flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                  currentView === item.key
                    ? 'bg-blue-500/10 text-blue-400 border border-blue-400/20'
                    : 'text-slate-400 hover:text-slate-200 hover:bg-slate-800/50'
                }`}
              >
                <item.icon className="w-5 h-5" />
                {item.label}
              </Link>
            ))}
          </nav>
        </div>
        
        <div className="flex items-center gap-4">
          {showSettings && (
            <Link
              to="/settings/general"
              className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
                currentView === 'settings'
                  ? 'bg-blue-500/10 text-blue-400 border border-blue-400/20'
                  : 'text-slate-400 hover:text-slate-200 hover:bg-slate-800/50'
              }`}
            >
              <Cog6ToothIcon className="w-5 h-5" />
              <span className="text-sm font-medium">Settings</span>
            </Link>
          )}
          
          <Menu as="div" className="relative">
            <Menu.Button
              onClick={onAccountMenuClick}
              className="flex items-center gap-2 px-3 py-2 text-slate-400 hover:text-slate-200 hover:bg-slate-800/50 rounded-lg transition-colors"
            >
              <UserCircleIcon className="w-5 h-5" />
              <span className="text-sm font-medium">{userName}</span>
              <ChevronDownIcon className="w-4 h-4" />
            </Menu.Button>

            <Transition
              show={isAccountMenuOpen}
              enter="transition ease-out duration-100"
              enterFrom="transform opacity-0 scale-95"
              enterTo="transform opacity-100 scale-100"
              leave="transition ease-in duration-75"
              leaveFrom="transform opacity-100 scale-100"
              leaveTo="transform opacity-0 scale-95"
            >
              <Menu.Items
                static
                className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-slate-200 focus:outline-none"
              >
                <div className="p-3 border-b border-slate-200">
                  <p className="text-sm font-medium text-slate-900">{userName}</p>
                  <p className="text-sm text-slate-500">{userEmail}</p>
                </div>

                <div className="p-1">
                  <Menu.Item>
                    {({ active }) => (
                      <Link
                        to="/settings/general"
                        className={`${
                          active ? 'bg-slate-50 text-slate-900' : 'text-slate-700'
                        } group flex items-center w-full px-3 py-2 text-sm rounded-md`}
                        onClick={onAccountMenuClose}
                      >
                        <Cog6ToothIcon className="w-5 h-5 mr-3" />
                        Settings
                      </Link>
                    )}
                  </Menu.Item>
                  <Menu.Item>
                    {({ active }) => (
                      <button
                        className={`${
                          active ? 'bg-slate-50 text-slate-900' : 'text-slate-700'
                        } group flex items-center w-full px-3 py-2 text-sm rounded-md`}
                        onClick={onAccountMenuClose}
                      >
                        <ArrowRightOnRectangleIcon className="w-5 h-5 mr-3" />
                        Sign out
                      </button>
                    )}
                  </Menu.Item>
                </div>
              </Menu.Items>
            </Transition>
          </Menu>
        </div>
      </div>
    </div>
  );
} 