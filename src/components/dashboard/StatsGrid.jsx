import React from 'react';
import { 
  CurrencyDollarIcon, 
  ChartBarIcon, 
  ArrowTrendingUpIcon, 
  ScaleIcon 
} from '@heroicons/react/24/outline';

export default function StatsGrid({ trades }) {
  const stats = calculateStats(trades);

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <StatCard
        title="Net P&L"
        value={formatCurrency(stats.netPnL)}
        change={stats.dailyChange}
        icon={<CurrencyDollarIcon className="w-6 h-6" />}
        iconBg="bg-green-100 text-green-600"
      />
      <StatCard
        title="Win Rate"
        value={`${(stats.winRate * 100).toFixed(1)}%`}
        subtitle={`${stats.winCount}/${stats.totalTrades} trades`}
        icon={<ChartBarIcon className="w-6 h-6" />}
        iconBg="bg-blue-100 text-blue-600"
      />
      <StatCard
        title="Avg Trade"
        value={formatCurrency(stats.avgTrade)}
        subtitle={`${formatCurrency(stats.avgWin)} / ${formatCurrency(Math.abs(stats.avgLoss))}`}
        icon={<ArrowTrendingUpIcon className="w-6 h-6" />}
        iconBg="bg-purple-100 text-purple-600"
      />
      <StatCard
        title="Total Commission"
        value={formatCurrency(stats.totalCommission)}
        subtitle={`Avg: ${formatCurrency(stats.avgCommission)}`}
        icon={<ScaleIcon className="w-6 h-6" />}
        iconBg="bg-red-100 text-red-600"
      />
    </div>
  );
}

function StatCard({ title, value, change, subtitle, icon, iconBg }) {
  return (
    <div className="dashboard-card">
      <div className="flex items-center justify-between">
        <div>
          <div className="text-sm font-medium text-slate-500">{title}</div>
          <div className="mt-1 text-2xl font-semibold">{value}</div>
          {subtitle && (
            <div className="mt-1 text-sm text-slate-500">{subtitle}</div>
          )}
        </div>
        <div className={`p-3 rounded-lg ${iconBg}`}>
          {icon}
        </div>
      </div>
      {change != null && (
        <div className="mt-2 flex items-center text-sm">
          <span className={change >= 0 ? 'text-green-500' : 'text-red-500'}>
            {change >= 0 ? '+' : ''}{change.toFixed(1)}%
          </span>
          <span className="text-slate-500 ml-2">vs previous</span>
        </div>
      )}
    </div>
  );
}

function calculateStats(trades) {
  if (!trades?.length) {
    return {
      netPnL: 0,
      winRate: 0,
      avgTrade: 0,
      totalCommission: 0,
      avgCommission: 0,
      winCount: 0,
      totalTrades: 0,
      avgWin: 0,
      avgLoss: 0,
      dailyChange: 0
    };
  }

  const winningTrades = trades.filter(t => t.profit_loss > 0);
  const losingTrades = trades.filter(t => t.profit_loss < 0);

  const netPnL = trades.reduce((sum, t) => sum + t.profit_loss, 0);
  const totalCommission = trades.reduce((sum, t) => sum + (t.commission || 0), 0);

  const winCount = winningTrades.length;
  const totalTrades = trades.length;
  const winRate = totalTrades > 0 ? winCount / totalTrades : 0;

  const avgTrade = totalTrades > 0 ? netPnL / totalTrades : 0;
  const avgCommission = totalTrades > 0 ? totalCommission / totalTrades : 0;

  const avgWin = winCount > 0 
    ? winningTrades.reduce((sum, t) => sum + t.profit_loss, 0) / winCount 
    : 0;

  const avgLoss = losingTrades.length > 0 
    ? losingTrades.reduce((sum, t) => sum + t.profit_loss, 0) / losingTrades.length 
    : 0;

  // Calculate daily change
  const sortedTrades = [...trades].sort((a, b) => b.timestamp - a.timestamp);
  const todayTrades = sortedTrades.filter(t => 
    new Date(t.timestamp).toDateString() === new Date().toDateString()
  );
  const yesterdayTrades = sortedTrades.filter(t => 
    new Date(t.timestamp).toDateString() === new Date(Date.now() - 86400000).toDateString()
  );

  const todayPnL = todayTrades.reduce((sum, t) => sum + t.profit_loss, 0);
  const yesterdayPnL = yesterdayTrades.reduce((sum, t) => sum + t.profit_loss, 0);

  const dailyChange = yesterdayPnL !== 0 
    ? ((todayPnL - yesterdayPnL) / Math.abs(yesterdayPnL)) * 100 
    : todayPnL > 0 ? 100 : 0;

  return {
    netPnL,
    winRate,
    avgTrade,
    totalCommission,
    avgCommission,
    winCount,
    totalTrades,
    avgWin,
    avgLoss,
    dailyChange
  };
}

function formatCurrency(value) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value || 0);
} 