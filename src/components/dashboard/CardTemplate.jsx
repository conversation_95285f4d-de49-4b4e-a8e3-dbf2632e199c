import { useState } from 'react';
import DraggableCard from './DraggableCard';

export default function CardTemplate({ title, children }) {
  return (
    <DraggableCard title={title}>
      <div className="h-full">
        {children}
      </div>
    </DraggableCard>
  );
}

// Example usage:
export function ExampleCard() {
  return (
    <CardTemplate title="Example Card">
      <div className="h-64">
        {/* Card content goes here */}
        <div className="flex items-center justify-center h-full bg-gray-50 rounded-lg">
          <p className="text-gray-500">Card Content</p>
        </div>
      </div>
    </CardTemplate>
  );
}

/**
 * Usage in other components:
 * 
 * 1. Remove internal titles from components
 * 2. Wrap each component with CardTemplate
 * 3. Pass the title as a prop
 * 4. Ensure consistent height with h-64 class
 * 
 * Example:
 * 
 * function MyComponent() {
 *   return (
 *     <CardTemplate title="My Component">
 *       <div className="h-64">
 *         // Component content
 *       </div>
 *     </CardTemplate>
 *   );
 * }
 */ 