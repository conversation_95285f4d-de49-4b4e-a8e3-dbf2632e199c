import React from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { ChevronRightIcon } from '@heroicons/react/24/solid';

const BreadcrumbContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const BreadcrumbLink = styled(Link)`
  color: #6b7280;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  
  ${props => props.$isLast && `
    pointer-events: none;
    color: #6b7280;
  `}
  
  &:hover {
    color: #1f2937;
  }
`;

const BreadcrumbSeparator = styled(ChevronRightIcon)`
  width: 1rem;
  height: 1rem;
  color: #9ca3af;
`;

export default function Breadcrumbs() {
  const links = [
    { to: '/', label: 'Dashboard' },
    { to: '/goal-history', label: 'Goal History' }
  ];

  return (
    <BreadcrumbContainer>
      {links.map((link, index) => {
        const isLast = index === links.length - 1;
        return (
          <React.Fragment key={link.to}>
            <BreadcrumbLink to={link.to} $isLast={isLast}>
              {link.label}
            </BreadcrumbLink>
            {!isLast && <BreadcrumbSeparator />}
          </React.Fragment>
        );
      })}
    </BreadcrumbContainer>
  );
} 