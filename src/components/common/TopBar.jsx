import React from 'react';
import {
  ArrowPathIcon,
  ArrowUturnLeftIcon,
  ViewColumnsIcon,
  DocumentChartBarIcon,
  EllipsisVerticalIcon
} from '@heroicons/react/24/outline';
import { Menu, Transition } from '@headlessui/react';

export default function TopBar({ 
  title,
  showRefresh = false,
  showReset = false,
  showImport = false,
  showWidgets = false,
  onRefresh,
  onReset,
  onImport,
  refreshing = false,
  hiddenCardsCount = 0,
  rightContent = null
}) {
  const menuItems = [
    showRefresh && {
      label: 'Refresh',
      icon: ArrowPathIcon,
      onClick: onRefresh,
      loading: refreshing
    },
    showReset && {
      label: 'Reset',
      icon: ArrowUturnLeftIcon,
      onClick: onReset
    },
    showImport && {
      label: 'Import CSV',
      icon: DocumentChartBarIcon,
      onClick: onImport
    },
    showWidgets && {
      label: 'Hidden Widgets',
      icon: ViewColumnsIcon,
      onClick: () => {
        const hiddenCardsSection = document.querySelector('.hidden-cards-section');
        if (hiddenCardsSection) {
          hiddenCardsSection.scrollIntoView({ behavior: 'smooth' });
        }
      },
      badge: hiddenCardsCount
    }
  ].filter(Boolean);

  return (
    <div className="fixed top-16 left-0 right-0 z-10 bg-slate-900 text-white">
      <div className="max-w-[1920px] mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between py-2 border-t border-slate-800">
          <h1 className="text-base sm:text-lg font-semibold text-white">{title}</h1>
          
          {/* Desktop Actions */}
          <div className="hidden sm:flex items-center gap-3">
            {rightContent}
            
            {menuItems.map((item, index) => (
              <React.Fragment key={item.label}>
                <button
                  onClick={item.onClick}
                  className="flex items-center gap-1.5 text-sm text-slate-400 hover:text-blue-400 transition-colors cursor-pointer group"
                  title={item.label}
                >
                  <item.icon className={`w-4 h-4 ${
                    item.loading ? 'animate-spin text-blue-400' : 'group-hover:text-blue-400'
                  }`} />
                  <span className="font-medium">{item.label}</span>
                  {item.badge !== undefined && (
                    <span className="ml-1 px-1.5 py-0.5 text-xs font-medium bg-slate-800 text-slate-300 rounded">
                      {item.badge}
                    </span>
                  )}
                </button>
                {index < menuItems.length - 1 && (
                  <div className="w-px h-3 bg-slate-800"></div>
                )}
              </React.Fragment>
            ))}
          </div>

          {/* Mobile Menu */}
          <div className="sm:hidden">
            <Menu as="div" className="relative">
              <Menu.Button className="flex items-center justify-center w-8 h-8 rounded-lg text-slate-400 hover:text-slate-200 hover:bg-slate-800/50">
                <EllipsisVerticalIcon className="w-5 h-5" />
              </Menu.Button>

              <Transition
                enter="transition duration-100 ease-out"
                enterFrom="transform scale-95 opacity-0"
                enterTo="transform scale-100 opacity-100"
                leave="transition duration-75 ease-in"
                leaveFrom="transform scale-100 opacity-100"
                leaveTo="transform scale-95 opacity-0"
              >
                <Menu.Items className="absolute right-0 mt-2 w-48 bg-slate-800 rounded-lg shadow-lg border border-slate-700 overflow-hidden">
                  {menuItems.map((item) => (
                    <Menu.Item key={item.label}>
                      {({ active }) => (
                        <button
                          onClick={item.onClick}
                          className={`w-full flex items-center gap-2 px-4 py-2 text-sm ${
                            active ? 'bg-slate-700/50 text-white' : 'text-slate-300'
                          }`}
                        >
                          <item.icon className={`w-4 h-4 ${
                            item.loading ? 'animate-spin text-blue-400' : ''
                          }`} />
                          {item.label}
                          {item.badge !== undefined && (
                            <span className="ml-auto px-1.5 py-0.5 text-xs font-medium bg-slate-700 text-slate-300 rounded">
                              {item.badge}
                            </span>
                          )}
                        </button>
                      )}
                    </Menu.Item>
                  ))}
                </Menu.Items>
              </Transition>
            </Menu>
          </div>
        </div>
      </div>
    </div>
  );
} 