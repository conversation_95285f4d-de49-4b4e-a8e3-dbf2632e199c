import React, { useState, useEffect, useCallback, useRef } from 'react';
import PropTypes from 'prop-types';
import { format } from 'date-fns';
import { saveGoalHistory, getGoalProgress } from '../services/goalService';

const DEBUG = process.env.NODE_ENV === 'development';
const SAVE_DEBOUNCE_MS = 1000;
const MAX_RETRIES = 3;
const CHANGE_THRESHOLD = 0.01;

const validateNumber = (value, defaultValue = 0) => {
  const num = Number(value);
  return !isNaN(num) ? num : defaultValue;
};

function GoalProgressTracker({ 
  profitProgress = 0, 
  guardProgress = 0, 
  focusProgress = 0,
  winRate = 0,
  profitFactor = 0,
  maxDrawdown = 0,
  avgWinLoss = 0,
  consecutiveWins = 0,
  riskRewardRatio = 0,
  sharpeRatio = 0,
  tradingFrequency = 0,
  avgTradeDuration = 0
}) {
  const [isLoading, setIsLoading] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const lastCheckedDate = useRef(null);
  const saveTimeout = useRef(null);
  const isMounted = useRef(true);

  const metricsRef = useRef({});

  useEffect(() => {
    metricsRef.current = {
      profitProgress: validateNumber(profitProgress),
      guardProgress: validateNumber(guardProgress),
      focusProgress: validateNumber(focusProgress),
      winRate: validateNumber(winRate),
      profitFactor: validateNumber(profitFactor),
      maxDrawdown: validateNumber(maxDrawdown),
      avgWinLoss: validateNumber(avgWinLoss),
      consecutiveWins: validateNumber(consecutiveWins),
      riskRewardRatio: validateNumber(riskRewardRatio),
      sharpeRatio: validateNumber(sharpeRatio),
      tradingFrequency: validateNumber(tradingFrequency),
      avgTradeDuration: validateNumber(avgTradeDuration)
    };
  }, [
    profitProgress,
    guardProgress,
    focusProgress,
    winRate,
    profitFactor,
    maxDrawdown,
    avgWinLoss,
    consecutiveWins,
    riskRewardRatio,
    sharpeRatio,
    tradingFrequency,
    avgTradeDuration
  ]);

  useEffect(() => {
    return () => {
      isMounted.current = false;
      if (saveTimeout.current) {
        clearTimeout(saveTimeout.current);
      }
    };
  }, []);

  const hasDataChanged = useCallback((currentData) => {
    if (!currentData) return false;

    const metrics = metricsRef.current;
    const hasChanged = Object.keys(metrics).some(field => {
      const currentValue = validateNumber(currentData[field]);
      const newValue = validateNumber(metrics[field]);
      const changed = Math.abs(currentValue - newValue) > CHANGE_THRESHOLD;
      
      if (DEBUG && changed) {
        console.debug(`Metric ${field} changed: ${currentValue} -> ${newValue}`);
      }
      return changed;
    });

    if (DEBUG) {
      console.debug(`Data changed: ${hasChanged}`);
    }

    return hasChanged;
  }, []);

  const saveCurrentProgress = useCallback(async () => {
    if (isLoading || !isMounted.current) return;

    const metrics = metricsRef.current;
    const goalData = {
      overallProgress: Math.round((
        validateNumber(metrics.profitProgress) + 
        validateNumber(metrics.guardProgress) + 
        validateNumber(metrics.focusProgress)
      ) / 3),
      ...metrics,
      date: format(new Date(), 'yyyy-MM-dd')
    };

    try {
      setIsLoading(true);
      await saveGoalHistory(goalData);
      if (isMounted.current) {
        setRetryCount(0);
      }
    } catch (error) {
      console.error('Error saving goal progress:', error);
      if (retryCount < MAX_RETRIES && isMounted.current) {
        const nextRetryDelay = 1000 * (retryCount + 1);
        setRetryCount(prev => prev + 1);
        if (saveTimeout.current) {
          clearTimeout(saveTimeout.current);
        }
        saveTimeout.current = setTimeout(saveCurrentProgress, nextRetryDelay);
      }
    } finally {
      if (isMounted.current) {
        setIsLoading(false);
      }
    }
  }, [isLoading, retryCount]);

  useEffect(() => {
    const today = format(new Date(), 'yyyy-MM-dd');
    if (lastCheckedDate.current === today || isLoading) return;

    const checkAndSave = async () => {
      try {
        const currentData = await getGoalProgress(today);
        if (isMounted.current && hasDataChanged(currentData)) {
          await saveCurrentProgress();
          lastCheckedDate.current = today;
        }
      } catch (error) {
        console.error('Error checking goal progress:', error);
      }
    };

    if (saveTimeout.current) {
      clearTimeout(saveTimeout.current);
    }

    if (!lastCheckedDate.current || lastCheckedDate.current !== today) {
      saveTimeout.current = setTimeout(checkAndSave, SAVE_DEBOUNCE_MS);
    }

    return () => {
      if (saveTimeout.current) {
        clearTimeout(saveTimeout.current);
      }
    };
  }, [hasDataChanged, saveCurrentProgress, isLoading]);

  return null;
}

GoalProgressTracker.propTypes = {
  profitProgress: PropTypes.number,
  guardProgress: PropTypes.number,
  focusProgress: PropTypes.number,
  winRate: PropTypes.number,
  profitFactor: PropTypes.number,
  maxDrawdown: PropTypes.number,
  avgWinLoss: PropTypes.number,
  consecutiveWins: PropTypes.number,
  riskRewardRatio: PropTypes.number,
  sharpeRatio: PropTypes.number,
  tradingFrequency: PropTypes.number,
  avgTradeDuration: PropTypes.number
};

export default React.memo(GoalProgressTracker); 