import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  PlusIcon,
  MinusIcon,
  Cog6ToothIcon,
  InformationCircleIcon,
  CalendarIcon,
  ArrowTrendingUpIcon,
  ShieldCheckIcon,
  BoltIcon
} from '@heroicons/react/24/solid';
import PropTypes from 'prop-types';
import { useNavigate } from 'react-router-dom';
import GoalProgressTracker from './GoalProgressTracker';
import { GOAL_ICONS, ACHIEVEMENT_THRESHOLDS } from '../config/goalIcons';
import { formatDecimal } from '../utils/formatters';

const keyframes = `
  @keyframes gaugeLoad {
    0% {
      transform: rotate(0deg);
      opacity: 0;
    }
    20% {
      opacity: 1;
    }
    100% {
      transform: rotate(var(--final-rotation));
      opacity: 1;
    }
  }

  @keyframes fadeIn {
    0% {
      opacity: 0;
      transform: translateY(10px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes glowPulse {
    0% {
      filter: drop-shadow(0 0 8px var(--color));
    }
    50% {
      filter: drop-shadow(0 0 15px var(--color));
    }
    100% {
      filter: drop-shadow(0 0 8px var(--color));
    }
  }
`;

const SILVER_COLOR = '#475569';
const GREEN_COLOR = '#16a34a';
const BLUE_COLOR = '#3b82f6';

const CardContainer = styled.div`
  background: #ffffff;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin: 0;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  gap: 30px;
  border: 1px solid #f0f0f0;
  width: 100%;
  animation: fadeIn 0.5s ease-out forwards;
  ${keyframes}

  @media (min-width: 1024px) {
    padding: 30px;
    flex-direction: row;
    align-items: center;
    gap: 60px;
  }

  &:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
`;

const MainGaugeContainer = styled.div`
  position: relative;
  width: 280px;
  height: 280px;
  margin: 0 auto;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  @media (min-width: 640px) {
    width: 320px;
    height: 320px;
  }
`;

const GaugeBackground = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #f8fafc;
`;

const SmallGaugesContainer = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
`;

const GaugeTicks = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  
  &::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 2px dashed rgba(45, 55, 72, 0.1);
  }

  &::after {
    content: '';
    position: absolute;
    width: 94%;
    height: 94%;
    top: 3%;
    left: 3%;
    border-radius: 50%;
    border: 2px dashed rgba(45, 55, 72, 0.1);
  }
`;

const WatchBezel = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(145deg, #e6e6e6, #ffffff);
  box-shadow: 
    inset 0 4px 8px rgba(0, 0, 0, 0.1),
    inset 0 -4px 8px rgba(255, 255, 255, 0.9);
`;

const SmallGaugeContent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1px;
  z-index: 1;
  background: linear-gradient(145deg, #f5f5f5, #e8e8e8);
  width: 100%;
  height: 100%;
  border-radius: 50%;
  justify-content: center;
  padding: 4px;
  box-sizing: border-box;
  box-shadow: 
    inset 0 2px 4px rgba(0, 0, 0, 0.1),
    inset 0 -2px 4px rgba(255, 255, 255, 0.8);
  animation: fadeIn 0.5s ease-out forwards;
  animation-delay: 0.3s;
  opacity: 0;
`;

const SmallGaugeIcon = styled.div`
  width: 18px;
  height: 18px;
  color: ${props => props.color};
  opacity: 0.95;
  stroke-width: 2.5;
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.15));
  svg {
    stroke-width: inherit;
  }
`;

const SmallGaugeValue = styled.div`
  font-size: 1.1rem;
  font-weight: 800;
  color: ${props => props.color};
  letter-spacing: 0.02em;
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.15));
  line-height: 1;
  margin-top: 1px;
`;

const SmallGaugeTitle = styled.div`
  font-size: 0.6rem;
  font-weight: 600;
  color: ${props => props.color};
  text-transform: uppercase;
  letter-spacing: 0.08em;
  opacity: 0.9;
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.15));
  line-height: 1;
`;

const SmallGauge = styled.div`
  width: 65px;
  height: 65px;
  position: absolute;
  border-radius: 50%;
  background: transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  @media (min-width: 640px) {
    width: 75px;
    height: 75px;
  }

  ${({ $position }) => {
    const centerOffset = '50%';
    const baseDistance = 55;
    const topDistance = baseDistance * 1.4;
    
    const largeBaseDistance = 65;
    const largeTopDistance = largeBaseDistance * 1.4;
    
    return `
      @media (max-width: 639px) {
        ${$position === 'top' ? `
          top: calc(${centerOffset} - ${topDistance}px);
          left: ${centerOffset};
          transform: translate(-50%, 0);
        ` : $position === 'bottomRight' ? `
          top: calc(${centerOffset} + ${baseDistance * 0.5}px);
          left: calc(${centerOffset} + ${baseDistance * 0.866}px);
          transform: translate(-50%, -50%);
        ` : $position === 'bottomLeft' ? `
          top: calc(${centerOffset} + ${baseDistance * 0.5}px);
          left: calc(${centerOffset} - ${baseDistance * 0.866}px);
          transform: translate(-50%, -50%);
        ` : ''}
      }

      @media (min-width: 640px) {
        ${$position === 'top' ? `
          top: calc(${centerOffset} - ${largeTopDistance}px);
          left: ${centerOffset};
          transform: translate(-50%, 0);
        ` : $position === 'bottomRight' ? `
          top: calc(${centerOffset} + ${largeBaseDistance * 0.5}px);
          left: calc(${centerOffset} + ${largeBaseDistance * 0.866}px);
          transform: translate(-50%, -50%);
        ` : $position === 'bottomLeft' ? `
          top: calc(${centerOffset} + ${largeBaseDistance * 0.5}px);
          left: calc(${centerOffset} - ${largeBaseDistance * 0.866}px);
          transform: translate(-50%, -50%);
        ` : ''}
      }
    `;
  }}

  &::before {
    content: '';
    position: absolute;
    inset: -8px;
    border-radius: 50%;
    background: ${props => {
      const color = props.$borderColor;
      return color === GREEN_COLOR ? `${color}20` : `${color}15`;
    }};
    box-shadow: 
      inset 0 0 20px ${props => {
        const color = props.$borderColor;
        return color === GREEN_COLOR ? `${color}40` : `${color}30`;
      }},
      0 0 20px ${props => {
        const color = props.$borderColor;
        return color === GREEN_COLOR ? `${color}40` : `${color}30`;
      }};
  }

  &::after {
    content: '';
    position: absolute;
    inset: -8px;
    border-radius: 50%;
    background: conic-gradient(
      from 0deg,
      ${props => {
        const color = props.$borderColor;
        return color === GREEN_COLOR ? `${color}FF` : 
               color === SILVER_COLOR ? `${color}FF` : 
               color;
      }}
      ${props => props.value * 3.6}deg,
      ${props => {
        const color = props.$borderColor;
        return color === GREEN_COLOR ? `${color}30` : `${color}25`;
      }} ${props => props.value * 3.6}deg,
      ${props => {
        const color = props.$borderColor;
        return color === GREEN_COLOR ? `${color}20` : `${color}15`;
      }} ${props => props.value * 3.6 + 15}deg,
      transparent ${props => props.value * 3.6 + 30}deg
    );
    mask: radial-gradient(
      circle at center,
      transparent 0%,
      rgba(0, 0, 0, 0.7) 1%,
      black 5%,
      black 88%,
      rgba(0, 0, 0, 0.7) 92%,
      transparent 93%
    );
    -webkit-mask: radial-gradient(
      circle at center,
      transparent 0%,
      rgba(0, 0, 0, 0.7) 1%,
      black 5%,
      black 88%,
      rgba(0, 0, 0, 0.7) 92%,
      transparent 93%
    );
    --color: ${props => props.$borderColor};
    filter: drop-shadow(0 0 10px ${props => {
      const color = props.$borderColor;
      return color === GREEN_COLOR ? `${color}90` :
             color === SILVER_COLOR ? `${color}60` :
             `${color}60`;
    }});
    --final-rotation: ${props => props.value * 3.6}deg;
    animation: 
      gaugeLoad 1.8s cubic-bezier(0.4, 0, 0.2, 1) forwards,
      glowPulse 3s ease-in-out infinite;
    transform-origin: center;
  }
`;

const GaugeValue = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.2rem;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.9);
  letter-spacing: 0.02em;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
`;

const GaugeMetrics = styled.div`
  flex-grow: 1;
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1rem;
  padding: 1rem;

  @media (min-width: 640px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (min-width: 1280px) {
    grid-template-columns: repeat(4, 1fr);
    padding: 1.5rem;
  }
`;

const MetricGroup = styled.div`
  display: flex;
  flex-direction: column;
  padding: 0.75rem;
  position: relative;
  width: 100%;

  @media (min-width: 640px) {
    &:not(:last-child) {
      border-right: none;
      border-bottom: 1px solid #e5e7eb;
    }
  }

  @media (min-width: 1280px) {
    border-right: 1px solid #e5e7eb;
    border-bottom: none;

    &:last-child {
      border-right: none;
    }
  }
`;

const GaugeTooltip = styled.div`
  position: absolute;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 12px;
  z-index: 50;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
  white-space: nowrap;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
`;

const Gauge = styled.div`
  width: 44px;
  height: 44px;
  border-radius: 50%;
  position: relative;
  background: ${props => props.color}15;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  ${keyframes}

  &::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 50%;
    background: conic-gradient(
      from 0deg,
      ${props => props.color} ${props => props.$progress * 3.6}deg,
      transparent ${props => props.$progress * 3.6}deg
    );
    mask: radial-gradient(circle at center, transparent 40%, black 45%);
    -webkit-mask: radial-gradient(circle at center, transparent 40%, black 45%);
  }

  svg {
    width: 20px;
    height: 20px;
    color: ${props => props.color};
    z-index: 1;
    opacity: ${props => props.$filled ? 1 : 0.6};
    transition: all 0.3s ease;
    ${props => props.$filled && `
      filter: drop-shadow(0 0 8px ${props.color});
      animation: glowPulse 2s ease-in-out infinite;
    `}
  }
`;

const MetricTitle = styled.h3`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: ${props => props.color || '#475569'};
  letter-spacing: 0.05em;
  margin: 0 0 1rem 0;
  padding: 0 0 0.75rem 0;
  text-transform: uppercase;
  text-align: left;
  position: relative;
  border-bottom: 1px solid ${props => `${props.color}15` || '#47556915'};

  @media (min-width: 640px) {
    font-size: 1rem;
    gap: 0.75rem;
    margin: 0 0 1.25rem 0;
  }
`;

const MetricTitleIcon = styled.div`
  width: 44px;
  height: 44px;
  position: relative;
`;

const MetricTitleInfo = styled.div`
  position: absolute;
  right: 0.5rem;
  top: 0.5rem;
`;

const MetricIndicator = styled.div`
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  position: relative;
  background: ${props => props.color}15;

  &::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 50%;
    background: conic-gradient(
      from 0deg,
      ${props => props.color} ${props => props.value * 3.6}deg,
      transparent ${props => props.value * 3.6}deg
    );
    mask: radial-gradient(circle at center, transparent 40%, black 45%);
    -webkit-mask: radial-gradient(circle at center, transparent 40%, black 45%);
  }
`;

const MetricContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  padding-top: 0.25rem;
`;

const MetricRow = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0.5rem 0;
  transition: all 0.2s ease;
  width: 100%;

  &:hover {
    transform: translateX(2px);
  }
`;

const MetricRowIcon = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: ${props => props.color || '#475569'};
  font-size: 0.875rem;
  font-weight: 500;
  position: relative;
  cursor: help;

  &:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 0;
    top: calc(100% + 5px);
    background: #1f2937;
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 400;
    letter-spacing: 0.025em;
    white-space: normal;
    width: max-content;
    max-width: 300px;
    z-index: 10;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    opacity: 0;
    animation: fadeIn 0.2s ease-out forwards;
  }
`;

const MetricValue = styled.div`
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  margin-left: 1.5rem;
`;

const WinLossValues = styled.div`
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-left: 1.5rem;
`;

const DetailLabel = styled.span`
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  margin: 0 0.25rem;
`;

const DetailValue = styled.span`
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
`;

const MainGauge = styled.div`
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  background: #ffffff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  
  &::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 35px solid #f8fafc;
    box-sizing: border-box;
  }

  &::after {
    content: '';
    position: absolute;
    inset: -1px;
    border-radius: 50%;
    background: conic-gradient(
      from 0deg,
      #2d3748 ${props => props.value * 3.6}deg,
      #2d374825 ${props => props.value * 3.6}deg,
      #2d374815 ${props => props.value * 3.6 + 15}deg,
      transparent ${props => props.value * 3.6 + 30}deg
    );
    mask: radial-gradient(
      circle at center,
      transparent 62%,
      rgba(0, 0, 0, 0.7) 63%,
      black 65%,
      black 86%,
      rgba(0, 0, 0, 0.7) 88%,
      transparent 89%
    );
    -webkit-mask: radial-gradient(
      circle at center,
      transparent 62%,
      rgba(0, 0, 0, 0.7) 63%,
      black 65%,
      black 86%,
      rgba(0, 0, 0, 0.7) 88%,
      transparent 89%
    );
    filter: drop-shadow(0 0 12px rgba(45, 55, 72, 0.4));
    --final-rotation: ${props => props.value * 3.6}deg;
    animation: 
      gaugeLoad 2s cubic-bezier(0.4, 0, 0.2, 1) forwards,
      glowPulse 3s ease-in-out infinite;
    transform-origin: center;
  }
`;

const ClockHand = styled.div`
  position: absolute;
  width: 3px;
  height: 120px;
  background: #2d3748;
  bottom: 50%;
  left: calc(50% - 1.5px);
  transform-origin: bottom;
  transform: rotate(${props => props.value * 3.6}deg);
  transition: transform 0.5s ease;
  z-index: 1;
  animation: gaugeLoad 2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  --final-rotation: ${props => props.value * 3.6}deg;
  opacity: 0;

  &::before {
    content: '';
    position: absolute;
    width: 12px;
    height: 12px;
    background: #2d3748;
    border-radius: 50%;
    bottom: -6px;
    left: -4.5px;
  }

  &::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 16px solid #2d3748;
    top: -14px;
    left: -4.5px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  }
`;

const ButtonContainer = styled.div`
  display: flex;
  gap: 2px;
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  z-index: 10;

  @media (min-width: 640px) {
    top: 1rem;
    right: 1rem;
    gap: 4px;
  }
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  padding: 0.5rem;
  border-radius: 0.5rem;
  border: none;
  background: transparent;
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;

  &:hover {
    background: #f1f5f9;
    color: #475569;
  }

  svg {
    width: 1.25rem;
    height: 1.25rem;
  }

  span {
    display: none;
  }

  @media (min-width: 640px) {
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;

    span {
      display: inline;
    }
  }
`;

const CustomizeButton = styled(ActionButton)``;
const HistoryButton = styled(ActionButton)``;

const InfoIcon = styled(InformationCircleIcon)`
  width: 1rem;
  height: 1rem;
  color: ${props => props.color || '#64748b'};
  opacity: 0.6;
  cursor: help;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 1;
  }
`;

const CircularProgress = ({ value, color, size = 120, strokeWidth = 12 }) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const progress = (value / 100) * circumference;
  const center = size / 2;

  return (
    <svg width={size} height={size} className="transform -rotate-90">
      {/* Background circle */}
      <circle
        cx={center}
        cy={center}
        r={radius}
        fill="none"
        stroke={`${color}/10`}
        strokeWidth={strokeWidth}
        className="opacity-20"
      />
      {/* Progress circle */}
      <circle
        cx={center}
        cy={center}
        r={radius}
        fill="none"
        stroke={`${color}/100`}
        strokeWidth={strokeWidth}
        strokeDasharray={circumference}
        strokeDashoffset={circumference - progress}
        strokeLinecap="round"
        className="transition-all duration-500 ease-out"
      />
    </svg>
  );
};

const GoalGaugeCard = ({ 
  overallProgress = 75, 
  profitProgress = 80, 
  guardProgress = 65, 
  focusProgress = 70,
  // Additional metrics
  winRate = 65,
  profitFactor = 1.8,
  maxDrawdown = 15,
  avgWinLoss = 1.5,
  consecutiveWins = 5,
  riskRewardRatio = 2.1,
  sharpeRatio = 1.2,
  tradingFrequency = 85,
  avgTradeDuration = 45,
  // Customization props
  onCustomize,
  onToggleCategory,
  visibleCategories = ['performance', 'profit', 'guard', 'focus'],
  onViewHistory
}) => {
  const navigate = useNavigate();
  const [activeMetrics, setActiveMetrics] = useState(() => {
    const savedMetrics = localStorage.getItem('goalCardMetrics');
    return savedMetrics ? JSON.parse(savedMetrics) : Object.fromEntries(
      ['overallProgress', 'winRate', 'profitFactor', 'avgWinLoss', 'sharpeRatio', 'riskReward', 'maxDrawdown', 'consecutiveWins', 'tradingFrequency', 'avgDuration']
        .map(id => [id, true])
    );
  });

  const [metricGoals, setMetricGoals] = useState(() => {
    const savedGoals = localStorage.getItem('goalCardMetricGoals');
    return savedGoals ? JSON.parse(savedGoals) : {
      overallProgress: 75,
      winRate: 55,
      profitFactor: 1.5,
      avgWinLoss: 1.5,
      sharpeRatio: 1.5,
      riskReward: 2,
      maxDrawdown: 10,
      consecutiveWins: 5,
      tradingFrequency: 85,
      avgDuration: 30
    };
  });

  // Ensure all values are valid numbers
  const normalizeValue = (value, defaultValue = 0) => {
    const num = Number(value);
    return isNaN(num) ? defaultValue : num;
  };

  // Normalize all progress values
  const normalizedProgress = {
    overall: normalizeValue(overallProgress, 0),
    profit: normalizeValue(profitProgress, 0),
    guard: normalizeValue(guardProgress, 0),
    focus: normalizeValue(focusProgress, 0),
    winRate: normalizeValue(winRate, 0),
    profitFactor: normalizeValue(profitFactor, 0),
    maxDrawdown: normalizeValue(maxDrawdown, 0),
    avgWinLoss: normalizeValue(avgWinLoss, 0),
    consecutiveWins: normalizeValue(consecutiveWins, 0),
    riskRewardRatio: normalizeValue(riskRewardRatio, 0),
    sharpeRatio: normalizeValue(sharpeRatio, 0),
    tradingFrequency: normalizeValue(tradingFrequency, 0),
    avgTradeDuration: normalizeValue(avgTradeDuration, 0)
  };

  useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === 'goalCardMetrics') {
        const savedMetrics = localStorage.getItem('goalCardMetrics');
        if (savedMetrics) {
          setActiveMetrics(JSON.parse(savedMetrics));
        }
      } else if (e.key === 'goalCardMetricGoals') {
        const savedGoals = localStorage.getItem('goalCardMetricGoals');
        if (savedGoals) {
          setMetricGoals(JSON.parse(savedGoals));
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  const handleCustomize = () => {
    navigate('/settings', { state: { selectedTab: 'goal-card' } });
  };

  const handleViewHistory = () => {
    if (onViewHistory) {
      onViewHistory();
    } else {
      navigate('/goal-history');
    }
  };

  // Calculate normalized values based on goals
  const calculateNormalizedValue = (value, goal) => {
    if (!goal || goal === 0) return 0;
    const normalized = Math.round((normalizeValue(value) / goal) * 100);
    return Math.min(100, Math.max(0, normalized));
  };

  // Calculate all normalized values
  const normalizedMetrics = {
    overall: calculateNormalizedValue(normalizedProgress.overall, metricGoals.overallProgress),
    profit: calculateNormalizedValue(normalizedProgress.profit, metricGoals.overallProgress),
    guard: calculateNormalizedValue(normalizedProgress.riskRewardRatio, metricGoals.riskReward),
    focus: calculateNormalizedValue(normalizedProgress.focus, metricGoals.overallProgress),
    winRate: calculateNormalizedValue(normalizedProgress.winRate, metricGoals.winRate),
    profitFactor: calculateNormalizedValue(normalizedProgress.profitFactor, metricGoals.profitFactor),
    avgWinLoss: calculateNormalizedValue(normalizedProgress.avgWinLoss, metricGoals.avgWinLoss),
    sharpeRatio: calculateNormalizedValue(normalizedProgress.sharpeRatio, metricGoals.sharpeRatio),
    riskReward: calculateNormalizedValue(normalizedProgress.riskRewardRatio, metricGoals.riskReward),
    maxDrawdown: calculateNormalizedValue(normalizedProgress.maxDrawdown, metricGoals.maxDrawdown),
    consecutiveWins: calculateNormalizedValue(normalizedProgress.consecutiveWins, metricGoals.consecutiveWins),
    tradingFrequency: calculateNormalizedValue(normalizedProgress.tradingFrequency, metricGoals.tradingFrequency),
    avgDuration: calculateNormalizedValue(normalizedProgress.avgTradeDuration, metricGoals.avgDuration)
  };

  const renderMetricIcon = (category, progress) => {
    const { icon: Icon, color } = GOAL_ICONS[category.toLowerCase()];
    const filled = progress >= ACHIEVEMENT_THRESHOLDS[category.toLowerCase()];

    return (
      <Gauge
        color={color}
        $progress={progress}
        $filled={filled}
      >
        <GaugeTooltip>
          {category} Progress: {Math.round(progress)}%
        </GaugeTooltip>
        <Icon />
      </Gauge>
    );
  };

  return (
    <CardContainer>
      <ButtonContainer>
        <HistoryButton onClick={handleViewHistory} title="View Goal Achievement History">
          <CalendarIcon />
          <span>History</span>
        </HistoryButton>
        <CustomizeButton onClick={handleCustomize} title="Customize Goal Card">
          <Cog6ToothIcon />
          <span>Customize</span>
        </CustomizeButton>
      </ButtonContainer>

      <GoalProgressTracker 
        overallProgress={normalizedProgress.overall}
        profitProgress={normalizedProgress.profit}
        guardProgress={normalizedProgress.guard}
        focusProgress={normalizedProgress.focus}
        winRate={normalizedProgress.winRate}
        profitFactor={normalizedProgress.profitFactor}
        maxDrawdown={normalizedProgress.maxDrawdown}
        avgWinLoss={normalizedProgress.avgWinLoss}
        consecutiveWins={normalizedProgress.consecutiveWins}
        riskRewardRatio={normalizedProgress.riskRewardRatio}
        sharpeRatio={normalizedProgress.sharpeRatio}
        tradingFrequency={normalizedProgress.tradingFrequency}
        avgTradeDuration={normalizedProgress.avgTradeDuration}
      />

      <MainGaugeContainer>
        <WatchBezel />
        <GaugeBackground />
        <GaugeTicks />
        <MainGauge value={normalizedMetrics.overall}>
          <ClockHand value={normalizedMetrics.overall} />
        </MainGauge>
        <SmallGaugesContainer>
          <SmallGauge 
            $position="top" 
            $borderColor={GREEN_COLOR} 
            value={normalizedMetrics.profit}
          >
            <SmallGaugeContent>
              <SmallGaugeIcon color={GREEN_COLOR}>
                <ArrowTrendingUpIcon />
              </SmallGaugeIcon>
              <SmallGaugeValue color={GREEN_COLOR}>{normalizedMetrics.profit}</SmallGaugeValue>
              <SmallGaugeTitle color={GREEN_COLOR}>Profit</SmallGaugeTitle>
            </SmallGaugeContent>
          </SmallGauge>
          <SmallGauge 
            $position="bottomLeft" 
            $borderColor={SILVER_COLOR}
            value={normalizedMetrics.guard}
          >
            <SmallGaugeContent>
              <SmallGaugeIcon color={SILVER_COLOR}>
                <ShieldCheckIcon />
              </SmallGaugeIcon>
              <SmallGaugeValue color={SILVER_COLOR}>{normalizedMetrics.guard}</SmallGaugeValue>
              <SmallGaugeTitle color={SILVER_COLOR}>Guard</SmallGaugeTitle>
            </SmallGaugeContent>
          </SmallGauge>
          <SmallGauge 
            $position="bottomRight" 
            $borderColor={BLUE_COLOR} 
            value={normalizedMetrics.focus}
          >
            <SmallGaugeContent>
              <SmallGaugeIcon color={BLUE_COLOR}>
                <BoltIcon />
              </SmallGaugeIcon>
              <SmallGaugeValue color={BLUE_COLOR}>{normalizedMetrics.focus}</SmallGaugeValue>
              <SmallGaugeTitle color={BLUE_COLOR}>Focus</SmallGaugeTitle>
            </SmallGaugeContent>
          </SmallGauge>
        </SmallGaugesContainer>
      </MainGaugeContainer>

      <GaugeMetrics>
        {visibleCategories.includes('performance') && activeMetrics['overallProgress'] && (
          <MetricGroup color="#2d3748">
            <MetricTitle color="#2d3748">
              {renderMetricIcon('performance', normalizedMetrics.overall)}
              PERFORMANCE
              <MetricTitleInfo>
                <InfoIcon 
                  color="#2d3748" 
                  title="Overall trading performance metrics and key indicators"
                />
              </MetricTitleInfo>
            </MetricTitle>
            <MetricContent>
              <MetricRow>
                <MetricRowIcon>
                  <MetricIndicator color="#2d3748" value={normalizedMetrics.overall} />
                  <span>Overall Progress</span>
                </MetricRowIcon>
                <MetricValue>{overallProgress.toFixed(2)}/{metricGoals.overallProgress}</MetricValue>
              </MetricRow>
            </MetricContent>
          </MetricGroup>
        )}

        {visibleCategories.includes('profit') && (
          <MetricGroup color={GREEN_COLOR}>
            <MetricTitle color={GREEN_COLOR}>
              {renderMetricIcon('profit', normalizedMetrics.profit)}
              PROFIT
              <MetricTitleInfo>
                <InfoIcon 
                  color={GREEN_COLOR} 
                  title="Profit-related metrics including win rate, profit factor and win/loss ratios"
                />
              </MetricTitleInfo>
            </MetricTitle>
            <MetricContent>
              {activeMetrics['winRate'] && (
                <MetricRow>
                  <MetricRowIcon>
                    <MetricIndicator color={GREEN_COLOR} value={normalizedMetrics.winRate} />
                    <span>Win Rate</span>
                  </MetricRowIcon>
                  <MetricValue>{winRate.toFixed(2)}/{metricGoals.winRate}%</MetricValue>
                </MetricRow>
              )}
              {activeMetrics['profitFactor'] && (
                <MetricRow>
                  <MetricRowIcon>
                    <MetricIndicator color={GREEN_COLOR} value={normalizedMetrics.profitFactor} />
                    <span>Profit Factor</span>
                  </MetricRowIcon>
                  <MetricValue>{profitFactor && formatDecimal(profitFactor)}/{formatDecimal(metricGoals.profitFactor)}</MetricValue>
                </MetricRow>
              )}
              {activeMetrics['avgWinLoss'] && (
                <MetricRow>
                  <MetricRowIcon>
                    <MetricIndicator color={GREEN_COLOR} value={normalizedMetrics.avgWinLoss} />
                    <span>Avg Win/Loss</span>
                  </MetricRowIcon>
                  <WinLossValues>
                    <DetailLabel>Goal</DetailLabel>
                    <DetailValue>{formatDecimal(metricGoals.avgWinLoss)}</DetailValue>
                    <DetailLabel>Current</DetailLabel>
                    <DetailValue>{formatDecimal(avgWinLoss)}</DetailValue>
                  </WinLossValues>
                </MetricRow>
              )}
            </MetricContent>
          </MetricGroup>
        )}

        {visibleCategories.includes('guard') && (
          <MetricGroup color={SILVER_COLOR}>
            <MetricTitle color={SILVER_COLOR}>
              {renderMetricIcon('guard', normalizedMetrics.guard)}
              GUARD
              <MetricTitleInfo>
                <InfoIcon 
                  color={SILVER_COLOR} 
                  title="Risk management metrics and protective measures"
                />
              </MetricTitleInfo>
            </MetricTitle>
            <MetricContent>
              {activeMetrics['sharpeRatio'] && (
                <MetricRow>
                  <MetricRowIcon>
                    <MetricIndicator color={SILVER_COLOR} value={normalizedMetrics.sharpeRatio} />
                    <span>Sharpe Ratio</span>
                  </MetricRowIcon>
                  <MetricValue>{formatDecimal(sharpeRatio)}/{formatDecimal(metricGoals.sharpeRatio)}</MetricValue>
                </MetricRow>
              )}
              {activeMetrics['riskReward'] && (
                <MetricRow>
                  <MetricRowIcon>
                    <MetricIndicator color={SILVER_COLOR} value={normalizedMetrics.riskReward} />
                    <span>Risk/Reward</span>
                  </MetricRowIcon>
                  <MetricValue>{formatDecimal(riskRewardRatio)}/{formatDecimal(metricGoals.riskReward)}</MetricValue>
                </MetricRow>
              )}
              {activeMetrics['maxDrawdown'] && (
                <MetricRow>
                  <MetricRowIcon>
                    <MetricIndicator color={SILVER_COLOR} value={normalizedMetrics.maxDrawdown} />
                    <span>Max Drawdown</span>
                  </MetricRowIcon>
                  <MetricValue>{maxDrawdown.toFixed(2)}/{metricGoals.maxDrawdown}%</MetricValue>
                </MetricRow>
              )}
            </MetricContent>
          </MetricGroup>
        )}

        {visibleCategories.includes('focus') && (
          <MetricGroup color={BLUE_COLOR}>
            <MetricTitle color={BLUE_COLOR}>
              {renderMetricIcon('focus', normalizedMetrics.focus)}
              FOCUS
              <MetricTitleInfo>
                <InfoIcon 
                  color={BLUE_COLOR} 
                  title="Trading consistency and focus metrics including trade timing and frequency"
                />
              </MetricTitleInfo>
            </MetricTitle>
            <MetricContent>
              {activeMetrics['consecutiveWins'] && (
                <MetricRow>
                  <MetricRowIcon>
                    <MetricIndicator color={BLUE_COLOR} value={normalizedMetrics.consecutiveWins} />
                    <span>Consecutive Wins</span>
                  </MetricRowIcon>
                  <MetricValue>{consecutiveWins}/{metricGoals.consecutiveWins}</MetricValue>
                </MetricRow>
              )}
              {activeMetrics['tradingFrequency'] && (
                <MetricRow>
                  <MetricRowIcon>
                    <MetricIndicator color={BLUE_COLOR} value={normalizedMetrics.tradingFrequency} />
                    <span>Trading Frequency</span>
                  </MetricRowIcon>
                  <MetricValue>{tradingFrequency}/{metricGoals.tradingFrequency}%</MetricValue>
                </MetricRow>
              )}
              {activeMetrics['avgDuration'] && (
                <MetricRow>
                  <MetricRowIcon>
                    <MetricIndicator color={BLUE_COLOR} value={normalizedMetrics.avgDuration} />
                    <span>Avg Duration</span>
                  </MetricRowIcon>
                  <MetricValue>{avgTradeDuration}/{metricGoals.avgDuration} min</MetricValue>
                </MetricRow>
              )}
            </MetricContent>
          </MetricGroup>
        )}
      </GaugeMetrics>
    </CardContainer>
  );
};

GoalGaugeCard.propTypes = {
  overallProgress: PropTypes.number,
  profitProgress: PropTypes.number,
  guardProgress: PropTypes.number,
  focusProgress: PropTypes.number,
  winRate: PropTypes.number,
  profitFactor: PropTypes.number,
  maxDrawdown: PropTypes.number,
  avgWinLoss: PropTypes.number,
  consecutiveWins: PropTypes.number,
  riskRewardRatio: PropTypes.number,
  sharpeRatio: PropTypes.number,
  tradingFrequency: PropTypes.number,
  avgTradeDuration: PropTypes.number,
  onCustomize: PropTypes.func,
  onToggleCategory: PropTypes.func,
  visibleCategories: PropTypes.arrayOf(PropTypes.string),
  onViewHistory: PropTypes.func
};

export default GoalGaugeCard; 