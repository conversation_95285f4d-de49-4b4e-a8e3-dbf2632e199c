import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { 
  PlusIcon,
  MinusIcon,
  ArrowPathIcon
} from '@heroicons/react/24/solid';
import { GOAL_ICONS, ACHIEVEMENT_THRESHOLDS } from '../../config/goalIcons';

const keyframes = `
  @keyframes gaugeLoad {
    0% {
      transform: rotate(0deg);
      opacity: 0;
    }
    20% {
      opacity: 1;
    }
    100% {
      transform: rotate(var(--final-rotation));
      opacity: 1;
    }
  }

  @keyframes fadeIn {
    0% {
      opacity: 0;
      transform: translateY(10px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes glowPulse {
    0% {
      filter: drop-shadow(0 0 8px var(--color));
    }
    50% {
      filter: drop-shadow(0 0 15px var(--color));
    }
    100% {
      filter: drop-shadow(0 0 8px var(--color));
    }
  }
`;

const Gauge = styled.div`
  width: 44px;
  height: 44px;
  border-radius: 50%;
  position: relative;
  background: ${props => props.color}15;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  ${keyframes}

  &::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 50%;
    background: conic-gradient(
      from 0deg,
      ${props => props.color} ${props => props.$progress * 3.6}deg,
      transparent ${props => props.$progress * 3.6}deg
    );
    mask: radial-gradient(circle at center, transparent 40%, black 45%);
    -webkit-mask: radial-gradient(circle at center, transparent 40%, black 45%);
  }

  svg {
    width: 20px;
    height: 20px;
    color: ${props => props.color};
    z-index: 1;
    opacity: ${props => props.filled ? 1 : 0.6};
    transition: all 0.3s ease;
    ${props => props.filled && `
      filter: drop-shadow(0 0 8px ${props.color});
      animation: glowPulse 2s ease-in-out infinite;
    `}
  }
`;

const GaugeTooltip = styled.div`
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: #1f2937;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  white-space: nowrap;
  z-index: 10;

  ${Gauge}:hover & {
    opacity: 1;
  }
`;

const SettingsContainer = styled.div`
  width: 100%;
`;

const SettingsHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
`;

const Title = styled.h1`
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
`;

const ResetButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  background: #f1f5f9;
  color: #475569;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #e2e8f0;
    color: #1f2937;
  }

  svg {
    width: 1rem;
    height: 1rem;
  }
`;

const CategoriesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  margin-top: 1.5rem;

  @media (max-width: 1536px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
  }
`;

const CategoryCard = styled.div`
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  height: fit-content;
`;

const CategoryHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  border-radius: 0.75rem;
  background: ${props => props.color}08;
  border: 1px solid ${props => props.color}15;
  transition: all 0.3s ease;

  &:hover {
    background: ${props => props.color}12;
  }
`;

const CategoryTitle = styled.h2`
  font-size: 1.125rem;
  font-weight: 600;
  color: ${props => props.color};
  text-transform: uppercase;
  letter-spacing: 0.05em;
  display: flex;
  align-items: center;
  gap: 0.75rem;
`;

const MetricsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const MetricItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-radius: 0.5rem;
  background: ${props => props.isActive ? props.color + '10' : '#f8fafc'};
  border: 1px solid ${props => props.isActive ? props.color + '20' : '#e5e7eb'};
  transition: all 0.2s ease;

  &:hover {
    background: ${props => props.color + '15'};
  }
`;

const MetricInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
`;

const MetricName = styled.div`
  font-size: 0.875rem;
  font-weight: 500;
  color: #1f2937;
`;

const MetricDescription = styled.div`
  font-size: 0.75rem;
  color: #6b7280;
`;

const ToggleButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 0.375rem;
  border: none;
  background: ${props => props.isActive ? props.color : '#f1f5f9'};
  color: ${props => props.isActive ? 'white' : '#64748b'};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${props => props.isActive ? props.color : '#e2e8f0'};
  }

  svg {
    width: 1.25rem;
    height: 1.25rem;
  }
`;

const BackButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  background: #f1f5f9;
  color: #475569;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #e2e8f0;
    color: #1f2937;
  }

  svg {
    width: 1rem;
    height: 1rem;
  }
`;

const HeaderActions = styled.div`
  display: flex;
  gap: 1rem;
`;

const CATEGORIES = {
  performance: {
    title: GOAL_ICONS.performance.title,
    color: GOAL_ICONS.performance.color,
    icon: GOAL_ICONS.performance.icon,
    metrics: [
      {
        id: 'overallProgress',
        name: 'Overall Progress',
        description: 'Combined score of all trading metrics',
        default: true,
        defaultGoal: ACHIEVEMENT_THRESHOLDS.overall,
        unit: '%',
        min: 0,
        max: 100
      }
    ]
  },
  profit: {
    title: GOAL_ICONS.profit.title,
    color: GOAL_ICONS.profit.color,
    icon: GOAL_ICONS.profit.icon,
    metrics: [
      {
        id: 'winRate',
        name: 'Win Rate',
        description: 'Percentage of profitable trades',
        default: true,
        defaultGoal: 55,
        unit: '%',
        min: 0,
        max: 100
      },
      {
        id: 'profitFactor',
        name: 'Profit Factor',
        description: 'Ratio of gross profits to gross losses',
        default: true,
        defaultGoal: 1.5,
        unit: '',
        min: 1,
        max: 10,
        step: 0.1
      },
      {
        id: 'avgWinLoss',
        name: 'Average Win/Loss',
        description: 'Comparison of average winning and losing trades',
        default: true,
        defaultGoal: 1.5,
        unit: '',
        min: 0.1,
        max: 10,
        step: 0.1
      },
      {
        id: 'returnOnCapital',
        name: 'Return on Capital',
        description: 'Monthly return on invested capital',
        default: false,
        defaultGoal: 5,
        unit: '%',
        min: 0,
        max: 100
      },
      {
        id: 'averageRReturn',
        name: 'Average R Return',
        description: 'Average return in terms of initial risk (R)',
        default: false,
        defaultGoal: 1.5,
        unit: 'R',
        min: 0.1,
        max: 10,
        step: 0.1
      },
      {
        id: 'expectancy',
        name: 'Expectancy',
        description: 'Expected profit/loss per trade in currency terms',
        default: false,
        defaultGoal: 75,
        unit: '$',
        min: 0,
        max: 1000
      }
    ]
  },
  guard: {
    title: GOAL_ICONS.guard.title,
    color: GOAL_ICONS.guard.color,
    icon: GOAL_ICONS.guard.icon,
    metrics: [
      {
        id: 'sharpeRatio',
        name: 'Sharpe Ratio',
        description: 'Risk-adjusted return measurement',
        default: true,
        defaultGoal: 1.5,
        unit: '',
        min: 0,
        max: 5,
        step: 0.1
      },
      {
        id: 'riskReward',
        name: 'Risk/Reward',
        description: 'Ratio of potential profit to potential loss',
        default: true,
        defaultGoal: 2,
        unit: '',
        min: 0.5,
        max: 10,
        step: 0.1
      },
      {
        id: 'maxDrawdown',
        name: 'Max Drawdown',
        description: 'Largest peak-to-trough decline',
        default: true,
        defaultGoal: 10,
        unit: '%',
        min: 1,
        max: 50
      },
      {
        id: 'sortinoRatio',
        name: 'Sortino Ratio',
        description: 'Risk-adjusted return focusing on downside volatility',
        default: false,
        defaultGoal: 2,
        unit: '',
        min: 0,
        max: 10,
        step: 0.1
      },
      {
        id: 'calmarRatio',
        name: 'Calmar Ratio',
        description: 'Risk-adjusted return relative to maximum drawdown',
        default: false,
        defaultGoal: 3,
        unit: '',
        min: 0,
        max: 10,
        step: 0.1
      },
      {
        id: 'riskPerTrade',
        name: 'Risk per Trade',
        description: 'Maximum risk per trade as percentage of capital',
        default: false,
        defaultGoal: 1,
        unit: '%',
        min: 0.1,
        max: 5,
        step: 0.1
      }
    ]
  },
  focus: {
    title: GOAL_ICONS.focus.title,
    color: GOAL_ICONS.focus.color,
    icon: GOAL_ICONS.focus.icon,
    metrics: [
      {
        id: 'consecutiveWins',
        name: 'Consecutive Wins',
        description: 'Number of winning trades in a row',
        default: true,
        defaultGoal: 5,
        unit: '',
        min: 1,
        max: 20
      },
      {
        id: 'tradingFrequency',
        name: 'Trading Frequency',
        description: 'How often trades are placed',
        default: true,
        defaultGoal: 85,
        unit: '%',
        min: 0,
        max: 100
      },
      {
        id: 'avgDuration',
        name: 'Average Duration',
        description: 'Average time spent in trades',
        default: true,
        defaultGoal: 45,
        unit: 'min',
        min: 1,
        max: 480
      },
      {
        id: 'timeEfficiency',
        name: 'Time Efficiency',
        description: 'Profit per hour in market',
        default: false,
        defaultGoal: 50,
        unit: '$/hr',
        min: 0,
        max: 1000
      },
      {
        id: 'winStreak',
        name: 'Win Streak',
        description: 'Longest sequence of winning trades',
        default: false,
        defaultGoal: 7,
        unit: '',
        min: 1,
        max: 20
      },
      {
        id: 'tradeConsistency',
        name: 'Trade Consistency',
        description: 'Standard deviation of trade results',
        default: false,
        defaultGoal: 15,
        unit: '%',
        min: 0,
        max: 100
      }
    ]
  }
};

const MetricInput = styled.input`
  width: 80px;
  padding: 0.25rem 0.5rem;
  border: 1px solid ${props => props.isActive ? props.color + '40' : '#e5e7eb'};
  border-radius: 0.375rem;
  font-size: 0.875rem;
  color: #1f2937;
  background: white;
  transition: all 0.2s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.color};
    box-shadow: 0 0 0 2px ${props => props.color + '20'};
  }

  &::-webkit-inner-spin-button,
  &::-webkit-outer-spin-button {
    opacity: 1;
  }
`;

const MetricControls = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const MetricUnit = styled.span`
  font-size: 0.75rem;
  color: #6b7280;
  min-width: 24px;
`;

const PageHeader = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
`;

const HeaderContent = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

export default function GoalCardSettings() {
  const navigate = useNavigate();
  const [activeMetrics, setActiveMetrics] = useState(() => {
    const savedMetrics = localStorage.getItem('goalCardMetrics');
    return savedMetrics ? JSON.parse(savedMetrics) : Object.fromEntries(
      Object.values(CATEGORIES).flatMap(category => 
        category.metrics.filter(metric => metric.default).map(metric => [metric.id, true])
      )
    );
  });

  const [metricGoals, setMetricGoals] = useState(() => {
    const savedGoals = localStorage.getItem('goalCardMetricGoals');
    return savedGoals ? JSON.parse(savedGoals) : Object.fromEntries(
      Object.values(CATEGORIES).flatMap(category => 
        category.metrics.map(metric => [metric.id, metric.defaultGoal])
      )
    );
  });

  const handleBack = () => {
    navigate(-1);
  };

  const handleToggleMetric = (metricId) => {
    setActiveMetrics(prev => {
      const updated = { ...prev, [metricId]: !prev[metricId] };
      localStorage.setItem('goalCardMetrics', JSON.stringify(updated));
      return updated;
    });
  };

  const handleGoalChange = (metricId, value) => {
    setMetricGoals(prev => {
      const updated = { ...prev, [metricId]: value };
      localStorage.setItem('goalCardMetricGoals', JSON.stringify(updated));
      return updated;
    });
  };

  const handleReset = () => {
    const defaultMetrics = Object.fromEntries(
      Object.values(CATEGORIES).flatMap(category => 
        category.metrics.filter(metric => metric.default).map(metric => [metric.id, true])
      )
    );
    const defaultGoals = Object.fromEntries(
      Object.values(CATEGORIES).flatMap(category => 
        category.metrics.map(metric => [metric.id, metric.defaultGoal])
      )
    );
    localStorage.setItem('goalCardMetrics', JSON.stringify(defaultMetrics));
    localStorage.setItem('goalCardMetricGoals', JSON.stringify(defaultGoals));
    setActiveMetrics(defaultMetrics);
    setMetricGoals(defaultGoals);
  };

  return (
    <SettingsContainer>
      <SettingsHeader>
        <Title>Goal Card Settings</Title>
        <ResetButton onClick={handleReset}>
          <ArrowPathIcon />
          Reset to Default
        </ResetButton>
      </SettingsHeader>

      <CategoriesGrid>
        {Object.entries(CATEGORIES).map(([key, category]) => (
          <CategoryCard key={key}>
            <CategoryHeader color={category.color}>
              <Gauge 
                color={category.color}
                $progress={75}
                filled={true}
              >
                <GaugeTooltip>
                  {category.title} Settings
                </GaugeTooltip>
                <category.icon />
              </Gauge>
              <CategoryTitle color={category.color}>{category.title}</CategoryTitle>
            </CategoryHeader>
            <MetricsList>
              {category.metrics.map(metric => (
                <MetricItem 
                  key={metric.id}
                  isActive={activeMetrics[metric.id]}
                  color={category.color}
                >
                  <MetricInfo>
                    <MetricName>{metric.name}</MetricName>
                    <MetricDescription>{metric.description}</MetricDescription>
                  </MetricInfo>
                  <MetricControls>
                    <MetricInput
                      type="number"
                      min={metric.min}
                      max={metric.max}
                      step={metric.step || 1}
                      value={metricGoals[metric.id]}
                      onChange={(e) => handleGoalChange(metric.id, parseFloat(e.target.value))}
                      color={category.color}
                      isActive={activeMetrics[metric.id]}
                      disabled={!activeMetrics[metric.id]}
                    />
                    <MetricUnit>{metric.unit}</MetricUnit>
                    <ToggleButton
                      isActive={activeMetrics[metric.id]}
                      color={category.color}
                      onClick={() => handleToggleMetric(metric.id)}
                    >
                      {activeMetrics[metric.id] ? <MinusIcon /> : <PlusIcon />}
                    </ToggleButton>
                  </MetricControls>
                </MetricItem>
              ))}
            </MetricsList>
          </CategoryCard>
        ))}
      </CategoriesGrid>
    </SettingsContainer>
  );
} 