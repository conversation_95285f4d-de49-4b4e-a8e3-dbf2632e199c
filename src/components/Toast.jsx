import { useState, useEffect } from 'react';
import { CheckCircleIcon, XCircleIcon, XMarkIcon } from '@heroicons/react/24/outline';

export default function Toast({ message, type = 'success', onClose, duration = 5000 }) {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      onClose?.();
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  const bgColor = type === 'success' ? 'bg-green-50' : 'bg-red-50';
  const textColor = type === 'success' ? 'text-green-800' : 'text-red-800';
  const borderColor = type === 'success' ? 'border-green-200' : 'border-red-200';
  const Icon = type === 'success' ? CheckCircleIcon : XCircleIcon;

  return isVisible ? (
    <div className={`fixed top-4 right-4 z-50 flex items-center p-4 space-x-3 ${bgColor} border ${borderColor} rounded-lg shadow-lg max-w-md animate-fade-in`}>
      <Icon className={`w-6 h-6 ${textColor}`} />
      <div className={`flex-1 text-sm font-medium ${textColor}`}>
        {message}
      </div>
      <button
        onClick={() => {
          setIsVisible(false);
          onClose?.();
        }}
        className={`${textColor} hover:opacity-70 transition-opacity`}
      >
        <XMarkIcon className="w-5 h-5" />
      </button>
    </div>
  ) : null;
} 