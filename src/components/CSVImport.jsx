import React, { useState, useEffect, useRef, memo } from 'react';
import { useNavigate } from 'react-router-dom';
import useSupabaseTradeStore from '../stores/supabaseTradeStore';
import { saveGoalHistory } from '../services/goalService';
import { TradeAnalytics } from '../utils/analytics';
import { useProfile } from '../contexts/ProfileContext';
import { useAuth } from '../contexts/AuthContext';

const CSVImport = memo(function CSVImport({ onImportComplete, onClose }) {
  const navigate = useNavigate();
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const { settings } = useProfile();
  const { user } = useAuth();
  const { addTrades, initialize } = useSupabaseTradeStore();
  const [progress, setProgress] = useState(0);

  // Initialize once when component mounts
  const isInitialized = useRef(false);

  // Get accounts and set default account
  const accounts = settings?.accounts || [];
  const defaultAccount = accounts.length > 0
    ? (accounts.find(acc => acc.isDefault) || accounts[0])
    : { id: 'default', name: 'Default Account' };

  const [selectedAccount, setSelectedAccount] = useState(defaultAccount.id);

  // Initialize trade store once when user is available
  useEffect(() => {
    if (user?.id && !isInitialized.current) {
      console.log('🔄 Initializing trade store for CSV import...');
      initialize(user.id);
      isInitialized.current = true;
    }
  }, [user?.id, initialize]);

  // Update selected account when accounts change
  useEffect(() => {
    if (accounts.length > 0 && !selectedAccount) {
      const account = accounts.find(acc => acc.isDefault) || accounts[0];
      setSelectedAccount(account.id);
    }
  }, [accounts, selectedAccount]);

  const parsePnL = (pnlStr) => {
    if (!pnlStr) return 0;
    // Remove $ and spaces
    const cleaned = pnlStr.trim().replace('$', '');
    if (cleaned.startsWith('(') && cleaned.endsWith(')')) {
      // Negative value: $(215.00) -> -215.00
      return -parseFloat(cleaned.slice(1, -1));
    }
    // Positive value: $30.00 -> 30.00
    return parseFloat(cleaned);
  };

  const parseTimestamp = (timestampStr) => {
    try {
      // Parse MM/DD/YYYY HH:mm:ss format
      const [datePart, timePart] = timestampStr.split(' ');
      const [month, day, year] = datePart.split('/');
      const [hours, minutes, seconds] = timePart.split(':');
      
      // Create a Date object with UTC values to avoid timezone issues
      const date = new Date(Date.UTC(
        parseInt(year),
        parseInt(month) - 1, // month is 0-based
        parseInt(day),
        parseInt(hours),
        parseInt(minutes),
        parseInt(seconds)
      ));

      if (isNaN(date.getTime())) {
        throw new Error('Invalid date');
      }

      return date.toISOString();
    } catch (err) {
      console.error('Error parsing timestamp:', timestampStr, err);
      throw new Error(`Invalid timestamp format: ${timestampStr}`);
    }
  };

  const parsePerformanceCSV = async (file) => {
    console.log('Starting CSV parsing...');
    const accountToUse = selectedAccount || 'default';
    console.log('📁 Using account for import:', accountToUse);

    const text = await file.text();
    console.log('CSV text loaded:', text.slice(0, 100) + '...'); // Log first 100 chars
    
    const rows = text.split(/\r?\n/).map(row => row.split(','));
    const headers = rows[0].map(header => header.trim());
    console.log('Headers found:', headers);

    // Validate Performance CSV format
    const requiredColumns = [
      'symbol',
      '_priceFormat',
      '_priceFormatType',
      '_tickSize',
      'buyFillId',
      'sellFillId',
      'qty',
      'pnl',
      'boughtTimestamp',
      'soldTimestamp',
      'duration'
    ];

    const missingColumns = requiredColumns.filter(col => !headers.includes(col));
    if (missingColumns.length > 0) {
      throw new Error(`Missing required columns: ${missingColumns.join(', ')}`);
    }

    // Convert rows to trades with progress tracking
    const validRows = rows.slice(1).filter(row => row.length === headers.length && row.some(cell => cell.trim()));
    const totalRows = validRows.length;
    console.log('📊 Total valid rows to process:', totalRows);

    const trades = [];
    const skippedRows = [];

    for (let i = 0; i < validRows.length; i++) {
      try {
        const row = validRows[i];
        const trade = {};
        headers.forEach((header, j) => {
          trade[header] = row[j].trim();
        });

        console.log(`Processing row ${i + 1}:`, trade);

        // Validate required fields
        if (!trade.symbol || trade.symbol.trim() === '') {
          const errorMsg = `Row ${i + 1}: Missing or empty symbol`;
          console.warn(errorMsg);
          skippedRows.push(errorMsg);
          continue; // Skip this trade if symbol is missing
        }

        // Parse and validate quantity
        const quantity = parseInt(trade.qty);
        if (isNaN(quantity) || quantity <= 0) {
          const errorMsg = `Row ${i + 1}: Invalid quantity "${trade.qty}" for symbol ${trade.symbol}`;
          console.warn(errorMsg);
          skippedRows.push(errorMsg);
          continue; // Skip this trade if quantity is invalid
        }

        // Parse and validate prices
        const entryPrice = parseFloat(trade.buyPrice);
        const exitPrice = parseFloat(trade.sellPrice);

        if (isNaN(entryPrice) || entryPrice <= 0) {
          const errorMsg = `Row ${i + 1}: Invalid entry price "${trade.buyPrice}" for symbol ${trade.symbol}`;
          console.warn(errorMsg);
          skippedRows.push(errorMsg);
          continue; // Skip this trade if entry price is invalid
        }

        if (isNaN(exitPrice) || exitPrice <= 0) {
          const errorMsg = `Row ${i + 1}: Invalid exit price "${trade.sellPrice}" for symbol ${trade.symbol}`;
          console.warn(errorMsg);
          skippedRows.push(errorMsg);
          continue; // Skip this trade if exit price is invalid
        }

        // Parse and validate timestamps
        let entryTimestamp, exitTimestamp;
        try {
          entryTimestamp = parseTimestamp(trade.boughtTimestamp);
        } catch (err) {
          const errorMsg = `Row ${i + 1}: Invalid entry timestamp "${trade.boughtTimestamp}" for symbol ${trade.symbol}`;
          console.warn(errorMsg);
          skippedRows.push(errorMsg);
          continue; // Skip this trade if entry timestamp is invalid
        }

        try {
          exitTimestamp = parseTimestamp(trade.soldTimestamp);
        } catch (err) {
          const errorMsg = `Row ${i + 1}: Invalid exit timestamp "${trade.soldTimestamp}" for symbol ${trade.symbol}`;
          console.warn(errorMsg);
          skippedRows.push(errorMsg);
          continue; // Skip this trade if exit timestamp is invalid
        }

        // Convert to dashboard format
        // Convert to dashboard format
        trades.push({
          timestamp: entryTimestamp,
          exitTimestamp: exitTimestamp,
          instrument: trade.symbol,
          entry_price: entryPrice,
          exit_price: exitPrice,
          size: quantity,
          commission: 0, // No commission for futures (fees included in spread)
          position_type: 'LONG',
          profit_loss: parsePnL(trade.pnl),
          duration: trade.duration,
          account: accountToUse,
          trade_details: {
            buyFillId: trade.buyFillId,
            sellFillId: trade.sellFillId,
            priceFormat: parseFloat(trade._priceFormat),
            priceFormatType: parseFloat(trade._priceFormatType),
            tickSize: parseFloat(trade._tickSize),
            exitTimestamp: exitTimestamp
          },
          notes: '',
          tags: [],
          screenshots: [],
          market_conditions: {
            trend: '',
            volatility: '',
            volume: '',
            key_levels: []
          },
          execution_quality: {
            entry_accuracy: 0,
            exit_accuracy: 0,
            slippage: 0,
            fill_quality: 0
          },
          emotions: {
            pre_trade: '',
            during_trade: '',
            post_trade: ''
          },
          lessons_learned: '',
          follow_up_actions: []
        });

        // Update progress
        setProgress(Math.round(((i + 1) / totalRows) * 100));
      } catch (err) {
        console.error(`Error processing row ${i + 2}:`, validRows[i], err);
        throw new Error(`Error in row ${i + 2}: ${err.message}`);
      }
    }

    console.log('Parsed trades:', trades);

    // Report skipped rows if any
    if (skippedRows.length > 0) {
      console.warn(`Skipped ${skippedRows.length} rows due to validation errors:`, skippedRows);
      // You could also show this to the user in the UI if needed
    }

    console.log('📊 CSV Parsing Summary:');
    console.log(`   - Total rows processed: ${totalRows}`);
    console.log(`   - Successfully parsed: ${trades.length}`);
    console.log(`   - Skipped due to errors: ${skippedRows.length}`);
    console.log('📈 Parsed trades P&L values:', trades.map(t => `${t.instrument}: $${t.profit_loss}`));

    return trades;
  };

  const handleSuccess = (trades) => {
    const totalPnL = trades.reduce((sum, t) => sum + t.profit_loss, 0);
    console.log('✅ CSV Import Success - Total PnL calculated:', totalPnL);
    console.log('✅ CSV Import Success - Sample trade:', trades[0]);

    // Get account name for display
    const accountName = accounts.find(acc => acc.id === selectedAccount)?.name || selectedAccount || 'Default Account';

    const successMessage = {
      tradesCount: trades.length,
      totalPnL,
      account: accountName
    };
    setSuccess(successMessage);

    // Notify parent component
    if (onImportComplete) {
      const response = {
        success: true,
        message: `Successfully imported ${trades.length} trades for ${accountName}`,
        ...successMessage,
        keepOpen: true
      };
      console.log('✅ Notifying parent with response:', response);
      onImportComplete(response);
    }
  };

  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    if (!selectedAccount) {
      console.log('⚠️ No account selected, using default account for import');
      setSelectedAccount('default');
    }

    console.log('Starting file upload process:', file.name);
    setIsProcessing(true);
    setError(null);
    setSuccess(null);
    setProgress(0);

    try {
      const trades = await parsePerformanceCSV(file);
      console.log('Parsed trades:', trades);
      
      if (!trades || trades.length === 0) {
        throw new Error('No valid trades found in the CSV file');
      }

      // Ensure user is authenticated before importing
      if (!user?.id) {
        throw new Error('Please log in to import trades');
      }

      console.log('🔄 Importing trades for user:', user.email);
      const result = await addTrades(trades);
      console.log('✅ Trades added to Supabase:', result);

      // Calculate metrics and save to goal history
      try {
        const analytics = new TradeAnalytics(trades);
        const metrics = analytics.summary;
        await saveGoalHistory({
          overallProgress: Math.round((metrics.profitProgress + metrics.guardProgress + metrics.focusProgress) / 3),
          profitProgress: Math.min(100, (metrics.profitFactor * 50)),
          guardProgress: Math.max(0, 100 - (metrics.maxDrawdown * 2)),
          focusProgress: Math.min(100, (metrics.expectancy * 100)),
          winRate: metrics.winRate,
          profitFactor: metrics.profitFactor,
          maxDrawdown: metrics.maxDrawdown,
          avgWinLoss: metrics.avgWinLoss,
          consecutiveWins: metrics.consecutiveWins,
          riskRewardRatio: metrics.riskRewardRatio,
          sharpeRatio: metrics.sharpeRatio,
          tradingFrequency: metrics.tradingFrequency,
          avgTradeDuration: metrics.avgTradeDuration
        });
      } catch (error) {
        console.error('Error saving goal metrics:', error);
      }
      
      // Reset file input
      event.target.value = null;
      
      // Handle success
      handleSuccess(trades);
    } catch (err) {
      console.error('Error processing CSV:', err);
      setError(err.message);
      setSuccess(null);
      
      if (onImportComplete) {
        onImportComplete({
          success: false,
          message: err.message,
          keepOpen: true
        });
      }
    } finally {
      setIsProcessing(false);
      setProgress(0);
    }
  };

  const handleImportMore = () => {
    setSuccess(null);
    setError(null);
    // Trigger file input click
    const fileInput = document.querySelector('.csv-import-input');
    if (fileInput) {
      fileInput.click();
    }
  };

  const handleClose = () => {
    if (onClose) {
      onClose();
    }
  };

  const handleAccountChange = (accountId) => {
    console.log('Changing account to:', accountId);
    setError(null);
    setSelectedAccount(accountId);
  };

  // Show authentication error if user is not logged in
  if (!user?.id) {
    return (
      <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
        <div className="flex items-center">
          <svg className="w-5 h-5 text-red-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <div>
            <h3 className="text-sm font-medium text-red-800">Authentication Required</h3>
            <p className="text-sm text-red-600 mt-1">Please log in to import trades.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      {/* User Info */}
      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-center text-sm">
          <svg className="w-4 h-4 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
          <span className="text-blue-700">Importing for: <strong>{user.email}</strong></span>
        </div>
      </div>

      {/* Instructions */}
      <div className="mb-6 text-sm text-slate-600">
        <h3 className="font-medium text-slate-800 mb-2">Instructions:</h3>
        <ol className="list-decimal list-inside space-y-1">
          <li>Select the account you want to import trades for</li>
          <li>Choose a CSV file exported from your trading platform</li>
          <li>Make sure your CSV file includes:
            <ul className="ml-6 mt-1 list-disc text-slate-500">
              <li>Symbol</li>
              <li>Buy/Sell timestamps</li>
              <li>Entry/Exit prices</li>
              <li>Position size</li>
              <li>P&L</li>
            </ul>
          </li>
          <li>Click import and wait for the process to complete</li>
        </ol>
      </div>

      {/* Account Selection */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-slate-700 mb-1">
          Select Account
        </label>
        <select
          value={selectedAccount}
          onChange={(e) => handleAccountChange(e.target.value)}
          className="block w-full px-3 py-2 bg-white border border-slate-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          required
        >
          <option value="">Select an account</option>
          {accounts.map(account => (
            <option key={account.id} value={account.id}>
              {account.name} (${Number(account.initialCapital).toLocaleString()})
              {account.isDefault ? ' (Default)' : ''}
            </option>
          ))}
        </select>
      </div>

      {!accounts.length ? (
        <div className="mt-2 text-sm text-amber-600 bg-amber-50 border border-amber-200 rounded-md p-2">
          No trading accounts found. Please <button 
            className="text-amber-700 underline hover:text-amber-800"
            onClick={() => navigate('/settings/accounts')}
          >
            set up at least one account
          </button> in Settings before importing trades.
        </div>
      ) : !selectedAccount && (
        <div className="mt-2 text-sm text-amber-600 bg-amber-50 border border-amber-200 rounded-md p-2">
          Please select an account before importing trades.
        </div>
      )}

      {/* File Input */}
      <input
        type="file"
        accept=".csv"
        onChange={handleFileUpload}
        disabled={isProcessing || !selectedAccount}
        className={`csv-import-input block w-full text-sm text-slate-500
          file:mr-4 file:py-2 file:px-4
          file:rounded-full file:border-0
          file:text-sm file:font-semibold
          file:bg-violet-50 file:text-violet-700
          hover:file:bg-violet-100
          disabled:opacity-50 disabled:cursor-not-allowed
          ${isProcessing ? 'cursor-not-allowed' : 'cursor-pointer'}`}
      />

      {/* Processing Overlay */}
      {isProcessing && (
        <div className="absolute inset-0 bg-white/75 backdrop-blur-sm flex flex-col items-center justify-center">
          <div className="flex flex-col items-center space-y-3">
            <div className="relative">
              <div className="w-12 h-12 border-4 border-violet-200 rounded-full animate-spin"></div>
              <div 
                className="absolute inset-0 border-4 border-violet-500 rounded-full"
                style={{
                  clipPath: `polygon(0 0, 100% 0, 100% ${progress}%, 0 ${progress}%)`
                }}
              ></div>
            </div>
            <div className="text-sm font-medium text-slate-600">
              Processing trades... {progress}%
            </div>
          </div>
        </div>
      )}

      {/* Success State */}
      {success && (
        <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center text-green-800 mb-2">
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span className="font-medium">Import Successful!</span>
          </div>
          <div className="text-sm text-green-700 mb-4">
            <p>Imported {success.tradesCount} trades for {success.account}</p>
            <p className="mt-1">Total P&L: ${success.totalPnL.toLocaleString()}</p>
          </div>
          <div className="flex justify-end space-x-3">
            <button
              onClick={handleImportMore}
              className="px-4 py-2 text-sm font-medium text-green-700 hover:text-green-800 bg-green-100 hover:bg-green-200 rounded-lg transition-colors"
            >
              Import More Trades
            </button>
            <button
              onClick={handleClose}
              className="px-4 py-2 text-sm font-medium text-slate-600 hover:text-slate-700 bg-slate-100 hover:bg-slate-200 rounded-lg transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="mt-2 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md p-2">
          {error}
        </div>
      )}
    </div>
  );
});

export default CSVImport;