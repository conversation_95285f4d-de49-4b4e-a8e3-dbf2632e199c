import React, { useState, useEffect, useRef, useContext } from 'react';
import { Routes, Route, useNavigate, useLocation, Link } from 'react-router-dom';
import { 
  Cog6ToothIcon,
  CurrencyDollarIcon,
  BellIcon,
  ShieldCheckIcon,
  LockClosedIcon,
  DocumentDuplicateIcon,
  ChartBarIcon,
  ArrowUpTrayIcon,
  XMarkIcon
} from '@heroicons/react/24/solid';
import { useProfile } from '../contexts/ProfileContext';
import { useAuth } from '../contexts/AuthContext';
import GoalCardSettings from './settings/GoalCardSettings';
import DataManagement from './data-management/DataManagement';
import Main_Topbar from './dashboard/Main_Topbar';
import { ImportContext } from '../contexts/ImportContext';

const menuItems = [
  { key: 'general', label: 'General', icon: Cog6ToothIcon, color: '#3b82f6', path: '/settings/general' },
  { key: 'accounts', label: 'Accounts', icon: CurrencyDollarIcon, color: '#16a34a', path: '/settings/accounts' },
  { key: 'notifications', label: 'Notifications', icon: BellIcon, color: '#eab308', path: '/settings/notifications' },
  { key: 'security', label: 'Security', icon: ShieldCheckIcon, color: '#475569', path: '/settings/security' },
  { key: 'privacy', label: 'Privacy', icon: LockClosedIcon, color: '#6366f1', path: '/settings/privacy' },
  { key: 'data', label: 'Data Management', icon: DocumentDuplicateIcon, color: '#ec4899', path: '/settings/data' },
  { key: 'goal-card', label: 'Goal Card', icon: ChartBarIcon, color: '#8b5cf6', path: '/settings/goal-card' }
];

// Individual Settings Pages
const GeneralSettings = () => {
  const { profile, settings, updateSettings } = useProfile();
  const { user } = useAuth();
  const [isSaving, setIsSaving] = useState(false);

  // Debug: Log profile data
  useEffect(() => {
    console.log('GeneralSettings - Profile data:', profile);
    console.log('GeneralSettings - User data:', user);
    console.log('GeneralSettings - Settings data:', settings);
  }, [profile, user, settings]);

  const handleSettingsChange = async (field, value) => {
    try {
      setIsSaving(true);
      const updatedSettings = {
        ...settings,
        [field]: value
      };
      await updateSettings(updatedSettings);
    } catch (error) {
      console.error('Error updating settings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div>
      <div className="flex items-center gap-4 mb-6 pb-4 border-b border-slate-200">
        <div className="p-3 rounded-xl bg-blue-50">
          <Cog6ToothIcon className="w-6 h-6 text-blue-500" />
        </div>
        <h2 className="text-xl font-bold text-slate-900">General Settings</h2>
      </div>

      <div className="space-y-6">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">Full Name</label>
            <input
              type="text"
              className="w-full px-3 py-2 bg-white border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow"
              value={profile?.fullName || `${profile?.firstName || ''} ${profile?.lastName || ''}`.trim() || user?.email?.split('@')[0] || ''}
              onChange={(e) => handleSettingsChange('fullName', e.target.value)}
              placeholder="Enter your full name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">Email</label>
            <input
              type="email"
              className="w-full px-3 py-2 bg-slate-100 border border-slate-300 rounded-lg text-sm focus:outline-none text-slate-600"
              value={profile?.email || user?.email || ''}
              readOnly
              placeholder="Email address"
            />
            <p className="text-xs text-slate-500 mt-1">Email cannot be changed here. Contact support if needed.</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">Time Zone</label>
            <select
              className="w-full px-3 py-2 bg-white border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow"
              value={settings.timeZone}
              onChange={(e) => handleSettingsChange('timeZone', e.target.value)}
            >
              <option value="America/New_York">Eastern Time (ET)</option>
              <option value="America/Chicago">Central Time (CT)</option>
              <option value="America/Denver">Mountain Time (MT)</option>
              <option value="America/Los_Angeles">Pacific Time (PT)</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );
};

const AccountsSettings = () => {
  const { settings, updateSettings, addAccount, updateAccount, deleteAccount } = useProfile();
  const { user } = useAuth();
  const [accounts, setAccounts] = useState([]);
  const [newAccount, setNewAccount] = useState({
    name: '',
    initialCapital: '',
    type: 'live',
    broker: ''
  });
  const [isSaving, setIsSaving] = useState(false);

  // Debug: Log account data
  useEffect(() => {
    console.log('AccountsSettings - Settings:', settings);
    console.log('AccountsSettings - Accounts from settings:', settings?.accounts);
    if (settings?.accounts) {
      setAccounts(settings.accounts);
    }
  }, [settings]);
  const { showImportModal } = useContext(ImportContext);

  useEffect(() => {
    if (settings?.accounts) {
      setAccounts(settings.accounts);
    }
  }, [settings]);

  const handleAddAccount = async () => {
    if (!newAccount.name || !newAccount.initialCapital) {
      return;
    }

    try {
      setIsSaving(true);
      const account = {
        ...newAccount,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isDefault: accounts.length === 0
      };
      
      const updatedAccounts = [...accounts, account];
      await updateSettings({
        ...settings,
        accounts: updatedAccounts
      });
      
      setNewAccount({
        name: '',
        initialCapital: '',
        type: 'live',
        broker: ''
      });
    } catch (error) {
      console.error('Error adding account:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeleteAccount = async (accountId) => {
    try {
      setIsSaving(true);
      const updatedAccounts = accounts.filter(account => account.id !== accountId);
      await updateSettings({
        ...settings,
        accounts: updatedAccounts
      });
    } catch (error) {
      console.error('Error deleting account:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleSetDefaultAccount = async (accountId) => {
    try {
      setIsSaving(true);
      const updatedAccounts = accounts.map(account => ({
        ...account,
        isDefault: account.id === accountId
      }));
      await updateSettings({
        ...settings,
        accounts: updatedAccounts
      });
    } catch (error) {
      console.error('Error setting default account:', error);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div>
      <div className="flex items-center justify-between gap-4 mb-6 pb-4 border-b border-slate-200">
        <div className="flex items-center gap-4">
          <div className="p-3 rounded-xl bg-green-50">
            <CurrencyDollarIcon className="w-6 h-6 text-green-500" />
          </div>
          <h2 className="text-xl font-bold text-slate-900">Trading Accounts</h2>
        </div>
        <button
          onClick={() => showImportModal()}
          className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-green-500 hover:bg-green-600 rounded-lg transition-colors"
        >
          <ArrowUpTrayIcon className="w-4 h-4" />
          Import CSV
        </button>
      </div>

      <div className="space-y-6">
        {/* Add New Account */}
        <div className="bg-slate-50 p-6 rounded-xl border border-slate-200">
          <h3 className="text-base font-semibold text-slate-900 mb-4">Add New Account</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-1">Account Name</label>
              <input
                type="text"
                value={newAccount.name}
                onChange={(e) => setNewAccount(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter account name"
                className="w-full px-3 py-2 bg-white border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-shadow"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-1">Initial Capital</label>
              <input
                type="number"
                value={newAccount.initialCapital}
                onChange={(e) => setNewAccount(prev => ({ ...prev, initialCapital: e.target.value }))}
                placeholder="Enter initial capital"
                className="w-full px-3 py-2 bg-white border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-shadow"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-1">Account Type</label>
              <select
                value={newAccount.type}
                onChange={(e) => setNewAccount(prev => ({ ...prev, type: e.target.value }))}
                className="w-full px-3 py-2 bg-white border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-shadow"
              >
                <option value="live">Live Account</option>
                <option value="demo">Demo Account</option>
                <option value="paper">Paper Trading</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-1">Broker</label>
              <input
                type="text"
                value={newAccount.broker}
                onChange={(e) => setNewAccount(prev => ({ ...prev, broker: e.target.value }))}
                placeholder="Enter broker name"
                className="w-full px-3 py-2 bg-white border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-shadow"
              />
            </div>
          </div>
          <div className="mt-4">
            <button
              onClick={handleAddAccount}
              disabled={isSaving || !newAccount.name || !newAccount.initialCapital}
              className={`w-full sm:w-auto px-4 py-2 text-sm font-medium text-white rounded-lg transition-colors ${
                isSaving || !newAccount.name || !newAccount.initialCapital
                  ? 'bg-slate-400 cursor-not-allowed'
                  : 'bg-green-500 hover:bg-green-600'
              }`}
            >
              {isSaving ? 'Adding...' : 'Add Account'}
            </button>
          </div>
        </div>

        {/* Existing Accounts */}
        <div className="space-y-4">
          <h3 className="text-base font-semibold text-slate-900">Your Accounts</h3>
          {accounts.length === 0 ? (
            <div className="text-center py-8 bg-slate-50 rounded-xl border border-slate-200">
              <p className="text-sm text-slate-600">No accounts added yet</p>
            </div>
          ) : (
            accounts.map(account => (
              <div 
                key={account.id} 
                className="p-4 bg-white rounded-xl border border-slate-200 hover:border-slate-300 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-green-50">
                      <CurrencyDollarIcon className="w-5 h-5 text-green-500" />
                    </div>
                    <div>
                      <h4 className="font-medium text-slate-900">{account.name}</h4>
                      <p className="text-sm text-slate-500">
                        Initial Capital: ${Number(account.initialCapital).toLocaleString()}
                      </p>
                    </div>
                    {account.isDefault && (
                      <span className="ml-2 px-2 py-1 text-xs font-medium text-green-700 bg-green-50 rounded-md border border-green-200">
                        Default
                      </span>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {!account.isDefault && (
                      <button
                        onClick={() => handleSetDefaultAccount(account.id)}
                        className="px-3 py-1.5 text-sm font-medium text-slate-700 hover:text-slate-900 hover:bg-slate-100 rounded-lg transition-colors"
                      >
                        Set as Default
                      </button>
                    )}
                    <button
                      onClick={() => handleDeleteAccount(account.id)}
                      className="px-3 py-1.5 text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors"
                    >
                      Delete
                    </button>
                  </div>
                </div>
                <div className="mt-3 pt-3 border-t border-slate-100 grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-slate-500">Type: </span>
                    <span className="font-medium text-slate-700 capitalize">{account.type}</span>
                  </div>
                  {account.broker && (
                    <div>
                      <span className="text-slate-500">Broker: </span>
                      <span className="font-medium text-slate-700">{account.broker}</span>
                    </div>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

const NotificationsSettings = () => (
  <div>
    <div className="flex items-center gap-4 mb-6 pb-4 border-b border-slate-200">
      <div className="p-3 rounded-xl bg-yellow-50">
        <BellIcon className="w-6 h-6 text-yellow-500" />
      </div>
      <h2 className="text-xl font-bold text-slate-900">Notification Preferences</h2>
    </div>

    <div className="space-y-4">
      <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-slate-200">
        <div>
          <h4 className="text-sm font-medium text-slate-700">Email Notifications</h4>
          <p className="text-sm text-slate-500">Receive email notifications for important updates</p>
        </div>
        <button className="px-3 py-2 text-sm font-medium rounded-lg text-slate-600 hover:bg-slate-100 transition-colors">
          Configure
        </button>
      </div>
      <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-slate-200">
        <div>
          <h4 className="text-sm font-medium text-slate-700">Trade Alerts</h4>
          <p className="text-sm text-slate-500">Get notified about trade executions and updates</p>
        </div>
        <button className="px-3 py-2 text-sm font-medium rounded-lg text-slate-600 hover:bg-slate-100 transition-colors">
          Configure
        </button>
      </div>
    </div>
  </div>
);

const SecuritySettings = () => (
  <div>
    <div className="flex items-center gap-4 mb-6 pb-4 border-b border-slate-200">
      <div className="p-3 rounded-xl bg-slate-100">
        <ShieldCheckIcon className="w-6 h-6 text-slate-600" />
      </div>
      <h2 className="text-xl font-bold text-slate-900">Security Settings</h2>
    </div>

    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-slate-700 mb-1">Current Password</label>
        <input
          type="password"
          className="w-full px-3 py-2 bg-white border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow"
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-slate-700 mb-1">New Password</label>
        <input
          type="password"
          className="w-full px-3 py-2 bg-white border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow"
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-slate-700 mb-1">Confirm New Password</label>
        <input
          type="password"
          className="w-full px-3 py-2 bg-white border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-shadow"
        />
      </div>
      <div className="pt-4">
        <button className="w-full flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg text-white bg-blue-500 hover:bg-blue-600 transition-colors">
          Update Password
        </button>
      </div>
    </div>
  </div>
);

const PrivacySettings = () => (
  <div>
    <div className="flex items-center gap-4 mb-6 pb-4 border-b border-slate-200">
      <div className="p-3 rounded-xl bg-indigo-50">
        <LockClosedIcon className="w-6 h-6 text-indigo-500" />
      </div>
      <h2 className="text-xl font-bold text-slate-900">Privacy Settings</h2>
    </div>

    <div className="space-y-4">
      <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-slate-200">
        <div>
          <h4 className="text-sm font-medium text-slate-700">Data Collection</h4>
          <p className="text-sm text-slate-500">Control how your trading data is collected and used</p>
        </div>
        <button className="px-3 py-2 text-sm font-medium rounded-lg text-slate-600 hover:bg-slate-100 transition-colors">
          Configure
        </button>
      </div>
      <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-slate-200">
        <div>
          <h4 className="text-sm font-medium text-slate-700">Data Sharing</h4>
          <p className="text-sm text-slate-500">Manage how your data is shared with third parties</p>
        </div>
        <button className="px-3 py-2 text-sm font-medium rounded-lg text-slate-600 hover:bg-slate-100 transition-colors">
          Configure
        </button>
      </div>
    </div>
  </div>
);

export default function Settings() {
  const navigate = useNavigate();
  const location = useLocation();
  const { profile, settings, error } = useProfile();
  const [currentView, setCurrentView] = useState('settings');

  const handleViewChange = (view) => {
    if (view === 'overview' || view === 'analysis' || view === 'journal') {
      navigate('/', { state: { view } });
    }
    setCurrentView(view);
  };

  // Redirect to general settings if no specific route is selected
  useEffect(() => {
    if (location.pathname === '/settings') {
      navigate('/settings/general');
    }
  }, [location.pathname, navigate]);

  return (
    <div className="min-h-screen bg-slate-50">
      <Main_Topbar 
        currentView={currentView}
        onViewChange={handleViewChange}
        refreshing={false}
      />

      <div className="pt-16 pb-24">
        <div className="max-w-[1920px] mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex gap-8">
            {/* Settings Navigation */}
            <div className="w-80 flex-shrink-0">
              <div className="bg-white rounded-2xl shadow-sm p-4">
                <div className="flex items-center gap-3 px-4 py-3 mb-4">
                  <div className="p-2 rounded-xl bg-blue-50">
                    <Cog6ToothIcon className="w-6 h-6 text-blue-500" />
                  </div>
                  <h2 className="text-xl font-semibold text-slate-900">Settings</h2>
                </div>
                <div className="space-y-1">
                  {menuItems.map((item) => (
                    <Link
                      key={item.key}
                      to={item.path}
                      className={`flex items-center gap-4 px-4 py-3 rounded-xl transition-colors ${
                        location.pathname === item.path
                          ? 'bg-slate-50 border-l-[3px]'
                          : 'hover:bg-slate-50'
                      }`}
                      style={{ 
                        borderLeftColor: location.pathname === item.path ? item.color : 'transparent'
                      }}
                    >
                      <div 
                        className="p-3 rounded-xl"
                        style={{ backgroundColor: `${item.color}10` }}
                      >
                        <item.icon 
                          className="w-5 h-5"
                          style={{ color: item.color }}
                        />
                      </div>
                      <span className="font-medium text-slate-900">{item.label}</span>
                    </Link>
                  ))}
                </div>
              </div>
            </div>

            {/* Settings Content */}
            <div className="flex-1">
              {error && (
                <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-sm text-red-600">{error}</p>
                </div>
              )}

              <div className="bg-white rounded-2xl shadow-sm p-6">
                <Routes>
                  <Route path="/" element={<GeneralSettings />} />
                  <Route path="/general" element={<GeneralSettings />} />
                  <Route path="/accounts" element={<AccountsSettings />} />
                  <Route path="/notifications" element={<NotificationsSettings />} />
                  <Route path="/security" element={<SecuritySettings />} />
                  <Route path="/privacy" element={<PrivacySettings />} />
                  <Route path="/data" element={<DataManagement />} />
                  <Route path="/goal-card" element={<GoalCardSettings />} />
                </Routes>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 