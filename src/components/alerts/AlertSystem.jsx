import { useState, useEffect } from 'react';
import { XMarkIcon, BellAlertIcon } from '@heroicons/react/24/outline';

export default function AlertSystem({ trades, rules }) {
  const [alerts, setAlerts] = useState([]);
  const [showAlerts, setShowAlerts] = useState(false);

  useEffect(() => {
    checkForAlerts(trades, rules, setAlerts);
  }, [trades, rules]);

  return (
    <>
      {/* Alert <PERSON> */}
      <button
        onClick={() => setShowAlerts(true)}
        className="relative p-2 text-slate-600 hover:bg-slate-100 rounded-lg"
      >
        <BellAlertIcon className="w-5 h-5" />
        {alerts.length > 0 && (
          <span className="absolute -top-1 -right-1 w-4 h-4 text-xs bg-red-500 text-white rounded-full flex items-center justify-center">
            {alerts.length}
          </span>
        )}
      </button>

      {/* Alert Panel */}
      {showAlerts && (
        <div className="fixed inset-0 bg-black/30 z-50">
          <div className="fixed right-0 top-0 h-full w-96 bg-white shadow-lg">
            <div className="p-4 border-b border-slate-200 flex items-center justify-between">
              <h3 className="font-semibold">Alerts & Notifications</h3>
              <button
                onClick={() => setShowAlerts(false)}
                className="text-slate-400 hover:text-slate-600"
              >
                <XMarkIcon className="w-5 h-5" />
              </button>
            </div>
            <div className="p-4 space-y-3 overflow-y-auto max-h-[calc(100vh-64px)]">
              {alerts.map((alert, index) => (
                <AlertCard key={index} alert={alert} />
              ))}
              {alerts.length === 0 && (
                <p className="text-sm text-slate-500">No active alerts</p>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
}

function AlertCard({ alert }) {
  const { type, message, timestamp, severity } = alert;
  const colors = {
    high: 'bg-red-50 border-red-200 text-red-700',
    medium: 'bg-yellow-50 border-yellow-200 text-yellow-700',
    low: 'bg-blue-50 border-blue-200 text-blue-700'
  };

  return (
    <div className={`p-3 rounded-lg border ${colors[severity]}`}>
      <div className="flex items-center justify-between mb-1">
        <span className="font-medium">{type}</span>
        <span className="text-xs">
          {new Date(timestamp).toLocaleTimeString()}
        </span>
      </div>
      <p className="text-sm">{message}</p>
    </div>
  );
}

function checkForAlerts(trades, rules, setAlerts) {
  if (!trades?.length) return;

  const newAlerts = [];
  const latestTrade = trades[trades.length - 1];
  const recentTrades = trades.slice(-10);

  // Daily Loss Alert
  const dailyPnL = recentTrades.reduce((sum, t) => {
    if (isSameDay(new Date(t.timestamp), new Date(latestTrade.timestamp))) {
      return sum + t.profit_loss;
    }
    return sum;
  }, 0);

  if (dailyPnL < rules.dailyLossLimit) {
    newAlerts.push({
      type: 'Daily Loss Limit',
      message: `Daily loss of ${formatCurrency(dailyPnL)} exceeds limit of ${formatCurrency(rules.dailyLossLimit)}`,
      timestamp: new Date(),
      severity: 'high'
    });
  }

  // Position Size Alert
  if (latestTrade.size > rules.maxPositionSize) {
    newAlerts.push({
      type: 'Position Size',
      message: `Position size of ${latestTrade.size} exceeds maximum of ${rules.maxPositionSize}`,
      timestamp: new Date(),
      severity: 'high'
    });
  }

  // Consecutive Losses Alert
  let consecutiveLosses = 0;
  for (let i = trades.length - 1; i >= 0; i--) {
    if (trades[i].profit_loss < 0) {
      consecutiveLosses++;
    } else {
      break;
    }
  }

  if (consecutiveLosses >= rules.maxConsecutiveLosses) {
    newAlerts.push({
      type: 'Consecutive Losses',
      message: `${consecutiveLosses} consecutive losing trades detected`,
      timestamp: new Date(),
      severity: 'medium'
    });
  }

  // Drawdown Alert
  const drawdown = calculateDrawdown(trades);
  if (drawdown > rules.maxDrawdown) {
    newAlerts.push({
      type: 'Drawdown',
      message: `Current drawdown of ${drawdown.toFixed(1)}% exceeds maximum of ${rules.maxDrawdown}%`,
      timestamp: new Date(),
      severity: 'high'
    });
  }

  setAlerts(prev => [...prev, ...newAlerts]);
}

function isSameDay(date1, date2) {
  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  );
}

function calculateDrawdown(trades) {
  let peak = 0;
  let currentValue = 0;
  let maxDrawdown = 0;

  trades.forEach(trade => {
    currentValue += trade.profit_loss;
    if (currentValue > peak) {
      peak = currentValue;
    }
    const drawdown = ((peak - currentValue) / Math.abs(peak)) * 100;
    maxDrawdown = Math.max(maxDrawdown, drawdown);
  });

  return maxDrawdown;
}

function formatCurrency(value) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0
  }).format(value);
} 