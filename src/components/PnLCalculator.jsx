import React, { useState } from "react";
import <PERSON> from "papaparse";
import { getContractMultiplier } from "../utils/pnlCalculator";

const PnLCalculator = () => {
  const [transactions, setTransactions] = useState([]);
  const [pnlResults, setPnlResults] = useState([]);
  const [error, setError] = useState("");

  // Function to handle file upload
  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        complete: (results) => {
          try {
            // Validate required columns
            const requiredColumns = ['timestamp', 'side', 'quantity', 'price', 'instrument'];
            const headers = Object.keys(results.data[0] || {});
            const missingColumns = requiredColumns.filter(col => !headers.includes(col));
            
            if (missingColumns.length > 0) {
              setError(`Missing required columns: ${missingColumns.join(', ')}`);
              return;
            }

            const parsedData = results.data
              .filter(row => {
                // Filter out rows with missing or invalid data
                return row.timestamp && row.side && row.quantity && row.price && row.instrument;
              })
              .map((row) => {
                // Parse and validate each field
                const timestamp = new Date(row.timestamp);
                const side = row.side.toString().trim().toLowerCase();
                const quantity = Math.abs(parseInt(row.quantity, 10));
                const price = parseFloat(row.price);
                const instrument = row.instrument.toString().trim().toUpperCase();

                // Validate side
                if (side !== 'buy' && side !== 'sell') {
                  throw new Error(`Invalid side value: ${row.side}. Must be 'Buy' or 'Sell'`);
                }

                return {
                  timestamp,
                  side: side === 'buy' ? 'Buy' : 'Sell',
                  quantity,
                  price,
                  instrument,
                };
              })
              .filter(trade => {
                // Filter out trades with invalid data
                return !isNaN(trade.timestamp.getTime()) && 
                       !isNaN(trade.quantity) && 
                       trade.quantity > 0 &&
                       !isNaN(trade.price) && 
                       trade.price > 0;
              });

            if (parsedData.length === 0) {
              setError("No valid trades found in CSV file");
              return;
            }

            // Sort trades by timestamp
            const sortedData = parsedData.sort((a, b) => a.timestamp - b.timestamp);
            setTransactions(sortedData);
            setError(""); // Clear any previous errors
            setPnlResults([]); // Clear previous results
          } catch (err) {
            setError(`Error processing CSV data: ${err.message}`);
          }
        },
        error: (err) => setError(`Error parsing file: ${err.message}`),
      });
    }
  };

  // ... rest of the component stays the same ...
}; 