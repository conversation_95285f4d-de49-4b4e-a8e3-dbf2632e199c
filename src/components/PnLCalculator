import React, { useState } from "react";
import <PERSON> from "papaparse";
import { getContractMultiplier } from "../utils/pnlCalculator";

const PnLCalculator = () => {
  const [transactions, setTransactions] = useState([]);
  const [pnlResults, setPnlResults] = useState([]);
  const [error, setError] = useState("");

  // Function to handle file upload
  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        complete: (results) => {
          const parsedData = results.data.map((row) => ({
            timestamp: new Date(row.timestamp), // Ensure proper date formatting
            side: row.side, // "Buy" or "Sell"
            quantity: parseInt(row.quantity, 10),
            price: parseFloat(row.price),
            instrument: row.instrument,
          }));
          setTransactions(parsedData);
        },
        error: (err) => setError(`Error parsing file: ${err.message}`),
      });
    }
  };

  // Function to calculate PnL
  const calculatePnL = () => {
    const sortedTransactions = [...transactions].sort(
      (a, b) => new Date(a.timestamp) - new Date(b.timestamp)
    );

    const openPositions = [];
    const realizedPnL = [];

    sortedTransactions.forEach((transaction) => {
      if (transaction.instrument !== "NQ") return; // Only process NQ contracts

      if (transaction.side === "Buy") {
        openPositions.push({ ...transaction }); // Add to open positions
      } else if (transaction.side === "Sell") {
        let remainingQuantity = transaction.quantity;

        while (remainingQuantity > 0) {
          if (openPositions.length === 0) {
            setError("Unmatched sell transaction found!");
            return;
          }

          const openPosition = openPositions[0];
          const matchQuantity = Math.min(remainingQuantity, openPosition.quantity);

          // Use centralized contract multiplier function
          const contractMultiplier = getContractMultiplier(transaction.instrument);
          const pnl =
            (transaction.price - openPosition.price) *
            matchQuantity *
            contractMultiplier;

          realizedPnL.push({
            buyPrice: openPosition.price,
            sellPrice: transaction.price,
            quantity: matchQuantity,
            pnl,
          });

          // Adjust quantities
          openPosition.quantity -= matchQuantity;
          remainingQuantity -= matchQuantity;

          // Remove fully matched open position
          if (openPosition.quantity === 0) {
            openPositions.shift();
          }
        }
      }
    });

    setPnlResults(realizedPnL);
  };

  return (
    <div style={{ padding: "20px" }}>
      <h1>NQ Futures PnL Calculator</h1>

      {/* File Upload */}
      <input
        type="file"
        accept=".csv"
        onChange={handleFileUpload}
        style={{ marginBottom: "20px" }}
      />

      <button onClick={calculatePnL} style={{ marginBottom: "20px" }}>
        Calculate PnL
      </button>

      {/* Display Errors */}
      {error && <p style={{ color: "red" }}>{error}</p>}

      {/* Display Results */}
      {pnlResults.length > 0 && (
        <table border="1" cellPadding="5" style={{ width: "100%" }}>
          <thead>
            <tr>
              <th>Buy Price</th>
              <th>Sell Price</th>
              <th>Quantity</th>
              <th>Realized PnL ($)</th>
            </tr>
          </thead>
          <tbody>
            {pnlResults.map((result, index) => (
              <tr key={index}>
                <td>{result.buyPrice.toFixed(2)}</td>
                <td>{result.sellPrice.toFixed(2)}</td>
                <td>{result.quantity}</td>
                <td>{result.pnl.toFixed(2)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
};

export default PnLCalculator;
