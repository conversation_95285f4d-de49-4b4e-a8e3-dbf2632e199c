import React from 'react';

/**
 * Professional bar chart using pure CSS/HTML
 * Replaces Chart.js for better reliability and performance
 */
export default function SimpleBarChart({ data, title = "P&L Chart" }) {
  if (!data || !data.datasets || !data.datasets[0] || !data.datasets[0].data) {
    return (
      <div className="p-4 bg-gray-100 rounded">
        <p className="text-gray-600">No chart data available</p>
      </div>
    );
  }

  const values = data.datasets[0].data;
  const labels = data.labels || [];
  
  if (values.length === 0) {
    return (
      <div className="p-4 bg-gray-100 rounded">
        <p className="text-gray-600">No data points to display</p>
      </div>
    );
  }

  // Check if all values are zero
  if (values.every(v => v === 0)) {
    return (
      <div className="p-4 bg-gray-100 rounded">
        <p className="text-gray-600">All trades have $0 P&L</p>
        <p className="text-sm text-gray-500 mt-1">Check if P&L values are being calculated correctly</p>
      </div>
    );
  }

  const maxValue = Math.max(...values.map(Math.abs));
  const minValue = Math.min(...values);
  const hasNegative = minValue < 0;

  // Debug logging
  console.log('📊 SimpleBarChart Debug:', {
    valuesCount: values.length,
    values: values.slice(0, 5), // First 5 values
    maxValue,
    minValue,
    hasNegative,
    allZero: values.every(v => v === 0)
  });

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  return (
    <div className="h-full flex flex-col space-y-2 p-2">
      {/* Horizontal bars */}
      <div className="flex-1 space-y-1 overflow-y-auto">
        {values.map((value, index) => {
          const percentage = maxValue > 0 ? (Math.abs(value) / maxValue) * 100 : 0;
          const isPositive = value >= 0;
          const barWidth = Math.max(percentage, 2); // Minimum 2% width for visibility

          return (
            <div key={index} className="flex items-center space-x-2 group hover:bg-slate-50 px-2 py-1 rounded">
              {/* Trade label */}
              <div className="w-24 text-xs text-slate-600 truncate flex-shrink-0">
                {labels[index]?.split(',')[0] || `Trade ${index + 1}`}
              </div>

              {/* Bar container */}
              <div className="flex-1 flex items-center relative">
                {hasNegative && (
                  <>
                    {/* Negative side */}
                    <div className="w-1/2 flex justify-end pr-1">
                      {!isPositive && (
                        <div
                          className="bg-red-500 hover:bg-red-600 h-6 rounded-l transition-all duration-200 border border-red-600"
                          style={{ width: `${barWidth}%` }}
                        />
                      )}
                    </div>

                    {/* Zero line */}
                    <div className="w-px bg-slate-400 h-8 flex-shrink-0"></div>

                    {/* Positive side */}
                    <div className="w-1/2 flex justify-start pl-1">
                      {isPositive && (
                        <div
                          className="bg-green-500 hover:bg-green-600 h-6 rounded-r transition-all duration-200 border border-green-600"
                          style={{ width: `${barWidth}%` }}
                        />
                      )}
                    </div>
                  </>
                )}

                {!hasNegative && (
                  <div className="w-full flex justify-start">
                    <div
                      className={`h-6 rounded transition-all duration-200 border-2 ${
                        isPositive
                          ? 'bg-green-500 hover:bg-green-600 border-green-600'
                          : 'bg-red-500 hover:bg-red-600 border-red-600'
                      }`}
                      style={{ width: `${barWidth}%`, minWidth: '8px' }}
                    />
                  </div>
                )}

                {/* Tooltip on hover */}
                <div className="absolute left-full ml-2 hidden group-hover:block bg-slate-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap z-10">
                  <div className="font-medium">{formatCurrency(value)}</div>
                  <div className="text-slate-300">{labels[index]?.split(' • ')[1] || 'Trade'}</div>
                </div>
              </div>

              {/* Value label */}
              <div className={`w-16 text-xs text-right font-medium flex-shrink-0 ${
                isPositive ? 'text-green-600' : 'text-red-600'
              }`}>
                {formatCurrency(value)}
              </div>
            </div>
          );
        })}
      </div>

      {/* Scale indicators */}
      {maxValue > 0 && (
        <div className="flex items-center justify-center space-x-4 text-xs text-slate-500 border-t pt-2">
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-red-500 rounded"></div>
            <span>Loss</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-green-500 rounded"></div>
            <span>Profit</span>
          </div>
          <div className="text-slate-400">
            Range: {formatCurrency(minValue)} to {formatCurrency(Math.max(...values))}
          </div>
        </div>
      )}
    </div>
  );
}
