import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { ACHIEVEMENT_THRESHOLDS } from '../../config/goalIcons';
import { 
  format, 
  startOfMonth, 
  endOfMonth, 
  eachDayOfInterval, 
  isSameMonth, 
  isToday, 
  isFuture,
  startOfWeek,
  endOfWeek,
  addWeeks,
  subWeeks,
  isWithinInterval,
  differenceInMinutes
} from 'date-fns';
import { 
  ChevronLeftIcon, 
  ChevronRightIcon,
  ArrowTrendingUpIcon,
  ShieldCheckIcon,
  BoltIcon,
  CalendarIcon,
  ViewColumnsIcon,
  PlusIcon,
  MinusIcon,
  ArrowPathIcon,
  InformationCircleIcon
} from '@heroicons/react/24/solid';
import Breadcrumbs from '../common/Breadcrumbs';
import Sidebar from '../dashboard/Sidebar';
import { getGoalProgressRange } from '../../services/goalService';
import useSupabaseTradeStore from '../../stores/supabaseTradeStore';
import { GOAL_ICONS } from '../../config/goalIcons';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const PageContainer = styled.div`
  min-height: 100vh;
  background: #f8fafc;
  display: flex;
`;

const MainContent = styled.div`
  flex: 1;
  padding: 1rem;
  margin-top: 4rem;

  @media (min-width: 640px) {
    padding: 2rem 2rem 2rem 0;
  }
`;

const Container = styled.div`
  max-width: 100%;
  margin: 0 auto;
  padding: 0;

  @media (min-width: 640px) {
    padding: 0 2rem;
  }
`;

const Header = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
  background: white;
  border-radius: 0.75rem;
  padding: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;

  @media (min-width: 640px) {
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
`;

const HeaderLeft = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;

  @media (min-width: 640px) {
    gap: 0.75rem;
    width: auto;
  }
`;

const Title = styled.h1`
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  svg {
    width: 1.5rem;
    height: 1.5rem;
    color: #3b82f6;
  }

  @media (min-width: 640px) {
    font-size: 1.75rem;
    gap: 0.75rem;

    svg {
      width: 2rem;
      height: 2rem;
    }
  }
`;

const Legend = styled.div`
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  background: #f8fafc;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;

  @media (min-width: 640px) {
    gap: 2rem;
    padding: 0.75rem 1rem;
    border-radius: 0.75rem;
    flex-wrap: nowrap;
  }
`;

const LegendItem = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #4b5563;
  font-weight: 500;

  svg {
    width: 1rem;
    height: 1rem;
  }

  @media (min-width: 640px) {
    font-size: 0.875rem;
    gap: 0.75rem;

    svg {
      width: 1.25rem;
      height: 1.25rem;
    }
  }
`;

const LegendDot = styled.div`
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background: ${props => props.color};
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

  @media (min-width: 640px) {
    width: 1rem;
    height: 1rem;
  }
`;

const Summary = styled.div`
  display: none;

  @media (min-width: 640px) {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
  }
`;

const SummaryCard = styled.div`
  background: white;
  border-radius: 0.5rem;
  padding: 0.75rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;

  @media (min-width: 640px) {
    border-radius: 0.75rem;
    padding: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
`;

const SummaryHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 0.375rem;
  margin-bottom: 0.375rem;
  color: ${props => props.color};
  font-weight: 500;
  font-size: 0.75rem;

  @media (min-width: 640px) {
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
  }

  svg {
    width: 0.875rem;
    height: 0.875rem;

    @media (min-width: 640px) {
      width: 1rem;
      height: 1rem;
    }
  }
`;

const SummaryValue = styled.div`
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;

  @media (min-width: 640px) {
    font-size: 1.5rem;
  }
`;

const SummaryLabel = styled.div`
  font-size: 0.675rem;
  color: #6b7280;

  @media (min-width: 640px) {
    font-size: 0.75rem;
  }
`;

const DetailsPanel = styled.div`
  background: white;
  border-radius: 0.5rem;
  padding: 0.75rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  min-height: calc(100vh - 160px);
  overflow-y: auto;
  position: sticky;
  top: 0;

  @media (min-width: 640px) {
    border-radius: 0.75rem;
    padding: 1rem;
    min-height: calc(100vh - 180px);
  }

  &::-webkit-scrollbar {
    width: 4px;
  }
`;

const DetailHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;

  @media (min-width: 640px) {
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
  }
`;

const DetailDate = styled.div`
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;

  @media (min-width: 640px) {
    font-size: 1.125rem;
  }
`;

const DetailSection = styled.div`
  margin-bottom: 0.75rem;
  background: #ffffff;
  border-radius: 0.5rem;
  padding: 0.75rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;

  @media (min-width: 640px) {
    margin-bottom: 1rem;
    border-radius: 0.75rem;
    padding: 1rem;
  }

  &:last-child {
    margin-bottom: 0;
  }
`;

const DetailTitle = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;

  @media (min-width: 640px) {
    gap: 0.75rem;
    margin-bottom: 1rem;
  }

  .title-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.125rem;

    @media (min-width: 640px) {
      gap: 0.25rem;
    }
  }

  .title-text {
    font-size: 0.875rem;
    font-weight: 600;
    color: ${props => props.$color};
    display: flex;
    align-items: center;
    gap: 0.25rem;

    @media (min-width: 640px) {
      font-size: 1rem;
      gap: 0.375rem;
    }

    .achievement {
      font-size: 1rem;

      @media (min-width: 640px) {
        font-size: 1.125rem;
      }
    }
  }
`;

const DetailIconContainer = styled.div`
  width: 36px;
  height: 36px;
  position: relative;

  @media (min-width: 640px) {
    width: 40px;
    height: 40px;
  }
`;

const DetailGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.5rem;
  padding-left: calc(36px + 0.75rem);

  @media (min-width: 640px) {
    gap: 0.625rem;
    padding-left: calc(40px + 1rem);
  }
`;

const DetailRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.375rem 0.5rem;
  background: #f8fafc;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  border: 1px solid #e5e7eb;

  @media (min-width: 640px) {
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
  }

  &:hover {
    background: #f1f5f9;
    transform: translateX(2px);
    border-color: #cbd5e1;
  }
`;

const DetailLabel = styled.div`
  font-size: 0.675rem;
  color: #4b5563;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.375rem;

  @media (min-width: 640px) {
    font-size: 0.75rem;
    gap: 0.5rem;
  }

  svg {
    width: 0.875rem;
    height: 0.875rem;
    color: #64748b;

    @media (min-width: 640px) {
      width: 1rem;
      height: 1rem;
    }
  }
`;

const DetailValue = styled.div`
  font-size: 0.675rem;
  font-weight: 600;
  color: #1f2937;
  padding: 0.25rem 0.5rem;
  background: white;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  min-width: 70px;
  text-align: right;
  transition: all 0.2s ease;
  border: 1px solid #e5e7eb;

  @media (min-width: 640px) {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.5rem;
    min-width: 80px;
  }

  ${DetailRow}:hover & {
    transform: scale(1.02);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-color: #cbd5e1;
  }
`;

const NoDataMessage = styled.div`
  text-align: center;
  padding: 2rem;
  color: #6b7280;
  font-size: 0.875rem;
`;

const Calendar = styled.div`
  position: relative;
  background: white;
  border-radius: 0.5rem;
  padding: 0.5rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;

  @media (min-width: 640px) {
    border-radius: 0.75rem;
    padding: 0.75rem;
  }
`;

const WeekdayHeader = styled.div`
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.25rem;
  margin-bottom: 0.5rem;
  padding: 0 0.125rem;

  @media (min-width: 640px) {
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    padding: 0 0.25rem;
  }
`;

const Weekday = styled.div`
  font-size: 0.625rem;
  font-weight: 600;
  color: #4b5563;
  padding: 0.125rem;
  text-align: center;

  @media (min-width: 640px) {
    font-size: 0.75rem;
    padding: 0.25rem;
  }
`;

const DaysGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.125rem;
  padding: 0.125rem;

  @media (min-width: 640px) {
    gap: 0.25rem;
    padding: 0.25rem;
  }
`;

const Day = styled.div`
  position: relative;
  background: ${props => {
    if (props.$isHoliday) return '#fef2f2';
    if (props.$isWeekend) return '#f8fafc';
    return 'white';
  }};
  border: 1px solid ${props => {
    if (props.$isSelected) return '#3b82f6';
    if (props.$isToday) return '#6b7280';
    return '#e5e7eb';
  }};
  border-radius: 0.375rem;
  padding: 0.25rem;
  min-height: 60px;
  opacity: ${props => !props.$isCurrentMonth ? 0.5 : 1};
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;

  @media (min-width: 640px) {
    border-radius: 0.5rem;
    padding: 0.375rem;
    min-height: 80px;
  }

  ${props => !props.$isWeekend && !props.$isHoliday && `
    &:hover {
      border-color: #93c5fd;
      box-shadow: 0 1px 2px rgba(59, 130, 246, 0.1);
      transform: translateY(-1px);
    }
  `}

  ${props => props.$isSelected && `
    box-shadow: 0 0 0 1px #93c5fd;
    transform: translateY(-1px);
  `}
`;

const DayContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
  height: 100%;
  padding-top: 0.125rem;

  @media (min-width: 640px) {
    gap: 0.25rem;
    padding-top: 0.25rem;
  }
`;

const DayGaugeContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50px;
  position: relative;

  @media (min-width: 640px) {
    height: 70px;
  }

  /* Top gauge (Profit) */
  & > *:first-child {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%) scale(0.75);

    @media (min-width: 640px) {
      transform: translateX(-50%) scale(0.85);
    }
  }

  /* Bottom left gauge (Guard) */
  & > *:nth-child(2) {
    position: absolute;
    bottom: 0;
    left: calc(50% - 18px);
    transform: translateX(-50%) scale(0.75);

    @media (min-width: 640px) {
      bottom: 2px;
      left: calc(50% - 22px);
      transform: translateX(-50%) scale(0.85);
    }
  }

  /* Bottom right gauge (Focus) */
  & > *:nth-child(3) {
    position: absolute;
    bottom: 0;
    right: calc(50% - 18px);
    transform: translateX(50%) scale(0.75);

    @media (min-width: 640px) {
      bottom: 2px;
      right: calc(50% - 22px);
      transform: translateX(50%) scale(0.85);
    }
  }
`;

const GaugeTooltip = styled.div`
  position: absolute;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 12px;
  z-index: 50;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
  white-space: nowrap;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
`;

const Gauge = styled.div`
  width: 28px;
  height: 28px;
  border-radius: 50%;
  position: relative;
  background: ${props => props.color}15;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  @media (min-width: 640px) {
    width: 36px;
    height: 36px;
  }

  &::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 50%;
    background: conic-gradient(
      from 0deg,
      ${props => props.color} ${props => props.$progress * 3.6}deg,
      transparent ${props => props.$progress * 3.6}deg
    );
    mask: radial-gradient(circle at center, transparent 40%, black 45%);
    -webkit-mask: radial-gradient(circle at center, transparent 40%, black 45%);
  }

  svg {
    width: 12px;
    height: 12px;
    color: ${props => props.color};
    z-index: 1;
    opacity: ${props => props.filled ? 1 : 0.6};
    transition: all 0.3s ease;

    @media (min-width: 640px) {
      width: 16px;
      height: 16px;
    }

    ${props => props.filled && `
      filter: drop-shadow(0 0 6px ${props.color});
      animation: glowPulse 2s ease-in-out infinite;
    `}
  }
`;

const PnLValue = styled.span`
  color: ${props => props.value > 0 ? '#22c55e' : props.value < 0 ? '#ef4444' : '#64748b'};
  font-weight: 600;
`;

const DayNumber = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.625rem;
  font-weight: 500;
  color: ${props => props.$isToday ? '#3b82f6' : '#4b5563'};
  margin-bottom: 0.125rem;
  padding: 0.125rem 0;
  border-bottom: 1px solid #e5e7eb;

  @media (min-width: 640px) {
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
  }

  .date {
    color: ${props => props.$isToday ? '#3b82f6' : '#4b5563'};
  }

  .pnl {
    font-size: 0.625rem;
    font-weight: 600;
    color: ${props => {
      if (props.$pnl > 0) return '#16a34a';
      if (props.$pnl < 0) return '#dc2626';
      return '#6b7280';
    }};
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    background: ${props => {
      if (props.$pnl > 0) return '#f0fdf4';
      if (props.$pnl < 0) return '#fef2f2';
      return 'transparent';
    }};

    @media (min-width: 640px) {
      font-size: 0.75rem;
    }
  }
`;

const AchievementIndicator = styled.div`
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background: ${props => {
    switch (props.type) {
      case 'profit': return '#16a34a';
      case 'guard': return '#475569';
      case 'focus': return '#0ea5e9';
      default: return '#e5e7eb';
    }
  }};
  position: absolute;
  bottom: 0.5rem;
  left: ${props => 0.5 + props.index * 0.75}rem;
`;

const WEEKDAYS = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

const ViewToggle = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: #f8fafc;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;

  @media (min-width: 640px) {
    gap: 0.75rem;
    border-radius: 0.75rem;
  }
`;

const ViewButton = styled.button`
  background: ${props => props.$active ? 'white' : 'transparent'};
  border: 1px solid ${props => props.$active ? '#cbd5e1' : 'transparent'};
  border-radius: 0.5rem;
  padding: 0.5rem 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  color: ${props => props.$active ? '#1f2937' : '#64748b'};
  font-weight: ${props => props.$active ? 500 : 400};
  font-size: 0.875rem;
  box-shadow: ${props => props.$active ? '0 1px 2px rgba(0, 0, 0, 0.05)' : 'none'};

  @media (min-width: 640px) {
    border-radius: 0.75rem;
    padding: 0.625rem 1.25rem;
    gap: 0.75rem;
    font-size: 1rem;
  }

  &:hover {
    background: ${props => props.$active ? 'white' : '#f1f5f9'};
    transform: translateY(-1px);
  }

  svg {
    width: 1rem;
    height: 1rem;
    transition: all 0.2s ease;

    @media (min-width: 640px) {
      width: 1.25rem;
      height: 1.25rem;
    }
  }

  &:hover svg {
    transform: scale(1.1);
    color: #3b82f6;
  }
`;

const HeaderRight = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const ContentLayout = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-top: 1rem;

  @media (min-width: 1024px) {
    grid-template-columns: 400px 1fr;
    gap: 2rem;
    margin-top: 2rem;
  }

  /* Reverse order of children on mobile */
  & > *:first-child {
    order: 2;
  }

  & > *:last-child {
    order: 1;
  }

  @media (min-width: 1024px) {
    & > *:first-child {
      order: 1;
    }

    & > *:last-child {
      order: 2;
    }
  }
`;

const CalendarContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;

  @media (min-width: 1024px) {
    gap: 2rem;
  }
`;

const ScrollableContainer = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  padding: 0.5rem;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  padding-right: 1rem;
  margin-right: -1rem;
  margin-left: -0.5rem;

  @media (min-width: 640px) {
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 0;
    margin-right: 0;
    margin-left: 0;
  }

  &::-webkit-scrollbar {
    display: none;
  }
`;

const StatsContainer = styled(ScrollableContainer)`
  // Additional styles specific to StatsContainer if needed
`;

const CardBase = styled.div`
  flex: 0 0 auto;
  min-width: 180px;
  max-width: 220px;
  background: white;
  border-radius: 0.5rem;
  padding: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  margin-right: 0.25rem;

  &:last-child {
    margin-right: 0.5rem;
  }

  @media (min-width: 640px) {
    min-width: 200px;
    max-width: 240px;
    padding: 1rem;
    gap: 0.75rem;
    border-radius: 0.75rem;
    margin-right: 0;

    &:last-child {
      margin-right: 0;
    }
  }
`;

const StatCard = styled(CardBase)`
  // Additional styles specific to StatCard if needed
`;

const MobileSummaryCard = styled(CardBase)`
  min-width: 160px;

  .icon {
    width: 1.5rem;
    height: 1.5rem;
    color: ${props => props.color};
  }

  .content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    min-width: 0;
    flex: 1;
  }

  .value {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
  }

  .label {
    font-size: 0.75rem;
    color: #6b7280;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
`;

const StatIcon = styled.div`
  flex-shrink: 0;
  width: 2rem;
  height: 2rem;
  border-radius: 0.5rem;
  background: ${props => props.$color}10;
  display: flex;
  align-items: center;
  justify-content: center;

  @media (min-width: 640px) {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.625rem;
  }

  svg {
    width: 1rem;
    height: 1rem;
    color: ${props => props.$color};

    @media (min-width: 640px) {
      width: 1.25rem;
      height: 1.25rem;
    }
  }
`;

const StatContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  min-width: 0;
  flex: 1;
  padding-right: 0.25rem;
`;

const StatLabel = styled.div`
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  @media (min-width: 640px) {
    font-size: 0.875rem;
  }
`;

const StatValue = styled.div`
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;

  @media (min-width: 640px) {
    font-size: 1.25rem;
  }
`;

const LoadingSpinner = styled.div`
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 20px auto;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const LoadingOverlay = styled.div`
  position: absolute;
  inset: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 1rem;
`;

const MonthNavigation = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  padding-bottom: 1.5rem;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
`;

const MonthButton = styled.button`
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  padding: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  svg {
    width: 1.25rem;
    height: 1.25rem;
    color: #64748b;
    transition: all 0.2s ease;
  }

  &:hover svg {
    transform: scale(1.1);
    color: #3b82f6;
  }
`;

const MonthLabel = styled.div`
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  min-width: 180px;
  text-align: center;
`;

// Update holidays list to include both 2024 and 2025
const HOLIDAYS = [
  // 2024 Holidays
  '2024-01-01', // New Year's Day
  '2024-01-15', // Martin Luther King Jr. Day
  '2024-02-19', // Presidents' Day
  '2024-05-27', // Memorial Day
  '2024-06-19', // Juneteenth
  '2024-07-04', // Independence Day
  '2024-09-02', // Labor Day
  '2024-10-14', // Columbus Day
  '2024-11-11', // Veterans Day
  '2024-11-28', // Thanksgiving Day
  '2024-12-25', // Christmas Day
  
  // 2025 Holidays
  '2025-01-01', // New Year's Day
  '2025-01-20', // Martin Luther King Jr. Day
  '2025-02-17', // Presidents' Day
  '2025-05-26', // Memorial Day
  '2025-06-19', // Juneteenth
  '2025-07-04', // Independence Day
  '2025-09-01', // Labor Day
  '2025-10-13', // Columbus Day
  '2025-11-11', // Veterans Day
  '2025-11-27', // Thanksgiving Day
  '2025-12-25', // Christmas Day
];

const isWeekend = (date) => {
  const day = date.getDay();
  return day === 0 || day === 6; // 0 is Sunday, 6 is Saturday
};

const isHoliday = (date) => {
  const dateStr = format(date, 'yyyy-MM-dd');
  return HOLIDAYS.includes(dateStr);
};

// Request queue and cache with timestamps
const requestQueue = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

class DataCache {
  constructor() {
    this.cache = new Map();
  }

  set(key, value) {
    this.cache.set(key, {
      data: value,
      timestamp: Date.now()
    });
  }

  get(key) {
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    if (Date.now() - entry.timestamp > CACHE_DURATION) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data;
  }

  has(key) {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    if (Date.now() - entry.timestamp > CACHE_DURATION) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }
}

const dataCache = new DataCache();

const DetailPnL = styled.div`
  font-size: 1rem;
  font-weight: 600;
  color: ${props => props.$positive ? '#16a34a' : '#dc2626'};
  padding: 0.375rem 0.75rem;
  background: ${props => props.$positive ? '#f0fdf4' : '#fef2f2'};
  border-radius: 0.5rem;
  border: 1px solid ${props => props.$positive ? '#bbf7d0' : '#fecaca'};
`;

const MetricTitleInfo = styled.div`
  position: absolute;
  right: 0.375rem;
  top: 0.375rem;

  @media (min-width: 640px) {
    right: 0.5rem;
    top: 0.5rem;
  }
`;

const InfoIcon = styled(InformationCircleIcon)`
  width: 0.875rem;
  height: 0.875rem;
  color: ${props => props.color || '#64748b'};
  opacity: 0.6;
  cursor: help;
  transition: opacity 0.2s ease;

  @media (min-width: 640px) {
    width: 1rem;
    height: 1rem;
  }

  &:hover {
    opacity: 1;
  }
`;

// Add responsive styles for navigation buttons
const NavigationButton = styled.button`
  display: flex;
  align-items: center;
  padding: 0.375rem;
  border-radius: 0.375rem;
  border: 1px solid #e5e7eb;
  background: white;
  color: #4b5563;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  @media (min-width: 640px) {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.5rem;
  }

  &:hover {
    background: #f8fafc;
    color: #1f2937;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  svg {
    width: 1rem;
    height: 1rem;

    @media (min-width: 640px) {
      width: 1.25rem;
      height: 1.25rem;
    }
  }
`;

// Add a container for better mobile layout
const ContentWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;

  @media (min-width: 1024px) {
    flex-direction: row;
    gap: 2rem;
  }
`;

const MainSection = styled.div`
  flex: 1;
`;

const SideSection = styled.div`
  width: 100%;

  @media (min-width: 1024px) {
    width: 300px;
  }
`;

const MobileSummary = styled(ScrollableContainer)`
  @media (min-width: 640px) {
    display: none;
  }
`;

function GoalHistory() {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [monthData, setMonthData] = useState({});
  const [viewMode, setViewMode] = useState('month');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const { trades, loadTrades, initialize } = useSupabaseTradeStore();
  const navigate = useNavigate();
  const { user } = useAuth();
  
  // Add currentView state and handler
  const [currentView, setCurrentView] = useState('goal-history');
  
  const handleViewChange = (view) => {
    if (view === 'overview' || view === 'analysis' || view === 'journal') {
      navigate('/', { state: { view } });
    } else if (view === 'settings') {
      navigate('/settings');
    } else if (view === 'profile') {
      navigate('/profile');
    }
    setCurrentView(view);
  };

  // Add goalAchievements state
  const [goalAchievements, setGoalAchievements] = useState([
    {
      title: 'Profit Goals',
      icon: <ArrowTrendingUpIcon />,
      color: '#16a34a',
      progress: 85,
      description: 'Track your profit-related goals including win rate and profit factor'
    },
    {
      title: 'Guard Goals',
      icon: <ShieldCheckIcon />,
      color: '#475569',
      progress: 78,
      description: 'Monitor risk management metrics and protective measures'
    },
    {
      title: 'Focus Goals',
      icon: <BoltIcon />,
      color: '#3b82f6',
      progress: 92,
      description: 'Track trading consistency and focus metrics'
    }
  ]);

  // Initialize Supabase trade store when user is available
  useEffect(() => {
    if (user?.id) {
      console.log('🔄 Initializing Supabase trade store for GoalHistory:', user.email);
      initialize(user.id);
    }
  }, [user?.id, initialize]);

  // Add effect to update goal achievements
  useEffect(() => {
    if (!monthData || Object.keys(monthData).length === 0) return;

    const calculateAverageProgress = (type) => {
      const values = Object.values(monthData)
        .filter(data => data && data[`${type}Progress`])
        .map(data => data[`${type}Progress`]);
      
      if (values.length === 0) return 0;
      return Math.round(values.reduce((sum, val) => sum + val, 0) / values.length);
    };

    const updatedAchievements = [
      {
        title: 'Profit Goals',
        icon: <ArrowTrendingUpIcon />,
        color: '#16a34a',
        progress: calculateAverageProgress('profit'),
        description: 'Track your profit-related goals including win rate and profit factor'
      },
      {
        title: 'Guard Goals',
        icon: <ShieldCheckIcon />,
        color: '#475569',
        progress: calculateAverageProgress('guard'),
        description: 'Monitor risk management metrics and protective measures'
      },
      {
        title: 'Focus Goals',
        icon: <BoltIcon />,
        color: '#3b82f6',
        progress: calculateAverageProgress('focus'),
        description: 'Track trading consistency and focus metrics'
      }
    ];

    setGoalAchievements(updatedAchievements);
  }, [monthData]);

  // Add state for goal settings
  const [metricGoals, setMetricGoals] = useState(() => {
    const savedGoals = localStorage.getItem('goalCardMetricGoals');
    return savedGoals ? JSON.parse(savedGoals) : {
      overallProgress: 75,
      winRate: 55,
      profitFactor: 1.5,
      avgWinLoss: 1.5,
      sharpeRatio: 1.5,
      riskReward: 2,
      maxDrawdown: 10,
      consecutiveWins: 5,
      tradingFrequency: 85,
      avgDuration: 45
    };
  });

  // Add effect to listen for goal setting changes
  useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === 'goalCardMetricGoals') {
        const savedGoals = localStorage.getItem('goalCardMetricGoals');
        if (savedGoals) {
          setMetricGoals(JSON.parse(savedGoals));
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  // Load trades when component mounts
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        await loadTrades(true);
      } catch (err) {
        console.error('Error loading trades:', err);
        setError('Failed to load trades. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    loadInitialData();
  }, [loadTrades]);

  useEffect(() => {
    const fetchData = async () => {
      if (!trades || trades.length === 0) {
        console.log('No trades available');
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        let start, end;
        
        if (viewMode === 'week') {
          start = startOfWeek(currentDate, { weekStartsOn: 0 });
          end = endOfWeek(currentDate, { weekStartsOn: 0 });
        } else {
          start = startOfMonth(currentDate);
          end = endOfMonth(currentDate);
        }

        // Get goal progress data
        const goalData = await getGoalProgressRange(
          format(start, 'yyyy-MM-dd'),
          format(end, 'yyyy-MM-dd')
        );
        
        console.log('Fetched goal data:', goalData);

        // Convert goalData array to object with dates as keys
        const goalDataByDate = goalData.reduce((acc, goal) => {
          if (goal && goal.date) {
            acc[goal.date] = {
              date: goal.date,
              profitProgress: goal.profitProgress ?? 0,
              guardProgress: goal.guardProgress ?? 0,
              focusProgress: goal.focusProgress ?? 0,
              winRate: goal.winRate ?? 0,
              profitFactor: goal.profitFactor ?? 0,
              maxDrawdown: goal.maxDrawdown ?? 0,
              avgWinLoss: goal.avgWinLoss ?? 0,
              consecutiveWins: goal.consecutiveWins ?? 0,
              riskRewardRatio: goal.riskRewardRatio ?? 0,
              sharpeRatio: goal.sharpeRatio ?? 0,
              tradingFrequency: goal.tradingFrequency ?? 0,
              avgTradeDuration: goal.avgTradeDuration ?? 0
            };
          }
          return acc;
        }, {});
        
        console.log('Converted goal data:', goalDataByDate);

        // Filter trades for the current period
        const periodTrades = trades.filter(trade => {
          const tradeDate = new Date(trade.timestamp);
          return isWithinInterval(tradeDate, { start, end });
        });

        console.log('Available trades:', trades);
        console.log('Filtered period trades:', periodTrades);
        console.log('Current period:', { start: format(start, 'yyyy-MM-dd'), end: format(end, 'yyyy-MM-dd') });

        // Initialize updatedMonthData with goalDataByDate
        const updatedMonthData = { ...goalDataByDate };

        // Calculate daily P&L and metrics from trades
        const dailyPnL = {};
        const dailyMetrics = {};

        // Group trades by date
        periodTrades.forEach(trade => {
          const date = format(new Date(trade.timestamp), 'yyyy-MM-dd');
          console.log('Processing trade for date:', date, trade);
          
          if (!dailyPnL[date]) {
            dailyPnL[date] = 0;
            dailyMetrics[date] = {
              trades: [],
              winningTrades: 0,
              losingTrades: 0,
              grossProfit: 0,
              grossLoss: 0,
              maxDrawdown: 0,
              consecutiveWins: 0,
              currentStreak: 0,
              totalDuration: 0
            };
          }
          
          const profitLoss = typeof trade.profit_loss === 'number' ? trade.profit_loss : 
                            typeof trade.profitLoss === 'number' ? trade.profitLoss : 0;
          
          dailyPnL[date] += profitLoss;
          dailyMetrics[date].trades.push(trade);

          // Update win/loss counts
          if (profitLoss > 0) {
            dailyMetrics[date].winningTrades++;
            dailyMetrics[date].grossProfit += profitLoss;
            dailyMetrics[date].currentStreak++;
            dailyMetrics[date].consecutiveWins = Math.max(dailyMetrics[date].consecutiveWins, dailyMetrics[date].currentStreak);
          } else if (profitLoss < 0) {
            dailyMetrics[date].losingTrades++;
            dailyMetrics[date].grossLoss += Math.abs(profitLoss);
            dailyMetrics[date].currentStreak = 0;
          }

          // Calculate trade duration if available
          if (trade.exit_time && trade.entry_time) {
            const duration = differenceInMinutes(new Date(trade.exit_time), new Date(trade.entry_time));
            dailyMetrics[date].totalDuration += duration;
          }
        });

        console.log('Daily P&L:', dailyPnL);
        console.log('Daily Metrics:', dailyMetrics);

        // Calculate metrics for each day
        Object.entries(dailyMetrics).forEach(([date, metrics]) => {
          const totalTrades = metrics.trades.length;
          if (totalTrades === 0) return;

          console.log('Calculating metrics for date:', date, 'with trades:', totalTrades);

          // Calculate running P&L for max drawdown
          let runningPnL = 0;
          let peak = 0;
          let maxDrawdown = 0;

          metrics.trades.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
            .forEach(trade => {
              const pnl = trade.profit_loss || 0;
              runningPnL += pnl;
              peak = Math.max(peak, runningPnL);
              const drawdown = peak > 0 ? ((peak - runningPnL) / peak) * 100 : 0;
              maxDrawdown = Math.max(maxDrawdown, drawdown);
            });

          // Calculate metrics
          const winRate = (metrics.winningTrades / totalTrades) * 100;
          const profitFactor = metrics.grossLoss > 0 ? metrics.grossProfit / metrics.grossLoss : metrics.grossProfit > 0 ? Infinity : 0;
          const avgWinLoss = metrics.losingTrades > 0 ? 
            (metrics.grossProfit / metrics.winningTrades) / (metrics.grossLoss / metrics.losingTrades) : 0;

          // Calculate Risk/Reward Ratio
          let totalRiskReward = 0;
          let validRRTrades = 0;
          metrics.trades.forEach(trade => {
            if (trade.entry_price && trade.stop_loss && trade.target_price) {
              const risk = Math.abs(trade.entry_price - trade.stop_loss);
              const reward = Math.abs(trade.target_price - trade.entry_price);
              if (risk > 0) {
                totalRiskReward += reward / risk;
                validRRTrades++;
              }
            }
          });
          const riskRewardRatio = validRRTrades > 0 ? totalRiskReward / validRRTrades : 0;

          // Calculate Trade Duration
          let totalDuration = 0;
          let validDurationTrades = 0;
          metrics.trades.forEach(trade => {
            if (trade.entry_time && trade.exit_time) {
              const entryTime = new Date(trade.entry_time);
              const exitTime = new Date(trade.exit_time);
              if (isValid(entryTime) && isValid(exitTime)) {
                const duration = differenceInMinutes(exitTime, entryTime);
                if (duration > 0) {
                  totalDuration += duration;
                  validDurationTrades++;
                }
              }
            }
          });
          const avgTradeDuration = validDurationTrades > 0 ? totalDuration / validDurationTrades : 0;

          // Calculate Sharpe Ratio
          const returns = metrics.trades.map(t => t.profit_loss || 0);
          const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
          const stdDev = Math.sqrt(
            returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length
          );
          const sharpeRatio = stdDev > 0 ? (avgReturn / stdDev) * Math.sqrt(252) : 0;

          // Calculate trading frequency (trades per hour)
          const tradingFrequency = (totalTrades / 6.5) * 100; // Assuming 6.5 trading hours

          // Update the metrics in monthData
          if (!updatedMonthData[date]) {
            updatedMonthData[date] = {
              date,
              profitProgress: 0,
              guardProgress: 0,
              focusProgress: 0
            };
          }

          // Calculate progress values based on thresholds and actual metrics
          const calculateProgress = (metrics, type) => {
            if (!metrics) return 0;
            
            switch (type) {
              case 'profit':
                // Profit progress based on win rate and profit factor
                const winRateProgress = Math.min(50, (metrics.winRate / 60) * 50); // Win rate target of 60%
                const profitFactorProgress = Math.min(50, (metrics.profitFactor / 2) * 50); // Profit factor target of 2.0
                const profitProgress = winRateProgress + profitFactorProgress;
                console.log('Profit progress calculation:', {
                  winRate: metrics.winRate,
                  profitFactor: metrics.profitFactor,
                  winRateProgress,
                  profitFactorProgress,
                  total: profitProgress
                });
                return profitProgress;
              
              case 'guard':
                // Guard progress based on max drawdown and risk/reward ratio
                const drawdownProgress = Math.min(50, Math.max(0, (1 - metrics.maxDrawdown / 10) * 50)); // Max drawdown target of 10%
                const riskRewardProgress = Math.min(50, (metrics.riskRewardRatio / 2) * 50); // Risk/Reward target of 2.0
                const guardProgress = drawdownProgress + riskRewardProgress;
                console.log('Guard progress calculation:', {
                  maxDrawdown: metrics.maxDrawdown,
                  riskRewardRatio: metrics.riskRewardRatio,
                  drawdownProgress,
                  riskRewardProgress,
                  total: guardProgress
                });
                return guardProgress;
              
              case 'focus':
                // Focus progress based on trading frequency and avg trade duration
                const frequencyProgress = Math.min(50, (metrics.tradingFrequency / 70) * 50); // Trading frequency target of 70%
                
                // Duration progress is highest when duration is between 15 and 45 minutes
                let durationProgress = 0;
                if (metrics.avgTradeDuration >= 15 && metrics.avgTradeDuration <= 45) {
                  durationProgress = 50; // Full progress when within target range
                } else if (metrics.avgTradeDuration < 15) {
                  durationProgress = Math.min(50, (metrics.avgTradeDuration / 15) * 50);
                } else {
                  durationProgress = Math.min(50, (60 - metrics.avgTradeDuration) / 15 * 50);
                }
                
                const focusProgress = frequencyProgress + durationProgress;
                console.log('Focus progress calculation:', {
                  tradingFrequency: metrics.tradingFrequency,
                  avgTradeDuration: metrics.avgTradeDuration,
                  frequencyProgress,
                  durationProgress,
                  total: focusProgress
                });
                return focusProgress;
              
              default:
                return 0;
            }
          };

          const metricValues = {
            winRate,
            profitFactor,
            maxDrawdown,
            riskRewardRatio,
            tradingFrequency,
            avgTradeDuration
          };

          console.log('Metric values for date:', date, metricValues);

          // Calculate progress values
          const profitProgress = calculateProgress(metricValues, 'profit');
          const guardProgress = calculateProgress(metricValues, 'guard');
          const focusProgress = calculateProgress(metricValues, 'focus');

          updatedMonthData[date] = {
            ...updatedMonthData[date],
            totalPnL: dailyPnL[date],
            profitProgress,
            guardProgress,
            focusProgress,
            winRate,
            profitFactor,
            maxDrawdown,
            avgWinLoss,
            consecutiveWins: metrics.consecutiveWins,
            riskRewardRatio,
            sharpeRatio,
            tradingFrequency,
            avgTradeDuration
          };

          console.log('Updated metrics for date:', date, {
            ...updatedMonthData[date],
            metricValues,
            progressValues: { profitProgress, guardProgress, focusProgress }
          });
        });

        console.log('Final month data:', updatedMonthData);
        setMonthData(updatedMonthData);
      } catch (error) {
        console.error('Error fetching data:', error);
        setError('Failed to fetch goal history data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [currentDate, viewMode, trades]);

  const handlePreviousClick = () => {
    if (viewMode === 'week') {
      setCurrentDate(prev => subWeeks(prev, 1));
    } else {
      setCurrentDate(prev => new Date(prev.getFullYear(), prev.getMonth() - 1));
    }
  };

  const handleNextClick = () => {
    if (viewMode === 'week') {
      setCurrentDate(prev => addWeeks(prev, 1));
    } else {
      setCurrentDate(prev => new Date(prev.getFullYear(), prev.getMonth() + 1));
    }
  };

  const handleDayClick = (day) => {
    if (!isFuture(day)) {
      console.log('Day clicked:', format(day, 'yyyy-MM-dd'));
      setSelectedDate(day);
    }
  };

  const getDays = () => {
    if (viewMode === 'week') {
      const weekStart = startOfWeek(currentDate, { weekStartsOn: 0 });
      const weekEnd = endOfWeek(currentDate, { weekStartsOn: 0 });
      return eachDayOfInterval({ start: weekStart, end: weekEnd });
    } else {
      const monthStart = startOfMonth(currentDate);
      const monthEnd = endOfMonth(currentDate);
      const calendarStart = startOfWeek(monthStart, { weekStartsOn: 0 });
      const calendarEnd = endOfWeek(monthEnd, { weekStartsOn: 0 });
      return eachDayOfInterval({ start: calendarStart, end: calendarEnd });
    }
  };

  const days = getDays();

  // Calculate statistics
  const monthlyStats = {
    profit: Object.values(monthData).filter(data => {
      if (!data) return false;
      const winRateAchieved = data.winRate >= metricGoals.winRate;
      const profitFactorAchieved = data.profitFactor >= metricGoals.profitFactor;
      return winRateAchieved && profitFactorAchieved;
    }).length,
    guard: Object.values(monthData).filter(data => {
      if (!data) return false;
      const drawdownAchieved = data.maxDrawdown <= metricGoals.maxDrawdown;
      const riskRewardAchieved = data.riskRewardRatio >= metricGoals.riskReward;
      return drawdownAchieved && riskRewardAchieved;
    }).length,
    focus: Object.values(monthData).filter(data => {
      if (!data) return false;
      const frequencyAchieved = data.tradingFrequency >= metricGoals.tradingFrequency;
      const durationAchieved = data.avgTradeDuration >= 15 && data.avgTradeDuration <= metricGoals.avgDuration;
      return frequencyAchieved && durationAchieved;
    }).length
  };

  console.log('Monthly achievement stats:', monthlyStats);

  const renderDetailsPanel = () => {
    const selectedDateStr = format(selectedDate, 'yyyy-MM-dd');
    const selectedDayData = monthData[selectedDateStr];
    
    if (!selectedDayData || !selectedDayData.date) {
      return (
        <DetailsPanel>
          <DetailHeader>
            <DetailDate>{format(selectedDate, 'MMMM d, yyyy')}</DetailDate>
          </DetailHeader>
          <NoDataMessage>No goal data available for this date.</NoDataMessage>
        </DetailsPanel>
      );
    }

    const formatValue = (value, type = 'number') => {
      if (value === undefined || value === null) return '-';
      switch (type) {
        case 'percent':
          return `${Number(value).toFixed(2)}%`;
        case 'decimal':
          return Number(value).toFixed(2);
        case 'minutes':
          return `${Math.round(value)} min`;
        case 'currency':
          return `$${Number(value).toFixed(2)}`;
        default:
          return value;
      }
    };

    return (
      <DetailsPanel>
        <DetailHeader>
          <DetailDate>{format(selectedDate, 'MMMM d, yyyy')}</DetailDate>
          {selectedDayData.totalPnL !== undefined && (
            <DetailPnL $positive={selectedDayData.totalPnL >= 0}>
              {selectedDayData.totalPnL >= 0 ? '+' : ''}${Math.abs(selectedDayData.totalPnL).toFixed(2)}
            </DetailPnL>
          )}
        </DetailHeader>

        <DetailSection>
          <DetailTitle $color="#16a34a">
            <DetailIconContainer>
              <Gauge 
                color="#16a34a"
                $progress={selectedDayData.profitProgress || 0}
                filled={selectedDayData.profitProgress >= ACHIEVEMENT_THRESHOLDS.profit}
              >
                <GaugeTooltip>
                  Profit Progress: {Math.round(selectedDayData.profitProgress)}%
                </GaugeTooltip>
                <ArrowTrendingUpIcon />
              </Gauge>
            </DetailIconContainer>
            <div className="title-content">
              <div className="title-text">
                Profit Goals
                {selectedDayData.profitProgress >= ACHIEVEMENT_THRESHOLDS.profit && <span className="achievement">✨</span>}
              </div>
              <div className="progress">{formatValue(selectedDayData.profitProgress, 'percent')}</div>
            </div>
          </DetailTitle>
          <DetailGrid>
            <DetailRow>
              <DetailLabel>Win Rate</DetailLabel>
              <DetailValue>{formatValue(selectedDayData.winRate, 'percent')} / {formatValue(metricGoals.winRate, 'percent')}</DetailValue>
            </DetailRow>
            <DetailRow>
              <DetailLabel>Profit Factor</DetailLabel>
              <DetailValue>{formatValue(selectedDayData.profitFactor, 'decimal')} / {formatValue(metricGoals.profitFactor, 'decimal')}</DetailValue>
            </DetailRow>
            <DetailRow>
              <DetailLabel>Avg Win/Loss</DetailLabel>
              <DetailValue>{formatValue(selectedDayData.avgWinLoss, 'currency')} / {formatValue(metricGoals.avgWinLoss, 'currency')}</DetailValue>
            </DetailRow>
            <DetailRow>
              <DetailLabel>Total P&L</DetailLabel>
              <DetailValue>{formatValue(selectedDayData.totalPnL, 'currency')}</DetailValue>
            </DetailRow>
          </DetailGrid>
        </DetailSection>

        <DetailSection>
          <DetailTitle $color="#475569">
            <DetailIconContainer>
              <Gauge 
                color="#475569"
                $progress={selectedDayData.guardProgress || 0}
                filled={selectedDayData.guardProgress >= ACHIEVEMENT_THRESHOLDS.guard}
              >
                <GaugeTooltip>
                  Guard Progress: {Math.round(selectedDayData.guardProgress)}%
                </GaugeTooltip>
                <ShieldCheckIcon />
              </Gauge>
            </DetailIconContainer>
            <div className="title-content">
              <div className="title-text">
                Guard Goals
                {selectedDayData.guardProgress >= ACHIEVEMENT_THRESHOLDS.guard && <span className="achievement">✨</span>}
              </div>
              <div className="progress">{formatValue(selectedDayData.guardProgress, 'percent')}</div>
            </div>
          </DetailTitle>
          <DetailGrid>
            <DetailRow>
              <DetailLabel>Max Drawdown</DetailLabel>
              <DetailValue>{formatValue(selectedDayData.maxDrawdown, 'percent')} / {formatValue(metricGoals.maxDrawdown, 'percent')}</DetailValue>
            </DetailRow>
            <DetailRow>
              <DetailLabel>Risk/Reward Ratio</DetailLabel>
              <DetailValue>{formatValue(selectedDayData.riskRewardRatio, 'decimal')} / {formatValue(metricGoals.riskReward, 'decimal')}</DetailValue>
            </DetailRow>
            <DetailRow>
              <DetailLabel>Sharpe Ratio</DetailLabel>
              <DetailValue>{formatValue(selectedDayData.sharpeRatio, 'decimal')} / {formatValue(metricGoals.sharpeRatio, 'decimal')}</DetailValue>
            </DetailRow>
          </DetailGrid>
        </DetailSection>

        <DetailSection>
          <DetailTitle $color="#0ea5e9">
            <DetailIconContainer>
              <Gauge 
                color="#0ea5e9"
                $progress={selectedDayData.focusProgress || 0}
                filled={selectedDayData.focusProgress >= ACHIEVEMENT_THRESHOLDS.focus}
              >
                <GaugeTooltip>
                  Focus Progress: {Math.round(selectedDayData.focusProgress)}%
                </GaugeTooltip>
                <BoltIcon />
              </Gauge>
            </DetailIconContainer>
            <div className="title-content">
              <div className="title-text">
                Focus Goals
                {selectedDayData.focusProgress >= ACHIEVEMENT_THRESHOLDS.focus && <span className="achievement">✨</span>}
              </div>
              <div className="progress">{formatValue(selectedDayData.focusProgress, 'percent')}</div>
            </div>
          </DetailTitle>
          <DetailGrid>
            <DetailRow>
              <DetailLabel>Trading Frequency</DetailLabel>
              <DetailValue>{formatValue(selectedDayData.tradingFrequency, 'decimal')} / {formatValue(metricGoals.tradingFrequency, 'decimal')}</DetailValue>
            </DetailRow>
            <DetailRow>
              <DetailLabel>Avg Trade Duration</DetailLabel>
              <DetailValue>{formatValue(selectedDayData.avgTradeDuration, 'minutes')} / {formatValue(metricGoals.avgDuration, 'minutes')}</DetailValue>
            </DetailRow>
          </DetailGrid>
        </DetailSection>
      </DetailsPanel>
    );
  };

  const renderCalendar = () => {
    return (
      <Calendar>
        {isLoading && (
          <LoadingOverlay>
            <LoadingSpinner />
          </LoadingOverlay>
        )}
        <MonthNavigation>
          <MonthButton onClick={handlePreviousClick}>
            <ChevronLeftIcon />
          </MonthButton>
          <MonthLabel>
            {viewMode === 'week' 
              ? `Week of ${format(startOfWeek(currentDate), 'MMM d, yyyy')}`
              : format(currentDate, 'MMMM yyyy')
            }
          </MonthLabel>
          <MonthButton onClick={handleNextClick}>
            <ChevronRightIcon />
          </MonthButton>
        </MonthNavigation>
        <WeekdayHeader>
          {WEEKDAYS.map(day => (
            <Weekday key={day}>{day}</Weekday>
          ))}
        </WeekdayHeader>
        <DaysGrid>
          {days.map(day => {
            const dateKey = format(day, 'yyyy-MM-dd');
            const dayData = monthData[dateKey];
            const isCurrentDay = isToday(day);
            const isPastOrToday = !isFuture(day);
            const dayIsWeekend = isWeekend(day);
            const dayIsHoliday = isHoliday(day);
            const canShowData = isPastOrToday && !dayIsWeekend && !dayIsHoliday;
            const totalPnL = dayData?.totalPnL ?? 0;
            
            return (
              <Day 
                key={day.toString()}
                $isCurrentMonth={isSameMonth(day, currentDate)}
                $isToday={isCurrentDay}
                $isSelected={selectedDate && format(selectedDate, 'yyyy-MM-dd') === dateKey}
                $isWeekend={dayIsWeekend}
                $isHoliday={dayIsHoliday}
                onClick={() => canShowData && handleDayClick(day)}
                style={{ cursor: canShowData ? 'pointer' : 'default' }}
              >
                <DayContent>
                  <DayNumber $isToday={isCurrentDay} $pnl={totalPnL}>
                    <span className="date">{format(day, 'd')}</span>
                    {canShowData && dayData && (
                      <span className="pnl">
                        {totalPnL > 0 ? '+' : ''}{totalPnL.toLocaleString(undefined, {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2
                        })}
                      </span>
                    )}
                  </DayNumber>
                  {canShowData && dayData && (
                    <DayGaugeContainer>
                      <Gauge 
                        color="#16a34a"
                        $progress={dayData.profitProgress || 0}
                        filled={dayData.profitProgress >= ACHIEVEMENT_THRESHOLDS.profit}
                      >
                        <GaugeTooltip>
                          Profit Progress: {Math.round(dayData.profitProgress)}%
                        </GaugeTooltip>
                        <ArrowTrendingUpIcon />
                      </Gauge>
                      <Gauge 
                        color="#475569"
                        $progress={dayData.guardProgress || 0}
                        filled={dayData.guardProgress >= ACHIEVEMENT_THRESHOLDS.guard}
                      >
                        <GaugeTooltip>
                          Guard Progress: {Math.round(dayData.guardProgress)}%
                        </GaugeTooltip>
                        <ShieldCheckIcon />
                      </Gauge>
                      <Gauge 
                        color="#0ea5e9"
                        $progress={dayData.focusProgress || 0}
                        filled={dayData.focusProgress >= ACHIEVEMENT_THRESHOLDS.focus}
                      >
                        <GaugeTooltip>
                          Focus Progress: {Math.round(dayData.focusProgress)}%
                        </GaugeTooltip>
                        <BoltIcon />
                      </Gauge>
                    </DayGaugeContainer>
                  )}
                </DayContent>
              </Day>
            );
          })}
        </DaysGrid>
      </Calendar>
    );
  };

  const renderMetricIcon = (category, progress) => {
    const { icon: Icon, color } = GOAL_ICONS[category.toLowerCase()];
    const filled = progress >= ACHIEVEMENT_THRESHOLDS[category.toLowerCase()];

    return (
      <Gauge 
        color={color}
        $progress={progress}
        filled={filled}
      >
        <GaugeTooltip>
          {category} Progress: {Math.round(progress)}%
        </GaugeTooltip>
        <Icon />
      </Gauge>
    );
  };

  // Add error display
  if (error) {
    return (
      <PageContainer>
        <Sidebar 
          currentView={currentView}
          onViewChange={handleViewChange}
        />
        <MainContent>
          <Container>
            <div className="text-center py-12">
              <div className="text-red-600 mb-4">{error}</div>
              <button
                onClick={() => {
                  setError(null);
                  loadTrades(true);
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Retry
              </button>
            </div>
          </Container>
        </MainContent>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <Sidebar 
        currentView={currentView}
        onViewChange={handleViewChange}
      />
      <MainContent>
        <Container>
          <Breadcrumbs 
            items={[
              { label: 'Dashboard', href: '/' },
              { label: 'Goal History', href: '/goal-history' }
            ]} 
          />
          
          <Header>
            <HeaderLeft>
              <Legend>
                <LegendItem>
                  <LegendDot color="#16a34a" />
                  Profit Goals
                </LegendItem>
                <LegendItem>
                  <LegendDot color="#475569" />
                  Guard Goals
                </LegendItem>
                <LegendItem>
                  <LegendDot color="#3b82f6" />
                  Focus Goals
                </LegendItem>
              </Legend>
            </HeaderLeft>
          </Header>

          <ContentWrapper>
            <MainSection>
              {/* Mobile Summary */}
              <MobileSummary>
                {goalAchievements.map((achievement, index) => (
                  <MobileSummaryCard key={index} color={achievement.color}>
                    <div className="icon">
                      {achievement.icon}
                    </div>
                    <div className="content">
                      <div className="value">{achievement.progress}%</div>
                      <div className="label">Avg</div>
                    </div>
                  </MobileSummaryCard>
                ))}
              </MobileSummary>

              {/* Desktop Summary */}
              <Summary>
                {goalAchievements.map((achievement, index) => (
                  <SummaryCard key={index}>
                    <SummaryHeader color={achievement.color}>
                      {achievement.icon}
                      {achievement.title}
                    </SummaryHeader>
                    <SummaryValue>{achievement.progress}%</SummaryValue>
                    <SummaryLabel>Average Achievement</SummaryLabel>
                  </SummaryCard>
                ))}
              </Summary>

              <ContentLayout>
                {renderDetailsPanel()}
                <CalendarContainer>
                  {renderCalendar()}
                </CalendarContainer>
              </ContentLayout>
            </MainSection>

            <SideSection>
              <HeaderRight>
                <ViewToggle>
                  <ViewButton 
                    $active={viewMode === 'week'} 
                    onClick={() => setViewMode('week')}
                  >
                    <ViewColumnsIcon />
                    <span className="hidden sm:inline">Week</span>
                  </ViewButton>
                  <ViewButton 
                    $active={viewMode === 'month'} 
                    onClick={() => setViewMode('month')}
                  >
                    <CalendarIcon />
                    <span className="hidden sm:inline">Month</span>
                  </ViewButton>
                </ViewToggle>
              </HeaderRight>
            </SideSection>
          </ContentWrapper>
        </Container>
      </MainContent>
    </PageContainer>
  );
}

export default GoalHistory; 