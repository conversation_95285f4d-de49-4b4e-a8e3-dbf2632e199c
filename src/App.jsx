import React, { useContext } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { ProfileProvider } from './contexts/ProfileContext';
import Dashboard from './components/Dashboard';
import Profile from './components/Profile';
import Settings from './components/Settings';
import GoalHistory from './components/goal-history/GoalHistory';
import { ImportProvider, ImportContext } from './contexts/ImportContext';
import CSVImport from './components/CSVImport';
import AuthForm from './components/auth/AuthForm';

function ImportModal() {
  const { isImportModalOpen, hideImportModal, handleImportComplete } = useContext(ImportContext);

  if (!isImportModalOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl p-4 sm:p-6 max-w-lg w-full mx-auto" onClick={e => e.stopPropagation()}>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-base sm:text-lg font-semibold text-slate-900">Import Trades</h2>
          <button
            onClick={hideImportModal}
            className="text-slate-400 hover:text-slate-500"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <CSVImport
          onImportComplete={handleImportComplete}
          onClose={hideImportModal}
        />
      </div>
    </div>
  );
}

// Protected routes component
function ProtectedApp() {
  const { user, isAuthenticated } = useAuth();

  if (!isAuthenticated()) {
    return <AuthForm />;
  }

  return (
    <ImportProvider>
      <ProfileProvider>
        <Router>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/profile" element={<Profile />} />
            <Route path="/settings/*" element={<Settings />} />
            <Route path="/goal-history" element={<GoalHistory />} />
          </Routes>
          <ImportModal />
        </Router>
      </ProfileProvider>
    </ImportProvider>
  );
}

function App() {
  return (
    <AuthProvider>
      <ProtectedApp />
    </AuthProvider>
  );
}

export default App; 