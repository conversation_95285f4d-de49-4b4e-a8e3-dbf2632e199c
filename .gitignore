# Dependencies
node_modules/
/.pnp
.pnp.js
yarn.lock
package-lock.json

# Testing
/coverage

# Production
/build
/dist

# Development
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Cache directories
.cache/
.npm/
.eslintcache
.parcel-cache

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# IDE - JetBrains
.idea/
*.iml
*.iws
.idea_modules/

# IDE - WebStorm
.idea/

# macOS
.DS_Store
.AppleDouble
.LSOverride
._*

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Temporary files
*.swp
*.swo
*~

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env*

# Local Netlify folder
.netlify 