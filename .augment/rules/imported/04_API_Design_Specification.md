---
type: "manual"
---

# 🔌 TRADEDGE: API Design Specification

**Version:** 1.0  
**Last Updated:** January 2025  
**Base URL:** `https://api.tradedge.com/v1`  

## 📋 Overview

This document defines the RESTful API specification for TRADEDGE, including authentication, data models, endpoints, and integration guidelines. The API follows REST principles with JSON payloads and standard HTTP status codes.

---

## 🔐 Authentication

### **JWT Token Authentication**
All protected endpoints require a Bearer token in the Authorization header.

```http
Authorization: Bearer <access_token>
```

### **Token Management**

#### **Login**
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "firstName": "<PERSON>",
      "lastName": "Doe"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIs...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
      "expiresIn": 900
    }
  }
}
```

#### **Refresh Token**
```http
POST /auth/refresh
Content-Type: application/json

{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}
```

---

## 📊 Data Models

### **Trade Model**
```typescript
interface Trade {
  id: string;
  userId: string;
  symbol: string;
  type: 'LONG' | 'SHORT';
  status: 'OPEN' | 'CLOSED' | 'CANCELLED';
  entryPrice: number;
  exitPrice?: number;
  quantity: number;
  stopLoss?: number;
  takeProfit?: number;
  entryDate: string; // ISO 8601
  exitDate?: string; // ISO 8601
  fees: number;
  pnl?: number;
  pnlPercentage?: number;
  notes?: string;
  tags: string[];
  screenshots: string[];
  createdAt: string;
  updatedAt: string;
}
```

### **User Model**
```typescript
interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  timezone: string;
  currency: string;
  preferences: UserPreferences;
  createdAt: string;
  updatedAt: string;
}

interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  notifications: NotificationSettings;
  trading: TradingSettings;
}
```

### **Analytics Model**
```typescript
interface PerformanceMetrics {
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  winRate: number;
  totalPnL: number;
  totalPnLPercentage: number;
  averageWin: number;
  averageLoss: number;
  profitFactor: number;
  sharpeRatio: number;
  maxDrawdown: number;
  expectancy: number;
  largestWin: number;
  largestLoss: number;
  averageHoldTime: number;
  period: {
    startDate: string;
    endDate: string;
  };
}
```

---

## 🛣️ API Endpoints

### **Authentication Endpoints**

#### **POST /auth/register**
Register a new user account.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "firstName": "John",
  "lastName": "Doe",
  "timezone": "America/New_York"
}
```

**Response:** `201 Created`
```json
{
  "success": true,
  "data": {
    "user": { /* User object */ },
    "tokens": { /* Token object */ }
  }
}
```

#### **POST /auth/logout**
Logout and invalidate tokens.

**Headers:** `Authorization: Bearer <token>`

**Response:** `200 OK`
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

#### **POST /auth/forgot-password**
Request password reset.

**Request:**
```json
{
  "email": "<EMAIL>"
}
```

---

### **User Management Endpoints**

#### **GET /users/profile**
Get current user profile.

**Headers:** `Authorization: Bearer <token>`

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    /* User object */
  }
}
```

#### **PUT /users/profile**
Update user profile.

**Headers:** `Authorization: Bearer <token>`

**Request:**
```json
{
  "firstName": "John",
  "lastName": "Smith",
  "timezone": "America/Los_Angeles",
  "preferences": {
    "theme": "dark",
    "language": "en"
  }
}
```

---

### **Trade Management Endpoints**

#### **GET /trades**
Get user's trades with filtering and pagination.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 20, max: 100)
- `symbol` (string): Filter by symbol
- `type` (string): Filter by trade type (LONG/SHORT)
- `status` (string): Filter by status (OPEN/CLOSED/CANCELLED)
- `startDate` (string): Filter from date (ISO 8601)
- `endDate` (string): Filter to date (ISO 8601)
- `tags` (string): Comma-separated tags
- `sortBy` (string): Sort field (entryDate, pnl, symbol)
- `sortOrder` (string): Sort order (asc, desc)

**Response:** `200 OK`
```json
{
  "success": true,
  "data": [
    {
      /* Trade objects */
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8,
    "hasNext": true,
    "hasPrev": false
  }
}
```

#### **POST /trades**
Create a new trade.

**Headers:** `Authorization: Bearer <token>`

**Request:**
```json
{
  "symbol": "AAPL",
  "type": "LONG",
  "entryPrice": 150.25,
  "quantity": 100,
  "stopLoss": 145.00,
  "takeProfit": 160.00,
  "entryDate": "2025-01-15T14:30:00Z",
  "notes": "Bullish breakout pattern",
  "tags": ["tech", "breakout"]
}
```

**Response:** `201 Created`
```json
{
  "success": true,
  "data": {
    /* Complete Trade object */
  }
}
```

#### **GET /trades/:id**
Get specific trade by ID.

**Headers:** `Authorization: Bearer <token>`

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    /* Trade object */
  }
}
```

#### **PUT /trades/:id**
Update existing trade.

**Headers:** `Authorization: Bearer <token>`

**Request:**
```json
{
  "exitPrice": 155.75,
  "exitDate": "2025-01-16T10:15:00Z",
  "status": "CLOSED",
  "notes": "Target reached"
}
```

#### **DELETE /trades/:id**
Delete a trade.

**Headers:** `Authorization: Bearer <token>`

**Response:** `204 No Content`

---

### **Analytics Endpoints**

#### **GET /analytics/performance**
Get performance metrics for specified period.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `startDate` (string): Start date (ISO 8601)
- `endDate` (string): End date (ISO 8601)
- `symbol` (string): Filter by symbol
- `tags` (string): Comma-separated tags

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    /* PerformanceMetrics object */
  }
}
```

#### **GET /analytics/equity-curve**
Get equity curve data points.

**Query Parameters:**
- `startDate` (string): Start date
- `endDate` (string): End date
- `interval` (string): Data interval (daily, weekly, monthly)

**Response:** `200 OK`
```json
{
  "success": true,
  "data": [
    {
      "date": "2025-01-15",
      "equity": 10500.00,
      "drawdown": -2.5,
      "trades": 3
    }
  ]
}
```

#### **GET /analytics/monthly-summary**
Get monthly performance summary.

**Query Parameters:**
- `year` (number): Year (default: current year)

**Response:** `200 OK`
```json
{
  "success": true,
  "data": [
    {
      "month": "2025-01",
      "trades": 25,
      "pnl": 1250.00,
      "winRate": 68.0,
      "bestTrade": 450.00,
      "worstTrade": -125.00
    }
  ]
}
```

---

### **Import/Export Endpoints**

#### **POST /import/csv**
Import trades from CSV file.

**Headers:** 
- `Authorization: Bearer <token>`
- `Content-Type: multipart/form-data`

**Request:**
```
file: <CSV file>
broker: "interactive_brokers" | "td_ameritrade" | "robinhood" | "custom"
mapping: {
  "symbol": "Symbol",
  "entryPrice": "Entry Price",
  "quantity": "Quantity"
}
```

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "imported": 45,
    "skipped": 2,
    "errors": [
      {
        "row": 15,
        "error": "Invalid symbol format"
      }
    ]
  }
}
```

#### **GET /export/csv**
Export trades to CSV.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `startDate` (string): Start date
- `endDate` (string): End date
- `format` (string): Export format (csv, xlsx, json)

**Response:** `200 OK`
```
Content-Type: text/csv
Content-Disposition: attachment; filename="trades_2025.csv"

Symbol,Type,Entry Price,Exit Price,Quantity,P&L
AAPL,LONG,150.25,155.75,100,550.00
```

---

### **Goal Management Endpoints**

#### **GET /goals**
Get user's trading goals.

**Response:** `200 OK`
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "title": "Monthly Profit Target",
      "type": "profit",
      "target": 5000.00,
      "current": 3250.00,
      "period": "monthly",
      "startDate": "2025-01-01",
      "endDate": "2025-01-31",
      "status": "active"
    }
  ]
}
```

#### **POST /goals**
Create a new goal.

**Request:**
```json
{
  "title": "Improve Win Rate",
  "type": "win_rate",
  "target": 70.0,
  "period": "quarterly",
  "startDate": "2025-01-01",
  "endDate": "2025-03-31"
}
```

---

## 🚨 Error Handling

### **Standard Error Response**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": [
      {
        "field": "entryPrice",
        "message": "Entry price must be positive",
        "value": -100
      }
    ]
  },
  "timestamp": "2025-01-15T14:30:00Z",
  "requestId": "req_123456789"
}
```

### **HTTP Status Codes**
- `200` - Success
- `201` - Created
- `204` - No Content
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `422` - Unprocessable Entity
- `429` - Too Many Requests
- `500` - Internal Server Error

### **Error Codes**
- `VALIDATION_ERROR` - Input validation failed
- `AUTHENTICATION_ERROR` - Authentication failed
- `AUTHORIZATION_ERROR` - Insufficient permissions
- `NOT_FOUND` - Resource not found
- `DUPLICATE_ENTRY` - Resource already exists
- `RATE_LIMIT_EXCEEDED` - Too many requests
- `INTERNAL_ERROR` - Server error

---

## 🔒 Security Considerations

### **Rate Limiting**
- Authentication endpoints: 5 requests per minute
- General endpoints: 100 requests per minute
- Import endpoints: 10 requests per hour

### **Input Validation**
- All inputs are validated and sanitized
- SQL injection protection via parameterized queries
- XSS protection via output encoding
- File upload restrictions and scanning

### **Data Protection**
- All sensitive data encrypted at rest
- HTTPS required for all communications
- Personal data anonymization in logs
- GDPR compliance for data handling

---

## 📚 Integration Examples

### **JavaScript/React**
```javascript
// API client example
class TradedgeAPI {
  constructor(baseURL, token) {
    this.baseURL = baseURL;
    this.token = token;
  }
  
  async getTrades(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const response = await fetch(`${this.baseURL}/trades?${queryString}`, {
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }
    
    return response.json();
  }
  
  async createTrade(tradeData) {
    const response = await fetch(`${this.baseURL}/trades`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(tradeData)
    });
    
    return response.json();
  }
}
```

### **Flutter/Dart**
```dart
// API service example
class TradedgeApiService {
  final String baseUrl;
  final String token;
  final Dio _dio;
  
  TradedgeApiService(this.baseUrl, this.token) : _dio = Dio() {
    _dio.options.headers['Authorization'] = 'Bearer $token';
    _dio.options.headers['Content-Type'] = 'application/json';
  }
  
  Future<List<Trade>> getTrades({Map<String, dynamic>? params}) async {
    try {
      final response = await _dio.get('/trades', queryParameters: params);
      
      if (response.data['success']) {
        return (response.data['data'] as List)
            .map((json) => Trade.fromJson(json))
            .toList();
      }
      
      throw ApiException(response.data['error']['message']);
    } catch (e) {
      throw ApiException('Failed to fetch trades: $e');
    }
  }
}
```

This API specification provides a comprehensive foundation for building robust integrations with the TRADEDGE platform.
