---
type: "manual"
---

# 📋 TRADEDGE: Comprehensive Coding Standards Guide

**Version:** 1.0  
**Last Updated:** January 2025  
**Maintainer:** Full-Stack Architecture Team  

## 🎯 Overview

This document establishes comprehensive coding standards for the TRADEDGE project across all platforms (React Web, Flutter Mobile, Node.js Backend, and Shared Core). These standards ensure code consistency, maintainability, and scalability across the entire codebase.

---

## 🏗️ Project Architecture Standards

### **Shared Core Package (`trade_reviewer_core`)**

#### Structure
```
trade_reviewer_core/
├── src/                           # TypeScript source
│   ├── models/                    # Shared data models
│   ├── services/                  # Business logic services
│   ├── utils/                     # Shared utilities
│   ├── interfaces/                # Core interfaces
│   ├── constants/                 # Shared constants
│   └── validators/                # Shared validation logic
├── flutter_bridge/               # Flutter compatibility layer
├── tests/                        # Core package tests
└── docs/                         # Core package documentation
```

#### Naming Conventions
- **Files**: `kebab-case.ts` (e.g., `trade-service.ts`)
- **Classes**: `PascalCase` (e.g., `TradeService`)
- **Interfaces**: `PascalCase` with `I` prefix (e.g., `ITradeRepository`)
- **Types**: `PascalCase` with `T` prefix (e.g., `TTradeStatus`)
- **Constants**: `SCREAMING_SNAKE_CASE` (e.g., `MAX_TRADE_SIZE`)

#### Code Standards
```typescript
// ✅ Good: Proper interface definition
interface ITradeService {
  calculatePnL(trade: Trade): Promise<number>;
  validateTrade(trade: Partial<Trade>): ValidationResult;
}

// ✅ Good: Service implementation
export class TradeService implements ITradeService {
  private readonly repository: ITradeRepository;
  
  constructor(repository: ITradeRepository) {
    this.repository = repository;
  }
  
  async calculatePnL(trade: Trade): Promise<number> {
    if (!this.isValidTrade(trade)) {
      throw new ValidationError('Invalid trade data');
    }
    
    return this.performCalculation(trade);
  }
  
  private isValidTrade(trade: Trade): boolean {
    return trade.entryPrice > 0 && trade.quantity > 0;
  }
}
```

---

## ⚛️ React Web Application Standards

### **Project Structure**
```
src/
├── components/                    # Reusable UI components
│   ├── common/                   # Shared components
│   ├── forms/                    # Form components
│   └── charts/                   # Chart components
├── features/                     # Feature-based modules
│   └── [feature]/
│       ├── components/           # Feature-specific components
│       ├── hooks/               # Feature-specific hooks
│       ├── services/            # Feature-specific services
│       └── types/               # Feature-specific types
├── hooks/                        # Global custom hooks
├── services/                     # API and business logic services
├── stores/                       # State management (Zustand)
├── utils/                        # Utility functions
├── types/                        # TypeScript type definitions
└── constants/                    # Application constants
```

### **Component Standards**

#### Functional Components with TypeScript
```typescript
// ✅ Good: Proper component structure
interface TradeCardProps {
  trade: Trade;
  onEdit?: (trade: Trade) => void;
  onDelete?: (tradeId: string) => void;
  className?: string;
}

export const TradeCard: React.FC<TradeCardProps> = ({
  trade,
  onEdit,
  onDelete,
  className = ''
}) => {
  const [isLoading, setIsLoading] = useState(false);
  
  const handleEdit = useCallback(() => {
    onEdit?.(trade);
  }, [trade, onEdit]);
  
  const handleDelete = useCallback(async () => {
    setIsLoading(true);
    try {
      await onDelete?.(trade.id);
    } catch (error) {
      console.error('Failed to delete trade:', error);
    } finally {
      setIsLoading(false);
    }
  }, [trade.id, onDelete]);
  
  return (
    <div className={`trade-card ${className}`}>
      {/* Component content */}
    </div>
  );
};
```

#### Custom Hooks Standards
```typescript
// ✅ Good: Custom hook with proper error handling
export const useTradeData = (tradeId: string) => {
  const [trade, setTrade] = useState<Trade | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    let isMounted = true;
    
    const fetchTrade = async () => {
      try {
        setLoading(true);
        setError(null);
        const tradeData = await tradeService.getTrade(tradeId);
        
        if (isMounted) {
          setTrade(tradeData);
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err.message : 'Unknown error');
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };
    
    fetchTrade();
    
    return () => {
      isMounted = false;
    };
  }, [tradeId]);
  
  return { trade, loading, error };
};
```

### **State Management Standards**

#### Zustand Store Pattern
```typescript
// ✅ Good: Zustand store with TypeScript
interface TradeStore {
  trades: Trade[];
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchTrades: () => Promise<void>;
  addTrade: (trade: Trade) => Promise<void>;
  updateTrade: (id: string, updates: Partial<Trade>) => Promise<void>;
  deleteTrade: (id: string) => Promise<void>;
  clearError: () => void;
}

export const useTradeStore = create<TradeStore>((set, get) => ({
  trades: [],
  loading: false,
  error: null,
  
  fetchTrades: async () => {
    set({ loading: true, error: null });
    try {
      const trades = await tradeService.getAllTrades();
      set({ trades, loading: false });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch trades',
        loading: false 
      });
    }
  },
  
  addTrade: async (trade: Trade) => {
    try {
      const newTrade = await tradeService.createTrade(trade);
      set(state => ({ 
        trades: [...state.trades, newTrade] 
      }));
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to add trade' 
      });
      throw error;
    }
  },
  
  clearError: () => set({ error: null })
}));
```

---

## 📱 Flutter Mobile Application Standards

### **Project Structure**
```
lib/
├── core/                         # Core functionality
│   ├── config/                  # App configuration
│   ├── constants/               # App constants
│   ├── theme/                   # Theme and styling
│   ├── utils/                   # Utility functions
│   └── widgets/                 # Core widgets
├── features/                     # Feature modules
│   └── [feature]/
│       ├── data/               # Data layer (repositories, data sources)
│       ├── domain/             # Domain layer (entities, use cases)
│       └── presentation/       # Presentation layer (screens, widgets)
├── shared/                       # Shared components and utilities
│   ├── widgets/                # Shared widgets
│   ├── models/                 # Shared models
│   └── services/               # Shared services
└── main.dart                     # App entry point
```

### **Widget Standards**

#### StatelessWidget Pattern
```dart
// ✅ Good: Proper widget structure
class TradeCard extends StatelessWidget {
  final Trade trade;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  
  const TradeCard({
    Key? key,
    required this.trade,
    this.onTap,
    this.onEdit,
    this.onDelete,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: AppTheme.elevationSM,
      margin: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingMD,
        vertical: AppTheme.spacingSM,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingMD),
          child: _buildContent(context),
        ),
      ),
    );
  }
  
  Widget _buildContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(context),
        const SizedBox(height: AppTheme.spacingSM),
        _buildMetrics(context),
        if (onEdit != null || onDelete != null) ...[
          const SizedBox(height: AppTheme.spacingSM),
          _buildActions(context),
        ],
      ],
    );
  }
  
  Widget _buildHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          trade.symbol,
          style: AppTextStyles.h6,
        ),
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppTheme.spacingSM,
            vertical: AppTheme.spacingXS,
          ),
          decoration: BoxDecoration(
            color: trade.pnl >= 0 
              ? AppColors.successGreen.withOpacity(0.1)
              : AppColors.dangerRed.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusSM),
          ),
          child: Text(
            trade.pnl >= 0 ? 'PROFIT' : 'LOSS',
            style: AppTextStyles.caption.copyWith(
              color: trade.pnl >= 0 
                ? AppColors.successGreen
                : AppColors.dangerRed,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}
```

### **Riverpod State Management Standards**

#### Provider Pattern
```dart
// ✅ Good: Riverpod provider structure
@riverpod
class TradeNotifier extends _$TradeNotifier {
  @override
  Future<List<Trade>> build() async {
    return _fetchTrades();
  }
  
  Future<void> addTrade(Trade trade) async {
    state = const AsyncValue.loading();
    
    try {
      final tradeService = ref.read(tradeServiceProvider);
      final newTrade = await tradeService.createTrade(trade);
      
      final currentTrades = await future;
      state = AsyncValue.data([...currentTrades, newTrade]);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
  
  Future<void> updateTrade(String id, Trade updatedTrade) async {
    try {
      final tradeService = ref.read(tradeServiceProvider);
      await tradeService.updateTrade(id, updatedTrade);
      
      final currentTrades = await future;
      final updatedTrades = currentTrades.map((trade) {
        return trade.id == id ? updatedTrade : trade;
      }).toList();
      
      state = AsyncValue.data(updatedTrades);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
  
  Future<List<Trade>> _fetchTrades() async {
    final tradeService = ref.read(tradeServiceProvider);
    return tradeService.getAllTrades();
  }
}
```

---

## 🔧 Backend API Standards

### **Project Structure**
```
server/
├── src/
│   ├── controllers/              # Route controllers
│   ├── middleware/               # Custom middleware
│   ├── models/                   # Database models
│   ├── routes/                   # API routes
│   ├── services/                 # Business logic services
│   ├── utils/                    # Utility functions
│   ├── validators/               # Request validators
│   └── config/                   # Configuration files
├── tests/                        # Test files
└── docs/                         # API documentation
```

### **Controller Standards**

#### Express Controller Pattern
```typescript
// ✅ Good: Controller with proper error handling
export class TradeController {
  private tradeService: TradeService;
  
  constructor(tradeService: TradeService) {
    this.tradeService = tradeService;
  }
  
  createTrade = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({ 
          error: 'Authentication required' 
        });
      }
      
      const validationResult = validateTradeInput(req.body);
      if (!validationResult.isValid) {
        return res.status(400).json({ 
          error: 'Validation failed',
          details: validationResult.errors 
        });
      }
      
      const trade = await this.tradeService.createTrade({
        ...req.body,
        userId
      });
      
      res.status(201).json({
        success: true,
        data: trade
      });
    } catch (error) {
      next(error);
    }
  };
  
  getTrades = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id;
      const { page = 1, limit = 20, startDate, endDate } = req.query;
      
      const trades = await this.tradeService.getTrades(userId, {
        page: Number(page),
        limit: Number(limit),
        startDate: startDate as string,
        endDate: endDate as string
      });
      
      res.json({
        success: true,
        data: trades.data,
        pagination: {
          page: trades.page,
          limit: trades.limit,
          total: trades.total,
          totalPages: trades.totalPages
        }
      });
    } catch (error) {
      next(error);
    }
  };
}
```

### **Validation Standards**

#### Joi Validation Pattern
```typescript
// ✅ Good: Comprehensive validation
import Joi from 'joi';

export const tradeValidationSchema = Joi.object({
  symbol: Joi.string()
    .required()
    .min(1)
    .max(10)
    .pattern(/^[A-Z0-9]+$/)
    .messages({
      'string.pattern.base': 'Symbol must contain only uppercase letters and numbers'
    }),
    
  type: Joi.string()
    .valid('LONG', 'SHORT')
    .required(),
    
  entryPrice: Joi.number()
    .positive()
    .precision(4)
    .required(),
    
  exitPrice: Joi.number()
    .positive()
    .precision(4)
    .optional(),
    
  quantity: Joi.number()
    .positive()
    .integer()
    .required(),
    
  stopLoss: Joi.number()
    .positive()
    .precision(4)
    .optional(),
    
  takeProfit: Joi.number()
    .positive()
    .precision(4)
    .optional(),
    
  entryDate: Joi.date()
    .iso()
    .required(),
    
  exitDate: Joi.date()
    .iso()
    .optional()
    .when('exitPrice', {
      is: Joi.exist(),
      then: Joi.required(),
      otherwise: Joi.optional()
    }),
    
  notes: Joi.string()
    .max(1000)
    .optional(),
    
  tags: Joi.array()
    .items(Joi.string().max(50))
    .max(10)
    .optional()
});

export const validateTradeInput = (data: any): ValidationResult => {
  const { error, value } = tradeValidationSchema.validate(data, {
    abortEarly: false,
    stripUnknown: true
  });
  
  if (error) {
    return {
      isValid: false,
      errors: error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }))
    };
  }
  
  return {
    isValid: true,
    data: value
  };
};
```

---

## 🧪 Testing Standards

### **Unit Testing**
```typescript
// ✅ Good: Comprehensive unit test
describe('TradeService', () => {
  let tradeService: TradeService;
  let mockRepository: jest.Mocked<ITradeRepository>;
  
  beforeEach(() => {
    mockRepository = {
      create: jest.fn(),
      findById: jest.fn(),
      findByUserId: jest.fn(),
      update: jest.fn(),
      delete: jest.fn()
    };
    
    tradeService = new TradeService(mockRepository);
  });
  
  describe('calculatePnL', () => {
    it('should calculate profit for long position', async () => {
      const trade: Trade = {
        id: '1',
        type: TradeType.LONG,
        entryPrice: 100,
        exitPrice: 110,
        quantity: 10,
        fees: 2
      };
      
      const result = await tradeService.calculatePnL(trade);
      
      expect(result).toBe(98); // (110-100)*10 - 2
    });
    
    it('should throw error for invalid trade data', async () => {
      const invalidTrade = {
        entryPrice: -100,
        quantity: 0
      } as Trade;
      
      await expect(tradeService.calculatePnL(invalidTrade))
        .rejects
        .toThrow('Invalid trade data');
    });
  });
});
```

---

## 📝 Documentation Standards

### **Code Documentation**
```typescript
/**
 * Service for managing trading operations and calculations
 * 
 * @example
 * ```typescript
 * const tradeService = new TradeService(repository);
 * const pnl = await tradeService.calculatePnL(trade);
 * ```
 */
export class TradeService {
  /**
   * Calculates profit and loss for a given trade
   * 
   * @param trade - The trade object containing entry/exit prices and quantity
   * @returns Promise resolving to the calculated P&L amount
   * @throws {ValidationError} When trade data is invalid
   * @throws {CalculationError} When calculation fails
   */
  async calculatePnL(trade: Trade): Promise<number> {
    // Implementation
  }
}
```

### **API Documentation**
```yaml
# ✅ Good: OpenAPI specification
/api/trades:
  post:
    summary: Create a new trade
    description: Creates a new trade record for the authenticated user
    tags:
      - Trades
    security:
      - bearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/CreateTradeRequest'
    responses:
      201:
        description: Trade created successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TradeResponse'
      400:
        description: Invalid input data
      401:
        description: Authentication required
      500:
        description: Internal server error
```

---

## 🔒 Security Standards

### **Input Validation**
- Always validate and sanitize user input
- Use parameterized queries for database operations
- Implement rate limiting on API endpoints
- Use HTTPS for all communications

### **Authentication & Authorization**
```typescript
// ✅ Good: Secure authentication middleware
export const authenticateToken = async (
  req: Request, 
  res: Response, 
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader?.split(' ')[1];
    
    if (!token) {
      return res.status(401).json({ error: 'Access token required' });
    }
    
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as JwtPayload;
    const user = await User.findById(decoded.userId);
    
    if (!user) {
      return res.status(401).json({ error: 'Invalid token' });
    }
    
    req.user = user;
    next();
  } catch (error) {
    return res.status(403).json({ error: 'Invalid or expired token' });
  }
};
```

---

## 📊 Performance Standards

### **React Performance**
- Use `React.memo` for expensive components
- Implement proper dependency arrays in hooks
- Use `useMemo` and `useCallback` appropriately
- Implement code splitting with `React.lazy`

### **Flutter Performance**
- Use `const` constructors where possible
- Implement proper `ListView.builder` for large lists
- Use `RepaintBoundary` for complex widgets
- Optimize image loading and caching

### **Backend Performance**
- Implement database indexing
- Use connection pooling
- Implement caching strategies
- Monitor and optimize query performance

---

## 🚀 Deployment Standards

### **Environment Configuration**
```typescript
// ✅ Good: Environment configuration
export const config = {
  port: process.env.PORT || 3000,
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    name: process.env.DB_NAME || 'tradedge',
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || ''
  },
  jwt: {
    secret: process.env.JWT_SECRET || (() => {
      throw new Error('JWT_SECRET environment variable is required');
    })(),
    expiresIn: process.env.JWT_EXPIRES_IN || '24h'
  },
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379'
  }
};
```

This coding standards guide should be followed by all team members and regularly updated as the project evolves.
