---
type: "manual"
---

# 🧪 TRADEDGE: Comprehensive Testing Strategy

**Version:** 1.0  
**Last Updated:** January 2025  
**Coverage Target:** 80%+ for critical business logic  

## 📋 Overview

This document outlines the comprehensive testing strategy for TRADEDGE across all platforms and components. The strategy emphasizes quality assurance, automated testing, and continuous integration to ensure reliable, bug-free software delivery.

---

## 🎯 Testing Objectives

### **Primary Goals**
- **Quality Assurance**: Ensure all features work as intended
- **Regression Prevention**: Catch breaking changes early
- **Performance Validation**: Maintain acceptable performance standards
- **Security Verification**: Validate security implementations
- **Cross-Platform Consistency**: Ensure uniform behavior across platforms

### **Success Metrics**
- **Code Coverage**: >80% for business logic, >60% overall
- **Bug Detection**: 95% of bugs caught before production
- **Performance**: All tests complete in <10 minutes
- **Reliability**: <1% flaky test rate
- **Automation**: 90% of tests automated

---

## 🏗️ Testing Pyramid Structure

### **Unit Tests (70%)**
- **Scope**: Individual functions, methods, and components
- **Speed**: Fast (<1ms per test)
- **Coverage**: Business logic, utilities, data transformations
- **Tools**: Jest (React), Dart Test (Flutter), Mocha/Jest (Node.js)

### **Integration Tests (20%)**
- **Scope**: Component interactions, API endpoints, database operations
- **Speed**: Medium (1-100ms per test)
- **Coverage**: Service integrations, data flow, API contracts
- **Tools**: Supertest (API), Flutter Integration Tests, React Testing Library

### **End-to-End Tests (10%)**
- **Scope**: Complete user workflows, cross-platform scenarios
- **Speed**: Slow (1-30s per test)
- **Coverage**: Critical user journeys, business workflows
- **Tools**: Playwright (Web), Flutter Driver (Mobile), Cypress

---

## ⚛️ React Web Application Testing

### **Unit Testing Standards**

#### **Component Testing**
```javascript
// TradeCard.test.jsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { TradeCard } from '../TradeCard';
import { mockTrade } from '../../__mocks__/trades';

describe('TradeCard', () => {
  const defaultProps = {
    trade: mockTrade,
    onEdit: jest.fn(),
    onDelete: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders trade information correctly', () => {
    render(<TradeCard {...defaultProps} />);
    
    expect(screen.getByText(mockTrade.symbol)).toBeInTheDocument();
    expect(screen.getByText(`$${mockTrade.pnl}`)).toBeInTheDocument();
    expect(screen.getByText(mockTrade.type)).toBeInTheDocument();
  });

  it('calls onEdit when edit button is clicked', async () => {
    render(<TradeCard {...defaultProps} />);
    
    const editButton = screen.getByRole('button', { name: /edit/i });
    fireEvent.click(editButton);
    
    await waitFor(() => {
      expect(defaultProps.onEdit).toHaveBeenCalledWith(mockTrade);
    });
  });

  it('shows loading state during delete operation', async () => {
    const mockDelete = jest.fn().mockImplementation(
      () => new Promise(resolve => setTimeout(resolve, 100))
    );
    
    render(<TradeCard {...defaultProps} onDelete={mockDelete} />);
    
    const deleteButton = screen.getByRole('button', { name: /delete/i });
    fireEvent.click(deleteButton);
    
    expect(screen.getByText(/deleting/i)).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.queryByText(/deleting/i)).not.toBeInTheDocument();
    });
  });

  it('handles error states gracefully', async () => {
    const mockDelete = jest.fn().mockRejectedValue(new Error('Delete failed'));
    
    render(<TradeCard {...defaultProps} onDelete={mockDelete} />);
    
    const deleteButton = screen.getByRole('button', { name: /delete/i });
    fireEvent.click(deleteButton);
    
    await waitFor(() => {
      expect(screen.getByText(/error/i)).toBeInTheDocument();
    });
  });
});
```

#### **Custom Hook Testing**
```javascript
// useTradeData.test.js
import { renderHook, waitFor } from '@testing-library/react';
import { useTradeData } from '../useTradeData';
import * as tradeService from '../../services/tradeService';

jest.mock('../../services/tradeService');

describe('useTradeData', () => {
  const mockTrade = { id: '1', symbol: 'AAPL', pnl: 100 };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('fetches trade data successfully', async () => {
    tradeService.getTrade.mockResolvedValue(mockTrade);
    
    const { result } = renderHook(() => useTradeData('1'));
    
    expect(result.current.loading).toBe(true);
    expect(result.current.trade).toBe(null);
    expect(result.current.error).toBe(null);
    
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(result.current.trade).toEqual(mockTrade);
      expect(result.current.error).toBe(null);
    });
  });

  it('handles fetch errors', async () => {
    const errorMessage = 'Failed to fetch trade';
    tradeService.getTrade.mockRejectedValue(new Error(errorMessage));
    
    const { result } = renderHook(() => useTradeData('1'));
    
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(result.current.trade).toBe(null);
      expect(result.current.error).toBe(errorMessage);
    });
  });

  it('cleans up on unmount', () => {
    const { unmount } = renderHook(() => useTradeData('1'));
    
    // Verify cleanup logic
    unmount();
    
    // Should not update state after unmount
    expect(tradeService.getTrade).toHaveBeenCalledTimes(1);
  });
});
```

### **Integration Testing**

#### **API Integration Tests**
```javascript
// tradeService.integration.test.js
import { tradeService } from '../tradeService';
import { setupTestServer } from '../../__mocks__/server';

const server = setupTestServer();

describe('TradeService Integration', () => {
  beforeAll(() => server.listen());
  afterEach(() => server.resetHandlers());
  afterAll(() => server.close());

  it('creates trade successfully', async () => {
    const tradeData = {
      symbol: 'AAPL',
      type: 'LONG',
      entryPrice: 150.00,
      quantity: 100
    };

    const result = await tradeService.createTrade(tradeData);

    expect(result).toMatchObject({
      id: expect.any(String),
      ...tradeData,
      status: 'OPEN',
      createdAt: expect.any(String)
    });
  });

  it('handles validation errors', async () => {
    const invalidTradeData = {
      symbol: '',
      entryPrice: -100
    };

    await expect(tradeService.createTrade(invalidTradeData))
      .rejects
      .toThrow('Validation failed');
  });

  it('handles network errors', async () => {
    server.use(
      rest.post('/api/trades', (req, res, ctx) => {
        return res.networkError('Network error');
      })
    );

    await expect(tradeService.createTrade({}))
      .rejects
      .toThrow('Network error');
  });
});
```

---

## 📱 Flutter Mobile Application Testing

### **Unit Testing Standards**

#### **Widget Testing**
```dart
// trade_card_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:tradedge_flutter/features/trades/presentation/widgets/trade_card.dart';
import 'package:tradedge_flutter/features/trades/domain/entities/trade.dart';

void main() {
  group('TradeCard Widget Tests', () {
    late Trade mockTrade;

    setUp(() {
      mockTrade = Trade(
        id: '1',
        symbol: 'AAPL',
        type: TradeType.long,
        entryPrice: 150.00,
        quantity: 100,
        pnl: 500.00,
        status: TradeStatus.closed,
      );
    });

    testWidgets('displays trade information correctly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TradeCard(trade: mockTrade),
          ),
        ),
      );

      expect(find.text('AAPL'), findsOneWidget);
      expect(find.text('\$500.00'), findsOneWidget);
      expect(find.text('LONG'), findsOneWidget);
    });

    testWidgets('shows profit indicator for positive P&L', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TradeCard(trade: mockTrade),
          ),
        ),
      );

      final profitIndicator = find.byKey(const Key('profit_indicator'));
      expect(profitIndicator, findsOneWidget);

      final widget = tester.widget<Container>(profitIndicator);
      expect(widget.decoration, isA<BoxDecoration>());
    });

    testWidgets('handles tap events', (tester) async {
      bool tapped = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TradeCard(
              trade: mockTrade,
              onTap: () => tapped = true,
            ),
          ),
        ),
      );

      await tester.tap(find.byType(TradeCard));
      await tester.pumpAndSettle();

      expect(tapped, isTrue);
    });
  });
}
```

#### **Provider Testing**
```dart
// trade_notifier_test.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tradedge_flutter/features/trades/presentation/providers/trade_notifier.dart';
import 'package:tradedge_flutter/features/trades/domain/repositories/trade_repository.dart';

class MockTradeRepository extends Mock implements TradeRepository {}

void main() {
  group('TradeNotifier Tests', () {
    late MockTradeRepository mockRepository;
    late ProviderContainer container;

    setUp(() {
      mockRepository = MockTradeRepository();
      container = ProviderContainer(
        overrides: [
          tradeRepositoryProvider.overrideWithValue(mockRepository),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    test('initial state is loading', () {
      final notifier = container.read(tradeNotifierProvider.notifier);
      final state = container.read(tradeNotifierProvider);

      expect(state, const AsyncValue<List<Trade>>.loading());
    });

    test('loads trades successfully', () async {
      final mockTrades = [
        Trade(id: '1', symbol: 'AAPL', type: TradeType.long),
        Trade(id: '2', symbol: 'GOOGL', type: TradeType.short),
      ];

      when(() => mockRepository.getAllTrades())
          .thenAnswer((_) async => mockTrades);

      final notifier = container.read(tradeNotifierProvider.notifier);
      await container.read(tradeNotifierProvider.future);

      final state = container.read(tradeNotifierProvider);
      expect(state.value, equals(mockTrades));
    });

    test('handles errors gracefully', () async {
      when(() => mockRepository.getAllTrades())
          .thenThrow(Exception('Network error'));

      final notifier = container.read(tradeNotifierProvider.notifier);
      
      try {
        await container.read(tradeNotifierProvider.future);
      } catch (e) {
        // Expected to throw
      }

      final state = container.read(tradeNotifierProvider);
      expect(state.hasError, isTrue);
      expect(state.error.toString(), contains('Network error'));
    });

    test('adds trade successfully', () async {
      final newTrade = Trade(id: '3', symbol: 'TSLA', type: TradeType.long);
      final existingTrades = [
        Trade(id: '1', symbol: 'AAPL', type: TradeType.long),
      ];

      when(() => mockRepository.getAllTrades())
          .thenAnswer((_) async => existingTrades);
      when(() => mockRepository.createTrade(any()))
          .thenAnswer((_) async => newTrade);

      final notifier = container.read(tradeNotifierProvider.notifier);
      
      // Load initial trades
      await container.read(tradeNotifierProvider.future);
      
      // Add new trade
      await notifier.addTrade(newTrade);

      final state = container.read(tradeNotifierProvider);
      expect(state.value?.length, equals(2));
      expect(state.value?.last, equals(newTrade));
    });
  });
}
```

### **Integration Testing**

#### **Feature Integration Tests**
```dart
// trade_flow_integration_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:tradedge_flutter/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Trade Management Flow', () {
    testWidgets('complete trade creation flow', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to add trade screen
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // Fill trade form
      await tester.enterText(find.byKey(const Key('symbol_field')), 'AAPL');
      await tester.enterText(find.byKey(const Key('entry_price_field')), '150.00');
      await tester.enterText(find.byKey(const Key('quantity_field')), '100');

      // Select trade type
      await tester.tap(find.byKey(const Key('trade_type_long')));
      await tester.pumpAndSettle();

      // Submit form
      await tester.tap(find.byKey(const Key('submit_button')));
      await tester.pumpAndSettle();

      // Verify trade was created
      expect(find.text('AAPL'), findsOneWidget);
      expect(find.text('\$150.00'), findsOneWidget);
      expect(find.text('LONG'), findsOneWidget);
    });

    testWidgets('trade editing flow', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Assume trade exists, tap to edit
      await tester.tap(find.byKey(const Key('trade_card_0')));
      await tester.pumpAndSettle();

      await tester.tap(find.byIcon(Icons.edit));
      await tester.pumpAndSettle();

      // Update exit price
      await tester.enterText(find.byKey(const Key('exit_price_field')), '155.00');
      
      // Save changes
      await tester.tap(find.byKey(const Key('save_button')));
      await tester.pumpAndSettle();

      // Verify changes
      expect(find.text('\$155.00'), findsOneWidget);
      expect(find.text('CLOSED'), findsOneWidget);
    });
  });
}
```

---

## 🔧 Backend API Testing

### **Unit Testing Standards**

#### **Service Layer Testing**
```javascript
// tradeService.test.js
import { TradeService } from '../tradeService';
import { mockTradeRepository } from '../../__mocks__/repositories';

describe('TradeService', () => {
  let tradeService;
  let mockRepository;

  beforeEach(() => {
    mockRepository = mockTradeRepository();
    tradeService = new TradeService(mockRepository);
  });

  describe('calculatePnL', () => {
    it('calculates profit for long position correctly', async () => {
      const trade = {
        type: 'LONG',
        entryPrice: 100,
        exitPrice: 110,
        quantity: 10,
        fees: 2
      };

      const result = await tradeService.calculatePnL(trade);

      expect(result).toBe(98); // (110-100)*10 - 2
    });

    it('calculates loss for short position correctly', async () => {
      const trade = {
        type: 'SHORT',
        entryPrice: 100,
        exitPrice: 110,
        quantity: 10,
        fees: 2
      };

      const result = await tradeService.calculatePnL(trade);

      expect(result).toBe(-102); // (100-110)*10 - 2
    });

    it('throws error for invalid trade data', async () => {
      const invalidTrade = {
        entryPrice: -100,
        quantity: 0
      };

      await expect(tradeService.calculatePnL(invalidTrade))
        .rejects
        .toThrow('Invalid trade data');
    });
  });

  describe('createTrade', () => {
    it('creates trade with valid data', async () => {
      const tradeData = {
        symbol: 'AAPL',
        type: 'LONG',
        entryPrice: 150.00,
        quantity: 100
      };

      mockRepository.create.mockResolvedValue({
        id: '1',
        ...tradeData,
        status: 'OPEN'
      });

      const result = await tradeService.createTrade('user1', tradeData);

      expect(result).toMatchObject({
        id: '1',
        ...tradeData,
        status: 'OPEN'
      });
      expect(mockRepository.create).toHaveBeenCalledWith({
        userId: 'user1',
        ...tradeData
      });
    });

    it('validates required fields', async () => {
      const invalidData = {
        symbol: '',
        entryPrice: -100
      };

      await expect(tradeService.createTrade('user1', invalidData))
        .rejects
        .toThrow('Validation failed');
    });
  });
});
```

#### **Controller Testing**
```javascript
// tradeController.test.js
import request from 'supertest';
import { app } from '../app';
import { TradeService } from '../services/tradeService';

jest.mock('../services/tradeService');

describe('Trade Controller', () => {
  let mockTradeService;

  beforeEach(() => {
    mockTradeService = {
      createTrade: jest.fn(),
      getTrades: jest.fn(),
      updateTrade: jest.fn(),
      deleteTrade: jest.fn()
    };
    TradeService.mockImplementation(() => mockTradeService);
  });

  describe('POST /api/trades', () => {
    it('creates trade successfully', async () => {
      const tradeData = {
        symbol: 'AAPL',
        type: 'LONG',
        entryPrice: 150.00,
        quantity: 100
      };

      const createdTrade = { id: '1', ...tradeData };
      mockTradeService.createTrade.mockResolvedValue(createdTrade);

      const response = await request(app)
        .post('/api/trades')
        .set('Authorization', 'Bearer valid-token')
        .send(tradeData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toMatchObject(createdTrade);
    });

    it('returns 400 for invalid data', async () => {
      const invalidData = {
        symbol: '',
        entryPrice: -100
      };

      const response = await request(app)
        .post('/api/trades')
        .set('Authorization', 'Bearer valid-token')
        .send(invalidData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });

    it('returns 401 for missing authentication', async () => {
      await request(app)
        .post('/api/trades')
        .send({})
        .expect(401);
    });
  });

  describe('GET /api/trades', () => {
    it('returns paginated trades', async () => {
      const mockTrades = {
        data: [{ id: '1', symbol: 'AAPL' }],
        pagination: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1
        }
      };

      mockTradeService.getTrades.mockResolvedValue(mockTrades);

      const response = await request(app)
        .get('/api/trades?page=1&limit=20')
        .set('Authorization', 'Bearer valid-token')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockTrades.data);
      expect(response.body.pagination).toEqual(mockTrades.pagination);
    });

    it('handles query parameters correctly', async () => {
      await request(app)
        .get('/api/trades?symbol=AAPL&startDate=2025-01-01')
        .set('Authorization', 'Bearer valid-token')
        .expect(200);

      expect(mockTradeService.getTrades).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          symbol: 'AAPL',
          startDate: '2025-01-01'
        })
      );
    });
  });
});
```

---

## 🔄 End-to-End Testing

### **Web Application E2E Tests**
```javascript
// trade-management.e2e.js
import { test, expect } from '@playwright/test';

test.describe('Trade Management', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/login');
    await page.fill('[data-testid=email]', '<EMAIL>');
    await page.fill('[data-testid=password]', 'password123');
    await page.click('[data-testid=login-button]');
    await page.waitForURL('/dashboard');
  });

  test('should create a new trade', async ({ page }) => {
    // Navigate to add trade
    await page.click('[data-testid=add-trade-button]');
    await page.waitForURL('/trades/new');

    // Fill trade form
    await page.fill('[data-testid=symbol-input]', 'AAPL');
    await page.selectOption('[data-testid=trade-type]', 'LONG');
    await page.fill('[data-testid=entry-price]', '150.00');
    await page.fill('[data-testid=quantity]', '100');
    await page.fill('[data-testid=stop-loss]', '145.00');

    // Submit form
    await page.click('[data-testid=submit-button]');

    // Verify success
    await expect(page.locator('[data-testid=success-message]')).toBeVisible();
    await page.waitForURL('/trades');
    
    // Verify trade appears in list
    await expect(page.locator('[data-testid=trade-card]').first()).toContainText('AAPL');
  });

  test('should edit existing trade', async ({ page }) => {
    // Go to trades page
    await page.goto('/trades');
    
    // Click first trade
    await page.click('[data-testid=trade-card]');
    await page.click('[data-testid=edit-button]');

    // Update exit price
    await page.fill('[data-testid=exit-price]', '155.00');
    await page.selectOption('[data-testid=status]', 'CLOSED');

    // Save changes
    await page.click('[data-testid=save-button]');

    // Verify changes
    await expect(page.locator('[data-testid=pnl-value]')).toContainText('$500.00');
    await expect(page.locator('[data-testid=status-badge]')).toContainText('CLOSED');
  });

  test('should import trades from CSV', async ({ page }) => {
    await page.goto('/import');

    // Upload CSV file
    const fileInput = page.locator('[data-testid=csv-file-input]');
    await fileInput.setInputFiles('test-data/sample-trades.csv');

    // Configure mapping
    await page.selectOption('[data-testid=symbol-mapping]', 'Symbol');
    await page.selectOption('[data-testid=price-mapping]', 'Entry Price');

    // Start import
    await page.click('[data-testid=import-button]');

    // Wait for completion
    await expect(page.locator('[data-testid=import-success]')).toBeVisible();
    await expect(page.locator('[data-testid=imported-count]')).toContainText('25 trades imported');
  });
});
```

---

## 📊 Performance Testing

### **Load Testing**
```javascript
// load-test.js
import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  stages: [
    { duration: '2m', target: 10 }, // Ramp up
    { duration: '5m', target: 50 }, // Stay at 50 users
    { duration: '2m', target: 0 },  // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests under 500ms
    http_req_failed: ['rate<0.1'],    // Error rate under 10%
  },
};

export default function () {
  // Login
  let loginResponse = http.post('http://localhost:3000/api/auth/login', {
    email: '<EMAIL>',
    password: 'password123',
  });

  check(loginResponse, {
    'login successful': (r) => r.status === 200,
  });

  let token = loginResponse.json('data.tokens.accessToken');

  // Get trades
  let tradesResponse = http.get('http://localhost:3000/api/trades', {
    headers: { Authorization: `Bearer ${token}` },
  });

  check(tradesResponse, {
    'trades fetched': (r) => r.status === 200,
    'response time OK': (r) => r.timings.duration < 500,
  });

  sleep(1);
}
```

---

## 🔒 Security Testing

### **Security Test Suite**
```javascript
// security.test.js
import request from 'supertest';
import { app } from '../app';

describe('Security Tests', () => {
  describe('Authentication', () => {
    it('rejects requests without token', async () => {
      await request(app)
        .get('/api/trades')
        .expect(401);
    });

    it('rejects invalid tokens', async () => {
      await request(app)
        .get('/api/trades')
        .set('Authorization', 'Bearer invalid-token')
        .expect(403);
    });

    it('rejects expired tokens', async () => {
      const expiredToken = 'expired-jwt-token';
      
      await request(app)
        .get('/api/trades')
        .set('Authorization', `Bearer ${expiredToken}`)
        .expect(403);
    });
  });

  describe('Input Validation', () => {
    it('sanitizes SQL injection attempts', async () => {
      const maliciousInput = {
        symbol: "'; DROP TABLE trades; --",
        entryPrice: 100
      };

      const response = await request(app)
        .post('/api/trades')
        .set('Authorization', 'Bearer valid-token')
        .send(maliciousInput)
        .expect(400);

      expect(response.body.error).toContain('Validation failed');
    });

    it('prevents XSS attacks', async () => {
      const xssInput = {
        symbol: '<script>alert("xss")</script>',
        entryPrice: 100
      };

      await request(app)
        .post('/api/trades')
        .set('Authorization', 'Bearer valid-token')
        .send(xssInput)
        .expect(400);
    });
  });

  describe('Rate Limiting', () => {
    it('enforces rate limits', async () => {
      const requests = Array(101).fill().map(() =>
        request(app)
          .get('/api/trades')
          .set('Authorization', 'Bearer valid-token')
      );

      const responses = await Promise.all(requests);
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });
});
```

---

## 🔄 Continuous Integration

### **GitHub Actions Workflow**
```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run unit tests
        run: npm run test:unit
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3

  flutter-tests:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.0'
      
      - name: Install dependencies
        run: flutter pub get
        working-directory: ./tradedge_flutter
      
      - name: Run tests
        run: flutter test --coverage
        working-directory: ./tradedge_flutter

  e2e-tests:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Install Playwright
        run: npx playwright install
      
      - name: Start application
        run: |
          npm run build
          npm start &
          sleep 30
      
      - name: Run E2E tests
        run: npm run test:e2e
```

This comprehensive testing strategy ensures high-quality, reliable software delivery across all TRADEDGE platforms and components.
