import React from 'react';
import styled from 'styled-components';
import GoalGaugeCard from './GoalGaugeCard';

const DashboardContainer = styled.div`
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
`;

const DashboardHeader = styled.h1`
  color: #2d3748;
  margin-bottom: 24px;
  font-size: 28px;
  font-weight: 700;
`;

const Dashboard = () => {
  // Example progress values - these would typically come from your application state or API
  const progressData = {
    overallProgress: 75,  // Overall performance
    profitProgress: 80,   // Golden gauge for Profit
    guardProgress: 65,    // Silver gauge for Risk
    focusProgress: 70     // Blue gauge for Consistency
  };

  return (
    <DashboardContainer>
      <DashboardHeader>Trading Dashboard</DashboardHeader>
      <GoalGaugeCard {...progressData} />
      {/* Add other dashboard components below */}
    </DashboardContainer>
  );
};

export default Dashboard; 