import React from 'react';
import styled from 'styled-components';

const CardContainer = styled.div`
  background: linear-gradient(145deg, #ffffff, #f5f7fa);
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.07);
  margin: 20px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 40px rgba(31, 38, 135, 0.1);
  }
`;

const MainGaugeContainer = styled.div`
  position: relative;
  width: 300px;
  height: 300px;
  margin: 0 auto;
`;

const GaugeBackground = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(
    from 180deg,
    #FF6B6B 0deg,
    #FFD93D 120deg,
    #6BCB77 240deg,
    #4D96FF 360deg
  );
  opacity: 0.1;
`;

const SmallGaugesContainer = styled.div`
  display: flex;
  justify-content: space-around;
  position: absolute;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
`;

const GaugeTitle = styled.h2`
  text-align: center;
  color: #2d3748;
  margin-bottom: 20px;
  font-weight: 600;
  font-size: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
`;

const GaugeTicks = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  
  &::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 2px dashed rgba(45, 55, 72, 0.1);
  }
`;

const SmallGauge = styled.div`
  width: 80px;
  height: 80px;
  position: relative;
  border-radius: 50%;
  border: 3px solid ${props => props.borderColor};
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }

  &::before {
    content: '';
    position: absolute;
    width: 90%;
    height: 90%;
    border-radius: 50%;
    border: 1px dashed ${props => props.borderColor}40;
  }

  &::after {
    content: '';
    position: absolute;
    width: 3px;
    height: 35px;
    background: ${props => props.borderColor};
    bottom: 50%;
    transform-origin: bottom;
    transform: rotate(${props => props.value * 1.8}deg);
    transition: transform 0.5s ease;
  }
`;

const GaugeLabel = styled.div`
  font-size: 12px;
  font-weight: 600;
  color: ${props => props.color};
  margin-top: 8px;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.05em;
`;

const GaugeValue = styled.div`
  font-size: 14px;
  font-weight: bold;
  color: #2d3748;
  position: absolute;
  bottom: 15px;
  background: white;
  padding: 2px 6px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const MainGauge = styled.div`
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4px solid #2d3748;
  position: relative;
  background: white;
  
  &::before {
    content: '';
    position: absolute;
    width: 95%;
    height: 95%;
    top: 2.5%;
    left: 2.5%;
    border-radius: 50%;
    border: 2px dashed rgba(45, 55, 72, 0.1);
  }

  &::after {
    content: '';
    position: absolute;
    width: 4px;
    height: 140px;
    background: linear-gradient(to top, #2d3748 70%, transparent);
    bottom: 50%;
    left: 50%;
    transform-origin: bottom;
    transform: rotate(${props => props.value * 1.8}deg);
    transition: transform 0.5s ease;
  }
`;

const MainGaugeValue = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2rem;
  font-weight: bold;
  color: #2d3748;
  text-align: center;
  
  span {
    display: block;
    font-size: 1rem;
    font-weight: normal;
    color: #718096;
    margin-top: 4px;
  }
`;

const GoalGaugeCard = ({ 
  overallProgress = 75, 
  profitProgress = 80, 
  guardProgress = 65, 
  focusProgress = 70 
}) => {
  return (
    <CardContainer>
      <GaugeTitle>Performance Dashboard</GaugeTitle>
      <MainGaugeContainer>
        <GaugeBackground />
        <GaugeTicks />
        <MainGauge value={overallProgress}>
          <MainGaugeValue>
            {overallProgress}%
            <span>Overall</span>
          </MainGaugeValue>
        </MainGauge>
        <SmallGaugesContainer>
          <div>
            <SmallGauge borderColor="#FFD700" value={profitProgress}>
              <GaugeValue>{profitProgress}%</GaugeValue>
            </SmallGauge>
            <GaugeLabel color="#FFD700">Profit</GaugeLabel>
          </div>
          <div>
            <SmallGauge borderColor="#C0C0C0" value={guardProgress}>
              <GaugeValue>{guardProgress}%</GaugeValue>
            </SmallGauge>
            <GaugeLabel color="#C0C0C0">Guard</GaugeLabel>
          </div>
          <div>
            <SmallGauge borderColor="#4299E1" value={focusProgress}>
              <GaugeValue>{focusProgress}%</GaugeValue>
            </SmallGauge>
            <GaugeLabel color="#4299E1">Focus</GaugeLabel>
          </div>
        </SmallGaugesContainer>
      </MainGaugeContainer>
    </CardContainer>
  );
};

export default GoalGaugeCard; 