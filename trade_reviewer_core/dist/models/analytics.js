"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsEntity = exports.AnalyticsSchema = exports.AnalyticsMetricsSchema = exports.TimeFrame = void 0;
const zod_1 = require("zod");
var TimeFrame;
(function (TimeFrame) {
    TimeFrame["ALL"] = "ALL";
    TimeFrame["DAILY"] = "DAILY";
    TimeFrame["WEEKLY"] = "WEEKLY";
    TimeFrame["MONTHLY"] = "MONTHLY";
    TimeFrame["YEARLY"] = "YEARLY";
    TimeFrame["CUSTOM"] = "CUSTOM";
})(TimeFrame || (exports.TimeFrame = TimeFrame = {}));
exports.AnalyticsMetricsSchema = zod_1.z.object({
    totalTrades: zod_1.z.number(),
    winningTrades: zod_1.z.number(),
    losingTrades: zod_1.z.number(),
    breakEvenTrades: zod_1.z.number(),
    totalPnL: zod_1.z.number(),
    totalRiskAmount: zod_1.z.number(),
    totalRewardAmount: zod_1.z.number(),
    averageRRR: zod_1.z.number(),
    winRate: zod_1.z.number(),
    profitFactor: zod_1.z.number(),
    averageWinAmount: zod_1.z.number(),
    averageLossAmount: zod_1.z.number(),
    largestWin: zod_1.z.number(),
    largestLoss: zod_1.z.number(),
    totalFees: zod_1.z.number()
});
exports.AnalyticsSchema = zod_1.z.object({
    userId: zod_1.z.string(),
    timeFrame: zod_1.z.nativeEnum(TimeFrame),
    startDate: zod_1.z.date(),
    endDate: zod_1.z.date(),
    metrics: exports.AnalyticsMetricsSchema,
    updatedAt: zod_1.z.date(),
    byType: zod_1.z.record(zod_1.z.string(), exports.AnalyticsMetricsSchema).optional(),
    bySymbol: zod_1.z.record(zod_1.z.string(), exports.AnalyticsMetricsSchema).optional()
});
class AnalyticsEntity {
    constructor(data) {
        Object.assign(this, exports.AnalyticsSchema.parse(data));
    }
    get totalTrades() {
        return this.metrics.totalTrades;
    }
    get winRate() {
        return this.metrics.winRate;
    }
    get profitFactor() {
        return this.metrics.profitFactor;
    }
    get averageRRR() {
        return this.metrics.averageRRR;
    }
    get totalPnL() {
        return this.metrics.totalPnL;
    }
    get totalFees() {
        return this.metrics.totalFees;
    }
    get netPnL() {
        return this.totalPnL - this.totalFees;
    }
}
exports.AnalyticsEntity = AnalyticsEntity;
