import { z } from 'zod';
export declare enum AccountType {
    DEMO = "DEMO",
    LIVE = "LIVE"
}
export declare enum ExperienceLevel {
    BEGINNER = "BEGINNER",
    INTERMEDIATE = "INTERMEDIATE",
    ADVANCED = "ADVANCED",
    PROFESSIONAL = "PROFESSIONAL"
}
export declare const ProfileSchema: z.ZodObject<{
    id: z.ZodString;
    userId: z.ZodString;
    name: z.ZodString;
    email: z.ZodString;
    avatar: z.ZodOptional<z.ZodString>;
    bio: z.ZodOptional<z.ZodString>;
    accountType: z.<PERSON>od<PERSON>ative<PERSON>num<typeof AccountType>;
    experienceLevel: z.Zod<PERSON><typeof ExperienceLevel>;
    tradingStyle: z.ZodArray<z.ZodString, "many">;
    preferredMarkets: z.ZodArray<z.ZodString, "many">;
    preferredTimeframes: z.<PERSON><z.ZodString, "many">;
    initialBalance: z.Z<PERSON>;
    currentBalance: z.ZodNumber;
    currency: z.Zod<PERSON>tring;
    riskPerTrade: z.ZodNumber;
    maxDrawdown: z.ZodNumber;
    profitTarget: z.ZodOptional<z.ZodNumber>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    userId: string;
    updatedAt: Date;
    id: string;
    createdAt: Date;
    email: string;
    name: string;
    accountType: AccountType;
    experienceLevel: ExperienceLevel;
    tradingStyle: string[];
    preferredMarkets: string[];
    preferredTimeframes: string[];
    initialBalance: number;
    currentBalance: number;
    currency: string;
    riskPerTrade: number;
    maxDrawdown: number;
    avatar?: string | undefined;
    bio?: string | undefined;
    profitTarget?: number | undefined;
}, {
    userId: string;
    updatedAt: Date;
    id: string;
    createdAt: Date;
    email: string;
    name: string;
    accountType: AccountType;
    experienceLevel: ExperienceLevel;
    tradingStyle: string[];
    preferredMarkets: string[];
    preferredTimeframes: string[];
    initialBalance: number;
    currentBalance: number;
    currency: string;
    riskPerTrade: number;
    maxDrawdown: number;
    avatar?: string | undefined;
    bio?: string | undefined;
    profitTarget?: number | undefined;
}>;
export type Profile = z.infer<typeof ProfileSchema>;
export declare class ProfileEntity implements Profile {
    readonly id: string;
    readonly userId: string;
    readonly name: string;
    readonly email: string;
    readonly avatar?: string;
    readonly bio?: string;
    readonly accountType: AccountType;
    readonly experienceLevel: ExperienceLevel;
    readonly tradingStyle: string[];
    readonly preferredMarkets: string[];
    readonly preferredTimeframes: string[];
    readonly initialBalance: number;
    currentBalance: number;
    readonly currency: string;
    riskPerTrade: number;
    maxDrawdown: number;
    profitTarget?: number;
    readonly createdAt: Date;
    updatedAt: Date;
    constructor(data: Profile);
    get drawdown(): number;
    get isAtRisk(): boolean;
    get profitPercentage(): number;
    get hasReachedTarget(): boolean;
    updateBalance(newBalance: number): void;
    updateRiskParameters(riskPerTrade: number, maxDrawdown: number): void;
    calculatePositionSize(price: number, stopLoss: number): number;
}
