import { z } from 'zod';
import { TradeType } from './trade';
export declare const MarketMetricsSchema: z.ZodObject<{
    symbol: z.ZodString;
    price: z.ZodNumber;
    volume: z.ZodNumber;
    high: z.ZodNumber;
    low: z.ZodNumber;
    open: z.ZodNumber;
    close: z.ZodNumber;
    change: z.ZodNumber;
    changePercent: z.ZodNumber;
    timestamp: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    symbol: string;
    price: number;
    volume: number;
    high: number;
    low: number;
    open: number;
    close: number;
    change: number;
    changePercent: number;
    timestamp: Date;
}, {
    symbol: string;
    price: number;
    volume: number;
    high: number;
    low: number;
    open: number;
    close: number;
    change: number;
    changePercent: number;
    timestamp: Date;
}>;
export declare const PositionMetricsSchema: z.ZodObject<{
    symbol: z.ZodString;
    type: z.ZodNativeEnum<typeof TradeType>;
    size: z.ZodNumber;
    entryPrice: z.ZodNumber;
    currentPrice: z.ZodNumber;
    unrealizedPnL: z.ZodNumber;
    unrealizedPnLPercent: z.ZodNumber;
    risk: z.ZodNumber;
    reward: z.ZodNumber;
    rrr: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    symbol: string;
    type: TradeType;
    entryPrice: number;
    risk: number;
    reward: number;
    size: number;
    currentPrice: number;
    unrealizedPnL: number;
    unrealizedPnLPercent: number;
    rrr: number;
}, {
    symbol: string;
    type: TradeType;
    entryPrice: number;
    risk: number;
    reward: number;
    size: number;
    currentPrice: number;
    unrealizedPnL: number;
    unrealizedPnLPercent: number;
    rrr: number;
}>;
export declare const AccountMetricsSchema: z.ZodObject<{
    balance: z.ZodNumber;
    equity: z.ZodNumber;
    openPositions: z.ZodNumber;
    marginUsed: z.ZodNumber;
    marginAvailable: z.ZodNumber;
    marginLevel: z.ZodNumber;
    dailyPnL: z.ZodNumber;
    dailyPnLPercent: z.ZodNumber;
    drawdown: z.ZodNumber;
    drawdownPercent: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    balance: number;
    equity: number;
    openPositions: number;
    marginUsed: number;
    marginAvailable: number;
    marginLevel: number;
    dailyPnL: number;
    dailyPnLPercent: number;
    drawdown: number;
    drawdownPercent: number;
}, {
    balance: number;
    equity: number;
    openPositions: number;
    marginUsed: number;
    marginAvailable: number;
    marginLevel: number;
    dailyPnL: number;
    dailyPnLPercent: number;
    drawdown: number;
    drawdownPercent: number;
}>;
export declare const MetricsSchema: z.ZodObject<{
    id: z.ZodString;
    userId: z.ZodString;
    timestamp: z.ZodDate;
    market: z.ZodRecord<z.ZodString, z.ZodObject<{
        symbol: z.ZodString;
        price: z.ZodNumber;
        volume: z.ZodNumber;
        high: z.ZodNumber;
        low: z.ZodNumber;
        open: z.ZodNumber;
        close: z.ZodNumber;
        change: z.ZodNumber;
        changePercent: z.ZodNumber;
        timestamp: z.ZodDate;
    }, "strip", z.ZodTypeAny, {
        symbol: string;
        price: number;
        volume: number;
        high: number;
        low: number;
        open: number;
        close: number;
        change: number;
        changePercent: number;
        timestamp: Date;
    }, {
        symbol: string;
        price: number;
        volume: number;
        high: number;
        low: number;
        open: number;
        close: number;
        change: number;
        changePercent: number;
        timestamp: Date;
    }>>;
    positions: z.ZodRecord<z.ZodString, z.ZodObject<{
        symbol: z.ZodString;
        type: z.ZodNativeEnum<typeof TradeType>;
        size: z.ZodNumber;
        entryPrice: z.ZodNumber;
        currentPrice: z.ZodNumber;
        unrealizedPnL: z.ZodNumber;
        unrealizedPnLPercent: z.ZodNumber;
        risk: z.ZodNumber;
        reward: z.ZodNumber;
        rrr: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        symbol: string;
        type: TradeType;
        entryPrice: number;
        risk: number;
        reward: number;
        size: number;
        currentPrice: number;
        unrealizedPnL: number;
        unrealizedPnLPercent: number;
        rrr: number;
    }, {
        symbol: string;
        type: TradeType;
        entryPrice: number;
        risk: number;
        reward: number;
        size: number;
        currentPrice: number;
        unrealizedPnL: number;
        unrealizedPnLPercent: number;
        rrr: number;
    }>>;
    account: z.ZodObject<{
        balance: z.ZodNumber;
        equity: z.ZodNumber;
        openPositions: z.ZodNumber;
        marginUsed: z.ZodNumber;
        marginAvailable: z.ZodNumber;
        marginLevel: z.ZodNumber;
        dailyPnL: z.ZodNumber;
        dailyPnLPercent: z.ZodNumber;
        drawdown: z.ZodNumber;
        drawdownPercent: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        balance: number;
        equity: number;
        openPositions: number;
        marginUsed: number;
        marginAvailable: number;
        marginLevel: number;
        dailyPnL: number;
        dailyPnLPercent: number;
        drawdown: number;
        drawdownPercent: number;
    }, {
        balance: number;
        equity: number;
        openPositions: number;
        marginUsed: number;
        marginAvailable: number;
        marginLevel: number;
        dailyPnL: number;
        dailyPnLPercent: number;
        drawdown: number;
        drawdownPercent: number;
    }>;
}, "strip", z.ZodTypeAny, {
    userId: string;
    id: string;
    timestamp: Date;
    market: Record<string, {
        symbol: string;
        price: number;
        volume: number;
        high: number;
        low: number;
        open: number;
        close: number;
        change: number;
        changePercent: number;
        timestamp: Date;
    }>;
    positions: Record<string, {
        symbol: string;
        type: TradeType;
        entryPrice: number;
        risk: number;
        reward: number;
        size: number;
        currentPrice: number;
        unrealizedPnL: number;
        unrealizedPnLPercent: number;
        rrr: number;
    }>;
    account: {
        balance: number;
        equity: number;
        openPositions: number;
        marginUsed: number;
        marginAvailable: number;
        marginLevel: number;
        dailyPnL: number;
        dailyPnLPercent: number;
        drawdown: number;
        drawdownPercent: number;
    };
}, {
    userId: string;
    id: string;
    timestamp: Date;
    market: Record<string, {
        symbol: string;
        price: number;
        volume: number;
        high: number;
        low: number;
        open: number;
        close: number;
        change: number;
        changePercent: number;
        timestamp: Date;
    }>;
    positions: Record<string, {
        symbol: string;
        type: TradeType;
        entryPrice: number;
        risk: number;
        reward: number;
        size: number;
        currentPrice: number;
        unrealizedPnL: number;
        unrealizedPnLPercent: number;
        rrr: number;
    }>;
    account: {
        balance: number;
        equity: number;
        openPositions: number;
        marginUsed: number;
        marginAvailable: number;
        marginLevel: number;
        dailyPnL: number;
        dailyPnLPercent: number;
        drawdown: number;
        drawdownPercent: number;
    };
}>;
export type MarketMetrics = z.infer<typeof MarketMetricsSchema>;
export type PositionMetrics = z.infer<typeof PositionMetricsSchema>;
export type AccountMetrics = z.infer<typeof AccountMetricsSchema>;
export type Metrics = z.infer<typeof MetricsSchema>;
export declare class MetricsEntity implements Metrics {
    readonly id: string;
    readonly userId: string;
    timestamp: Date;
    market: Record<string, MarketMetrics>;
    positions: Record<string, PositionMetrics>;
    account: AccountMetrics;
    constructor(data: Metrics);
    getMarketMetrics(symbol: string): MarketMetrics | undefined;
    getPositionMetrics(symbol: string): PositionMetrics | undefined;
    get totalUnrealizedPnL(): number;
    get totalRisk(): number;
    get averageRRR(): number;
    get marginUtilization(): number;
    updateMarketMetrics(symbol: string, metrics: MarketMetrics): void;
    updatePositionMetrics(symbol: string, metrics: PositionMetrics): void;
    updateAccountMetrics(metrics: AccountMetrics): void;
}
