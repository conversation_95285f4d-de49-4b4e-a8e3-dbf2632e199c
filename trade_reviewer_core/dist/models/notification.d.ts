import { z } from 'zod';
export declare enum NotificationType {
    TRADE_ALERT = "TRADE_ALERT",
    PRICE_ALERT = "PRICE_ALERT",
    GOAL_ACHIEVED = "GOAL_ACHIEVED",
    RISK_WARNING = "RISK_WARNING",
    SYSTEM_UPDATE = "SYSTEM_UPDATE",
    BACKUP_REMINDER = "BACKUP_REMINDER",
    JOURNAL_REMINDER = "JOURNAL_REMINDER",
    TRADE_STATS = "TRADE_STATS"
}
export declare enum NotificationPriority {
    LOW = "LOW",
    MEDIUM = "MEDIUM",
    HIGH = "HIGH",
    URGENT = "URGENT"
}
export declare enum NotificationChannel {
    IN_APP = "IN_APP",
    EMAIL = "EMAIL",
    PUSH = "PUSH",
    SMS = "SMS"
}
export declare const NotificationTriggerSchema: z.ZodObject<{
    type: z.ZodNative<PERSON>num<typeof NotificationType>;
    conditions: z.<PERSON><PERSON><PERSON><PERSON><z.ZodString, z.ZodAny>;
    channels: z.<PERSON><z.ZodNativeEnum<typeof NotificationChannel>, "many">;
    schedule: z.ZodOptional<z.ZodString>;
    active: z.ZodDefault<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    type: NotificationType;
    conditions: Record<string, any>;
    channels: NotificationChannel[];
    active: boolean;
    schedule?: string | undefined;
}, {
    type: NotificationType;
    conditions: Record<string, any>;
    channels: NotificationChannel[];
    schedule?: string | undefined;
    active?: boolean | undefined;
}>;
export declare const NotificationSchema: z.ZodObject<{
    id: z.ZodString;
    userId: z.ZodString;
    type: z.ZodNativeEnum<typeof NotificationType>;
    title: z.ZodString;
    message: z.ZodString;
    priority: z.ZodDefault<z.ZodNativeEnum<typeof NotificationPriority>>;
    channels: z.ZodArray<z.ZodNativeEnum<typeof NotificationChannel>, "many">;
    data: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
    read: z.ZodDefault<z.ZodBoolean>;
    dismissed: z.ZodDefault<z.ZodBoolean>;
    scheduledFor: z.ZodOptional<z.ZodDate>;
    expiresAt: z.ZodOptional<z.ZodDate>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    message: string;
    type: NotificationType;
    userId: string;
    updatedAt: Date;
    id: string;
    createdAt: Date;
    channels: NotificationChannel[];
    title: string;
    priority: NotificationPriority;
    read: boolean;
    dismissed: boolean;
    data?: Record<string, any> | undefined;
    scheduledFor?: Date | undefined;
    expiresAt?: Date | undefined;
}, {
    message: string;
    type: NotificationType;
    userId: string;
    updatedAt: Date;
    id: string;
    createdAt: Date;
    channels: NotificationChannel[];
    title: string;
    priority?: NotificationPriority | undefined;
    data?: Record<string, any> | undefined;
    read?: boolean | undefined;
    dismissed?: boolean | undefined;
    scheduledFor?: Date | undefined;
    expiresAt?: Date | undefined;
}>;
export type NotificationTrigger = z.infer<typeof NotificationTriggerSchema>;
export type Notification = z.infer<typeof NotificationSchema>;
export declare class NotificationEntity implements Notification {
    id: string;
    userId: string;
    type: NotificationType;
    title: string;
    message: string;
    priority: NotificationPriority;
    channels: NotificationChannel[];
    data?: Record<string, any>;
    read: boolean;
    dismissed: boolean;
    scheduledFor?: Date;
    expiresAt?: Date;
    createdAt: Date;
    updatedAt: Date;
    constructor(data: Notification);
    get isExpired(): boolean;
    get isScheduled(): boolean;
    get isUrgent(): boolean;
    get shouldDisplay(): boolean;
    get channelPreferences(): string[];
    markAsRead(): void;
    dismiss(): void;
    reschedule(newDate: Date): void;
    updateExpiry(newDate: Date): void;
    addChannel(channel: NotificationChannel): void;
    removeChannel(channel: NotificationChannel): void;
    updatePriority(priority: NotificationPriority): void;
}
