"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserEntity = exports.UserSchema = exports.UserPreferencesSchema = exports.NotificationPreference = exports.UserRole = void 0;
const zod_1 = require("zod");
var UserRole;
(function (UserRole) {
    UserRole["USER"] = "USER";
    UserRole["PREMIUM"] = "PREMIUM";
    UserRole["ADMIN"] = "ADMIN";
})(UserRole || (exports.UserRole = UserRole = {}));
var NotificationPreference;
(function (NotificationPreference) {
    NotificationPreference["ALL"] = "ALL";
    NotificationPreference["IMPORTANT"] = "IMPORTANT";
    NotificationPreference["NONE"] = "NONE";
})(NotificationPreference || (exports.NotificationPreference = NotificationPreference = {}));
exports.UserPreferencesSchema = zod_1.z.object({
    notificationPreference: zod_1.z.nativeEnum(NotificationPreference).default(NotificationPreference.IMPORTANT),
    darkMode: zod_1.z.boolean().default(false),
    defaultCurrency: zod_1.z.string().default('USD'),
    timezone: zod_1.z.string().default('UTC'),
    language: zod_1.z.string().default('en'),
    showPnLInPercent: zod_1.z.boolean().default(true),
    autoSyncEnabled: zod_1.z.boolean().default(true)
});
exports.UserSchema = zod_1.z.object({
    id: zod_1.z.string().uuid(),
    email: zod_1.z.string().email(),
    name: zod_1.z.string().min(2).max(50),
    role: zod_1.z.nativeEnum(UserRole).default(UserRole.USER),
    preferences: exports.UserPreferencesSchema,
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date(),
    lastLoginAt: zod_1.z.date().optional(),
    isEmailVerified: zod_1.z.boolean().default(false),
    profileImageUrl: zod_1.z.string().url().optional()
});
class UserEntity {
    constructor(data) {
        this.role = UserRole.USER;
        this.isEmailVerified = false;
        Object.assign(this, exports.UserSchema.parse(data));
    }
    get isAdmin() {
        return this.role === UserRole.ADMIN;
    }
    get isPremium() {
        return this.role === UserRole.PREMIUM || this.isAdmin;
    }
    get displayName() {
        return this.name || this.email.split('@')[0];
    }
    get canAccessPremiumFeatures() {
        return this.isPremium;
    }
    updatePreferences(newPreferences) {
        this.preferences = {
            ...this.preferences,
            ...newPreferences
        };
        this.updatedAt = new Date();
    }
    verifyEmail() {
        this.isEmailVerified = true;
        this.updatedAt = new Date();
    }
    updateLastLogin() {
        this.lastLoginAt = new Date();
        this.updatedAt = new Date();
    }
}
exports.UserEntity = UserEntity;
