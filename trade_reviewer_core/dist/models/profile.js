"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfileEntity = exports.ProfileSchema = exports.ExperienceLevel = exports.AccountType = void 0;
const zod_1 = require("zod");
var AccountType;
(function (AccountType) {
    AccountType["DEMO"] = "DEMO";
    AccountType["LIVE"] = "LIVE";
})(AccountType || (exports.AccountType = AccountType = {}));
var ExperienceLevel;
(function (ExperienceLevel) {
    ExperienceLevel["BEGINNER"] = "BEGINNER";
    ExperienceLevel["INTERMEDIATE"] = "INTERMEDIATE";
    ExperienceLevel["ADVANCED"] = "ADVANCED";
    ExperienceLevel["PROFESSIONAL"] = "PROFESSIONAL";
})(ExperienceLevel || (exports.ExperienceLevel = ExperienceLevel = {}));
exports.ProfileSchema = zod_1.z.object({
    id: zod_1.z.string().uuid(),
    userId: zod_1.z.string().uuid(),
    name: zod_1.z.string().min(1),
    email: zod_1.z.string().email(),
    avatar: zod_1.z.string().url().optional(),
    bio: zod_1.z.string().optional(),
    accountType: zod_1.z.nativeEnum(AccountType),
    experienceLevel: zod_1.z.nativeEnum(ExperienceLevel),
    tradingStyle: zod_1.z.array(zod_1.z.string()),
    preferredMarkets: zod_1.z.array(zod_1.z.string()),
    preferredTimeframes: zod_1.z.array(zod_1.z.string()),
    initialBalance: zod_1.z.number().positive(),
    currentBalance: zod_1.z.number(),
    currency: zod_1.z.string(),
    riskPerTrade: zod_1.z.number().min(0).max(100),
    maxDrawdown: zod_1.z.number().min(0).max(100),
    profitTarget: zod_1.z.number().positive().optional(),
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date()
});
class ProfileEntity {
    constructor(data) {
        const validated = exports.ProfileSchema.parse(data);
        this.id = validated.id;
        this.userId = validated.userId;
        this.name = validated.name;
        this.email = validated.email;
        this.avatar = validated.avatar;
        this.bio = validated.bio;
        this.accountType = validated.accountType;
        this.experienceLevel = validated.experienceLevel;
        this.tradingStyle = validated.tradingStyle;
        this.preferredMarkets = validated.preferredMarkets;
        this.preferredTimeframes = validated.preferredTimeframes;
        this.initialBalance = validated.initialBalance;
        this.currentBalance = validated.currentBalance;
        this.currency = validated.currency;
        this.riskPerTrade = validated.riskPerTrade;
        this.maxDrawdown = validated.maxDrawdown;
        this.profitTarget = validated.profitTarget;
        this.createdAt = validated.createdAt;
        this.updatedAt = validated.updatedAt;
    }
    get drawdown() {
        if (this.currentBalance >= this.initialBalance)
            return 0;
        return ((this.initialBalance - this.currentBalance) / this.initialBalance) * 100;
    }
    get isAtRisk() {
        return this.drawdown >= this.maxDrawdown;
    }
    get profitPercentage() {
        return ((this.currentBalance - this.initialBalance) / this.initialBalance) * 100;
    }
    get hasReachedTarget() {
        if (!this.profitTarget)
            return false;
        return this.profitPercentage >= this.profitTarget;
    }
    updateBalance(newBalance) {
        this.currentBalance = newBalance;
        this.updatedAt = new Date();
    }
    updateRiskParameters(riskPerTrade, maxDrawdown) {
        this.riskPerTrade = Math.min(Math.max(riskPerTrade, 0), 100);
        this.maxDrawdown = Math.min(Math.max(maxDrawdown, 0), 100);
        this.updatedAt = new Date();
    }
    calculatePositionSize(price, stopLoss) {
        const riskAmount = (this.currentBalance * this.riskPerTrade) / 100;
        const riskPerUnit = Math.abs(price - stopLoss);
        return riskAmount / riskPerUnit;
    }
}
exports.ProfileEntity = ProfileEntity;
