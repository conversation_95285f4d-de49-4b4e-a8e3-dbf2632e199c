"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.JournalEntryEntity = exports.JournalEntrySchema = exports.TradePlanSchema = exports.EmotionalState = exports.TradingMistake = exports.MarketCondition = void 0;
const zod_1 = require("zod");
var MarketCondition;
(function (MarketCondition) {
    MarketCondition["BULLISH"] = "BULLISH";
    MarketCondition["BEARISH"] = "BEARISH";
    MarketCondition["SIDEWAYS"] = "SIDEWAYS";
    MarketCondition["VOLATILE"] = "VOLATILE";
    MarketCondition["TRENDING"] = "TRENDING";
    MarketCondition["RANGING"] = "RANGING";
})(MarketCondition || (exports.MarketCondition = MarketCondition = {}));
var TradingMistake;
(function (TradingMistake) {
    TradingMistake["FOMO"] = "FOMO";
    TradingMistake["EARLY_ENTRY"] = "EARLY_ENTRY";
    TradingMistake["LATE_ENTRY"] = "LATE_ENTRY";
    TradingMistake["MOVED_STOP_LOSS"] = "MOVED_STOP_LOSS";
    TradingMistake["EARLY_EXIT"] = "EARLY_EXIT";
    TradingMistake["LATE_EXIT"] = "LATE_EXIT";
    TradingMistake["POSITION_SIZING"] = "POSITION_SIZING";
    TradingMistake["IGNORED_PLAN"] = "IGNORED_PLAN";
    TradingMistake["EMOTIONAL_TRADING"] = "EMOTIONAL_TRADING";
    TradingMistake["REVENGE_TRADING"] = "REVENGE_TRADING";
})(TradingMistake || (exports.TradingMistake = TradingMistake = {}));
var EmotionalState;
(function (EmotionalState) {
    EmotionalState["CALM"] = "CALM";
    EmotionalState["CONFIDENT"] = "CONFIDENT";
    EmotionalState["ANXIOUS"] = "ANXIOUS";
    EmotionalState["FEARFUL"] = "FEARFUL";
    EmotionalState["GREEDY"] = "GREEDY";
    EmotionalState["FRUSTRATED"] = "FRUSTRATED";
    EmotionalState["TILTED"] = "TILTED";
})(EmotionalState || (exports.EmotionalState = EmotionalState = {}));
exports.TradePlanSchema = zod_1.z.object({
    setup: zod_1.z.string().min(1),
    entry: zod_1.z.string().min(1),
    stopLoss: zod_1.z.string().min(1),
    targets: zod_1.z.array(zod_1.z.string()).min(1),
    timeFrame: zod_1.z.string().min(1),
    risk: zod_1.z.number().min(0),
    reward: zod_1.z.number().min(0)
});
exports.JournalEntrySchema = zod_1.z.object({
    id: zod_1.z.string().uuid(),
    userId: zod_1.z.string().uuid(),
    tradeId: zod_1.z.string().uuid(),
    date: zod_1.z.date(),
    marketCondition: zod_1.z.nativeEnum(MarketCondition),
    emotionalState: zod_1.z.nativeEnum(EmotionalState),
    tradePlan: exports.TradePlanSchema,
    mistakes: zod_1.z.array(zod_1.z.nativeEnum(TradingMistake)).default([]),
    analysis: zod_1.z.string().min(1),
    lessons: zod_1.z.array(zod_1.z.string()).min(1),
    screenshots: zod_1.z.array(zod_1.z.string().url()).default([]),
    rating: zod_1.z.number().int().min(1).max(5),
    tags: zod_1.z.array(zod_1.z.string()).default([]),
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date()
});
class JournalEntryEntity {
    constructor(data) {
        this.mistakes = [];
        this.screenshots = [];
        this.tags = [];
        Object.assign(this, exports.JournalEntrySchema.parse(data));
    }
    get riskRewardRatio() {
        return this.tradePlan.reward / this.tradePlan.risk;
    }
    get hasScreenshots() {
        return this.screenshots.length > 0;
    }
    get hasMistakes() {
        return this.mistakes.length > 0;
    }
    get isEmotionallyNeutral() {
        return [EmotionalState.CALM, EmotionalState.CONFIDENT].includes(this.emotionalState);
    }
    addMistake(mistake) {
        if (!this.mistakes.includes(mistake)) {
            this.mistakes.push(mistake);
            this.updatedAt = new Date();
        }
    }
    addLesson(lesson) {
        if (!this.lessons.includes(lesson)) {
            this.lessons.push(lesson);
            this.updatedAt = new Date();
        }
    }
    addScreenshot(url) {
        if (!this.screenshots.includes(url)) {
            this.screenshots.push(url);
            this.updatedAt = new Date();
        }
    }
    updateAnalysis(analysis) {
        this.analysis = analysis;
        this.updatedAt = new Date();
    }
    updateRating(rating) {
        if (rating >= 1 && rating <= 5) {
            this.rating = rating;
            this.updatedAt = new Date();
        }
    }
}
exports.JournalEntryEntity = JournalEntryEntity;
