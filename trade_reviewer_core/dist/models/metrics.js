"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MetricsEntity = exports.MetricsSchema = exports.AccountMetricsSchema = exports.PositionMetricsSchema = exports.MarketMetricsSchema = void 0;
const zod_1 = require("zod");
const trade_1 = require("./trade");
exports.MarketMetricsSchema = zod_1.z.object({
    symbol: zod_1.z.string(),
    price: zod_1.z.number(),
    volume: zod_1.z.number(),
    high: zod_1.z.number(),
    low: zod_1.z.number(),
    open: zod_1.z.number(),
    close: zod_1.z.number(),
    change: zod_1.z.number(),
    changePercent: zod_1.z.number(),
    timestamp: zod_1.z.date()
});
exports.PositionMetricsSchema = zod_1.z.object({
    symbol: zod_1.z.string(),
    type: zod_1.z.nativeEnum(trade_1.TradeType),
    size: zod_1.z.number(),
    entryPrice: zod_1.z.number(),
    currentPrice: zod_1.z.number(),
    unrealizedPnL: zod_1.z.number(),
    unrealizedPnLPercent: zod_1.z.number(),
    risk: zod_1.z.number(),
    reward: zod_1.z.number(),
    rrr: zod_1.z.number()
});
exports.AccountMetricsSchema = zod_1.z.object({
    balance: zod_1.z.number(),
    equity: zod_1.z.number(),
    openPositions: zod_1.z.number(),
    marginUsed: zod_1.z.number(),
    marginAvailable: zod_1.z.number(),
    marginLevel: zod_1.z.number(),
    dailyPnL: zod_1.z.number(),
    dailyPnLPercent: zod_1.z.number(),
    drawdown: zod_1.z.number(),
    drawdownPercent: zod_1.z.number()
});
exports.MetricsSchema = zod_1.z.object({
    id: zod_1.z.string().uuid(),
    userId: zod_1.z.string().uuid(),
    timestamp: zod_1.z.date(),
    market: zod_1.z.record(zod_1.z.string(), exports.MarketMetricsSchema),
    positions: zod_1.z.record(zod_1.z.string(), exports.PositionMetricsSchema),
    account: exports.AccountMetricsSchema
});
class MetricsEntity {
    constructor(data) {
        const validated = exports.MetricsSchema.parse(data);
        this.id = validated.id;
        this.userId = validated.userId;
        this.timestamp = validated.timestamp;
        this.market = validated.market;
        this.positions = validated.positions;
        this.account = validated.account;
    }
    getMarketMetrics(symbol) {
        return this.market[symbol];
    }
    getPositionMetrics(symbol) {
        return this.positions[symbol];
    }
    get totalUnrealizedPnL() {
        return Object.values(this.positions).reduce((sum, position) => sum + position.unrealizedPnL, 0);
    }
    get totalRisk() {
        return Object.values(this.positions).reduce((sum, position) => sum + position.risk, 0);
    }
    get averageRRR() {
        const positions = Object.values(this.positions);
        if (positions.length === 0)
            return 0;
        return positions.reduce((sum, position) => sum + position.rrr, 0) / positions.length;
    }
    get marginUtilization() {
        return (this.account.marginUsed / (this.account.marginUsed + this.account.marginAvailable)) * 100;
    }
    updateMarketMetrics(symbol, metrics) {
        this.market[symbol] = metrics;
        this.timestamp = new Date();
    }
    updatePositionMetrics(symbol, metrics) {
        this.positions[symbol] = metrics;
        this.timestamp = new Date();
    }
    updateAccountMetrics(metrics) {
        this.account = metrics;
        this.timestamp = new Date();
    }
}
exports.MetricsEntity = MetricsEntity;
