import { z } from 'zod';
export declare enum MarketCondition {
    BULLISH = "BULLISH",
    BEARISH = "BEARISH",
    SIDEWAYS = "SIDEWAYS",
    VOLATILE = "VOLATILE",
    TRENDING = "TRENDING",
    RANGING = "RANGING"
}
export declare enum TradingMistake {
    FOMO = "FOMO",
    EARLY_ENTRY = "EARLY_ENTRY",
    LATE_ENTRY = "LATE_ENTRY",
    MOVED_STOP_LOSS = "MOVED_STOP_LOSS",
    EARLY_EXIT = "EARLY_EXIT",
    LATE_EXIT = "LATE_EXIT",
    POSITION_SIZING = "POSITION_SIZING",
    IGNORED_PLAN = "IGNORED_PLAN",
    EMOTIONAL_TRADING = "EMOTIONAL_TRADING",
    REVENGE_TRADING = "REVENGE_TRADING"
}
export declare enum EmotionalState {
    CALM = "CALM",
    CONFIDENT = "CONFIDENT",
    ANXIOUS = "ANXIOUS",
    FEARFUL = "FEARFUL",
    GREEDY = "GREEDY",
    FRUSTRATED = "FRUSTRATED",
    TILTED = "TILTED"
}
export declare const TradePlanSchema: z.ZodObject<{
    setup: z.ZodString;
    entry: z.ZodString;
    stopLoss: z.ZodString;
    targets: z.ZodArray<z.ZodString, "many">;
    timeFrame: z.ZodString;
    risk: z.ZodNumber;
    reward: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    timeFrame: string;
    stopLoss: string;
    risk: number;
    reward: number;
    setup: string;
    entry: string;
    targets: string[];
}, {
    timeFrame: string;
    stopLoss: string;
    risk: number;
    reward: number;
    setup: string;
    entry: string;
    targets: string[];
}>;
export declare const JournalEntrySchema: z.ZodObject<{
    id: z.ZodString;
    userId: z.ZodString;
    tradeId: z.ZodString;
    date: z.ZodDate;
    marketCondition: z.ZodNativeEnum<typeof MarketCondition>;
    emotionalState: z.ZodNativeEnum<typeof EmotionalState>;
    tradePlan: z.ZodObject<{
        setup: z.ZodString;
        entry: z.ZodString;
        stopLoss: z.ZodString;
        targets: z.ZodArray<z.ZodString, "many">;
        timeFrame: z.ZodString;
        risk: z.ZodNumber;
        reward: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        timeFrame: string;
        stopLoss: string;
        risk: number;
        reward: number;
        setup: string;
        entry: string;
        targets: string[];
    }, {
        timeFrame: string;
        stopLoss: string;
        risk: number;
        reward: number;
        setup: string;
        entry: string;
        targets: string[];
    }>;
    mistakes: z.ZodDefault<z.ZodArray<z.ZodNativeEnum<typeof TradingMistake>, "many">>;
    analysis: z.ZodString;
    lessons: z.ZodArray<z.ZodString, "many">;
    screenshots: z.ZodDefault<z.ZodArray<z.ZodString, "many">>;
    rating: z.ZodNumber;
    tags: z.ZodDefault<z.ZodArray<z.ZodString, "many">>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    userId: string;
    date: Date;
    updatedAt: Date;
    id: string;
    createdAt: Date;
    tags: string[];
    tradeId: string;
    marketCondition: MarketCondition;
    emotionalState: EmotionalState;
    tradePlan: {
        timeFrame: string;
        stopLoss: string;
        risk: number;
        reward: number;
        setup: string;
        entry: string;
        targets: string[];
    };
    mistakes: TradingMistake[];
    analysis: string;
    lessons: string[];
    screenshots: string[];
    rating: number;
}, {
    userId: string;
    date: Date;
    updatedAt: Date;
    id: string;
    createdAt: Date;
    tradeId: string;
    marketCondition: MarketCondition;
    emotionalState: EmotionalState;
    tradePlan: {
        timeFrame: string;
        stopLoss: string;
        risk: number;
        reward: number;
        setup: string;
        entry: string;
        targets: string[];
    };
    analysis: string;
    lessons: string[];
    rating: number;
    tags?: string[] | undefined;
    mistakes?: TradingMistake[] | undefined;
    screenshots?: string[] | undefined;
}>;
export type TradePlan = z.infer<typeof TradePlanSchema>;
export type JournalEntry = z.infer<typeof JournalEntrySchema>;
export declare class JournalEntryEntity implements JournalEntry {
    id: string;
    userId: string;
    tradeId: string;
    date: Date;
    marketCondition: MarketCondition;
    emotionalState: EmotionalState;
    tradePlan: TradePlan;
    mistakes: TradingMistake[];
    analysis: string;
    lessons: string[];
    screenshots: string[];
    rating: number;
    tags: string[];
    createdAt: Date;
    updatedAt: Date;
    constructor(data: JournalEntry);
    get riskRewardRatio(): number;
    get hasScreenshots(): boolean;
    get hasMistakes(): boolean;
    get isEmotionallyNeutral(): boolean;
    addMistake(mistake: TradingMistake): void;
    addLesson(lesson: string): void;
    addScreenshot(url: string): void;
    updateAnalysis(analysis: string): void;
    updateRating(rating: number): void;
}
