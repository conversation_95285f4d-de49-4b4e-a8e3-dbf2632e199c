import { z } from 'zod';
export declare enum ChartTimeframe {
    M1 = "M1",
    M5 = "M5",
    M15 = "M15",
    M30 = "M30",
    H1 = "H1",
    H4 = "H4",
    D1 = "D1",
    W1 = "W1",
    MN = "MN"
}
export declare enum RiskCalculationType {
    FIXED = "FIXED",
    PERCENTAGE = "PERCENTAGE",
    R_MULTIPLE = "R_MULTIPLE"
}
export declare enum PnLDisplayMode {
    CURRENCY = "CURRENCY",
    PERCENTAGE = "PERCENTAGE",
    R_MULTIPLE = "R_MULTIPLE"
}
export declare enum AccountStatus {
    CONNECTED = "CONNECTED",
    DISCONNECTED = "DISCONNECTED",
    PENDING = "PENDING",
    ERROR = "ERROR"
}
export declare const TradeDefaultsSchema: z.ZodObject<{
    defaultRisk: z.ZodNumber;
    defaultPosition: z.<PERSON>odNumber;
    defaultTimeframe: z.<PERSON>od<PERSON><typeof ChartTimeframe>;
    riskCalculationType: z.<PERSON>od<PERSON><typeof RiskCalculationType>;
    defaultCommission: z.Zod<PERSON>;
    defaultFees: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    defaultRisk: number;
    defaultPosition: number;
    defaultTimeframe: ChartTimeframe;
    riskCalculationType: RiskCalculationType;
    defaultCommission: number;
    defaultFees: number;
}, {
    defaultRisk: number;
    defaultPosition: number;
    defaultTimeframe: ChartTimeframe;
    riskCalculationType: RiskCalculationType;
    defaultCommission: number;
    defaultFees: number;
}>;
export declare const DisplaySettingsSchema: z.ZodObject<{
    theme: z.ZodString;
    pnlDisplayMode: z.ZodNativeEnum<typeof PnLDisplayMode>;
    showRunningPnL: z.ZodBoolean;
    showEquityCurve: z.ZodBoolean;
    showTradeStatistics: z.ZodBoolean;
    compactMode: z.ZodBoolean;
    dateFormat: z.ZodString;
    timeFormat: z.ZodString;
    timezone: z.ZodString;
    goalNotificationsEnabled: z.ZodDefault<z.ZodBoolean>;
    weeklyReportsEnabled: z.ZodDefault<z.ZodBoolean>;
    monthlyReportsEnabled: z.ZodDefault<z.ZodBoolean>;
    tradeAlertsEnabled: z.ZodDefault<z.ZodBoolean>;
    marketNewsEnabled: z.ZodDefault<z.ZodBoolean>;
    priceAlertsEnabled: z.ZodDefault<z.ZodBoolean>;
    journalRemindersEnabled: z.ZodDefault<z.ZodBoolean>;
    emailNotificationsEnabled: z.ZodDefault<z.ZodBoolean>;
    pushNotificationsEnabled: z.ZodDefault<z.ZodBoolean>;
    smsNotificationsEnabled: z.ZodDefault<z.ZodBoolean>;
    defaultAccountView: z.ZodDefault<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    timezone: string;
    theme: string;
    pnlDisplayMode: PnLDisplayMode;
    showRunningPnL: boolean;
    showEquityCurve: boolean;
    showTradeStatistics: boolean;
    compactMode: boolean;
    dateFormat: string;
    timeFormat: string;
    goalNotificationsEnabled: boolean;
    weeklyReportsEnabled: boolean;
    monthlyReportsEnabled: boolean;
    tradeAlertsEnabled: boolean;
    marketNewsEnabled: boolean;
    priceAlertsEnabled: boolean;
    journalRemindersEnabled: boolean;
    emailNotificationsEnabled: boolean;
    pushNotificationsEnabled: boolean;
    smsNotificationsEnabled: boolean;
    defaultAccountView: string;
}, {
    timezone: string;
    theme: string;
    pnlDisplayMode: PnLDisplayMode;
    showRunningPnL: boolean;
    showEquityCurve: boolean;
    showTradeStatistics: boolean;
    compactMode: boolean;
    dateFormat: string;
    timeFormat: string;
    goalNotificationsEnabled?: boolean | undefined;
    weeklyReportsEnabled?: boolean | undefined;
    monthlyReportsEnabled?: boolean | undefined;
    tradeAlertsEnabled?: boolean | undefined;
    marketNewsEnabled?: boolean | undefined;
    priceAlertsEnabled?: boolean | undefined;
    journalRemindersEnabled?: boolean | undefined;
    emailNotificationsEnabled?: boolean | undefined;
    pushNotificationsEnabled?: boolean | undefined;
    smsNotificationsEnabled?: boolean | undefined;
    defaultAccountView?: string | undefined;
}>;
export declare const BacktestSettingsSchema: z.ZodObject<{
    initialCapital: z.ZodNumber;
    useFixedPosition: z.ZodBoolean;
    maxRiskPerTrade: z.ZodNumber;
    maxOpenTrades: z.ZodNumber;
    includeFees: z.ZodBoolean;
    includeSlippage: z.ZodBoolean;
    slippageAmount: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    initialCapital: number;
    useFixedPosition: boolean;
    maxRiskPerTrade: number;
    maxOpenTrades: number;
    includeFees: boolean;
    includeSlippage: boolean;
    slippageAmount: number;
}, {
    initialCapital: number;
    useFixedPosition: boolean;
    maxRiskPerTrade: number;
    maxOpenTrades: number;
    includeFees: boolean;
    includeSlippage: boolean;
    slippageAmount: number;
}>;
export declare const GoalSettingsSchema: z.ZodObject<{
    activeMetrics: z.ZodRecord<z.ZodString, z.ZodBoolean>;
    metricGoals: z.ZodRecord<z.ZodString, z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    activeMetrics: Record<string, boolean>;
    metricGoals: Record<string, number>;
}, {
    activeMetrics: Record<string, boolean>;
    metricGoals: Record<string, number>;
}>;
export declare const TradingAccountSchema: z.ZodObject<{
    id: z.ZodString,
    name: z.ZodString,
    provider: z.ZodString,
    status: z.ZodNativeEnum<typeof AccountStatus>,
    lastSynced: z.ZodDate,
    credentials: z.ZodRecord<z.ZodString, z.ZodString>,
}, "strip", z.ZodTypeAny>;
export declare const ApiKeySchema: z.ZodObject<{
    id: z.ZodString,
    name: z.ZodString,
    key: z.ZodString,
    secret: z.ZodOptional<z.ZodString>,
    status: z.ZodEnum<["ACTIVE", "INACTIVE", "EXPIRED"]>,
    expiryDate: z.ZodOptional<z.ZodDate>,
}, "strip", z.ZodTypeAny>;
export declare const AccountSettingsSchema: z.ZodObject<{
    tradingAccounts: z.ZodArray<typeof TradingAccountSchema>,
    apiKeys: z.ZodArray<typeof ApiKeySchema>,
    autoSync: z.ZodBoolean,
    syncFrequency: z.ZodNumber,
}, "strip", z.ZodTypeAny>;
export declare const SettingsSchema: z.ZodObject<{
    userId: z.ZodString;
    tradeDefaults: z.ZodObject<{
        defaultRisk: z.ZodNumber;
        defaultPosition: z.ZodNumber;
        defaultTimeframe: z.ZodNativeEnum<typeof ChartTimeframe>;
        riskCalculationType: z.ZodNativeEnum<typeof RiskCalculationType>;
        defaultCommission: z.ZodNumber;
        defaultFees: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        defaultRisk: number;
        defaultPosition: number;
        defaultTimeframe: ChartTimeframe;
        riskCalculationType: RiskCalculationType;
        defaultCommission: number;
        defaultFees: number;
    }, {
        defaultRisk: number;
        defaultPosition: number;
        defaultTimeframe: ChartTimeframe;
        riskCalculationType: RiskCalculationType;
        defaultCommission: number;
        defaultFees: number;
    }>;
    display: z.ZodObject<{
        theme: z.ZodString;
        pnlDisplayMode: z.ZodNativeEnum<typeof PnLDisplayMode>;
        showRunningPnL: z.ZodBoolean;
        showEquityCurve: z.ZodBoolean;
        showTradeStatistics: z.ZodBoolean;
        compactMode: z.ZodBoolean;
        dateFormat: z.ZodString;
        timeFormat: z.ZodString;
        timezone: z.ZodString;
        goalNotificationsEnabled: z.ZodDefault<z.ZodBoolean>;
        weeklyReportsEnabled: z.ZodDefault<z.ZodBoolean>;
        monthlyReportsEnabled: z.ZodDefault<z.ZodBoolean>;
        tradeAlertsEnabled: z.ZodDefault<z.ZodBoolean>;
        marketNewsEnabled: z.ZodDefault<z.ZodBoolean>;
        priceAlertsEnabled: z.ZodDefault<z.ZodBoolean>;
        journalRemindersEnabled: z.ZodDefault<z.ZodBoolean>;
        emailNotificationsEnabled: z.ZodDefault<z.ZodBoolean>;
        pushNotificationsEnabled: z.ZodDefault<z.ZodBoolean>;
        smsNotificationsEnabled: z.ZodDefault<z.ZodBoolean>;
        defaultAccountView: z.ZodDefault<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        timezone: string;
        theme: string;
        pnlDisplayMode: PnLDisplayMode;
        showRunningPnL: boolean;
        showEquityCurve: boolean;
        showTradeStatistics: boolean;
        compactMode: boolean;
        dateFormat: string;
        timeFormat: string;
        goalNotificationsEnabled: boolean;
        weeklyReportsEnabled: boolean;
        monthlyReportsEnabled: boolean;
        tradeAlertsEnabled: boolean;
        marketNewsEnabled: boolean;
        priceAlertsEnabled: boolean;
        journalRemindersEnabled: boolean;
        emailNotificationsEnabled: boolean;
        pushNotificationsEnabled: boolean;
        smsNotificationsEnabled: boolean;
        defaultAccountView: string;
    }, {
        timezone: string;
        theme: string;
        pnlDisplayMode: PnLDisplayMode;
        showRunningPnL: boolean;
        showEquityCurve: boolean;
        showTradeStatistics: boolean;
        compactMode: boolean;
        dateFormat: string;
        timeFormat: string;
        goalNotificationsEnabled?: boolean | undefined;
        weeklyReportsEnabled?: boolean | undefined;
        monthlyReportsEnabled?: boolean | undefined;
        tradeAlertsEnabled?: boolean | undefined;
        marketNewsEnabled?: boolean | undefined;
        priceAlertsEnabled?: boolean | undefined;
        journalRemindersEnabled?: boolean | undefined;
        emailNotificationsEnabled?: boolean | undefined;
        pushNotificationsEnabled?: boolean | undefined;
        smsNotificationsEnabled?: boolean | undefined;
        defaultAccountView?: string | undefined;
    }>;
    backtest: z.ZodObject<{
        initialCapital: z.ZodNumber;
        useFixedPosition: z.ZodBoolean;
        maxRiskPerTrade: z.ZodNumber;
        maxOpenTrades: z.ZodNumber;
        includeFees: z.ZodBoolean;
        includeSlippage: z.ZodBoolean;
        slippageAmount: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        initialCapital: number;
        useFixedPosition: boolean;
        maxRiskPerTrade: number;
        maxOpenTrades: number;
        includeFees: boolean;
        includeSlippage: boolean;
        slippageAmount: number;
    }, {
        initialCapital: number;
        useFixedPosition: boolean;
        maxRiskPerTrade: number;
        maxOpenTrades: number;
        includeFees: boolean;
        includeSlippage: boolean;
        slippageAmount: number;
    }>;
    goals: z.ZodObject<{
        activeMetrics: z.ZodRecord<z.ZodString, z.ZodBoolean>;
        metricGoals: z.ZodRecord<z.ZodString, z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        activeMetrics: Record<string, boolean>;
        metricGoals: Record<string, number>;
    }, {
        activeMetrics: Record<string, boolean>;
        metricGoals: Record<string, number>;
    }>;
    autoSync: z.ZodBoolean;
    autoBackup: z.ZodBoolean;
    backupFrequency: z.ZodNumber;
    updatedAt: z.ZodDate;
    accounts: typeof AccountSettingsSchema;
}, "strip", z.ZodTypeAny, {
    userId: string;
    updatedAt: Date;
    tradeDefaults: {
        defaultRisk: number;
        defaultPosition: number;
        defaultTimeframe: ChartTimeframe;
        riskCalculationType: RiskCalculationType;
        defaultCommission: number;
        defaultFees: number;
    };
    display: {
        timezone: string;
        theme: string;
        pnlDisplayMode: PnLDisplayMode;
        showRunningPnL: boolean;
        showEquityCurve: boolean;
        showTradeStatistics: boolean;
        compactMode: boolean;
        dateFormat: string;
        timeFormat: string;
        goalNotificationsEnabled: boolean;
        weeklyReportsEnabled: boolean;
        monthlyReportsEnabled: boolean;
        tradeAlertsEnabled: boolean;
        marketNewsEnabled: boolean;
        priceAlertsEnabled: boolean;
        journalRemindersEnabled: boolean;
        emailNotificationsEnabled: boolean;
        pushNotificationsEnabled: boolean;
        smsNotificationsEnabled: boolean;
        defaultAccountView: string;
    };
    backtest: {
        initialCapital: number;
        useFixedPosition: boolean;
        maxRiskPerTrade: number;
        maxOpenTrades: number;
        includeFees: boolean;
        includeSlippage: boolean;
        slippageAmount: number;
    };
    goals: z.infer<typeof GoalSettingsSchema>;
    autoSync: boolean;
    autoBackup: boolean;
    backupFrequency: number;
    accounts: typeof AccountSettingsSchema;
}, {
    userId: string;
    updatedAt: Date;
    tradeDefaults: {
        defaultRisk: number;
        defaultPosition: number;
        defaultTimeframe: ChartTimeframe;
        riskCalculationType: RiskCalculationType;
        defaultCommission: number;
        defaultFees: number;
    };
    display: {
        timezone: string;
        theme: string;
        pnlDisplayMode: PnLDisplayMode;
        showRunningPnL: boolean;
        showEquityCurve: boolean;
        showTradeStatistics: boolean;
        compactMode: boolean;
        dateFormat: string;
        timeFormat: string;
        goalNotificationsEnabled?: boolean | undefined;
        weeklyReportsEnabled?: boolean | undefined;
        monthlyReportsEnabled?: boolean | undefined;
        tradeAlertsEnabled?: boolean | undefined;
        marketNewsEnabled?: boolean | undefined;
        priceAlertsEnabled?: boolean | undefined;
        journalRemindersEnabled?: boolean | undefined;
        emailNotificationsEnabled?: boolean | undefined;
        pushNotificationsEnabled?: boolean | undefined;
        smsNotificationsEnabled?: boolean | undefined;
        defaultAccountView?: string | undefined;
    };
    backtest: {
        initialCapital: number;
        useFixedPosition: boolean;
        maxRiskPerTrade: number;
        maxOpenTrades: number;
        includeFees: boolean;
        includeSlippage: boolean;
        slippageAmount: number;
    };
    goals: z.infer<typeof GoalSettingsSchema>;
    autoSync: boolean;
    autoBackup: boolean;
    backupFrequency: number;
    accounts: typeof AccountSettingsSchema;
}>;
export type TradeDefaults = z.infer<typeof TradeDefaultsSchema>;
export type DisplaySettings = z.infer<typeof DisplaySettingsSchema>;
export type BacktestSettings = z.infer<typeof BacktestSettingsSchema>;
export type GoalSettings = z.infer<typeof GoalSettingsSchema>;
export type Settings = z.infer<typeof SettingsSchema>;
export declare class SettingsEntity implements Settings {
    userId: string;
    tradeDefaults: TradeDefaults;
    display: DisplaySettings;
    backtest: BacktestSettings;
    goals: GoalSettings;
    autoSync: boolean;
    autoBackup: boolean;
    backupFrequency: number;
    updatedAt: Date;
    accounts: typeof AccountSettingsSchema;
    constructor(settings: Settings);
    updateTradeDefaults(defaults: Partial<TradeDefaults>): void;
    updateDisplaySettings(display: Partial<DisplaySettings>): void;
    updateBacktestSettings(backtest: Partial<BacktestSettings>): void;
    toJSON(): Settings;
}
