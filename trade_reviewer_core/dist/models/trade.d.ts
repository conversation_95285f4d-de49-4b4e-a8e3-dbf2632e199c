import { z } from 'zod';
export declare enum TradeType {
    LONG = "LONG",
    SHORT = "SHORT"
}
export declare enum TradeStatus {
    OPEN = "OPEN",
    CLOSED = "CLOSED",
    CANCELLED = "CANCELLED"
}
export interface RiskRewardRatio {
    risk: number;
    reward: number;
    ratio: number;
}
export declare const TradeSchema: z.ZodObject<{
    id: z.ZodString;
    userId: z.ZodString;
    symbol: z.ZodString;
    type: z.ZodNativeEnum<typeof TradeType>;
    status: z.ZodNativeEnum<typeof TradeStatus>;
    entryPrice: z.ZodNumber;
    exitPrice: z.ZodOptional<z.ZodNumber>;
    stopLoss: z.ZodNumber;
    takeProfit: z.ZodNumber;
    quantity: z.ZodNumber;
    fees: z.ZodDefault<z.ZodNumber>;
    tags: z.ZodDefault<z.ZodArray<z.ZodString, "many">>;
    notes: z.ZodOptional<z.ZodString>;
    entryDate: z.ZodDate;
    exitDate: z.ZodOptional<z.ZodDate>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    symbol: string;
    type: TradeType;
    status: TradeStatus;
    userId: string;
    updatedAt: Date;
    id: string;
    createdAt: Date;
    entryPrice: number;
    stopLoss: number;
    takeProfit: number;
    quantity: number;
    fees: number;
    tags: string[];
    entryDate: Date;
    exitPrice?: number | undefined;
    notes?: string | undefined;
    exitDate?: Date | undefined;
}, {
    symbol: string;
    type: TradeType;
    status: TradeStatus;
    userId: string;
    updatedAt: Date;
    id: string;
    createdAt: Date;
    entryPrice: number;
    stopLoss: number;
    takeProfit: number;
    quantity: number;
    entryDate: Date;
    exitPrice?: number | undefined;
    fees?: number | undefined;
    tags?: string[] | undefined;
    notes?: string | undefined;
    exitDate?: Date | undefined;
}>;
export type Trade = z.infer<typeof TradeSchema>;
export declare class TradeEntity implements Trade {
    id: string;
    userId: string;
    symbol: string;
    type: TradeType;
    status: TradeStatus;
    entryPrice: number;
    exitPrice?: number;
    stopLoss: number;
    takeProfit: number;
    quantity: number;
    fees: number;
    tags: string[];
    notes?: string;
    entryDate: Date;
    exitDate?: Date;
    createdAt: Date;
    updatedAt: Date;
    constructor(data: Trade);
    calculatePnL(): number;
    calculateRRR(): RiskRewardRatio | null;
    close(exitPrice: number, exitDate?: Date): void;
    cancel(): void;
    addTags(newTags: string[]): void;
    removeTags(tagsToRemove: string[]): void;
    updateStopLoss(price: number): void;
    updateTakeProfit(price: number): void;
    addNotes(notes: string): void;
}
