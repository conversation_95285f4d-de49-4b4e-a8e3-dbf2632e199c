import { z } from 'zod';
export declare enum TimeFrame {
    ALL = "ALL",
    DAILY = "DAILY",
    WEEKLY = "WEEKLY",
    MONTHLY = "MONTHLY",
    YEARLY = "YEARLY",
    CUSTOM = "CUSTOM"
}
export declare const AnalyticsMetricsSchema: z.ZodObject<{
    totalTrades: z.ZodNumber;
    winningTrades: z.ZodNumber;
    losingTrades: z.ZodNumber;
    breakEvenTrades: z.ZodNumber;
    totalPnL: z.ZodNumber;
    totalRiskAmount: z.ZodNumber;
    totalRewardAmount: z.Zod<PERSON>umber;
    averageRRR: z.ZodNumber;
    winRate: z.ZodNumber;
    profitFactor: z.ZodNumber;
    averageWinAmount: z.ZodNumber;
    averageLossAmount: z.ZodNumber;
    largestWin: z.ZodNumber;
    largestLoss: z.ZodNumber;
    totalFees: z.<PERSON><PERSON>;
}, "strip", z.<PERSON>odTypeAny, {
    totalTrades: number;
    winningTrades: number;
    losingTrades: number;
    breakEvenTrades: number;
    totalPnL: number;
    totalRiskAmount: number;
    totalRewardAmount: number;
    averageRRR: number;
    winRate: number;
    profitFactor: number;
    averageWinAmount: number;
    averageLossAmount: number;
    largestWin: number;
    largestLoss: number;
    totalFees: number;
}, {
    totalTrades: number;
    winningTrades: number;
    losingTrades: number;
    breakEvenTrades: number;
    totalPnL: number;
    totalRiskAmount: number;
    totalRewardAmount: number;
    averageRRR: number;
    winRate: number;
    profitFactor: number;
    averageWinAmount: number;
    averageLossAmount: number;
    largestWin: number;
    largestLoss: number;
    totalFees: number;
}>;
export declare const AnalyticsSchema: z.ZodObject<{
    userId: z.ZodString;
    timeFrame: z.ZodNativeEnum<typeof TimeFrame>;
    startDate: z.ZodDate;
    endDate: z.ZodDate;
    metrics: z.ZodObject<{
        totalTrades: z.ZodNumber;
        winningTrades: z.ZodNumber;
        losingTrades: z.ZodNumber;
        breakEvenTrades: z.ZodNumber;
        totalPnL: z.ZodNumber;
        totalRiskAmount: z.ZodNumber;
        totalRewardAmount: z.ZodNumber;
        averageRRR: z.ZodNumber;
        winRate: z.ZodNumber;
        profitFactor: z.ZodNumber;
        averageWinAmount: z.ZodNumber;
        averageLossAmount: z.ZodNumber;
        largestWin: z.ZodNumber;
        largestLoss: z.ZodNumber;
        totalFees: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        totalTrades: number;
        winningTrades: number;
        losingTrades: number;
        breakEvenTrades: number;
        totalPnL: number;
        totalRiskAmount: number;
        totalRewardAmount: number;
        averageRRR: number;
        winRate: number;
        profitFactor: number;
        averageWinAmount: number;
        averageLossAmount: number;
        largestWin: number;
        largestLoss: number;
        totalFees: number;
    }, {
        totalTrades: number;
        winningTrades: number;
        losingTrades: number;
        breakEvenTrades: number;
        totalPnL: number;
        totalRiskAmount: number;
        totalRewardAmount: number;
        averageRRR: number;
        winRate: number;
        profitFactor: number;
        averageWinAmount: number;
        averageLossAmount: number;
        largestWin: number;
        largestLoss: number;
        totalFees: number;
    }>;
    updatedAt: z.ZodDate;
    byType: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodObject<{
        totalTrades: z.ZodNumber;
        winningTrades: z.ZodNumber;
        losingTrades: z.ZodNumber;
        breakEvenTrades: z.ZodNumber;
        totalPnL: z.ZodNumber;
        totalRiskAmount: z.ZodNumber;
        totalRewardAmount: z.ZodNumber;
        averageRRR: z.ZodNumber;
        winRate: z.ZodNumber;
        profitFactor: z.ZodNumber;
        averageWinAmount: z.ZodNumber;
        averageLossAmount: z.ZodNumber;
        largestWin: z.ZodNumber;
        largestLoss: z.ZodNumber;
        totalFees: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        totalTrades: number;
        winningTrades: number;
        losingTrades: number;
        breakEvenTrades: number;
        totalPnL: number;
        totalRiskAmount: number;
        totalRewardAmount: number;
        averageRRR: number;
        winRate: number;
        profitFactor: number;
        averageWinAmount: number;
        averageLossAmount: number;
        largestWin: number;
        largestLoss: number;
        totalFees: number;
    }, {
        totalTrades: number;
        winningTrades: number;
        losingTrades: number;
        breakEvenTrades: number;
        totalPnL: number;
        totalRiskAmount: number;
        totalRewardAmount: number;
        averageRRR: number;
        winRate: number;
        profitFactor: number;
        averageWinAmount: number;
        averageLossAmount: number;
        largestWin: number;
        largestLoss: number;
        totalFees: number;
    }>>>;
    bySymbol: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodObject<{
        totalTrades: z.ZodNumber;
        winningTrades: z.ZodNumber;
        losingTrades: z.ZodNumber;
        breakEvenTrades: z.ZodNumber;
        totalPnL: z.ZodNumber;
        totalRiskAmount: z.ZodNumber;
        totalRewardAmount: z.ZodNumber;
        averageRRR: z.ZodNumber;
        winRate: z.ZodNumber;
        profitFactor: z.ZodNumber;
        averageWinAmount: z.ZodNumber;
        averageLossAmount: z.ZodNumber;
        largestWin: z.ZodNumber;
        largestLoss: z.ZodNumber;
        totalFees: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        totalTrades: number;
        winningTrades: number;
        losingTrades: number;
        breakEvenTrades: number;
        totalPnL: number;
        totalRiskAmount: number;
        totalRewardAmount: number;
        averageRRR: number;
        winRate: number;
        profitFactor: number;
        averageWinAmount: number;
        averageLossAmount: number;
        largestWin: number;
        largestLoss: number;
        totalFees: number;
    }, {
        totalTrades: number;
        winningTrades: number;
        losingTrades: number;
        breakEvenTrades: number;
        totalPnL: number;
        totalRiskAmount: number;
        totalRewardAmount: number;
        averageRRR: number;
        winRate: number;
        profitFactor: number;
        averageWinAmount: number;
        averageLossAmount: number;
        largestWin: number;
        largestLoss: number;
        totalFees: number;
    }>>>;
}, "strip", z.ZodTypeAny, {
    userId: string;
    timeFrame: TimeFrame;
    startDate: Date;
    endDate: Date;
    metrics: {
        totalTrades: number;
        winningTrades: number;
        losingTrades: number;
        breakEvenTrades: number;
        totalPnL: number;
        totalRiskAmount: number;
        totalRewardAmount: number;
        averageRRR: number;
        winRate: number;
        profitFactor: number;
        averageWinAmount: number;
        averageLossAmount: number;
        largestWin: number;
        largestLoss: number;
        totalFees: number;
    };
    updatedAt: Date;
    byType?: Record<string, {
        totalTrades: number;
        winningTrades: number;
        losingTrades: number;
        breakEvenTrades: number;
        totalPnL: number;
        totalRiskAmount: number;
        totalRewardAmount: number;
        averageRRR: number;
        winRate: number;
        profitFactor: number;
        averageWinAmount: number;
        averageLossAmount: number;
        largestWin: number;
        largestLoss: number;
        totalFees: number;
    }> | undefined;
    bySymbol?: Record<string, {
        totalTrades: number;
        winningTrades: number;
        losingTrades: number;
        breakEvenTrades: number;
        totalPnL: number;
        totalRiskAmount: number;
        totalRewardAmount: number;
        averageRRR: number;
        winRate: number;
        profitFactor: number;
        averageWinAmount: number;
        averageLossAmount: number;
        largestWin: number;
        largestLoss: number;
        totalFees: number;
    }> | undefined;
}, {
    userId: string;
    timeFrame: TimeFrame;
    startDate: Date;
    endDate: Date;
    metrics: {
        totalTrades: number;
        winningTrades: number;
        losingTrades: number;
        breakEvenTrades: number;
        totalPnL: number;
        totalRiskAmount: number;
        totalRewardAmount: number;
        averageRRR: number;
        winRate: number;
        profitFactor: number;
        averageWinAmount: number;
        averageLossAmount: number;
        largestWin: number;
        largestLoss: number;
        totalFees: number;
    };
    updatedAt: Date;
    byType?: Record<string, {
        totalTrades: number;
        winningTrades: number;
        losingTrades: number;
        breakEvenTrades: number;
        totalPnL: number;
        totalRiskAmount: number;
        totalRewardAmount: number;
        averageRRR: number;
        winRate: number;
        profitFactor: number;
        averageWinAmount: number;
        averageLossAmount: number;
        largestWin: number;
        largestLoss: number;
        totalFees: number;
    }> | undefined;
    bySymbol?: Record<string, {
        totalTrades: number;
        winningTrades: number;
        losingTrades: number;
        breakEvenTrades: number;
        totalPnL: number;
        totalRiskAmount: number;
        totalRewardAmount: number;
        averageRRR: number;
        winRate: number;
        profitFactor: number;
        averageWinAmount: number;
        averageLossAmount: number;
        largestWin: number;
        largestLoss: number;
        totalFees: number;
    }> | undefined;
}>;
export type AnalyticsMetrics = z.infer<typeof AnalyticsMetricsSchema>;
export type Analytics = z.infer<typeof AnalyticsSchema>;
export declare class AnalyticsEntity implements Analytics {
    userId: string;
    timeFrame: TimeFrame;
    startDate: Date;
    endDate: Date;
    metrics: AnalyticsMetrics;
    updatedAt: Date;
    byType?: Record<string, AnalyticsMetrics>;
    bySymbol?: Record<string, AnalyticsMetrics>;
    constructor(data: Analytics);
    get totalTrades(): number;
    get winRate(): number;
    get profitFactor(): number;
    get averageRRR(): number;
    get totalPnL(): number;
    get totalFees(): number;
    get netPnL(): number;
}
