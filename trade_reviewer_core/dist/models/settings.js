"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SettingsEntity = exports.SettingsSchema = exports.BacktestSettingsSchema = exports.DisplaySettingsSchema = exports.TradeDefaultsSchema = exports.PnLDisplayMode = exports.RiskCalculationType = exports.ChartTimeframe = void 0;
const zod_1 = require("zod");
var ChartTimeframe;
(function (ChartTimeframe) {
    ChartTimeframe["M1"] = "M1";
    ChartTimeframe["M5"] = "M5";
    ChartTimeframe["M15"] = "M15";
    ChartTimeframe["M30"] = "M30";
    ChartTimeframe["H1"] = "H1";
    ChartTimeframe["H4"] = "H4";
    ChartTimeframe["D1"] = "D1";
    ChartTimeframe["W1"] = "W1";
    ChartTimeframe["MN"] = "MN";
})(ChartTimeframe || (exports.ChartTimeframe = ChartTimeframe = {}));
var RiskCalculationType;
(function (RiskCalculationType) {
    RiskCalculationType["FIXED"] = "FIXED";
    RiskCalculationType["PERCENTAGE"] = "PERCENTAGE";
    RiskCalculationType["R_MULTIPLE"] = "R_MULTIPLE";
})(RiskCalculationType || (exports.RiskCalculationType = RiskCalculationType = {}));
var PnLDisplayMode;
(function (PnLDisplayMode) {
    PnLDisplayMode["CURRENCY"] = "CURRENCY";
    PnLDisplayMode["PERCENTAGE"] = "PERCENTAGE";
    PnLDisplayMode["R_MULTIPLE"] = "R_MULTIPLE";
})(PnLDisplayMode || (exports.PnLDisplayMode = PnLDisplayMode = {}));
exports.TradeDefaultsSchema = zod_1.z.object({
    defaultRisk: zod_1.z.number().min(0),
    defaultPosition: zod_1.z.number().min(0),
    defaultTimeframe: zod_1.z.nativeEnum(ChartTimeframe),
    riskCalculationType: zod_1.z.nativeEnum(RiskCalculationType),
    defaultCommission: zod_1.z.number().min(0),
    defaultFees: zod_1.z.number().min(0)
});
exports.DisplaySettingsSchema = zod_1.z.object({
    theme: zod_1.z.string(),
    pnlDisplayMode: zod_1.z.nativeEnum(PnLDisplayMode),
    showRunningPnL: zod_1.z.boolean(),
    showEquityCurve: zod_1.z.boolean(),
    showTradeStatistics: zod_1.z.boolean(),
    compactMode: zod_1.z.boolean(),
    dateFormat: zod_1.z.string(),
    timeFormat: zod_1.z.string(),
    timezone: zod_1.z.string(),
    goalNotificationsEnabled: zod_1.z.boolean().default(true),
    weeklyReportsEnabled: zod_1.z.boolean().default(true),
    monthlyReportsEnabled: zod_1.z.boolean().default(true),
    tradeAlertsEnabled: zod_1.z.boolean().default(true),
    marketNewsEnabled: zod_1.z.boolean().default(true),
    priceAlertsEnabled: zod_1.z.boolean().default(true),
    journalRemindersEnabled: zod_1.z.boolean().default(true),
    emailNotificationsEnabled: zod_1.z.boolean().default(true),
    pushNotificationsEnabled: zod_1.z.boolean().default(true),
    smsNotificationsEnabled: zod_1.z.boolean().default(false),
});
exports.BacktestSettingsSchema = zod_1.z.object({
    initialCapital: zod_1.z.number().min(0),
    useFixedPosition: zod_1.z.boolean(),
    maxRiskPerTrade: zod_1.z.number().min(0),
    maxOpenTrades: zod_1.z.number().min(1),
    includeFees: zod_1.z.boolean(),
    includeSlippage: zod_1.z.boolean(),
    slippageAmount: zod_1.z.number().min(0)
});
exports.SettingsSchema = zod_1.z.object({
    userId: zod_1.z.string(),
    tradeDefaults: exports.TradeDefaultsSchema,
    display: exports.DisplaySettingsSchema,
    backtest: exports.BacktestSettingsSchema,
    autoSync: zod_1.z.boolean(),
    autoBackup: zod_1.z.boolean(),
    backupFrequency: zod_1.z.number().min(1),
    updatedAt: zod_1.z.date()
});
class SettingsEntity {
    constructor(settings) {
        const validated = exports.SettingsSchema.parse(settings);
        Object.assign(this, validated);
    }
    updateTradeDefaults(defaults) {
        this.tradeDefaults = {
            ...this.tradeDefaults,
            ...defaults
        };
        this.updatedAt = new Date();
    }
    updateDisplaySettings(display) {
        this.display = {
            ...this.display,
            ...display
        };
        this.updatedAt = new Date();
    }
    updateBacktestSettings(backtest) {
        this.backtest = {
            ...this.backtest,
            ...backtest
        };
        this.updatedAt = new Date();
    }
    toJSON() {
        return {
            userId: this.userId,
            tradeDefaults: this.tradeDefaults,
            display: this.display,
            backtest: this.backtest,
            autoSync: this.autoSync,
            autoBackup: this.autoBackup,
            backupFrequency: this.backupFrequency,
            updatedAt: this.updatedAt
        };
    }
}
exports.SettingsEntity = SettingsEntity;
