import { z } from 'zod';
export declare enum GoalType {
    WIN_RATE = "WIN_RATE",
    PROFIT = "PROFIT",
    AVERAGE_RRR = "AVERAGE_RRR",
    CONSECUTIVE_WINS = "CONSECUTIVE_WINS",
    MAX_DRAWDOWN = "MAX_DRAWDOWN",
    PROFIT_FACTOR = "PROFIT_FACTOR",
    SHARPE_RATIO = "SHARPE_RATIO",
    TRADE_COUNT = "TRADE_COUNT"
}
export declare const GoalSchema: z.ZodObject<{
    id: z.ZodString;
    userId: z.ZodString;
    type: z.ZodNativeEnum<typeof GoalType>;
    target: z.ZodNumber;
    progress: z.<PERSON>odDefault<z.ZodNumber>;
    isActive: z.ZodDefault<z.ZodBoolean>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    type: GoalType;
    userId: string;
    updatedAt: Date;
    id: string;
    target: number;
    progress: number;
    isActive: boolean;
    createdAt: Date;
}, {
    type: GoalType;
    userId: string;
    updatedAt: Date;
    id: string;
    target: number;
    createdAt: Date;
    progress?: number | undefined;
    isActive?: boolean | undefined;
}>;
export type Goal = z.infer<typeof GoalSchema>;
export declare class GoalEntity implements Goal {
    id: string;
    userId: string;
    type: GoalType;
    target: number;
    progress: number;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
    constructor(data: Goal);
}
