"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationEntity = exports.NotificationSchema = exports.NotificationTriggerSchema = exports.NotificationChannel = exports.NotificationPriority = exports.NotificationType = void 0;
const zod_1 = require("zod");
var NotificationType;
(function (NotificationType) {
    NotificationType["TRADE_ALERT"] = "TRADE_ALERT";
    NotificationType["PRICE_ALERT"] = "PRICE_ALERT";
    NotificationType["GOAL_ACHIEVED"] = "GOAL_ACHIEVED";
    NotificationType["RISK_WARNING"] = "RISK_WARNING";
    NotificationType["SYSTEM_UPDATE"] = "SYSTEM_UPDATE";
    NotificationType["BACKUP_REMINDER"] = "BACKUP_REMINDER";
    NotificationType["JOURNAL_REMINDER"] = "JOURNAL_REMINDER";
    NotificationType["TRADE_STATS"] = "TRADE_STATS";
})(NotificationType || (exports.NotificationType = NotificationType = {}));
var NotificationPriority;
(function (NotificationPriority) {
    NotificationPriority["LOW"] = "LOW";
    NotificationPriority["MEDIUM"] = "MEDIUM";
    NotificationPriority["HIGH"] = "HIGH";
    NotificationPriority["URGENT"] = "URGENT";
})(NotificationPriority || (exports.NotificationPriority = NotificationPriority = {}));
var NotificationChannel;
(function (NotificationChannel) {
    NotificationChannel["IN_APP"] = "IN_APP";
    NotificationChannel["EMAIL"] = "EMAIL";
    NotificationChannel["PUSH"] = "PUSH";
    NotificationChannel["SMS"] = "SMS";
})(NotificationChannel || (exports.NotificationChannel = NotificationChannel = {}));
exports.NotificationTriggerSchema = zod_1.z.object({
    type: zod_1.z.nativeEnum(NotificationType),
    conditions: zod_1.z.record(zod_1.z.string(), zod_1.z.any()),
    channels: zod_1.z.array(zod_1.z.nativeEnum(NotificationChannel)).min(1),
    schedule: zod_1.z.string().optional(), // cron expression
    active: zod_1.z.boolean().default(true)
});
exports.NotificationSchema = zod_1.z.object({
    id: zod_1.z.string().uuid(),
    userId: zod_1.z.string().uuid(),
    type: zod_1.z.nativeEnum(NotificationType),
    title: zod_1.z.string().min(1),
    message: zod_1.z.string().min(1),
    priority: zod_1.z.nativeEnum(NotificationPriority).default(NotificationPriority.MEDIUM),
    channels: zod_1.z.array(zod_1.z.nativeEnum(NotificationChannel)).min(1),
    data: zod_1.z.record(zod_1.z.string(), zod_1.z.any()).optional(),
    read: zod_1.z.boolean().default(false),
    dismissed: zod_1.z.boolean().default(false),
    scheduledFor: zod_1.z.date().optional(),
    expiresAt: zod_1.z.date().optional(),
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date()
});
class NotificationEntity {
    constructor(data) {
        this.priority = NotificationPriority.MEDIUM;
        this.read = false;
        this.dismissed = false;
        Object.assign(this, exports.NotificationSchema.parse(data));
    }
    get isExpired() {
        return this.expiresAt ? new Date() > this.expiresAt : false;
    }
    get isScheduled() {
        return !!this.scheduledFor && this.scheduledFor > new Date();
    }
    get isUrgent() {
        return this.priority === NotificationPriority.URGENT;
    }
    get shouldDisplay() {
        return !this.dismissed && !this.isExpired && (!this.scheduledFor || !this.isScheduled);
    }
    get channelPreferences() {
        return this.channels.map(channel => channel.toLowerCase());
    }
    markAsRead() {
        this.read = true;
        this.updatedAt = new Date();
    }
    dismiss() {
        this.dismissed = true;
        this.updatedAt = new Date();
    }
    reschedule(newDate) {
        this.scheduledFor = newDate;
        this.updatedAt = new Date();
    }
    updateExpiry(newDate) {
        this.expiresAt = newDate;
        this.updatedAt = new Date();
    }
    addChannel(channel) {
        if (!this.channels.includes(channel)) {
            this.channels.push(channel);
            this.updatedAt = new Date();
        }
    }
    removeChannel(channel) {
        const index = this.channels.indexOf(channel);
        if (index > -1) {
            this.channels.splice(index, 1);
            this.updatedAt = new Date();
        }
    }
    updatePriority(priority) {
        this.priority = priority;
        this.updatedAt = new Date();
    }
}
exports.NotificationEntity = NotificationEntity;
