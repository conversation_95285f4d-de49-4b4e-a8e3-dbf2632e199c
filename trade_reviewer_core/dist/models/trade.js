"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradeEntity = exports.TradeSchema = exports.TradeStatus = exports.TradeType = void 0;
const zod_1 = require("zod");
var TradeType;
(function (TradeType) {
    TradeType["LONG"] = "LONG";
    TradeType["SHORT"] = "SHORT";
})(TradeType || (exports.TradeType = TradeType = {}));
var TradeStatus;
(function (TradeStatus) {
    TradeStatus["OPEN"] = "OPEN";
    TradeStatus["CLOSED"] = "CLOSED";
    TradeStatus["CANCELLED"] = "CANCELLED";
})(TradeStatus || (exports.TradeStatus = TradeStatus = {}));
exports.TradeSchema = zod_1.z.object({
    id: zod_1.z.string().uuid(),
    userId: zod_1.z.string(),
    symbol: zod_1.z.string(),
    type: zod_1.z.nativeEnum(TradeType),
    status: zod_1.z.nativeEnum(TradeStatus),
    entryPrice: zod_1.z.number(),
    exitPrice: zod_1.z.number().optional(),
    stopLoss: zod_1.z.number(),
    takeProfit: zod_1.z.number(),
    quantity: zod_1.z.number(),
    fees: zod_1.z.number().default(0),
    tags: zod_1.z.array(zod_1.z.string()).default([]),
    notes: zod_1.z.string().optional(),
    entryDate: zod_1.z.date(),
    exitDate: zod_1.z.date().optional(),
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date()
});
class TradeEntity {
    constructor(data) {
        this.fees = 0;
        this.tags = [];
        Object.assign(this, exports.TradeSchema.parse(data));
    }
    calculatePnL() {
        if (!this.exitPrice)
            return 0;
        const multiplier = this.type === TradeType.LONG ? 1 : -1;
        return multiplier * (this.exitPrice - this.entryPrice) * this.quantity - this.fees;
    }
    calculateRRR() {
        if (this.status !== TradeStatus.CLOSED || !this.exitPrice)
            return null;
        const risk = Math.abs(this.entryPrice - this.stopLoss) * this.quantity;
        const reward = Math.abs(this.takeProfit - this.entryPrice) * this.quantity;
        const ratio = reward / risk;
        return { risk, reward, ratio };
    }
    close(exitPrice, exitDate = new Date()) {
        this.exitPrice = exitPrice;
        this.exitDate = exitDate;
        this.status = TradeStatus.CLOSED;
        this.updatedAt = new Date();
    }
    cancel() {
        this.status = TradeStatus.CANCELLED;
        this.updatedAt = new Date();
    }
    addTags(newTags) {
        this.tags = [...new Set([...this.tags, ...newTags])];
        this.updatedAt = new Date();
    }
    removeTags(tagsToRemove) {
        this.tags = this.tags.filter(tag => !tagsToRemove.includes(tag));
        this.updatedAt = new Date();
    }
    updateStopLoss(price) {
        this.stopLoss = price;
        this.updatedAt = new Date();
    }
    updateTakeProfit(price) {
        this.takeProfit = price;
        this.updatedAt = new Date();
    }
    addNotes(notes) {
        this.notes = notes;
        this.updatedAt = new Date();
    }
}
exports.TradeEntity = TradeEntity;
