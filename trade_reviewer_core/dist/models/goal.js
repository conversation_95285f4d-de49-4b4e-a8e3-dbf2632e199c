"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoalEntity = exports.GoalSchema = exports.GoalType = void 0;
const zod_1 = require("zod");
var GoalType;
(function (GoalType) {
    GoalType["WIN_RATE"] = "WIN_RATE";
    GoalType["PROFIT"] = "PROFIT";
    GoalType["AVERAGE_RRR"] = "AVERAGE_RRR";
    GoalType["CONSECUTIVE_WINS"] = "CONSECUTIVE_WINS";
    GoalType["MAX_DRAWDOWN"] = "MAX_DRAWDOWN";
    GoalType["PROFIT_FACTOR"] = "PROFIT_FACTOR";
    GoalType["SHARPE_RATIO"] = "SHARPE_RATIO";
    GoalType["TRADE_COUNT"] = "TRADE_COUNT";
})(GoalType || (exports.GoalType = GoalType = {}));
exports.GoalSchema = zod_1.z.object({
    id: zod_1.z.string().uuid(),
    userId: zod_1.z.string(),
    type: zod_1.z.nativeEnum(GoalType),
    target: zod_1.z.number(),
    progress: zod_1.z.number().default(0),
    isActive: zod_1.z.boolean().default(true),
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date()
});
class GoalEntity {
    constructor(data) {
        this.progress = 0;
        this.isActive = true;
        Object.assign(this, exports.GoalSchema.parse(data));
    }
}
exports.GoalEntity = GoalEntity;
