import { z } from 'zod';
export declare enum UserRole {
    USER = "USER",
    PREMIUM = "PREMIUM",
    ADMIN = "ADMIN"
}
export declare enum NotificationPreference {
    ALL = "ALL",
    IMPORTANT = "IMPORTANT",
    NONE = "NONE"
}
export declare const UserPreferencesSchema: z.ZodObject<{
    notificationPreference: z.ZodDefault<z.ZodNativeEnum<typeof NotificationPreference>>;
    darkMode: z.ZodDefault<z.ZodBoolean>;
    defaultCurrency: z.ZodDefault<z.ZodString>;
    timezone: z.ZodDefault<z.ZodString>;
    language: z.ZodDefault<z.ZodString>;
    showPnLInPercent: z.ZodDefault<z.ZodBoolean>;
    autoSyncEnabled: z.ZodDefault<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    notificationPreference: NotificationPreference;
    darkMode: boolean;
    defaultCurrency: string;
    timezone: string;
    language: string;
    showPnLInPercent: boolean;
    autoSyncEnabled: boolean;
}, {
    notificationPreference?: NotificationPreference | undefined;
    darkMode?: boolean | undefined;
    defaultCurrency?: string | undefined;
    timezone?: string | undefined;
    language?: string | undefined;
    showPnLInPercent?: boolean | undefined;
    autoSyncEnabled?: boolean | undefined;
}>;
export declare const UserSchema: z.ZodObject<{
    id: z.ZodString;
    email: z.ZodString;
    name: z.ZodString;
    role: z.ZodDefault<z.ZodNativeEnum<typeof UserRole>>;
    preferences: z.ZodObject<{
        notificationPreference: z.ZodDefault<z.ZodNativeEnum<typeof NotificationPreference>>;
        darkMode: z.ZodDefault<z.ZodBoolean>;
        defaultCurrency: z.ZodDefault<z.ZodString>;
        timezone: z.ZodDefault<z.ZodString>;
        language: z.ZodDefault<z.ZodString>;
        showPnLInPercent: z.ZodDefault<z.ZodBoolean>;
        autoSyncEnabled: z.ZodDefault<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        notificationPreference: NotificationPreference;
        darkMode: boolean;
        defaultCurrency: string;
        timezone: string;
        language: string;
        showPnLInPercent: boolean;
        autoSyncEnabled: boolean;
    }, {
        notificationPreference?: NotificationPreference | undefined;
        darkMode?: boolean | undefined;
        defaultCurrency?: string | undefined;
        timezone?: string | undefined;
        language?: string | undefined;
        showPnLInPercent?: boolean | undefined;
        autoSyncEnabled?: boolean | undefined;
    }>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
    lastLoginAt: z.ZodOptional<z.ZodDate>;
    isEmailVerified: z.ZodDefault<z.ZodBoolean>;
    profileImageUrl: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    updatedAt: Date;
    id: string;
    createdAt: Date;
    email: string;
    name: string;
    role: UserRole;
    preferences: {
        notificationPreference: NotificationPreference;
        darkMode: boolean;
        defaultCurrency: string;
        timezone: string;
        language: string;
        showPnLInPercent: boolean;
        autoSyncEnabled: boolean;
    };
    isEmailVerified: boolean;
    lastLoginAt?: Date | undefined;
    profileImageUrl?: string | undefined;
}, {
    updatedAt: Date;
    id: string;
    createdAt: Date;
    email: string;
    name: string;
    preferences: {
        notificationPreference?: NotificationPreference | undefined;
        darkMode?: boolean | undefined;
        defaultCurrency?: string | undefined;
        timezone?: string | undefined;
        language?: string | undefined;
        showPnLInPercent?: boolean | undefined;
        autoSyncEnabled?: boolean | undefined;
    };
    role?: UserRole | undefined;
    lastLoginAt?: Date | undefined;
    isEmailVerified?: boolean | undefined;
    profileImageUrl?: string | undefined;
}>;
export type UserPreferences = z.infer<typeof UserPreferencesSchema>;
export type User = z.infer<typeof UserSchema>;
export declare class UserEntity implements User {
    id: string;
    email: string;
    name: string;
    role: UserRole;
    preferences: UserPreferences;
    createdAt: Date;
    updatedAt: Date;
    lastLoginAt?: Date;
    isEmailVerified: boolean;
    profileImageUrl?: string;
    constructor(data: User);
    get isAdmin(): boolean;
    get isPremium(): boolean;
    get displayName(): string;
    get canAccessPremiumFeatures(): boolean;
    updatePreferences(newPreferences: Partial<UserPreferences>): void;
    verifyEmail(): void;
    updateLastLogin(): void;
}
