"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsService = void 0;
const trade_1 = require("../models/trade");
const timeframe_1 = require("../models/timeframe");
class AnalyticsService {
    constructor(storageService) {
        this.storageService = storageService;
    }
    async getAnalytics(timeFrame) {
        const trades = await this.storageService.getTrades();
        const tradeEntities = trades.map((trade) => new trade_1.TradeEntity(trade));
        const filteredTrades = this.filterTradesByTimeFrame(tradeEntities, timeFrame);
        const closedTrades = filteredTrades.filter(trade => trade.status === trade_1.TradeStatus.CLOSED);
        if (closedTrades.length === 0) {
            return this.getEmptyAnalytics();
        }
        const winningTrades = closedTrades.filter(trade => trade.calculatePnL() > 0);
        const losingTrades = closedTrades.filter(trade => trade.calculatePnL() < 0);
        const totalPnL = closedTrades.reduce((sum, trade) => sum + trade.calculatePnL(), 0);
        const winningPnL = winningTrades.reduce((sum, trade) => sum + trade.calculatePnL(), 0);
        const losingPnL = losingTrades.reduce((sum, trade) => sum + trade.calculatePnL(), 0);
        const rrrs = closedTrades
            .map(trade => trade.calculateRRR())
            .filter((rrr) => rrr !== null);
        const averageRRR = rrrs.length > 0
            ? rrrs.reduce((sum, rrr) => sum + rrr.ratio, 0) / rrrs.length
            : 0;
        return {
            totalTrades: closedTrades.length,
            winningTrades: winningTrades.length,
            losingTrades: losingTrades.length,
            winRate: (winningTrades.length / closedTrades.length) * 100,
            averageRRR,
            profitFactor: Math.abs(winningPnL / losingPnL) || 0,
            totalPnL,
            averagePnL: totalPnL / closedTrades.length,
            largestWin: Math.max(...winningTrades.map(trade => trade.calculatePnL()), 0),
            largestLoss: Math.min(...losingTrades.map(trade => trade.calculatePnL()), 0),
            averageWin: winningTrades.length > 0 ? winningPnL / winningTrades.length : 0,
            averageLoss: losingTrades.length > 0 ? losingPnL / losingTrades.length : 0,
            consecutiveWins: this.calculateConsecutiveWins(closedTrades),
            consecutiveLosses: this.calculateConsecutiveLosses(closedTrades),
            maxDrawdown: this.calculateMaxDrawdown(closedTrades),
            sharpeRatio: this.calculateSharpeRatio(closedTrades),
            trades: filteredTrades,
        };
    }
    filterTradesByTimeFrame(trades, timeFrame) {
        const now = new Date();
        const startDate = new Date();
        switch (timeFrame) {
            case timeframe_1.TimeFrame.DAY:
                startDate.setDate(now.getDate() - 1);
                break;
            case timeframe_1.TimeFrame.WEEK:
                startDate.setDate(now.getDate() - 7);
                break;
            case timeframe_1.TimeFrame.MONTH:
                startDate.setMonth(now.getMonth() - 1);
                break;
            case timeframe_1.TimeFrame.YEAR:
                startDate.setFullYear(now.getFullYear() - 1);
                break;
            case timeframe_1.TimeFrame.ALL:
                return trades;
        }
        return trades.filter(trade => trade.entryDate >= startDate);
    }
    calculateConsecutiveWins(trades) {
        return this.calculateConsecutive(trades, true);
    }
    calculateConsecutiveLosses(trades) {
        return this.calculateConsecutive(trades, false);
    }
    calculateConsecutive(trades, isWin) {
        let maxConsecutive = 0;
        let currentConsecutive = 0;
        for (const trade of trades) {
            const pnl = trade.calculatePnL();
            if ((isWin && pnl > 0) || (!isWin && pnl < 0)) {
                currentConsecutive++;
                maxConsecutive = Math.max(maxConsecutive, currentConsecutive);
            }
            else {
                currentConsecutive = 0;
            }
        }
        return maxConsecutive;
    }
    calculateMaxDrawdown(trades) {
        let peak = 0;
        let maxDrawdown = 0;
        let currentValue = 0;
        for (const trade of trades) {
            currentValue += trade.calculatePnL();
            peak = Math.max(peak, currentValue);
            maxDrawdown = Math.max(maxDrawdown, peak - currentValue);
        }
        return maxDrawdown;
    }
    calculateSharpeRatio(trades) {
        if (trades.length < 2)
            return 0;
        const returns = trades.map(trade => trade.calculatePnL());
        const averageReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
        const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - averageReturn, 2), 0) / (returns.length - 1);
        const standardDeviation = Math.sqrt(variance);
        return standardDeviation === 0 ? 0 : averageReturn / standardDeviation;
    }
    getEmptyAnalytics() {
        return {
            totalTrades: 0,
            winningTrades: 0,
            losingTrades: 0,
            winRate: 0,
            averageRRR: 0,
            profitFactor: 0,
            totalPnL: 0,
            averagePnL: 0,
            largestWin: 0,
            largestLoss: 0,
            averageWin: 0,
            averageLoss: 0,
            consecutiveWins: 0,
            consecutiveLosses: 0,
            maxDrawdown: 0,
            sharpeRatio: 0,
            trades: [],
        };
    }
}
exports.AnalyticsService = AnalyticsService;
