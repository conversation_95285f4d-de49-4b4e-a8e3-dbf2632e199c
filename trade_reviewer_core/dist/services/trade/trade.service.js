"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradeService = void 0;
const models_1 = require("../../models");
class TradeService {
    constructor(storageService) {
        this.trades = new Map();
        this.storageService = storageService;
    }
    async initialize(userId) {
        const savedTrades = await this.storageService.get(TradeService.TRADES_KEY);
        if (savedTrades) {
            const trades = savedTrades
                .filter(trade => trade.userId === userId)
                .map(trade => new models_1.TradeEntity(trade));
            this.trades.set(userId, trades);
            return trades;
        }
        this.trades.set(userId, []);
        return [];
    }
    async getTrades(userId, filter) {
        const trades = this.trades.get(userId) || [];
        if (!filter)
            return trades;
        return trades.filter(trade => {
            if (filter.symbol && trade.symbol !== filter.symbol)
                return false;
            if (filter.type && trade.type !== filter.type)
                return false;
            if (filter.status && trade.status !== filter.status)
                return false;
            if (filter.startDate && trade.entryDate < filter.startDate)
                return false;
            if (filter.endDate && trade.entryDate > filter.endDate)
                return false;
            if (filter.tags && filter.tags.length > 0) {
                if (!filter.tags.some(tag => trade.tags.includes(tag)))
                    return false;
            }
            const pnl = trade.calculatePnL();
            if (filter.minPnL !== undefined && pnl < filter.minPnL)
                return false;
            if (filter.maxPnL !== undefined && pnl > filter.maxPnL)
                return false;
            return true;
        });
    }
    async getTradeById(userId, tradeId) {
        const trades = this.trades.get(userId) || [];
        return trades.find(trade => trade.id === tradeId) || null;
    }
    async createTrade(userId, trade) {
        const newTrade = new models_1.TradeEntity({
            ...trade,
            id: this.generateTradeId(),
            userId,
            updatedAt: new Date()
        });
        const userTrades = this.trades.get(userId) || [];
        userTrades.push(newTrade);
        this.trades.set(userId, userTrades);
        await this.save();
        return newTrade;
    }
    async updateTrade(userId, tradeId, updates) {
        const trades = this.trades.get(userId) || [];
        const index = trades.findIndex(trade => trade.id === tradeId);
        if (index === -1) {
            throw new Error('Trade not found');
        }
        const currentTrade = trades[index];
        const updatedTrade = new models_1.TradeEntity({
            ...currentTrade,
            ...updates,
            userId, // Ensure userId cannot be changed
            updatedAt: new Date()
        });
        trades[index] = updatedTrade;
        await this.save();
        return updatedTrade;
    }
    async deleteTrade(userId, tradeId) {
        const trades = this.trades.get(userId) || [];
        const filteredTrades = trades.filter(trade => trade.id !== tradeId);
        if (filteredTrades.length === trades.length) {
            throw new Error('Trade not found');
        }
        this.trades.set(userId, filteredTrades);
        await this.save();
    }
    async closeTrade(userId, tradeId, exitPrice, exitDate = new Date()) {
        return this.updateTrade(userId, tradeId, {
            exitPrice,
            exitDate,
            status: models_1.TradeStatus.CLOSED
        });
    }
    async addTags(userId, tradeId, tags) {
        const trade = await this.getTradeById(userId, tradeId);
        if (!trade) {
            throw new Error('Trade not found');
        }
        const uniqueTags = Array.from(new Set([...trade.tags, ...tags]));
        return this.updateTrade(userId, tradeId, { tags: uniqueTags });
    }
    async removeTags(userId, tradeId, tags) {
        const trade = await this.getTradeById(userId, tradeId);
        if (!trade) {
            throw new Error('Trade not found');
        }
        const updatedTags = trade.tags.filter(tag => !tags.includes(tag));
        return this.updateTrade(userId, tradeId, { tags: updatedTags });
    }
    async getTradesBySymbol(userId, symbol) {
        return this.getTrades(userId, { symbol });
    }
    async getTradesByType(userId, type) {
        return this.getTrades(userId, { type });
    }
    async getTradesByStatus(userId, status) {
        return this.getTrades(userId, { status });
    }
    async getTradesByDateRange(userId, startDate, endDate) {
        return this.getTrades(userId, { startDate, endDate });
    }
    async getTradesByTag(userId, tag) {
        return this.getTrades(userId, { tags: [tag] });
    }
    generateTradeId() {
        return `tr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    async save() {
        const allTrades = Array.from(this.trades.values()).flat();
        await this.storageService.set(TradeService.TRADES_KEY, allTrades);
    }
}
exports.TradeService = TradeService;
TradeService.TRADES_KEY = 'trade_reviewer_trades';
