import { Trade, TradeEntity, TradeStatus, TradeType } from '../../models';
import { StorageService } from '../storage/storage.service';
export interface TradeFilter {
    symbol?: string;
    type?: TradeType;
    status?: TradeStatus;
    startDate?: Date;
    endDate?: Date;
    tags?: string[];
    minPnL?: number;
    maxPnL?: number;
}
export declare class TradeService {
    private static readonly TRADES_KEY;
    private trades;
    private storageService;
    constructor(storageService: StorageService);
    initialize(userId: string): Promise<TradeEntity[]>;
    getTrades(userId: string, filter?: TradeFilter): Promise<TradeEntity[]>;
    getTradeById(userId: string, tradeId: string): Promise<TradeEntity | null>;
    createTrade(userId: string, trade: Omit<Trade, 'id' | 'userId' | 'updatedAt'>): Promise<TradeEntity>;
    updateTrade(userId: string, tradeId: string, updates: Partial<Trade>): Promise<TradeEntity>;
    deleteTrade(userId: string, tradeId: string): Promise<void>;
    closeTrade(userId: string, tradeId: string, exitPrice: number, exitDate?: Date): Promise<TradeEntity>;
    addTags(userId: string, tradeId: string, tags: string[]): Promise<TradeEntity>;
    removeTags(userId: string, tradeId: string, tags: string[]): Promise<TradeEntity>;
    getTradesBySymbol(userId: string, symbol: string): Promise<TradeEntity[]>;
    getTradesByType(userId: string, type: TradeType): Promise<TradeEntity[]>;
    getTradesByStatus(userId: string, status: TradeStatus): Promise<TradeEntity[]>;
    getTradesByDateRange(userId: string, startDate: Date, endDate: Date): Promise<TradeEntity[]>;
    getTradesByTag(userId: string, tag: string): Promise<TradeEntity[]>;
    private generateTradeId;
    private save;
}
