import { Trade, TradeEntity, JournalEntry, JournalEntryEntity } from '../../models';
import { StorageService } from '../storage/storage.service';
export declare class TradesService {
    private static readonly TRADES_KEY;
    private static readonly JOURNAL_KEY;
    private trades;
    private journal;
    private storageService;
    constructor(storageService: StorageService);
    initialize(userId: string): Promise<{
        trades: TradeEntity[];
        journal: JournalEntryEntity[];
    }>;
    getTrades(userId: string): Promise<TradeEntity[]>;
    getOpenTrades(userId: string): Promise<TradeEntity[]>;
    getClosedTrades(userId: string): Promise<TradeEntity[]>;
    getTradeById(tradeId: string, userId: string): Promise<TradeEntity | null>;
    addTrade(trade: Trade): Promise<TradeEntity>;
    updateTrade(tradeId: string, userId: string, updates: Partial<Trade>): Promise<TradeEntity | null>;
    closeTrade(tradeId: string, userId: string, exitPrice: number, exitDate: Date): Promise<TradeEntity | null>;
    deleteTrade(tradeId: string, userId: string): Promise<void>;
    getJournalEntries(userId: string): Promise<JournalEntryEntity[]>;
    getJournalEntryByTradeId(tradeId: string, userId: string): Promise<JournalEntryEntity | null>;
    addJournalEntry(entry: JournalEntry): Promise<JournalEntryEntity>;
    updateJournalEntry(entryId: string, userId: string, updates: Partial<JournalEntry>): Promise<JournalEntryEntity | null>;
    deleteJournalEntry(entryId: string, userId: string): Promise<void>;
    clear(userId: string): Promise<void>;
    private saveTrades;
    private saveJournal;
}
