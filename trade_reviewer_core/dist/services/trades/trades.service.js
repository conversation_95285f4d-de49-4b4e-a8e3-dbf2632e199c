"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradesService = void 0;
const models_1 = require("../../models");
class TradesService {
    constructor(storageService) {
        this.trades = [];
        this.journal = [];
        this.storageService = storageService;
    }
    async initialize(userId) {
        const [savedTrades, savedJournal] = await Promise.all([
            this.storageService.get(TradesService.TRADES_KEY),
            this.storageService.get(TradesService.JOURNAL_KEY)
        ]);
        if (savedTrades) {
            this.trades = savedTrades
                .filter(t => t.userId === userId)
                .map(t => new models_1.TradeEntity(t));
        }
        if (savedJournal) {
            this.journal = savedJournal
                .filter(j => j.userId === userId)
                .map(j => new models_1.JournalEntryEntity(j));
        }
        return { trades: this.trades, journal: this.journal };
    }
    async getTrades(userId) {
        return this.trades.filter(t => t.userId === userId);
    }
    async getOpenTrades(userId) {
        return this.trades.filter(t => t.userId === userId && t.status === models_1.TradeStatus.OPEN);
    }
    async getClosedTrades(userId) {
        return this.trades.filter(t => t.userId === userId && t.status === models_1.TradeStatus.CLOSED);
    }
    async getTradeById(tradeId, userId) {
        return this.trades.find(t => t.id === tradeId && t.userId === userId) || null;
    }
    async addTrade(trade) {
        const entity = new models_1.TradeEntity(trade);
        this.trades.push(entity);
        await this.saveTrades();
        return entity;
    }
    async updateTrade(tradeId, userId, updates) {
        const trade = this.trades.find(t => t.id === tradeId && t.userId === userId);
        if (trade) {
            Object.assign(trade, updates);
            trade.updatedAt = new Date();
            await this.saveTrades();
            return trade;
        }
        return null;
    }
    async closeTrade(tradeId, userId, exitPrice, exitDate) {
        const trade = this.trades.find(t => t.id === tradeId && t.userId === userId);
        if (trade) {
            trade.exitPrice = exitPrice;
            trade.exitDate = exitDate;
            trade.status = models_1.TradeStatus.CLOSED;
            trade.updatedAt = new Date();
            await this.saveTrades();
            return trade;
        }
        return null;
    }
    async deleteTrade(tradeId, userId) {
        this.trades = this.trades.filter(t => !(t.id === tradeId && t.userId === userId));
        await this.saveTrades();
    }
    async getJournalEntries(userId) {
        return this.journal.filter(j => j.userId === userId);
    }
    async getJournalEntryByTradeId(tradeId, userId) {
        return this.journal.find(j => j.tradeId === tradeId && j.userId === userId) || null;
    }
    async addJournalEntry(entry) {
        const entity = new models_1.JournalEntryEntity(entry);
        this.journal.push(entity);
        await this.saveJournal();
        return entity;
    }
    async updateJournalEntry(entryId, userId, updates) {
        const entry = this.journal.find(j => j.id === entryId && j.userId === userId);
        if (entry) {
            Object.assign(entry, updates);
            entry.updatedAt = new Date();
            await this.saveJournal();
            return entry;
        }
        return null;
    }
    async deleteJournalEntry(entryId, userId) {
        this.journal = this.journal.filter(j => !(j.id === entryId && j.userId === userId));
        await this.saveJournal();
    }
    async clear(userId) {
        this.trades = this.trades.filter(t => t.userId !== userId);
        this.journal = this.journal.filter(j => j.userId !== userId);
        await Promise.all([
            this.saveTrades(),
            this.saveJournal()
        ]);
    }
    async saveTrades() {
        await this.storageService.set(TradesService.TRADES_KEY, this.trades);
    }
    async saveJournal() {
        await this.storageService.set(TradesService.JOURNAL_KEY, this.journal);
    }
}
exports.TradesService = TradesService;
TradesService.TRADES_KEY = 'trade_reviewer_trades';
TradesService.JOURNAL_KEY = 'trade_reviewer_journal';
