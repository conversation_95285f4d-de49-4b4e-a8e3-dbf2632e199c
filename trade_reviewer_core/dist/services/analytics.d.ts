import { Trade } from '../models/trade';
import { TimeFrame } from '../models/timeframe';
import { StorageService } from './storage';
export interface TradeAnalytics {
    totalTrades: number;
    winningTrades: number;
    losingTrades: number;
    winRate: number;
    averageRRR: number;
    profitFactor: number;
    totalPnL: number;
    averagePnL: number;
    largestWin: number;
    largestLoss: number;
    averageWin: number;
    averageLoss: number;
    consecutiveWins: number;
    consecutiveLosses: number;
    maxDrawdown: number;
    sharpeRatio: number;
    trades: Trade[];
}
export declare class AnalyticsService {
    private storageService;
    constructor(storageService: StorageService);
    getAnalytics(timeFrame: TimeFrame): Promise<TradeAnalytics>;
    private filterTradesByTimeFrame;
    private calculateConsecutiveWins;
    private calculateConsecutiveLosses;
    private calculateConsecutive;
    private calculateMaxDrawdown;
    private calculateSharpeRatio;
    private getEmptyAnalytics;
}
