import { AnalyticsEntity, TimeFrame } from '../../models';
import { StorageService } from '../storage/storage.service';
import { TradeService } from '../trade/trade.service';
export declare class AnalyticsService {
    private static readonly ANALYTICS_KEY;
    private analytics;
    private storageService;
    private tradeService;
    constructor(storageService: StorageService, tradeService: TradeService);
    initialize(userId: string): Promise<AnalyticsEntity>;
    getAnalytics(userId: string): Promise<AnalyticsEntity | null>;
    updateAnalytics(userId: string, timeFrame?: TimeFrame): Promise<AnalyticsEntity>;
    private save;
}
