"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsService = void 0;
const models_1 = require("../../models");
class AnalyticsService {
    constructor(storageService, tradeService) {
        this.analytics = new Map();
        this.storageService = storageService;
        this.tradeService = tradeService;
    }
    async initialize(userId) {
        const savedAnalytics = await this.storageService.get(AnalyticsService.ANALYTICS_KEY);
        if (savedAnalytics) {
            const userAnalytics = savedAnalytics.find(a => a.userId === userId);
            if (userAnalytics) {
                const analytics = new models_1.AnalyticsEntity(userAnalytics);
                this.analytics.set(userId, analytics);
                return analytics;
            }
        }
        // Create default analytics if none exists
        const defaultAnalytics = new models_1.AnalyticsEntity({
            userId,
            timeFrame: models_1.TimeFrame.ALL,
            startDate: new Date(0),
            endDate: new Date(),
            metrics: {
                totalTrades: 0,
                winningTrades: 0,
                losingTrades: 0,
                breakEvenTrades: 0,
                totalPnL: 0,
                totalRiskAmount: 0,
                totalRewardAmount: 0,
                averageRRR: 0,
                winRate: 0,
                profitFactor: 0,
                averageWinAmount: 0,
                averageLossAmount: 0,
                largestWin: 0,
                largestLoss: 0,
                totalFees: 0
            },
            updatedAt: new Date()
        });
        this.analytics.set(userId, defaultAnalytics);
        await this.save();
        return defaultAnalytics;
    }
    async getAnalytics(userId) {
        return this.analytics.get(userId) || null;
    }
    async updateAnalytics(userId, timeFrame = models_1.TimeFrame.ALL) {
        const analytics = this.analytics.get(userId);
        if (!analytics) {
            throw new Error('Analytics not found');
        }
        const trades = await this.tradeService.getTrades(userId);
        const closedTrades = trades.filter(t => t.exitDate !== null);
        if (closedTrades.length === 0) {
            return analytics;
        }
        const startDate = timeFrame === models_1.TimeFrame.ALL
            ? new Date(Math.min(...closedTrades.map(t => t.entryDate.getTime())))
            : new Date();
        const endDate = new Date();
        let totalPnL = 0;
        let totalRiskAmount = 0;
        let totalRewardAmount = 0;
        let winningTrades = 0;
        let losingTrades = 0;
        let breakEvenTrades = 0;
        let totalWinAmount = 0;
        let totalLossAmount = 0;
        let largestWin = 0;
        let largestLoss = 0;
        let totalFees = 0;
        closedTrades.forEach(trade => {
            const pnl = trade.calculatePnL();
            totalPnL += pnl;
            totalFees += trade.fees || 0;
            if (pnl > 0) {
                winningTrades++;
                totalWinAmount += pnl;
                largestWin = Math.max(largestWin, pnl);
            }
            else if (pnl < 0) {
                losingTrades++;
                totalLossAmount += Math.abs(pnl);
                largestLoss = Math.max(largestLoss, Math.abs(pnl));
            }
            else {
                breakEvenTrades++;
            }
            const rrr = trade.calculateRRR();
            if (rrr) {
                totalRiskAmount += rrr.risk;
                totalRewardAmount += rrr.reward;
            }
        });
        const updatedAnalytics = new models_1.AnalyticsEntity({
            userId,
            timeFrame,
            startDate,
            endDate,
            metrics: {
                totalTrades: closedTrades.length,
                winningTrades,
                losingTrades,
                breakEvenTrades,
                totalPnL,
                totalRiskAmount,
                totalRewardAmount,
                averageRRR: totalRiskAmount > 0 ? totalRewardAmount / totalRiskAmount : 0,
                winRate: closedTrades.length > 0 ? winningTrades / closedTrades.length : 0,
                profitFactor: totalLossAmount > 0 ? totalWinAmount / totalLossAmount : 0,
                averageWinAmount: winningTrades > 0 ? totalWinAmount / winningTrades : 0,
                averageLossAmount: losingTrades > 0 ? totalLossAmount / losingTrades : 0,
                largestWin,
                largestLoss,
                totalFees
            },
            updatedAt: new Date()
        });
        this.analytics.set(userId, updatedAnalytics);
        await this.save();
        return updatedAnalytics;
    }
    async save() {
        const allAnalytics = Array.from(this.analytics.values());
        await this.storageService.set(AnalyticsService.ANALYTICS_KEY, allAnalytics);
    }
}
exports.AnalyticsService = AnalyticsService;
AnalyticsService.ANALYTICS_KEY = 'trade_reviewer_analytics';
