import { JournalEntry, JournalEntryEntity } from '../../models';
import { StorageService } from '../storage/storage.service';
export interface JournalFilter {
    startDate?: Date;
    endDate?: Date;
    tradeId?: string;
    marketCondition?: string;
    emotionalState?: string;
    tags?: string[];
    rating?: number;
}
export declare class JournalService {
    private static readonly JOURNAL_KEY;
    private entries;
    private storageService;
    constructor(storageService: StorageService);
    initialize(userId: string): Promise<JournalEntryEntity[]>;
    getEntries(userId: string, filter?: JournalFilter): Promise<JournalEntryEntity[]>;
    getEntry(userId: string, entryId: string): Promise<JournalEntryEntity | undefined>;
    createEntry(userId: string, entry: JournalEntry): Promise<JournalEntryEntity>;
    updateEntry(userId: string, entryId: string, updates: Partial<JournalEntry>): Promise<JournalEntryEntity>;
    deleteEntry(userId: string, entryId: string): Promise<void>;
    addTags(userId: string, entryId: string, tags: string[]): Promise<JournalEntryEntity>;
    removeTags(userId: string, entryId: string, tags: string[]): Promise<JournalEntryEntity>;
    updateRating(userId: string, entryId: string, rating: number): Promise<JournalEntryEntity>;
    updateAnalysis(userId: string, entryId: string, analysis: string): Promise<JournalEntryEntity>;
    addLesson(userId: string, entryId: string, lesson: string): Promise<JournalEntryEntity>;
    updateLessons(userId: string, entryId: string, lessons: string[]): Promise<JournalEntryEntity>;
    private save;
}
