"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.JournalService = void 0;
const models_1 = require("../../models");
class JournalService {
    constructor(storageService) {
        this.entries = new Map();
        this.storageService = storageService;
    }
    async initialize(userId) {
        const savedEntries = await this.storageService.get(JournalService.JOURNAL_KEY);
        if (savedEntries) {
            const userEntries = savedEntries
                .filter(entry => entry.userId === userId)
                .map(entry => new models_1.JournalEntryEntity(entry));
            this.entries.set(userId, userEntries);
            return userEntries;
        }
        this.entries.set(userId, []);
        return [];
    }
    async getEntries(userId, filter) {
        const entries = this.entries.get(userId) || [];
        if (!filter)
            return entries;
        return entries.filter(entry => {
            if (filter.startDate && entry.createdAt < filter.startDate)
                return false;
            if (filter.endDate && entry.createdAt > filter.endDate)
                return false;
            if (filter.tradeId && entry.tradeId !== filter.tradeId)
                return false;
            if (filter.marketCondition && entry.marketCondition !== filter.marketCondition)
                return false;
            if (filter.emotionalState && entry.emotionalState !== filter.emotionalState)
                return false;
            if (filter.rating && entry.rating !== filter.rating)
                return false;
            if (filter.tags && filter.tags.length > 0) {
                return filter.tags.every(tag => entry.tags.includes(tag));
            }
            return true;
        });
    }
    async getEntry(userId, entryId) {
        const entries = this.entries.get(userId) || [];
        return entries.find(entry => entry.id === entryId);
    }
    async createEntry(userId, entry) {
        const entries = this.entries.get(userId) || [];
        const newEntry = new models_1.JournalEntryEntity({
            ...entry,
            userId,
            createdAt: new Date(),
            updatedAt: new Date()
        });
        entries.push(newEntry);
        this.entries.set(userId, entries);
        await this.save();
        return newEntry;
    }
    async updateEntry(userId, entryId, updates) {
        const entries = this.entries.get(userId) || [];
        const index = entries.findIndex(entry => entry.id === entryId);
        if (index === -1) {
            throw new Error('Journal entry not found');
        }
        const updatedEntry = new models_1.JournalEntryEntity({
            ...entries[index],
            ...updates,
            updatedAt: new Date()
        });
        entries[index] = updatedEntry;
        this.entries.set(userId, entries);
        await this.save();
        return updatedEntry;
    }
    async deleteEntry(userId, entryId) {
        const entries = this.entries.get(userId) || [];
        const filteredEntries = entries.filter(entry => entry.id !== entryId);
        this.entries.set(userId, filteredEntries);
        await this.save();
    }
    async addTags(userId, entryId, tags) {
        const entries = this.entries.get(userId) || [];
        const entry = entries.find(e => e.id === entryId);
        if (!entry) {
            throw new Error('Journal entry not found');
        }
        const uniqueTags = [...new Set([...entry.tags, ...tags])];
        return this.updateEntry(userId, entryId, { tags: uniqueTags });
    }
    async removeTags(userId, entryId, tags) {
        const entries = this.entries.get(userId) || [];
        const entry = entries.find(e => e.id === entryId);
        if (!entry) {
            throw new Error('Journal entry not found');
        }
        const updatedTags = entry.tags.filter(tag => !tags.includes(tag));
        return this.updateEntry(userId, entryId, { tags: updatedTags });
    }
    async updateRating(userId, entryId, rating) {
        return this.updateEntry(userId, entryId, { rating });
    }
    async updateAnalysis(userId, entryId, analysis) {
        return this.updateEntry(userId, entryId, { analysis });
    }
    async addLesson(userId, entryId, lesson) {
        const entries = this.entries.get(userId) || [];
        const entry = entries.find(e => e.id === entryId);
        if (!entry) {
            throw new Error('Journal entry not found');
        }
        const updatedLessons = [...entry.lessons, lesson];
        return this.updateEntry(userId, entryId, { lessons: updatedLessons });
    }
    async updateLessons(userId, entryId, lessons) {
        return this.updateEntry(userId, entryId, { lessons });
    }
    async save() {
        const allEntries = Array.from(this.entries.values()).flat();
        await this.storageService.set(JournalService.JOURNAL_KEY, allEntries);
    }
}
exports.JournalService = JournalService;
JournalService.JOURNAL_KEY = 'trade_reviewer_journal';
