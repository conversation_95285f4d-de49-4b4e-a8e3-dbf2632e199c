import { Goal } from '../../models/goal';
import { StorageService } from '../storage';
import { TimeFrame } from '../../models/timeframe';
export declare class GoalsService {
    private storageService;
    constructor(storageService: StorageService);
    getGoalProgress(goal: Goal, timeFrame: TimeFrame): Promise<number>;
    private filterTradesByTimeFrame;
    private calculateWinRate;
    private calculateTotalProfit;
    private calculateAverageRRR;
    private calculateConsecutiveWins;
    private calculateMaxDrawdown;
    private calculateProfitFactor;
    private calculateSharpeRatio;
}
