"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoalsService = void 0;
const trade_1 = require("../../models/trade");
const goal_1 = require("../../models/goal");
const timeframe_1 = require("../../models/timeframe");
class GoalsService {
    constructor(storageService) {
        this.storageService = storageService;
    }
    async getGoalProgress(goal, timeFrame) {
        const trades = await this.storageService.getTrades();
        const tradeEntities = trades.map(trade => new trade_1.TradeEntity(trade));
        const validTrades = this.filterTradesByTimeFrame(tradeEntities, timeFrame);
        switch (goal.type) {
            case goal_1.GoalType.WIN_RATE:
                return this.calculateWinRate(validTrades);
            case goal_1.GoalType.PROFIT:
                return this.calculateTotalProfit(validTrades);
            case goal_1.GoalType.AVERAGE_RRR:
                return this.calculateAverageRRR(validTrades);
            case goal_1.GoalType.CONSECUTIVE_WINS:
                return this.calculateConsecutiveWins(validTrades);
            case goal_1.GoalType.MAX_DRAWDOWN:
                return this.calculateMaxDrawdown(validTrades);
            case goal_1.GoalType.PROFIT_FACTOR:
                return this.calculateProfitFactor(validTrades);
            case goal_1.GoalType.SHARPE_RATIO:
                return this.calculateSharpeRatio(validTrades);
            default:
                return 0;
        }
    }
    filterTradesByTimeFrame(trades, timeFrame) {
        const now = new Date();
        const startDate = new Date();
        switch (timeFrame) {
            case timeframe_1.TimeFrame.DAY:
                startDate.setDate(now.getDate() - 1);
                break;
            case timeframe_1.TimeFrame.WEEK:
                startDate.setDate(now.getDate() - 7);
                break;
            case timeframe_1.TimeFrame.MONTH:
                startDate.setMonth(now.getMonth() - 1);
                break;
            case timeframe_1.TimeFrame.YEAR:
                startDate.setFullYear(now.getFullYear() - 1);
                break;
            case timeframe_1.TimeFrame.ALL:
                return trades;
        }
        return trades.filter(trade => trade.entryDate >= startDate);
    }
    calculateWinRate(trades) {
        const closedTrades = trades.filter(trade => trade.status === trade_1.TradeStatus.CLOSED);
        if (closedTrades.length === 0)
            return 0;
        const winningTrades = closedTrades.filter(trade => trade.calculatePnL() > 0);
        return (winningTrades.length / closedTrades.length) * 100;
    }
    calculateTotalProfit(trades) {
        return trades
            .filter(trade => trade.status === trade_1.TradeStatus.CLOSED)
            .reduce((sum, trade) => sum + trade.calculatePnL(), 0);
    }
    calculateAverageRRR(trades) {
        const validTrades = trades.filter(trade => trade.status === trade_1.TradeStatus.CLOSED);
        if (validTrades.length === 0)
            return 0;
        const totalRRR = validTrades.reduce((sum, trade) => {
            const rrr = trade.calculateRRR();
            return sum + (rrr ? rrr.ratio : 0);
        }, 0);
        return totalRRR / validTrades.length;
    }
    calculateConsecutiveWins(trades) {
        const closedTrades = trades.filter(trade => trade.status === trade_1.TradeStatus.CLOSED);
        let maxConsecutive = 0;
        let currentConsecutive = 0;
        for (const trade of closedTrades) {
            if (trade.calculatePnL() > 0) {
                currentConsecutive++;
                maxConsecutive = Math.max(maxConsecutive, currentConsecutive);
            }
            else {
                currentConsecutive = 0;
            }
        }
        return maxConsecutive;
    }
    calculateMaxDrawdown(trades) {
        const closedTrades = trades.filter(trade => trade.status === trade_1.TradeStatus.CLOSED);
        let peak = 0;
        let maxDrawdown = 0;
        let currentValue = 0;
        for (const trade of closedTrades) {
            currentValue += trade.calculatePnL();
            peak = Math.max(peak, currentValue);
            maxDrawdown = Math.max(maxDrawdown, peak - currentValue);
        }
        return maxDrawdown;
    }
    calculateProfitFactor(trades) {
        const closedTrades = trades.filter(trade => trade.status === trade_1.TradeStatus.CLOSED);
        const winningTrades = closedTrades.filter(trade => trade.calculatePnL() > 0);
        const losingTrades = closedTrades.filter(trade => trade.calculatePnL() < 0);
        const totalWinning = winningTrades.reduce((sum, trade) => sum + trade.calculatePnL(), 0);
        const totalLosing = Math.abs(losingTrades.reduce((sum, trade) => sum + trade.calculatePnL(), 0));
        return totalLosing === 0 ? 0 : totalWinning / totalLosing;
    }
    calculateSharpeRatio(trades) {
        const closedTrades = trades.filter(trade => trade.status === trade_1.TradeStatus.CLOSED);
        if (closedTrades.length < 2)
            return 0;
        const returns = closedTrades.map(trade => trade.calculatePnL());
        const averageReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
        const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - averageReturn, 2), 0) / (returns.length - 1);
        const standardDeviation = Math.sqrt(variance);
        return standardDeviation === 0 ? 0 : averageReturn / standardDeviation;
    }
}
exports.GoalsService = GoalsService;
