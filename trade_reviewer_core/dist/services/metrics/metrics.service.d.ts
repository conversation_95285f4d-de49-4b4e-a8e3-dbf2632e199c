import { MetricsEntity, MarketMetrics, PositionMetrics, AccountMetrics } from '../../models';
import { StorageService } from '../storage/storage.service';
export declare class MetricsService {
    private static readonly METRICS_KEY;
    private metrics;
    private storageService;
    constructor(storageService: StorageService);
    initialize(userId: string): Promise<MetricsEntity>;
    getMetrics(userId: string): Promise<MetricsEntity | null>;
    updateMarketMetrics(userId: string, symbol: string, metrics: MarketMetrics): Promise<MetricsEntity>;
    updatePositionMetrics(userId: string, symbol: string, metrics: PositionMetrics): Promise<MetricsEntity>;
    updateAccountMetrics(userId: string, metrics: AccountMetrics): Promise<MetricsEntity>;
    getMarketMetrics(userId: string, symbol: string): Promise<MarketMetrics | undefined>;
    getPositionMetrics(userId: string, symbol: string): Promise<PositionMetrics | undefined>;
    getAccountMetrics(userId: string): Promise<AccountMetrics>;
    getTotalUnrealizedPnL(userId: string): Promise<number>;
    getTotalRisk(userId: string): Promise<number>;
    getAverageRRR(userId: string): Promise<number>;
    getMarginUtilization(userId: string): Promise<number>;
    private save;
}
