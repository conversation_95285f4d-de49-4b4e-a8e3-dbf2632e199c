"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MetricsService = void 0;
const models_1 = require("../../models");
class MetricsService {
    constructor(storageService) {
        this.metrics = new Map();
        this.storageService = storageService;
    }
    async initialize(userId) {
        const savedMetrics = await this.storageService.get(MetricsService.METRICS_KEY);
        if (savedMetrics) {
            const userMetrics = savedMetrics.find(metrics => metrics.userId === userId);
            if (userMetrics) {
                const metrics = new models_1.MetricsEntity(userMetrics);
                this.metrics.set(userId, metrics);
                return metrics;
            }
        }
        const newMetrics = new models_1.MetricsEntity({
            id: crypto.randomUUID(),
            userId,
            timestamp: new Date(),
            market: {},
            positions: {},
            account: {
                balance: 0,
                equity: 0,
                openPositions: 0,
                marginUsed: 0,
                marginAvailable: 0,
                marginLevel: 0,
                dailyPnL: 0,
                dailyPnLPercent: 0,
                drawdown: 0,
                drawdownPercent: 0
            }
        });
        this.metrics.set(userId, newMetrics);
        await this.save();
        return newMetrics;
    }
    async getMetrics(userId) {
        const metrics = this.metrics.get(userId);
        return metrics || null;
    }
    async updateMarketMetrics(userId, symbol, metrics) {
        const userMetrics = this.metrics.get(userId);
        if (!userMetrics) {
            throw new Error('Metrics not initialized');
        }
        userMetrics.updateMarketMetrics(symbol, metrics);
        await this.save();
        return userMetrics;
    }
    async updatePositionMetrics(userId, symbol, metrics) {
        const userMetrics = this.metrics.get(userId);
        if (!userMetrics) {
            throw new Error('Metrics not initialized');
        }
        userMetrics.updatePositionMetrics(symbol, metrics);
        await this.save();
        return userMetrics;
    }
    async updateAccountMetrics(userId, metrics) {
        const userMetrics = this.metrics.get(userId);
        if (!userMetrics) {
            throw new Error('Metrics not initialized');
        }
        userMetrics.updateAccountMetrics(metrics);
        await this.save();
        return userMetrics;
    }
    async getMarketMetrics(userId, symbol) {
        const userMetrics = this.metrics.get(userId);
        if (!userMetrics) {
            throw new Error('Metrics not initialized');
        }
        return userMetrics.getMarketMetrics(symbol);
    }
    async getPositionMetrics(userId, symbol) {
        const userMetrics = this.metrics.get(userId);
        if (!userMetrics) {
            throw new Error('Metrics not initialized');
        }
        return userMetrics.getPositionMetrics(symbol);
    }
    async getAccountMetrics(userId) {
        const userMetrics = this.metrics.get(userId);
        if (!userMetrics) {
            throw new Error('Metrics not initialized');
        }
        return userMetrics.account;
    }
    async getTotalUnrealizedPnL(userId) {
        const userMetrics = this.metrics.get(userId);
        if (!userMetrics) {
            throw new Error('Metrics not initialized');
        }
        return userMetrics.totalUnrealizedPnL;
    }
    async getTotalRisk(userId) {
        const userMetrics = this.metrics.get(userId);
        if (!userMetrics) {
            throw new Error('Metrics not initialized');
        }
        return userMetrics.totalRisk;
    }
    async getAverageRRR(userId) {
        const userMetrics = this.metrics.get(userId);
        if (!userMetrics) {
            throw new Error('Metrics not initialized');
        }
        return userMetrics.averageRRR;
    }
    async getMarginUtilization(userId) {
        const userMetrics = this.metrics.get(userId);
        if (!userMetrics) {
            throw new Error('Metrics not initialized');
        }
        return userMetrics.marginUtilization;
    }
    async save() {
        const allMetrics = Array.from(this.metrics.values());
        await this.storageService.set(MetricsService.METRICS_KEY, allMetrics);
    }
}
exports.MetricsService = MetricsService;
MetricsService.METRICS_KEY = 'trade_reviewer_metrics';
