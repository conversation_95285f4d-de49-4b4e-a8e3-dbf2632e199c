import { SettingsEntity, TradeDefaults, DisplaySettings, BacktestSettings } from '../../models';
import { IStorageService } from '../storage/storage.service';
export declare class SettingsService {
    private storageService;
    private static readonly SETTINGS_KEY;
    private settings;
    constructor(storageService: IStorageService);
    initialize(userId: string): Promise<SettingsEntity>;
    getSettings(): Promise<SettingsEntity>;
    updateTradeDefaults(defaults: Partial<TradeDefaults>): Promise<SettingsEntity>;
    updateDisplaySettings(display: Partial<DisplaySettings>): Promise<SettingsEntity>;
    updateBacktestSettings(backtest: Partial<BacktestSettings>): Promise<SettingsEntity>;
    updateAutoSync(enabled: boolean): Promise<SettingsEntity>;
    updateAutoBackup(enabled: boolean, frequency?: number): Promise<SettingsEntity>;
    reset(): Promise<SettingsEntity>;
    private save;
    private _createDefaultSettings;
}
