"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SettingsService = void 0;
const models_1 = require("../../models");
class SettingsService {
    constructor(storageService) {
        this.storageService = storageService;
        this.settings = null;
    }
    async initialize(userId) {
        const savedSettings = await this.storageService.get(SettingsService.SETTINGS_KEY);
        if (savedSettings) {
            this.settings = new models_1.SettingsEntity(savedSettings);
        }
        else {
            this.settings = await this._createDefaultSettings(userId);
            await this.save();
        }
        await this.storageService.set(SettingsService.SETTINGS_KEY, this.settings);
        return this.settings;
    }
    async getSettings() {
        if (!this.settings) {
            throw new Error('Settings not initialized. Call initialize() first.');
        }
        return this.settings;
    }
    async updateTradeDefaults(defaults) {
        if (!this.settings) {
            throw new Error('Settings not initialized. Call initialize() first.');
        }
        this.settings.updateTradeDefaults(defaults);
        await this.save();
        return this.settings;
    }
    async updateDisplaySettings(display) {
        if (!this.settings) {
            throw new Error('Settings not initialized. Call initialize() first.');
        }
        this.settings.updateDisplaySettings(display);
        await this.save();
        return this.settings;
    }
    async updateBacktestSettings(backtest) {
        if (!this.settings) {
            throw new Error('Settings not initialized. Call initialize() first.');
        }
        this.settings.updateBacktestSettings(backtest);
        await this.save();
        return this.settings;
    }
    async updateAutoSync(enabled) {
        if (!this.settings) {
            throw new Error('Settings not initialized. Call initialize() first.');
        }
        this.settings.autoSync = enabled;
        this.settings.updatedAt = new Date();
        await this.save();
        return this.settings;
    }
    async updateAutoBackup(enabled, frequency) {
        if (!this.settings) {
            throw new Error('Settings not initialized. Call initialize() first.');
        }
        this.settings.autoBackup = enabled;
        if (frequency !== undefined) {
            this.settings.backupFrequency = frequency;
        }
        this.settings.updatedAt = new Date();
        await this.save();
        return this.settings;
    }
    async reset() {
        if (!this.settings) {
            throw new Error('Settings not initialized. Call initialize() first.');
        }
        const userId = this.settings.userId;
        await this.storageService.remove(SettingsService.SETTINGS_KEY);
        return this.initialize(userId);
    }
    async save() {
        if (!this.settings) {
            throw new Error('Settings not initialized. Call initialize() first.');
        }
        await this.storageService.set(SettingsService.SETTINGS_KEY, this.settings);
    }
    async _createDefaultSettings(userId) {
        const settings = {
            userId,
            tradeDefaults: {
                defaultRisk: 1,
                defaultPosition: 100,
                defaultTimeframe: models_1.ChartTimeframe.D1,
                riskCalculationType: models_1.RiskCalculationType.PERCENTAGE,
                defaultCommission: 0,
                defaultFees: 0,
            },
            display: {
                theme: 'system',
                pnlDisplayMode: models_1.PnLDisplayMode.CURRENCY,
                showRunningPnL: true,
                showEquityCurve: true,
                showTradeStatistics: true,
                compactMode: false,
                dateFormat: 'yyyy-MM-dd',
                timeFormat: 'HH:mm:ss',
                timezone: 'UTC',
                goalNotificationsEnabled: true,
                weeklyReportsEnabled: true,
                monthlyReportsEnabled: true,
                tradeAlertsEnabled: true,
                marketNewsEnabled: true,
                priceAlertsEnabled: true,
                journalRemindersEnabled: true,
                emailNotificationsEnabled: true,
                pushNotificationsEnabled: true,
                smsNotificationsEnabled: false,
            },
            backtest: {
                initialCapital: 100000,
                useFixedPosition: false,
                maxRiskPerTrade: 2,
                maxOpenTrades: 5,
                includeFees: true,
                includeSlippage: true,
                slippageAmount: 0.1,
            },
            autoSync: true,
            autoBackup: true,
            backupFrequency: 7,
            updatedAt: new Date(),
        };
        return new models_1.SettingsEntity(settings);
    }
}
exports.SettingsService = SettingsService;
SettingsService.SETTINGS_KEY = 'trade_reviewer_settings';
