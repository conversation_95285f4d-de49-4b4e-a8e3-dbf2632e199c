import { StorageService } from './storage/storage.service';
import { SettingsService } from './settings/settings.service';
import { TradeService } from './trade/trade.service';
import { GoalService } from './goal/goal.service';
import { AnalyticsService } from './analytics/analytics.service';
import { JournalService } from './journal/journal.service';
import { NotificationsService } from './notifications/notifications.service';
import { ProfileService } from './profile/profile.service';
export declare class ServiceFactory {
    private storageService;
    private settingsService;
    private tradeService;
    private goalService;
    private analyticsService;
    private journalService;
    private notificationsService;
    private profileService;
    constructor();
    getStorageService(): StorageService;
    getSettingsService(): SettingsService;
    getTradeService(): TradeService;
    getGoalService(): GoalService;
    getAnalyticsService(): AnalyticsService;
    getJournalService(): JournalService;
    getNotificationsService(): NotificationsService;
    getProfileService(): ProfileService;
}
