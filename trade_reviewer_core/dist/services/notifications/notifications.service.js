"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationsService = void 0;
const models_1 = require("../../models");
class NotificationsService {
    constructor(storageService) {
        this.notifications = [];
        this.storageService = storageService;
    }
    async initialize(userId) {
        const savedNotifications = await this.storageService.get(NotificationsService.NOTIFICATIONS_KEY);
        if (savedNotifications) {
            this.notifications = savedNotifications
                .filter(n => n.userId === userId)
                .map(n => new models_1.NotificationEntity(n));
        }
        return this.notifications;
    }
    async getNotifications(userId) {
        return this.notifications.filter(n => n.userId === userId);
    }
    async getUnreadNotifications(userId) {
        return this.notifications.filter(n => n.userId === userId && !n.read);
    }
    async addNotification(notification) {
        const entity = new models_1.NotificationEntity(notification);
        this.notifications.push(entity);
        await this.save();
        return entity;
    }
    async markAsRead(notificationId, userId) {
        const notification = this.notifications.find(n => n.id === notificationId && n.userId === userId);
        if (notification) {
            notification.markAsRead();
            await this.save();
            return notification;
        }
        return null;
    }
    async markAllAsRead(userId) {
        const userNotifications = this.notifications.filter(n => n.userId === userId);
        userNotifications.forEach(n => n.markAsRead());
        await this.save();
        return userNotifications;
    }
    async dismiss(notificationId, userId) {
        const notification = this.notifications.find(n => n.id === notificationId && n.userId === userId);
        if (notification) {
            notification.dismiss();
            await this.save();
            return notification;
        }
        return null;
    }
    async dismissAll(userId) {
        const userNotifications = this.notifications.filter(n => n.userId === userId);
        userNotifications.forEach(n => n.dismiss());
        await this.save();
        return userNotifications;
    }
    async removeExpired() {
        const now = new Date();
        this.notifications = this.notifications.filter(n => {
            if (!n.expiresAt)
                return true;
            return n.expiresAt > now;
        });
        await this.save();
    }
    async clear(userId) {
        this.notifications = this.notifications.filter(n => n.userId !== userId);
        await this.save();
    }
    async save() {
        await this.storageService.set(NotificationsService.NOTIFICATIONS_KEY, this.notifications);
    }
}
exports.NotificationsService = NotificationsService;
NotificationsService.NOTIFICATIONS_KEY = 'trade_reviewer_notifications';
