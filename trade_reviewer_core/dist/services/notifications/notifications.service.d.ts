import { Notification, NotificationEntity } from '../../models';
import { StorageService } from '../storage/storage.service';
export declare class NotificationsService {
    private static readonly NOTIFICATIONS_KEY;
    private notifications;
    private storageService;
    constructor(storageService: StorageService);
    initialize(userId: string): Promise<NotificationEntity[]>;
    getNotifications(userId: string): Promise<NotificationEntity[]>;
    getUnreadNotifications(userId: string): Promise<NotificationEntity[]>;
    addNotification(notification: Notification): Promise<NotificationEntity>;
    markAsRead(notificationId: string, userId: string): Promise<NotificationEntity | null>;
    markAllAsRead(userId: string): Promise<NotificationEntity[]>;
    dismiss(notificationId: string, userId: string): Promise<NotificationEntity | null>;
    dismissAll(userId: string): Promise<NotificationEntity[]>;
    removeExpired(): Promise<void>;
    clear(userId: string): Promise<void>;
    private save;
}
