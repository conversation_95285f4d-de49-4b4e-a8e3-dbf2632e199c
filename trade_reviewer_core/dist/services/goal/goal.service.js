"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoalService = void 0;
const goal_1 = require("../../models/goal");
class GoalService {
    constructor(storageService) {
        this.storageService = storageService;
        this.goals = [];
    }
    async initialize(userId) {
        const savedGoals = await this.storageService.get(GoalService.GOALS_KEY);
        if (savedGoals) {
            this.goals = savedGoals
                .filter(g => g.userId === userId)
                .map(g => new goal_1.GoalEntity(g));
        }
        return this.goals;
    }
    async getGoals(userId, filter) {
        let filteredGoals = this.goals.filter(g => g.userId === userId);
        if (filter) {
            filteredGoals = filteredGoals.filter(goal => {
                if (filter.type && goal.type !== filter.type)
                    return false;
                if (filter.isActive !== undefined && goal.isActive !== filter.isActive)
                    return false;
                if (filter.isCompleted !== undefined && goal.progress >= goal.target !== filter.isCompleted)
                    return false;
                return true;
            });
        }
        return filteredGoals;
    }
    async getActiveGoals(userId) {
        return this.goals.filter(g => g.userId === userId && g.isActive);
    }
    async getGoalById(goalId, userId) {
        return this.goals.find(g => g.id === goalId && g.userId === userId) || null;
    }
    async addGoal(goal) {
        const entity = new goal_1.GoalEntity(goal);
        this.goals.push(entity);
        await this.save();
        return entity;
    }
    async updateGoal(goalId, userId, updates) {
        const goal = this.goals.find(g => g.id === goalId && g.userId === userId);
        if (goal) {
            Object.assign(goal, updates);
            goal.updatedAt = new Date();
            await this.save();
            return goal;
        }
        return null;
    }
    async deleteGoal(goalId, userId) {
        this.goals = this.goals.filter(g => !(g.id === goalId && g.userId === userId));
        await this.save();
    }
    async updateProgress(goalId, userId, value) {
        const goal = await this.getGoalById(goalId, userId);
        if (goal) {
            goal.progress = value;
            goal.updatedAt = new Date();
            await this.save();
            return goal;
        }
        return null;
    }
    async clear(userId) {
        this.goals = this.goals.filter(g => g.userId !== userId);
        await this.save();
    }
    async save() {
        await this.storageService.set(GoalService.GOALS_KEY, this.goals);
    }
}
exports.GoalService = GoalService;
GoalService.GOALS_KEY = 'trade_reviewer_goals';
