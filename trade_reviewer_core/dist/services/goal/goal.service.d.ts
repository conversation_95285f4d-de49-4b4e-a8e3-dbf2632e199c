import { Goal, GoalEntity, GoalType } from '../../models/goal';
import { StorageService } from '../storage';
export interface GoalFilter {
    type?: GoalType;
    isActive?: boolean;
    isCompleted?: boolean;
}
export declare class GoalService {
    private storageService;
    private static readonly GOALS_KEY;
    private goals;
    constructor(storageService: StorageService);
    initialize(userId: string): Promise<GoalEntity[]>;
    getGoals(userId: string, filter?: GoalFilter): Promise<GoalEntity[]>;
    getActiveGoals(userId: string): Promise<GoalEntity[]>;
    getGoalById(goalId: string, userId: string): Promise<GoalEntity | null>;
    addGoal(goal: Goal): Promise<GoalEntity>;
    updateGoal(goalId: string, userId: string, updates: Partial<Goal>): Promise<GoalEntity | null>;
    deleteGoal(goalId: string, userId: string): Promise<void>;
    updateProgress(goalId: string, userId: string, value: number): Promise<GoalEntity | null>;
    clear(userId: string): Promise<void>;
    private save;
}
