"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceFactory = void 0;
const storage_service_1 = require("./storage/storage.service");
const settings_service_1 = require("./settings/settings.service");
const trade_service_1 = require("./trade/trade.service");
const goal_service_1 = require("./goal/goal.service");
const analytics_service_1 = require("./analytics/analytics.service");
const journal_service_1 = require("./journal/journal.service");
const notifications_service_1 = require("./notifications/notifications.service");
const profile_service_1 = require("./profile/profile.service");
class ServiceFactory {
    constructor() {
        // Initialize storage service first as it's required by other services
        this.storageService = new storage_service_1.StorageService();
        // Initialize other services
        this.settingsService = new settings_service_1.SettingsService(this.storageService);
        this.tradeService = new trade_service_1.TradeService(this.storageService);
        this.goalService = new goal_service_1.GoalService(this.storageService);
        this.analyticsService = new analytics_service_1.AnalyticsService(this.storageService, this.tradeService);
        this.journalService = new journal_service_1.JournalService(this.storageService);
        this.notificationsService = new notifications_service_1.NotificationsService(this.storageService);
        this.profileService = new profile_service_1.ProfileService(this.storageService);
    }
    getStorageService() {
        return this.storageService;
    }
    getSettingsService() {
        return this.settingsService;
    }
    getTradeService() {
        return this.tradeService;
    }
    getGoalService() {
        return this.goalService;
    }
    getAnalyticsService() {
        return this.analyticsService;
    }
    getJournalService() {
        return this.journalService;
    }
    getNotificationsService() {
        return this.notificationsService;
    }
    getProfileService() {
        return this.profileService;
    }
}
exports.ServiceFactory = ServiceFactory;
