"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfileService = void 0;
const models_1 = require("../../models");
class ProfileService {
    constructor(storageService) {
        this.storageService = storageService;
        this.profiles = new Map();
    }
    async initialize(userId) {
        // Try to get the profile from storage service
        const storedProfile = await this.storageService.getProfile(userId);
        if (storedProfile) {
            const profile = new models_1.UserEntity(storedProfile);
            this.profiles.set(userId, profile);
            return profile;
        }
        // Create default profile if none exists
        const defaultProfile = new models_1.UserEntity({
            id: userId,
            email: '',
            name: '',
            role: models_1.UserRole.USER,
            preferences: {
                notificationPreference: models_1.NotificationPreference.IMPORTANT,
                darkMode: false,
                defaultCurrency: 'USD',
                timezone: 'UTC',
                language: 'en',
                showPnLInPercent: true,
                autoSyncEnabled: true
            },
            createdAt: new Date(),
            updatedAt: new Date(),
            isEmailVerified: false
        });
        // Save to storage and local cache
        await this.storageService.saveProfile(defaultProfile);
        this.profiles.set(userId, defaultProfile);
        return defaultProfile;
    }
    async getProfile(userId) {
        // Try from cache first
        let profile = this.profiles.get(userId);
        if (profile)
            return profile;
        // Try from storage
        const storedProfile = await this.storageService.getProfile(userId);
        if (storedProfile) {
            profile = new models_1.UserEntity(storedProfile);
            this.profiles.set(userId, profile);
            return profile;
        }
        return null;
    }
    async updateProfile(userId, updates) {
        const profile = await this.getProfile(userId);
        if (!profile) {
            throw new Error('Profile not found');
        }
        const updatedProfile = new models_1.UserEntity({
            ...profile,
            ...updates,
            id: userId, // Ensure userId cannot be changed
            updatedAt: new Date()
        });
        // Update storage and cache
        await this.storageService.updateProfile(updatedProfile);
        this.profiles.set(userId, updatedProfile);
        return updatedProfile;
    }
    async updatePreferences(userId, preferences) {
        const profile = await this.getProfile(userId);
        if (!profile) {
            throw new Error('Profile not found');
        }
        profile.updatePreferences(preferences);
        // Update storage and cache
        await this.storageService.updateProfile(profile);
        this.profiles.set(userId, profile);
        return profile;
    }
    async verifyEmail(userId) {
        const profile = await this.getProfile(userId);
        if (!profile) {
            throw new Error('Profile not found');
        }
        profile.verifyEmail();
        // Update storage and cache
        await this.storageService.updateProfile(profile);
        this.profiles.set(userId, profile);
        return profile;
    }
    async updateLastLogin(userId) {
        const profile = await this.getProfile(userId);
        if (!profile) {
            throw new Error('Profile not found');
        }
        profile.updateLastLogin();
        // Update storage and cache
        await this.storageService.updateProfile(profile);
        this.profiles.set(userId, profile);
        return profile;
    }
}
exports.ProfileService = ProfileService;
