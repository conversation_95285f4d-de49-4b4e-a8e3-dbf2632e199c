import { User, UserEntity, UserPreferences } from '../../models';
import { StorageService } from '../storage/storage.service';
export declare class ProfileService {
    private storageService;
    private profiles;
    constructor(storageService: StorageService);
    initialize(userId: string): Promise<UserEntity>;
    getProfile(userId: string): Promise<UserEntity | null>;
    updateProfile(userId: string, updates: Partial<User>): Promise<UserEntity>;
    updatePreferences(userId: string, preferences: Partial<UserPreferences>): Promise<UserEntity>;
    verifyEmail(userId: string): Promise<UserEntity>;
    updateLastLogin(userId: string): Promise<UserEntity>;
}
