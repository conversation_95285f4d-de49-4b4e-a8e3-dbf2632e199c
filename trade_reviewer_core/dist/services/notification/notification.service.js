"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationService = void 0;
const models_1 = require("../../models");
class NotificationService {
    constructor(storageService) {
        this.notifications = new Map();
        this.storageService = storageService;
    }
    async initialize(userId) {
        const savedNotifications = await this.storageService.get(NotificationService.NOTIFICATIONS_KEY);
        if (savedNotifications) {
            const notifications = savedNotifications
                .filter(notification => notification.userId === userId)
                .map(notification => new models_1.NotificationEntity(notification));
            this.notifications.set(userId, notifications);
            return notifications;
        }
        this.notifications.set(userId, []);
        return [];
    }
    async getNotifications(userId, filter) {
        const notifications = this.notifications.get(userId) || [];
        if (!filter)
            return notifications;
        return notifications.filter(notification => {
            if (filter.type && notification.type !== filter.type)
                return false;
            if (filter.isRead !== undefined && notification.read !== filter.isRead)
                return false;
            if (filter.isDismissed !== undefined && notification.dismissed !== filter.isDismissed)
                return false;
            if (filter.priority !== undefined && notification.priority !== filter.priority)
                return false;
            if (filter.startDate && notification.createdAt < filter.startDate)
                return false;
            if (filter.endDate && notification.createdAt > filter.endDate)
                return false;
            return true;
        });
    }
    async getNotificationById(userId, notificationId) {
        const notifications = this.notifications.get(userId) || [];
        return notifications.find(notification => notification.id === notificationId) || null;
    }
    async createNotification(userId, notification) {
        const newNotification = new models_1.NotificationEntity({
            ...notification,
            id: this.generateNotificationId(),
            userId,
            read: false,
            dismissed: false,
            createdAt: new Date(),
            updatedAt: new Date()
        });
        const userNotifications = this.notifications.get(userId) || [];
        userNotifications.push(newNotification);
        this.notifications.set(userId, userNotifications);
        await this.save();
        return newNotification;
    }
    async updateNotification(userId, notificationId, updates) {
        const notifications = this.notifications.get(userId) || [];
        const index = notifications.findIndex(notification => notification.id === notificationId);
        if (index === -1) {
            throw new Error('Notification not found');
        }
        const currentNotification = notifications[index];
        const updatedNotification = new models_1.NotificationEntity({
            ...currentNotification,
            ...updates,
            userId, // Ensure userId cannot be changed
            updatedAt: new Date()
        });
        notifications[index] = updatedNotification;
        await this.save();
        return updatedNotification;
    }
    async deleteNotification(userId, notificationId) {
        const notifications = this.notifications.get(userId) || [];
        const filteredNotifications = notifications.filter(notification => notification.id !== notificationId);
        if (filteredNotifications.length === notifications.length) {
            throw new Error('Notification not found');
        }
        this.notifications.set(userId, filteredNotifications);
        await this.save();
    }
    async markAsRead(userId, notificationId) {
        return this.updateNotification(userId, notificationId, { read: true });
    }
    async markAllAsRead(userId) {
        const notifications = this.notifications.get(userId) || [];
        const updatedNotifications = notifications.map(notification => new models_1.NotificationEntity({
            ...notification,
            read: true,
            updatedAt: new Date()
        }));
        this.notifications.set(userId, updatedNotifications);
        await this.save();
    }
    async dismiss(userId, notificationId) {
        return this.updateNotification(userId, notificationId, { dismissed: true });
    }
    async dismissAll(userId) {
        const notifications = this.notifications.get(userId) || [];
        const updatedNotifications = notifications.map(notification => new models_1.NotificationEntity({
            ...notification,
            dismissed: true,
            updatedAt: new Date()
        }));
        this.notifications.set(userId, updatedNotifications);
        await this.save();
    }
    async getUnreadNotifications(userId) {
        return this.getNotifications(userId, { isRead: false });
    }
    async getActiveNotifications(userId) {
        return this.getNotifications(userId, { isDismissed: false });
    }
    async getNotificationsByType(userId, type) {
        return this.getNotifications(userId, { type });
    }
    async getNotificationsByDateRange(userId, startDate, endDate) {
        return this.getNotifications(userId, { startDate, endDate });
    }
    generateNotificationId() {
        return `nt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    async save() {
        const allNotifications = Array.from(this.notifications.values()).flat();
        await this.storageService.set(NotificationService.NOTIFICATIONS_KEY, allNotifications);
    }
}
exports.NotificationService = NotificationService;
NotificationService.NOTIFICATIONS_KEY = 'trade_reviewer_notifications';
