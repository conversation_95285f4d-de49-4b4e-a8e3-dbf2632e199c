import { Notification, NotificationEntity, NotificationType, NotificationPriority } from '../../models';
import { StorageService } from '../storage/storage.service';
export interface NotificationFilter {
    type?: NotificationType;
    isRead?: boolean;
    isDismissed?: boolean;
    startDate?: Date;
    endDate?: Date;
    priority?: NotificationPriority;
}
export declare class NotificationService {
    private static readonly NOTIFICATIONS_KEY;
    private notifications;
    private storageService;
    constructor(storageService: StorageService);
    initialize(userId: string): Promise<NotificationEntity[]>;
    getNotifications(userId: string, filter?: NotificationFilter): Promise<NotificationEntity[]>;
    getNotificationById(userId: string, notificationId: string): Promise<NotificationEntity | null>;
    createNotification(userId: string, notification: Omit<Notification, 'id' | 'userId' | 'read' | 'dismissed' | 'createdAt' | 'updatedAt'>): Promise<NotificationEntity>;
    updateNotification(userId: string, notificationId: string, updates: Partial<Notification>): Promise<NotificationEntity>;
    deleteNotification(userId: string, notificationId: string): Promise<void>;
    markAsRead(userId: string, notificationId: string): Promise<NotificationEntity>;
    markAllAsRead(userId: string): Promise<void>;
    dismiss(userId: string, notificationId: string): Promise<NotificationEntity>;
    dismissAll(userId: string): Promise<void>;
    getUnreadNotifications(userId: string): Promise<NotificationEntity[]>;
    getActiveNotifications(userId: string): Promise<NotificationEntity[]>;
    getNotificationsByType(userId: string, type: NotificationType): Promise<NotificationEntity[]>;
    getNotificationsByDateRange(userId: string, startDate: Date, endDate: Date): Promise<NotificationEntity[]>;
    private generateNotificationId;
    private save;
}
