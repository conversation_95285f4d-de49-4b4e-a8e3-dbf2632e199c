"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorageService = void 0;
class StorageService {
    async getTrades() {
        const trades = await this.get(StorageService.TRADES_KEY);
        return trades || [];
    }
    async saveTrade(trade) {
        const trades = await this.getTrades();
        trades.push(trade);
        await this.set(StorageService.TRADES_KEY, trades);
    }
    async updateTrade(trade) {
        const trades = await this.getTrades();
        const index = trades.findIndex(t => t.id === trade.id);
        if (index !== -1) {
            trades[index] = trade;
            await this.set(StorageService.TRADES_KEY, trades);
        }
    }
    async deleteTrade(tradeId) {
        const trades = await this.getTrades();
        const filteredTrades = trades.filter(t => t.id !== tradeId);
        await this.set(StorageService.TRADES_KEY, filteredTrades);
    }
    async getProfile(userId) {
        const profiles = await this.getProfiles();
        return profiles.find(p => p.id === userId) || null;
    }
    async saveProfile(profile) {
        const profiles = await this.getProfiles();
        const index = profiles.findIndex(p => p.id === profile.id);
        if (index !== -1) {
            profiles[index] = profile;
        }
        else {
            profiles.push(profile);
        }
        await this.set(StorageService.PROFILES_KEY, profiles);
    }
    async updateProfile(profile) {
        await this.saveProfile(profile);
    }
    async deleteProfile(userId) {
        const profiles = await this.getProfiles();
        const filteredProfiles = profiles.filter(p => p.id !== userId);
        await this.set(StorageService.PROFILES_KEY, filteredProfiles);
    }
    async get(key) {
        throw new Error('Method not implemented');
    }
    async set(key, value) {
        throw new Error('Method not implemented');
    }
    async remove(key) {
        throw new Error('Method not implemented');
    }
    async clear() {
        throw new Error('Method not implemented');
    }
    async getProfiles() {
        const profiles = await this.get(StorageService.PROFILES_KEY);
        return profiles || [];
    }
}
exports.StorageService = StorageService;
StorageService.TRADES_KEY = 'trade_reviewer_trades';
StorageService.PROFILES_KEY = 'trade_reviewer_profiles';
