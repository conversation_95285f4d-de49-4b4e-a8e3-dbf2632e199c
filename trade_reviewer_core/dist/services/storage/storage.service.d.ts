import { Trade } from '../../models/trade';
import { User } from '../../models/user';
export interface IStorageService {
    get<T>(key: string): Promise<T | null>;
    set<T>(key: string, value: T): Promise<void>;
    remove(key: string): Promise<void>;
    clear(): Promise<void>;
}
export declare class StorageService implements IStorageService {
    private static readonly TRADES_KEY;
    private static readonly PROFILES_KEY;
    getTrades(): Promise<Trade[]>;
    saveTrade(trade: Trade): Promise<void>;
    updateTrade(trade: Trade): Promise<void>;
    deleteTrade(tradeId: string): Promise<void>;
    getProfile(userId: string): Promise<User | null>;
    saveProfile(profile: User): Promise<void>;
    updateProfile(profile: User): Promise<void>;
    deleteProfile(userId: string): Promise<void>;
    get<T>(key: string): Promise<T | null>;
    set<T>(key: string, value: T): Promise<void>;
    remove(key: string): Promise<void>;
    clear(): Promise<void>;
    private getProfiles;
}
