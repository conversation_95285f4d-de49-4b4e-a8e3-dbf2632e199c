import { z } from 'zod';
import { TradeType } from './trade';

export const MarketMetricsSchema = z.object({
  symbol: z.string(),
  price: z.number(),
  volume: z.number(),
  high: z.number(),
  low: z.number(),
  open: z.number(),
  close: z.number(),
  change: z.number(),
  changePercent: z.number(),
  timestamp: z.date()
});

export const PositionMetricsSchema = z.object({
  symbol: z.string(),
  type: z.nativeEnum(TradeType),
  size: z.number(),
  entryPrice: z.number(),
  currentPrice: z.number(),
  unrealizedPnL: z.number(),
  unrealizedPnLPercent: z.number(),
  risk: z.number(),
  reward: z.number(),
  rrr: z.number()
});

export const AccountMetricsSchema = z.object({
  balance: z.number(),
  equity: z.number(),
  openPositions: z.number(),
  marginUsed: z.number(),
  marginAvailable: z.number(),
  marginLevel: z.number(),
  dailyPnL: z.number(),
  dailyPnLPercent: z.number(),
  drawdown: z.number(),
  drawdownPercent: z.number()
});

export const MetricsSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  timestamp: z.date(),
  market: z.record(z.string(), MarketMetricsSchema),
  positions: z.record(z.string(), PositionMetricsSchema),
  account: AccountMetricsSchema
});

export type MarketMetrics = z.infer<typeof MarketMetricsSchema>;
export type PositionMetrics = z.infer<typeof PositionMetricsSchema>;
export type AccountMetrics = z.infer<typeof AccountMetricsSchema>;
export type Metrics = z.infer<typeof MetricsSchema>;

export class MetricsEntity implements Metrics {
  readonly id: string;
  readonly userId: string;
  timestamp: Date;
  market: Record<string, MarketMetrics>;
  positions: Record<string, PositionMetrics>;
  account: AccountMetrics;

  constructor(data: Metrics) {
    const validated = MetricsSchema.parse(data);
    this.id = validated.id;
    this.userId = validated.userId;
    this.timestamp = validated.timestamp;
    this.market = validated.market;
    this.positions = validated.positions;
    this.account = validated.account;
  }

  getMarketMetrics(symbol: string): MarketMetrics | undefined {
    return this.market[symbol];
  }

  getPositionMetrics(symbol: string): PositionMetrics | undefined {
    return this.positions[symbol];
  }

  get totalUnrealizedPnL(): number {
    return Object.values(this.positions).reduce(
      (sum, position) => sum + position.unrealizedPnL,
      0
    );
  }

  get totalRisk(): number {
    return Object.values(this.positions).reduce(
      (sum, position) => sum + position.risk,
      0
    );
  }

  get averageRRR(): number {
    const positions = Object.values(this.positions);
    if (positions.length === 0) return 0;
    return positions.reduce((sum, position) => sum + position.rrr, 0) / positions.length;
  }

  get marginUtilization(): number {
    return (this.account.marginUsed / (this.account.marginUsed + this.account.marginAvailable)) * 100;
  }

  updateMarketMetrics(symbol: string, metrics: MarketMetrics): void {
    this.market[symbol] = metrics;
    this.timestamp = new Date();
  }

  updatePositionMetrics(symbol: string, metrics: PositionMetrics): void {
    this.positions[symbol] = metrics;
    this.timestamp = new Date();
  }

  updateAccountMetrics(metrics: AccountMetrics): void {
    this.account = metrics;
    this.timestamp = new Date();
  }
} 