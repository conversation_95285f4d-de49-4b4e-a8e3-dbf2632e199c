import { z } from 'zod';

export enum MarketCondition {
  BULLISH = 'BULLISH',
  BEARISH = 'BEARISH',
  SIDEWAYS = 'SIDEWAYS',
  VOLATILE = 'VOLATILE',
  TRENDING = 'TRENDING',
  RANGING = 'RANGING'
}

export enum TradingMistake {
  FOMO = 'FOMO',
  EARLY_ENTRY = 'EARLY_ENTRY',
  LATE_ENTRY = 'LATE_ENTRY',
  MOVED_STOP_LOSS = 'MOVED_STOP_LOSS',
  EARLY_EXIT = 'EARLY_EXIT',
  LATE_EXIT = 'LATE_EXIT',
  POSITION_SIZING = 'POSITION_SIZING',
  IGNORED_PLAN = 'IGNORED_PLAN',
  EMOTIONAL_TRADING = 'EMOTIONAL_TRADING',
  REVENGE_TRADING = 'REVENGE_TRADING'
}

export enum EmotionalState {
  CALM = 'CALM',
  CONFIDENT = 'CONFIDENT',
  ANXIOUS = 'ANXIOUS',
  FEARFUL = 'FEARFUL',
  GREEDY = 'GREEDY',
  FRUSTRATED = 'FRUSTRATED',
  TILTED = 'TILTED'
}

export const TradePlanSchema = z.object({
  setup: z.string().min(1),
  entry: z.string().min(1),
  stopLoss: z.string().min(1),
  targets: z.array(z.string()).min(1),
  timeFrame: z.string().min(1),
  risk: z.number().min(0),
  reward: z.number().min(0)
});

export const JournalEntrySchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  tradeId: z.string().uuid(),
  date: z.date(),
  marketCondition: z.nativeEnum(MarketCondition),
  emotionalState: z.nativeEnum(EmotionalState),
  tradePlan: TradePlanSchema,
  mistakes: z.array(z.nativeEnum(TradingMistake)).default([]),
  analysis: z.string().min(1),
  lessons: z.array(z.string()).min(1),
  screenshots: z.array(z.string().url()).default([]),
  rating: z.number().int().min(1).max(5),
  tags: z.array(z.string()).default([]),
  createdAt: z.date(),
  updatedAt: z.date()
});

export type TradePlan = z.infer<typeof TradePlanSchema>;
export type JournalEntry = z.infer<typeof JournalEntrySchema>;

export class JournalEntryEntity implements JournalEntry {
  id!: string;
  userId!: string;
  tradeId!: string;
  date!: Date;
  marketCondition!: MarketCondition;
  emotionalState!: EmotionalState;
  tradePlan!: TradePlan;
  mistakes: TradingMistake[] = [];
  analysis!: string;
  lessons!: string[];
  screenshots: string[] = [];
  rating!: number;
  tags: string[] = [];
  createdAt!: Date;
  updatedAt!: Date;

  constructor(data: JournalEntry) {
    Object.assign(this, JournalEntrySchema.parse(data));
  }

  get riskRewardRatio(): number {
    return this.tradePlan.reward / this.tradePlan.risk;
  }

  get hasScreenshots(): boolean {
    return this.screenshots.length > 0;
  }

  get hasMistakes(): boolean {
    return this.mistakes.length > 0;
  }

  get isEmotionallyNeutral(): boolean {
    return [EmotionalState.CALM, EmotionalState.CONFIDENT].includes(this.emotionalState);
  }

  addMistake(mistake: TradingMistake): void {
    if (!this.mistakes.includes(mistake)) {
      this.mistakes.push(mistake);
      this.updatedAt = new Date();
    }
  }

  addLesson(lesson: string): void {
    if (!this.lessons.includes(lesson)) {
      this.lessons.push(lesson);
      this.updatedAt = new Date();
    }
  }

  addScreenshot(url: string): void {
    if (!this.screenshots.includes(url)) {
      this.screenshots.push(url);
      this.updatedAt = new Date();
    }
  }

  updateAnalysis(analysis: string): void {
    this.analysis = analysis;
    this.updatedAt = new Date();
  }

  updateRating(rating: number): void {
    if (rating >= 1 && rating <= 5) {
      this.rating = rating;
      this.updatedAt = new Date();
    }
  }
} 