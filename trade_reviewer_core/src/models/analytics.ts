import { z } from 'zod';

export enum TimeFrame {
  ALL = 'ALL',
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY',
  YEARLY = 'YEARLY',
  CUSTOM = 'CUSTOM'
}

export const AnalyticsMetricsSchema = z.object({
  totalTrades: z.number(),
  winningTrades: z.number(),
  losingTrades: z.number(),
  breakEvenTrades: z.number(),
  totalPnL: z.number(),
  totalRiskAmount: z.number(),
  totalRewardAmount: z.number(),
  averageRRR: z.number(),
  winRate: z.number(),
  profitFactor: z.number(),
  averageWinAmount: z.number(),
  averageLossAmount: z.number(),
  largestWin: z.number(),
  largestLoss: z.number(),
  totalFees: z.number()
});

export const AnalyticsSchema = z.object({
  userId: z.string(),
  timeFrame: z.nativeEnum(TimeFrame),
  startDate: z.date(),
  endDate: z.date(),
  metrics: AnalyticsMetricsSchema,
  updatedAt: z.date(),
  byType: z.record(z.string(), AnalyticsMetricsSchema).optional(),
  bySymbol: z.record(z.string(), AnalyticsMetricsSchema).optional()
});

export type AnalyticsMetrics = z.infer<typeof AnalyticsMetricsSchema>;
export type Analytics = z.infer<typeof AnalyticsSchema>;

export class AnalyticsEntity implements Analytics {
  userId!: string;
  timeFrame!: TimeFrame;
  startDate!: Date;
  endDate!: Date;
  metrics!: AnalyticsMetrics;
  updatedAt!: Date;
  byType?: Record<string, AnalyticsMetrics>;
  bySymbol?: Record<string, AnalyticsMetrics>;

  constructor(data: Analytics) {
    Object.assign(this, AnalyticsSchema.parse(data));
  }

  get totalTrades(): number {
    return this.metrics.totalTrades;
  }

  get winRate(): number {
    return this.metrics.winRate;
  }

  get profitFactor(): number {
    return this.metrics.profitFactor;
  }

  get averageRRR(): number {
    return this.metrics.averageRRR;
  }

  get totalPnL(): number {
    return this.metrics.totalPnL;
  }

  get totalFees(): number {
    return this.metrics.totalFees;
  }

  get netPnL(): number {
    return this.totalPnL - this.totalFees;
  }
} 