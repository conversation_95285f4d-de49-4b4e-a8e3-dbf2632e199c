import { z } from 'zod';

export enum TradeType {
  LONG = 'LONG',
  SHORT = 'SHORT'
}

export enum TradeStatus {
  OPEN = 'OPEN',
  CLOSED = 'CLOSED',
  CANCELLED = 'CANCELLED'
}

export interface RiskRewardRatio {
  risk: number;
  reward: number;
  ratio: number;
}

export const TradeSchema = z.object({
  id: z.string().uuid(),
  userId: z.string(),
  symbol: z.string(),
  type: z.nativeEnum(TradeType),
  status: z.nativeEnum(TradeStatus),
  entryPrice: z.number(),
  exitPrice: z.number().optional(),
  stopLoss: z.number(),
  takeProfit: z.number(),
  quantity: z.number(),
  fees: z.number().default(0),
  tags: z.array(z.string()).default([]),
  notes: z.string().optional(),
  entryDate: z.date(),
  exitDate: z.date().optional(),
  createdAt: z.date(),
  updatedAt: z.date()
});

export type Trade = z.infer<typeof TradeSchema>;

export class TradeEntity implements Trade {
  id!: string;
  userId!: string;
  symbol!: string;
  type!: TradeType;
  status!: TradeStatus;
  entryPrice!: number;
  exitPrice?: number;
  stopLoss!: number;
  takeProfit!: number;
  quantity!: number;
  fees: number = 0;
  tags: string[] = [];
  notes?: string;
  entryDate!: Date;
  exitDate?: Date;
  createdAt!: Date;
  updatedAt!: Date;

  constructor(data: Trade) {
    Object.assign(this, TradeSchema.parse(data));
  }

  calculatePnL(): number {
    if (!this.exitPrice) return 0;
    const multiplier = this.type === TradeType.LONG ? 1 : -1;
    return multiplier * (this.exitPrice - this.entryPrice) * this.quantity - this.fees;
  }

  calculateRRR(): RiskRewardRatio | null {
    if (this.status !== TradeStatus.CLOSED || !this.exitPrice) return null;

    const risk = Math.abs(this.entryPrice - this.stopLoss) * this.quantity;
    const reward = Math.abs(this.takeProfit - this.entryPrice) * this.quantity;
    const ratio = reward / risk;

    return { risk, reward, ratio };
  }

  close(exitPrice: number, exitDate: Date = new Date()): void {
    this.exitPrice = exitPrice;
    this.exitDate = exitDate;
    this.status = TradeStatus.CLOSED;
    this.updatedAt = new Date();
  }

  cancel(): void {
    this.status = TradeStatus.CANCELLED;
    this.updatedAt = new Date();
  }

  addTags(newTags: string[]): void {
    this.tags = [...new Set([...this.tags, ...newTags])];
    this.updatedAt = new Date();
  }

  removeTags(tagsToRemove: string[]): void {
    this.tags = this.tags.filter(tag => !tagsToRemove.includes(tag));
    this.updatedAt = new Date();
  }

  updateStopLoss(price: number): void {
    this.stopLoss = price;
    this.updatedAt = new Date();
  }

  updateTakeProfit(price: number): void {
    this.takeProfit = price;
    this.updatedAt = new Date();
  }

  addNotes(notes: string): void {
    this.notes = notes;
    this.updatedAt = new Date();
  }
} 