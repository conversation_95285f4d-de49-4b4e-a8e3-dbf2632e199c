import { z } from 'zod';

export enum GoalType {
  WIN_RATE = 'WIN_RATE',
  PROFIT = 'PROFIT',
  AVERAGE_RRR = 'AVERAGE_RRR',
  CONSECUTIVE_WINS = 'CONSECUTIVE_WINS',
  MAX_DRAWDOWN = 'MAX_DRAWDOWN',
  PROFIT_FACTOR = 'PROFIT_FACTOR',
  SHARPE_RATIO = 'SHARPE_RATIO',
  TRADE_COUNT = 'TRADE_COUNT'
}

export const GoalSchema = z.object({
  id: z.string().uuid(),
  userId: z.string(),
  type: z.nativeEnum(GoalType),
  target: z.number(),
  progress: z.number().default(0),
  isActive: z.boolean().default(true),
  createdAt: z.date(),
  updatedAt: z.date()
});

export type Goal = z.infer<typeof GoalSchema>;

export class GoalEntity implements Goal {
  id!: string;
  userId!: string;
  type!: GoalType;
  target!: number;
  progress: number = 0;
  isActive: boolean = true;
  createdAt!: Date;
  updatedAt!: Date;

  constructor(data: Goal) {
    Object.assign(this, GoalSchema.parse(data));
  }
} 