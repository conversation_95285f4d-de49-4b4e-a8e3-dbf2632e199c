import { z } from 'zod';

export enum AccountType {
  DEMO = 'DEMO',
  LIVE = 'LIVE'
}

export enum ExperienceLevel {
  BEGINNER = 'BEGINNER',
  INTERMEDIATE = 'INTERMEDIATE',
  ADVANCED = 'ADVANCED',
  PROFESSIONAL = 'PROFESSIONAL'
}

export const ProfileSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  name: z.string().min(1),
  email: z.string().email(),
  avatar: z.string().url().optional(),
  bio: z.string().optional(),
  accountType: z.nativeEnum(AccountType),
  experienceLevel: z.nativeEnum(ExperienceLevel),
  tradingStyle: z.array(z.string()),
  preferredMarkets: z.array(z.string()),
  preferredTimeframes: z.array(z.string()),
  initialBalance: z.number().positive(),
  currentBalance: z.number(),
  currency: z.string(),
  riskPerTrade: z.number().min(0).max(100),
  maxDrawdown: z.number().min(0).max(100),
  profitTarget: z.number().positive().optional(),
  createdAt: z.date(),
  updatedAt: z.date()
});

export type Profile = z.infer<typeof ProfileSchema>;

export class ProfileEntity implements Profile {
  readonly id: string;
  readonly userId: string;
  readonly name: string;
  readonly email: string;
  readonly avatar?: string;
  readonly bio?: string;
  readonly accountType: AccountType;
  readonly experienceLevel: ExperienceLevel;
  readonly tradingStyle: string[];
  readonly preferredMarkets: string[];
  readonly preferredTimeframes: string[];
  readonly initialBalance: number;
  currentBalance: number;
  readonly currency: string;
  riskPerTrade: number;
  maxDrawdown: number;
  profitTarget?: number;
  readonly createdAt: Date;
  updatedAt: Date;

  constructor(data: Profile) {
    const validated = ProfileSchema.parse(data);
    this.id = validated.id;
    this.userId = validated.userId;
    this.name = validated.name;
    this.email = validated.email;
    this.avatar = validated.avatar;
    this.bio = validated.bio;
    this.accountType = validated.accountType;
    this.experienceLevel = validated.experienceLevel;
    this.tradingStyle = validated.tradingStyle;
    this.preferredMarkets = validated.preferredMarkets;
    this.preferredTimeframes = validated.preferredTimeframes;
    this.initialBalance = validated.initialBalance;
    this.currentBalance = validated.currentBalance;
    this.currency = validated.currency;
    this.riskPerTrade = validated.riskPerTrade;
    this.maxDrawdown = validated.maxDrawdown;
    this.profitTarget = validated.profitTarget;
    this.createdAt = validated.createdAt;
    this.updatedAt = validated.updatedAt;
  }

  get drawdown(): number {
    if (this.currentBalance >= this.initialBalance) return 0;
    return ((this.initialBalance - this.currentBalance) / this.initialBalance) * 100;
  }

  get isAtRisk(): boolean {
    return this.drawdown >= this.maxDrawdown;
  }

  get profitPercentage(): number {
    return ((this.currentBalance - this.initialBalance) / this.initialBalance) * 100;
  }

  get hasReachedTarget(): boolean {
    if (!this.profitTarget) return false;
    return this.profitPercentage >= this.profitTarget;
  }

  updateBalance(newBalance: number): void {
    this.currentBalance = newBalance;
    this.updatedAt = new Date();
  }

  updateRiskParameters(riskPerTrade: number, maxDrawdown: number): void {
    this.riskPerTrade = Math.min(Math.max(riskPerTrade, 0), 100);
    this.maxDrawdown = Math.min(Math.max(maxDrawdown, 0), 100);
    this.updatedAt = new Date();
  }

  calculatePositionSize(price: number, stopLoss: number): number {
    const riskAmount = (this.currentBalance * this.riskPerTrade) / 100;
    const riskPerUnit = Math.abs(price - stopLoss);
    return riskAmount / riskPerUnit;
  }
} 