import { z } from 'zod';

export enum NotificationType {
  TRADE_ALERT = 'TRADE_ALERT',
  PRICE_ALERT = 'PRICE_ALERT',
  GOAL_ACHIEVED = 'GOAL_ACHIEVED',
  RISK_WARNING = 'RISK_WARNING',
  SYSTEM_UPDATE = 'SYSTEM_UPDATE',
  BACKUP_REMINDER = 'BACKUP_REMINDER',
  JOURNAL_REMINDER = 'JOURNAL_REMINDER',
  TRADE_STATS = 'TRADE_STATS'
}

export enum NotificationPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

export enum NotificationChannel {
  IN_APP = 'IN_APP',
  EMAIL = 'EMAIL',
  PUSH = 'PUSH',
  SMS = 'SMS'
}

export const NotificationTriggerSchema = z.object({
  type: z.nativeEnum(NotificationType),
  conditions: z.record(z.string(), z.any()),
  channels: z.array(z.nativeEnum(NotificationChannel)).min(1),
  schedule: z.string().optional(), // cron expression
  active: z.boolean().default(true)
});

export const NotificationSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  type: z.nativeEnum(NotificationType),
  title: z.string().min(1),
  message: z.string().min(1),
  priority: z.nativeEnum(NotificationPriority).default(NotificationPriority.MEDIUM),
  channels: z.array(z.nativeEnum(NotificationChannel)).min(1),
  data: z.record(z.string(), z.any()).optional(),
  read: z.boolean().default(false),
  dismissed: z.boolean().default(false),
  scheduledFor: z.date().optional(),
  expiresAt: z.date().optional(),
  createdAt: z.date(),
  updatedAt: z.date()
});

export type NotificationTrigger = z.infer<typeof NotificationTriggerSchema>;
export type Notification = z.infer<typeof NotificationSchema>;

export class NotificationEntity implements Notification {
  id!: string;
  userId!: string;
  type!: NotificationType;
  title!: string;
  message!: string;
  priority: NotificationPriority = NotificationPriority.MEDIUM;
  channels!: NotificationChannel[];
  data?: Record<string, any>;
  read: boolean = false;
  dismissed: boolean = false;
  scheduledFor?: Date;
  expiresAt?: Date;
  createdAt!: Date;
  updatedAt!: Date;

  constructor(data: Notification) {
    Object.assign(this, NotificationSchema.parse(data));
  }

  get isExpired(): boolean {
    return this.expiresAt ? new Date() > this.expiresAt : false;
  }

  get isScheduled(): boolean {
    return !!this.scheduledFor && this.scheduledFor > new Date();
  }

  get isUrgent(): boolean {
    return this.priority === NotificationPriority.URGENT;
  }

  get shouldDisplay(): boolean {
    return !this.dismissed && !this.isExpired && (!this.scheduledFor || !this.isScheduled);
  }

  get channelPreferences(): string[] {
    return this.channels.map(channel => channel.toLowerCase());
  }

  markAsRead(): void {
    this.read = true;
    this.updatedAt = new Date();
  }

  dismiss(): void {
    this.dismissed = true;
    this.updatedAt = new Date();
  }

  reschedule(newDate: Date): void {
    this.scheduledFor = newDate;
    this.updatedAt = new Date();
  }

  updateExpiry(newDate: Date): void {
    this.expiresAt = newDate;
    this.updatedAt = new Date();
  }

  addChannel(channel: NotificationChannel): void {
    if (!this.channels.includes(channel)) {
      this.channels.push(channel);
      this.updatedAt = new Date();
    }
  }

  removeChannel(channel: NotificationChannel): void {
    const index = this.channels.indexOf(channel);
    if (index > -1) {
      this.channels.splice(index, 1);
      this.updatedAt = new Date();
    }
  }

  updatePriority(priority: NotificationPriority): void {
    this.priority = priority;
    this.updatedAt = new Date();
  }
} 