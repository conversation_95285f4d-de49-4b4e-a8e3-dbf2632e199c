import { z } from 'zod';

export enum ChartTimeframe {
  M1 = 'M1',
  M5 = 'M5',
  M15 = 'M15',
  M30 = 'M30',
  H1 = 'H1',
  H4 = 'H4',
  D1 = 'D1',
  W1 = 'W1',
  MN = 'MN'
}

export enum RiskCalculationType {
  FIXED = 'FIXED',
  PERCENTAGE = 'PERCENTAGE',
  R_MULTIPLE = 'R_MULTIPLE'
}

export enum PnLDisplayMode {
  CURRENCY = 'CURRENCY',
  PERCENTAGE = 'PERCENTAGE',
  R_MULTIPLE = 'R_MULTIPLE'
}

export const TradeDefaultsSchema = z.object({
  defaultRisk: z.number().min(0),
  defaultPosition: z.number().min(0),
  defaultTimeframe: z.nativeEnum(ChartTimeframe),
  riskCalculationType: z.nativeEnum(RiskCalculationType),
  defaultCommission: z.number().min(0),
  defaultFees: z.number().min(0)
});

export const DisplaySettingsSchema = z.object({
  theme: z.string(),
  pnlDisplayMode: z.nativeEnum(PnLDisplayMode),
  showRunningPnL: z.boolean(),
  showEquityCurve: z.boolean(),
  showTradeStatistics: z.boolean(),
  compactMode: z.boolean(),
  dateFormat: z.string(),
  timeFormat: z.string(),
  timezone: z.string(),
  goalNotificationsEnabled: z.boolean().default(true),
  weeklyReportsEnabled: z.boolean().default(true),
  monthlyReportsEnabled: z.boolean().default(true),
  tradeAlertsEnabled: z.boolean().default(true),
  marketNewsEnabled: z.boolean().default(true),
  priceAlertsEnabled: z.boolean().default(true),
  journalRemindersEnabled: z.boolean().default(true),
  emailNotificationsEnabled: z.boolean().default(true),
  pushNotificationsEnabled: z.boolean().default(true),
  smsNotificationsEnabled: z.boolean().default(false),
  defaultAccountView: z.string().default('all'),
  defaultTimePeriod: z.string().default('month'),
});

export const BacktestSettingsSchema = z.object({
  initialCapital: z.number().min(0),
  useFixedPosition: z.boolean(),
  maxRiskPerTrade: z.number().min(0),
  maxOpenTrades: z.number().min(1),
  includeFees: z.boolean(),
  includeSlippage: z.boolean(),
  slippageAmount: z.number().min(0)
});

export const GoalSettingsSchema = z.object({
  activeMetrics: z.record(z.string(), z.boolean()),
  metricGoals: z.record(z.string(), z.number()),
});

export const AccountSettingsSchema = z.object({
  tradingAccounts: z.array(z.object({
    id: z.string(),
    name: z.string(),
    provider: z.string(),
    initialCapital: z.number().optional(),
  })),
  apiKeys: z.array(z.object({
    id: z.string(),
    name: z.string(),
    key: z.string(),
    secret: z.string().optional(),
  })),
  autoSync: z.boolean(),
  syncFrequency: z.number(),
});

export const NotificationSettingsSchema = z.object({
  // Global Settings
  enabled: z.boolean().default(true),
  soundEnabled: z.boolean().default(true),
  hapticEnabled: z.boolean().default(true),

  // Delivery Methods
  pushEnabled: z.boolean().default(true),
  emailEnabled: z.boolean().default(true),
  inAppEnabled: z.boolean().default(true),

  // Trading Notifications
  tradeExecutionAlertsEnabled: z.boolean().default(true),
  journalRemindersEnabled: z.boolean().default(true),
  weeklyReportsEnabled: z.boolean().default(true),
  monthlyReportsEnabled: z.boolean().default(true),
  goalAlertsEnabled: z.boolean().default(true),

  // System Notifications
  accountStatusAlertsEnabled: z.boolean().default(true),
  systemMaintenanceAlertsEnabled: z.boolean().default(true),
  securityAlertsEnabled: z.boolean().default(true),

  // Notification Preferences
  quietHoursStart: z.string().default('21:00'),
  quietHoursEnd: z.string().default('09:00'),
  journalReminderFrequency: z.string().default('daily'),
  notificationPriority: z.string().default('high'),
});

export const SettingsSchema = z.object({
  userId: z.string(),
  tradeDefaults: TradeDefaultsSchema,
  display: DisplaySettingsSchema,
  backtest: BacktestSettingsSchema,
  goals: GoalSettingsSchema,
  accounts: AccountSettingsSchema,
  notifications: NotificationSettingsSchema,
  autoSync: z.boolean(),
  autoBackup: z.boolean(),
  backupFrequency: z.number().min(1),
  updatedAt: z.date(),
});

export type TradeDefaults = z.infer<typeof TradeDefaultsSchema>;
export type DisplaySettings = z.infer<typeof DisplaySettingsSchema>;
export type BacktestSettings = z.infer<typeof BacktestSettingsSchema>;
export type GoalSettings = z.infer<typeof GoalSettingsSchema>;
export type AccountSettings = z.infer<typeof AccountSettingsSchema>;
export type NotificationSettings = z.infer<typeof NotificationSettingsSchema>;
export type Settings = z.infer<typeof SettingsSchema>;

export class SettingsEntity implements Settings {
  userId!: string;
  tradeDefaults!: TradeDefaults;
  display!: DisplaySettings;
  backtest!: BacktestSettings;
  goals!: GoalSettings;
  accounts!: AccountSettings;
  notifications!: NotificationSettings;
  autoSync!: boolean;
  autoBackup!: boolean;
  backupFrequency!: number;
  updatedAt!: Date;

  constructor(settings: Settings) {
    const validated = SettingsSchema.parse(settings);
    Object.assign(this, validated);
  }

  updateTradeDefaults(defaults: Partial<TradeDefaults>): void {
    this.tradeDefaults = {
      ...this.tradeDefaults,
      ...defaults
    };
    this.updatedAt = new Date();
  }

  updateDisplaySettings(display: Partial<DisplaySettings>): void {
    this.display = {
      ...this.display,
      ...display
    };
    this.updatedAt = new Date();
  }

  updateBacktestSettings(backtest: Partial<BacktestSettings>): void {
    this.backtest = {
      ...this.backtest,
      ...backtest
    };
    this.updatedAt = new Date();
  }

  updateNotificationSettings(notifications: Partial<NotificationSettings>): void {
    this.notifications = {
      ...this.notifications,
      ...notifications
    };
    this.updatedAt = new Date();
  }

  toJSON(): Settings {
    return {
      userId: this.userId,
      tradeDefaults: this.tradeDefaults,
      display: this.display,
      backtest: this.backtest,
      goals: this.goals,
      accounts: this.accounts,
      notifications: this.notifications,
      autoSync: this.autoSync,
      autoBackup: this.autoBackup,
      backupFrequency: this.backupFrequency,
      updatedAt: this.updatedAt
    };
  }
} 