import { z } from 'zod';

export enum UserRole {
  USER = 'USER',
  PREMIUM = 'PREMIUM',
  ADMIN = 'ADMIN'
}

export enum NotificationPreference {
  ALL = 'ALL',
  IMPORTANT = 'IMPORTANT',
  NONE = 'NONE'
}

export const UserPreferencesSchema = z.object({
  notificationPreference: z.nativeEnum(NotificationPreference).default(NotificationPreference.IMPORTANT),
  darkMode: z.boolean().default(false),
  defaultCurrency: z.string().default('USD'),
  timezone: z.string().default('UTC'),
  language: z.string().default('en'),
  showPnLInPercent: z.boolean().default(true),
  autoSyncEnabled: z.boolean().default(true)
});

export const UserSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  name: z.string().min(2).max(50),
  role: z.nativeEnum(UserRole).default(UserRole.USER),
  preferences: UserPreferencesSchema,
  createdAt: z.date(),
  updatedAt: z.date(),
  lastLoginAt: z.date().optional(),
  isEmailVerified: z.boolean().default(false),
  profileImageUrl: z.string().url().optional()
});

export type UserPreferences = z.infer<typeof UserPreferencesSchema>;
export type User = z.infer<typeof UserSchema>;

export class UserEntity implements User {
  id!: string;
  email!: string;
  name!: string;
  role: UserRole = UserRole.USER;
  preferences!: UserPreferences;
  createdAt!: Date;
  updatedAt!: Date;
  lastLoginAt?: Date;
  isEmailVerified: boolean = false;
  profileImageUrl?: string;

  constructor(data: User) {
    Object.assign(this, UserSchema.parse(data));
  }

  get isAdmin(): boolean {
    return this.role === UserRole.ADMIN;
  }

  get isPremium(): boolean {
    return this.role === UserRole.PREMIUM || this.isAdmin;
  }

  get displayName(): string {
    return this.name || this.email.split('@')[0];
  }

  get canAccessPremiumFeatures(): boolean {
    return this.isPremium;
  }

  updatePreferences(newPreferences: Partial<UserPreferences>): void {
    this.preferences = {
      ...this.preferences,
      ...newPreferences
    };
    this.updatedAt = new Date();
  }

  verifyEmail(): void {
    this.isEmailVerified = true;
    this.updatedAt = new Date();
  }

  updateLastLogin(): void {
    this.lastLoginAt = new Date();
    this.updatedAt = new Date();
  }
} 