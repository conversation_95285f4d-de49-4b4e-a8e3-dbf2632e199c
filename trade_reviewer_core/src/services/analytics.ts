import { Trade, TradeEntity, TradeStatus, TradeType } from '../models/trade';
import { TimeFrame } from '../models/timeframe';
import { StorageService } from './storage';

export interface TradeAnalytics {
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  winRate: number;
  averageRRR: number;
  profitFactor: number;
  totalPnL: number;
  averagePnL: number;
  largestWin: number;
  largestLoss: number;
  averageWin: number;
  averageLoss: number;
  consecutiveWins: number;
  consecutiveLosses: number;
  maxDrawdown: number;
  sharpeRatio: number;
  trades: Trade[];
}

export class AnalyticsService {
  constructor(private storageService: StorageService) {}

  async getAnalytics(timeFrame: TimeFrame): Promise<TradeAnalytics> {
    const trades = await this.storageService.getTrades();
    const tradeEntities = trades.map((trade: Trade) => new TradeEntity(trade));
    const filteredTrades = this.filterTradesByTimeFrame(tradeEntities, timeFrame);
    const closedTrades = filteredTrades.filter(trade => trade.status === TradeStatus.CLOSED);

    if (closedTrades.length === 0) {
      return this.getEmptyAnalytics();
    }

    const winningTrades = closedTrades.filter(trade => trade.calculatePnL() > 0);
    const losingTrades = closedTrades.filter(trade => trade.calculatePnL() < 0);

    const totalPnL = closedTrades.reduce((sum, trade) => sum + trade.calculatePnL(), 0);
    const winningPnL = winningTrades.reduce((sum, trade) => sum + trade.calculatePnL(), 0);
    const losingPnL = losingTrades.reduce((sum, trade) => sum + trade.calculatePnL(), 0);

    const rrrs = closedTrades
      .map(trade => trade.calculateRRR())
      .filter((rrr): rrr is NonNullable<typeof rrr> => rrr !== null);

    const averageRRR = rrrs.length > 0
      ? rrrs.reduce((sum, rrr) => sum + rrr.ratio, 0) / rrrs.length
      : 0;

    return {
      totalTrades: closedTrades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate: (winningTrades.length / closedTrades.length) * 100,
      averageRRR,
      profitFactor: Math.abs(winningPnL / losingPnL) || 0,
      totalPnL,
      averagePnL: totalPnL / closedTrades.length,
      largestWin: Math.max(...winningTrades.map(trade => trade.calculatePnL()), 0),
      largestLoss: Math.min(...losingTrades.map(trade => trade.calculatePnL()), 0),
      averageWin: winningTrades.length > 0 ? winningPnL / winningTrades.length : 0,
      averageLoss: losingTrades.length > 0 ? losingPnL / losingTrades.length : 0,
      consecutiveWins: this.calculateConsecutiveWins(closedTrades),
      consecutiveLosses: this.calculateConsecutiveLosses(closedTrades),
      maxDrawdown: this.calculateMaxDrawdown(closedTrades),
      sharpeRatio: this.calculateSharpeRatio(closedTrades),
      trades: filteredTrades,
    };
  }

  private filterTradesByTimeFrame(trades: TradeEntity[], timeFrame: TimeFrame): TradeEntity[] {
    const now = new Date();
    const startDate = new Date();

    switch (timeFrame) {
      case TimeFrame.DAY:
        startDate.setDate(now.getDate() - 1);
        break;
      case TimeFrame.WEEK:
        startDate.setDate(now.getDate() - 7);
        break;
      case TimeFrame.MONTH:
        startDate.setMonth(now.getMonth() - 1);
        break;
      case TimeFrame.YEAR:
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      case TimeFrame.ALL:
        return trades;
    }

    return trades.filter(trade => trade.entryDate >= startDate);
  }

  private calculateConsecutiveWins(trades: TradeEntity[]): number {
    return this.calculateConsecutive(trades, true);
  }

  private calculateConsecutiveLosses(trades: TradeEntity[]): number {
    return this.calculateConsecutive(trades, false);
  }

  private calculateConsecutive(trades: TradeEntity[], isWin: boolean): number {
    let maxConsecutive = 0;
    let currentConsecutive = 0;

    for (const trade of trades) {
      const pnl = trade.calculatePnL();
      if ((isWin && pnl > 0) || (!isWin && pnl < 0)) {
        currentConsecutive++;
        maxConsecutive = Math.max(maxConsecutive, currentConsecutive);
      } else {
        currentConsecutive = 0;
      }
    }

    return maxConsecutive;
  }

  private calculateMaxDrawdown(trades: TradeEntity[]): number {
    let peak = 0;
    let maxDrawdown = 0;
    let currentValue = 0;

    for (const trade of trades) {
      currentValue += trade.calculatePnL();
      peak = Math.max(peak, currentValue);
      maxDrawdown = Math.max(maxDrawdown, peak - currentValue);
    }

    return maxDrawdown;
  }

  private calculateSharpeRatio(trades: TradeEntity[]): number {
    if (trades.length < 2) return 0;

    const returns = trades.map(trade => trade.calculatePnL());
    const averageReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - averageReturn, 2), 0) / (returns.length - 1);
    const standardDeviation = Math.sqrt(variance);

    return standardDeviation === 0 ? 0 : averageReturn / standardDeviation;
  }

  private getEmptyAnalytics(): TradeAnalytics {
    return {
      totalTrades: 0,
      winningTrades: 0,
      losingTrades: 0,
      winRate: 0,
      averageRRR: 0,
      profitFactor: 0,
      totalPnL: 0,
      averagePnL: 0,
      largestWin: 0,
      largestLoss: 0,
      averageWin: 0,
      averageLoss: 0,
      consecutiveWins: 0,
      consecutiveLosses: 0,
      maxDrawdown: 0,
      sharpeRatio: 0,
      trades: [],
    };
  }
} 