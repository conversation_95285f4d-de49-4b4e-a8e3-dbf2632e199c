import { 
  Notification, 
  NotificationEntity, 
  NotificationType 
} from '../../models';
import { StorageService } from '../storage/storage.service';

export class NotificationsService {
  private static readonly NOTIFICATIONS_KEY = 'trade_reviewer_notifications';
  private notifications: NotificationEntity[] = [];
  private storageService: StorageService;

  constructor(storageService: StorageService) {
    this.storageService = storageService;
  }

  async initialize(userId: string): Promise<NotificationEntity[]> {
    const savedNotifications = await this.storageService.get<Notification[]>(NotificationsService.NOTIFICATIONS_KEY);
    
    if (savedNotifications) {
      this.notifications = savedNotifications
        .filter(n => n.userId === userId)
        .map(n => new NotificationEntity(n));
    }

    return this.notifications;
  }

  async getNotifications(userId: string): Promise<NotificationEntity[]> {
    return this.notifications.filter(n => n.userId === userId);
  }

  async getUnreadNotifications(userId: string): Promise<NotificationEntity[]> {
    return this.notifications.filter(n => n.userId === userId && !n.read);
  }

  async addNotification(notification: Notification): Promise<NotificationEntity> {
    const entity = new NotificationEntity(notification);
    this.notifications.push(entity);
    await this.save();
    return entity;
  }

  async markAsRead(notificationId: string, userId: string): Promise<NotificationEntity | null> {
    const notification = this.notifications.find(
      n => n.id === notificationId && n.userId === userId
    );

    if (notification) {
      notification.markAsRead();
      await this.save();
      return notification;
    }

    return null;
  }

  async markAllAsRead(userId: string): Promise<NotificationEntity[]> {
    const userNotifications = this.notifications.filter(n => n.userId === userId);
    userNotifications.forEach(n => n.markAsRead());
    await this.save();
    return userNotifications;
  }

  async dismiss(notificationId: string, userId: string): Promise<NotificationEntity | null> {
    const notification = this.notifications.find(
      n => n.id === notificationId && n.userId === userId
    );

    if (notification) {
      notification.dismiss();
      await this.save();
      return notification;
    }

    return null;
  }

  async dismissAll(userId: string): Promise<NotificationEntity[]> {
    const userNotifications = this.notifications.filter(n => n.userId === userId);
    userNotifications.forEach(n => n.dismiss());
    await this.save();
    return userNotifications;
  }

  async removeExpired(): Promise<void> {
    const now = new Date();
    this.notifications = this.notifications.filter(n => {
      if (!n.expiresAt) return true;
      return n.expiresAt > now;
    });
    await this.save();
  }

  async clear(userId: string): Promise<void> {
    this.notifications = this.notifications.filter(n => n.userId !== userId);
    await this.save();
  }

  private async save(): Promise<void> {
    await this.storageService.set(
      NotificationsService.NOTIFICATIONS_KEY,
      this.notifications
    );
  }
} 