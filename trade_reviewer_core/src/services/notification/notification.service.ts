import { Notification, NotificationEntity, NotificationType, NotificationPriority } from '../../models';
import { StorageService } from '../storage/storage.service';

export interface NotificationFilter {
  type?: NotificationType;
  isRead?: boolean;
  isDismissed?: boolean;
  startDate?: Date;
  endDate?: Date;
  priority?: NotificationPriority;
}

export class NotificationService {
  private static readonly NOTIFICATIONS_KEY = 'trade_reviewer_notifications';
  private notifications: Map<string, NotificationEntity[]> = new Map();
  private storageService: StorageService;

  constructor(storageService: StorageService) {
    this.storageService = storageService;
  }

  async initialize(userId: string): Promise<NotificationEntity[]> {
    const savedNotifications = await this.storageService.get<Notification[]>(NotificationService.NOTIFICATIONS_KEY);
    
    if (savedNotifications) {
      const notifications = savedNotifications
        .filter(notification => notification.userId === userId)
        .map(notification => new NotificationEntity(notification));
      this.notifications.set(userId, notifications);
      return notifications;
    }

    this.notifications.set(userId, []);
    return [];
  }

  async getNotifications(userId: string, filter?: NotificationFilter): Promise<NotificationEntity[]> {
    const notifications = this.notifications.get(userId) || [];
    if (!filter) return notifications;

    return notifications.filter(notification => {
      if (filter.type && notification.type !== filter.type) return false;
      if (filter.isRead !== undefined && notification.read !== filter.isRead) return false;
      if (filter.isDismissed !== undefined && notification.dismissed !== filter.isDismissed) return false;
      if (filter.priority !== undefined && notification.priority !== filter.priority) return false;
      
      if (filter.startDate && notification.createdAt < filter.startDate) return false;
      if (filter.endDate && notification.createdAt > filter.endDate) return false;

      return true;
    });
  }

  async getNotificationById(userId: string, notificationId: string): Promise<NotificationEntity | null> {
    const notifications = this.notifications.get(userId) || [];
    return notifications.find(notification => notification.id === notificationId) || null;
  }

  async createNotification(userId: string, notification: Omit<Notification, 'id' | 'userId' | 'read' | 'dismissed' | 'createdAt' | 'updatedAt'>): Promise<NotificationEntity> {
    const newNotification = new NotificationEntity({
      ...notification,
      id: this.generateNotificationId(),
      userId,
      read: false,
      dismissed: false,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    const userNotifications = this.notifications.get(userId) || [];
    userNotifications.push(newNotification);
    this.notifications.set(userId, userNotifications);
    
    await this.save();
    return newNotification;
  }

  async updateNotification(userId: string, notificationId: string, updates: Partial<Notification>): Promise<NotificationEntity> {
    const notifications = this.notifications.get(userId) || [];
    const index = notifications.findIndex(notification => notification.id === notificationId);
    
    if (index === -1) {
      throw new Error('Notification not found');
    }

    const currentNotification = notifications[index];
    const updatedNotification = new NotificationEntity({
      ...currentNotification,
      ...updates,
      userId, // Ensure userId cannot be changed
      updatedAt: new Date()
    });

    notifications[index] = updatedNotification;
    await this.save();
    return updatedNotification;
  }

  async deleteNotification(userId: string, notificationId: string): Promise<void> {
    const notifications = this.notifications.get(userId) || [];
    const filteredNotifications = notifications.filter(notification => notification.id !== notificationId);
    
    if (filteredNotifications.length === notifications.length) {
      throw new Error('Notification not found');
    }

    this.notifications.set(userId, filteredNotifications);
    await this.save();
  }

  async markAsRead(userId: string, notificationId: string): Promise<NotificationEntity> {
    return this.updateNotification(userId, notificationId, { read: true });
  }

  async markAllAsRead(userId: string): Promise<void> {
    const notifications = this.notifications.get(userId) || [];
    const updatedNotifications = notifications.map(notification => 
      new NotificationEntity({
        ...notification,
        read: true,
        updatedAt: new Date()
      })
    );
    this.notifications.set(userId, updatedNotifications);
    await this.save();
  }

  async dismiss(userId: string, notificationId: string): Promise<NotificationEntity> {
    return this.updateNotification(userId, notificationId, { dismissed: true });
  }

  async dismissAll(userId: string): Promise<void> {
    const notifications = this.notifications.get(userId) || [];
    const updatedNotifications = notifications.map(notification => 
      new NotificationEntity({
        ...notification,
        dismissed: true,
        updatedAt: new Date()
      })
    );
    this.notifications.set(userId, updatedNotifications);
    await this.save();
  }

  async getUnreadNotifications(userId: string): Promise<NotificationEntity[]> {
    return this.getNotifications(userId, { isRead: false });
  }

  async getActiveNotifications(userId: string): Promise<NotificationEntity[]> {
    return this.getNotifications(userId, { isDismissed: false });
  }

  async getNotificationsByType(userId: string, type: NotificationType): Promise<NotificationEntity[]> {
    return this.getNotifications(userId, { type });
  }

  async getNotificationsByDateRange(userId: string, startDate: Date, endDate: Date): Promise<NotificationEntity[]> {
    return this.getNotifications(userId, { startDate, endDate });
  }

  private generateNotificationId(): string {
    return `nt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async save(): Promise<void> {
    const allNotifications = Array.from(this.notifications.values()).flat();
    await this.storageService.set(NotificationService.NOTIFICATIONS_KEY, allNotifications);
  }
} 