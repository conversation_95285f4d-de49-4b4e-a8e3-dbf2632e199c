import { Analytics, AnalyticsEntity, TimeFrame } from '../../models';
import { StorageService } from '../storage/storage.service';
import { TradeService } from '../trade/trade.service';

export class AnalyticsService {
  private static readonly ANALYTICS_KEY = 'trade_reviewer_analytics';
  private analytics: Map<string, AnalyticsEntity> = new Map();
  private storageService: StorageService;
  private tradeService: TradeService;

  constructor(storageService: StorageService, tradeService: TradeService) {
    this.storageService = storageService;
    this.tradeService = tradeService;
  }

  async initialize(userId: string): Promise<AnalyticsEntity> {
    const savedAnalytics = await this.storageService.get<Analytics[]>(AnalyticsService.ANALYTICS_KEY);
    
    if (savedAnalytics) {
      const userAnalytics = savedAnalytics.find(a => a.userId === userId);
      if (userAnalytics) {
        const analytics = new AnalyticsEntity(userAnalytics);
        this.analytics.set(userId, analytics);
        return analytics;
      }
    }

    // Create default analytics if none exists
    const defaultAnalytics = new AnalyticsEntity({
      userId,
      timeFrame: TimeFrame.ALL,
      startDate: new Date(0),
      endDate: new Date(),
      metrics: {
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        breakEvenTrades: 0,
        totalPnL: 0,
        totalRiskAmount: 0,
        totalRewardAmount: 0,
        averageRRR: 0,
        winRate: 0,
        profitFactor: 0,
        averageWinAmount: 0,
        averageLossAmount: 0,
        largestWin: 0,
        largestLoss: 0,
        totalFees: 0
      },
      updatedAt: new Date()
    });

    this.analytics.set(userId, defaultAnalytics);
    await this.save();
    return defaultAnalytics;
  }

  async getAnalytics(userId: string): Promise<AnalyticsEntity | null> {
    return this.analytics.get(userId) || null;
  }

  async updateAnalytics(userId: string, timeFrame: TimeFrame = TimeFrame.ALL): Promise<AnalyticsEntity> {
    const analytics = this.analytics.get(userId);
    if (!analytics) {
      throw new Error('Analytics not found');
    }

    const trades = await this.tradeService.getTrades(userId);
    const closedTrades = trades.filter(t => t.exitDate !== null);

    if (closedTrades.length === 0) {
      return analytics;
    }

    const startDate = timeFrame === TimeFrame.ALL 
      ? new Date(Math.min(...closedTrades.map(t => t.entryDate.getTime())))
      : new Date();
    const endDate = new Date();

    let totalPnL = 0;
    let totalRiskAmount = 0;
    let totalRewardAmount = 0;
    let winningTrades = 0;
    let losingTrades = 0;
    let breakEvenTrades = 0;
    let totalWinAmount = 0;
    let totalLossAmount = 0;
    let largestWin = 0;
    let largestLoss = 0;
    let totalFees = 0;

    closedTrades.forEach(trade => {
      const pnl = trade.calculatePnL();
      totalPnL += pnl;
      totalFees += trade.fees || 0;

      if (pnl > 0) {
        winningTrades++;
        totalWinAmount += pnl;
        largestWin = Math.max(largestWin, pnl);
      } else if (pnl < 0) {
        losingTrades++;
        totalLossAmount += Math.abs(pnl);
        largestLoss = Math.max(largestLoss, Math.abs(pnl));
      } else {
        breakEvenTrades++;
      }

      const rrr = trade.calculateRRR();
      if (rrr) {
        totalRiskAmount += rrr.risk;
        totalRewardAmount += rrr.reward;
      }
    });

    const updatedAnalytics = new AnalyticsEntity({
      userId,
      timeFrame,
      startDate,
      endDate,
      metrics: {
        totalTrades: closedTrades.length,
        winningTrades,
        losingTrades,
        breakEvenTrades,
        totalPnL,
        totalRiskAmount,
        totalRewardAmount,
        averageRRR: totalRiskAmount > 0 ? totalRewardAmount / totalRiskAmount : 0,
        winRate: closedTrades.length > 0 ? winningTrades / closedTrades.length : 0,
        profitFactor: totalLossAmount > 0 ? totalWinAmount / totalLossAmount : 0,
        averageWinAmount: winningTrades > 0 ? totalWinAmount / winningTrades : 0,
        averageLossAmount: losingTrades > 0 ? totalLossAmount / losingTrades : 0,
        largestWin,
        largestLoss,
        totalFees
      },
      updatedAt: new Date()
    });

    this.analytics.set(userId, updatedAnalytics);
    await this.save();
    return updatedAnalytics;
  }

  private async save(): Promise<void> {
    const allAnalytics = Array.from(this.analytics.values());
    await this.storageService.set(AnalyticsService.ANALYTICS_KEY, allAnalytics);
  }
} 