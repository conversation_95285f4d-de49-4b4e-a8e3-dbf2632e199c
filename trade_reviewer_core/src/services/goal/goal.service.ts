import { Goal, GoalEntity, GoalType } from '../../models/goal';
import { StorageService } from '../storage';

export interface GoalFilter {
  type?: GoalType;
  isActive?: boolean;
  isCompleted?: boolean;
}

export class GoalService {
  private static readonly GOALS_KEY = 'trade_reviewer_goals';
  private goals: GoalEntity[] = [];

  constructor(private storageService: StorageService) {}

  async initialize(userId: string): Promise<GoalEntity[]> {
    const savedGoals = await this.storageService.get<Goal[]>(GoalService.GOALS_KEY);
    
    if (savedGoals) {
      this.goals = savedGoals
        .filter(g => g.userId === userId)
        .map(g => new GoalEntity(g));
    }

    return this.goals;
  }

  async getGoals(userId: string, filter?: GoalFilter): Promise<GoalEntity[]> {
    let filteredGoals = this.goals.filter(g => g.userId === userId);

    if (filter) {
      filteredGoals = filteredGoals.filter(goal => {
        if (filter.type && goal.type !== filter.type) return false;
        if (filter.isActive !== undefined && goal.isActive !== filter.isActive) return false;
        if (filter.isCompleted !== undefined && goal.progress >= goal.target !== filter.isCompleted) return false;
        return true;
      });
    }

    return filteredGoals;
  }

  async getActiveGoals(userId: string): Promise<GoalEntity[]> {
    return this.goals.filter(g => g.userId === userId && g.isActive);
  }

  async getGoalById(goalId: string, userId: string): Promise<GoalEntity | null> {
    return this.goals.find(g => g.id === goalId && g.userId === userId) || null;
  }

  async addGoal(goal: Goal): Promise<GoalEntity> {
    const entity = new GoalEntity(goal);
    this.goals.push(entity);
    await this.save();
    return entity;
  }

  async updateGoal(goalId: string, userId: string, updates: Partial<Goal>): Promise<GoalEntity | null> {
    const goal = this.goals.find(g => g.id === goalId && g.userId === userId);
    
    if (goal) {
      Object.assign(goal, updates);
      goal.updatedAt = new Date();
      await this.save();
      return goal;
    }

    return null;
  }

  async deleteGoal(goalId: string, userId: string): Promise<void> {
    this.goals = this.goals.filter(g => !(g.id === goalId && g.userId === userId));
    await this.save();
  }

  async updateProgress(goalId: string, userId: string, value: number): Promise<GoalEntity | null> {
    const goal = await this.getGoalById(goalId, userId);
    
    if (goal) {
      goal.progress = value;
      goal.updatedAt = new Date();
      await this.save();
      return goal;
    }

    return null;
  }

  async clear(userId: string): Promise<void> {
    this.goals = this.goals.filter(g => g.userId !== userId);
    await this.save();
  }

  private async save(): Promise<void> {
    await this.storageService.set(GoalService.GOALS_KEY, this.goals);
  }
} 