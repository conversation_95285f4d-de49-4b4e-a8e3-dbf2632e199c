import { JournalEntry, JournalEntryEntity } from '../../models';
import { StorageService } from '../storage/storage.service';

export interface JournalFilter {
  startDate?: Date;
  endDate?: Date;
  tradeId?: string;
  marketCondition?: string;
  emotionalState?: string;
  tags?: string[];
  rating?: number;
}

export class JournalService {
  private static readonly JOURNAL_KEY = 'trade_reviewer_journal';
  private entries: Map<string, JournalEntryEntity[]> = new Map();
  private storageService: StorageService;

  constructor(storageService: StorageService) {
    this.storageService = storageService;
  }

  async initialize(userId: string): Promise<JournalEntryEntity[]> {
    const savedEntries = await this.storageService.get<JournalEntry[]>(JournalService.JOURNAL_KEY);
    
    if (savedEntries) {
      const userEntries = savedEntries
        .filter(entry => entry.userId === userId)
        .map(entry => new JournalEntryEntity(entry));
      this.entries.set(userId, userEntries);
      return userEntries;
    }

    this.entries.set(userId, []);
    return [];
  }

  async getEntries(userId: string, filter?: JournalFilter): Promise<JournalEntryEntity[]> {
    const entries = this.entries.get(userId) || [];
    if (!filter) return entries;

    return entries.filter(entry => {
      if (filter.startDate && entry.createdAt < filter.startDate) return false;
      if (filter.endDate && entry.createdAt > filter.endDate) return false;
      if (filter.tradeId && entry.tradeId !== filter.tradeId) return false;
      if (filter.marketCondition && entry.marketCondition !== filter.marketCondition) return false;
      if (filter.emotionalState && entry.emotionalState !== filter.emotionalState) return false;
      if (filter.rating && entry.rating !== filter.rating) return false;
      if (filter.tags && filter.tags.length > 0) {
        return filter.tags.every(tag => entry.tags.includes(tag));
      }
      return true;
    });
  }

  async getEntry(userId: string, entryId: string): Promise<JournalEntryEntity | undefined> {
    const entries = this.entries.get(userId) || [];
    return entries.find(entry => entry.id === entryId);
  }

  async createEntry(userId: string, entry: JournalEntry): Promise<JournalEntryEntity> {
    const entries = this.entries.get(userId) || [];
    const newEntry = new JournalEntryEntity({
      ...entry,
      userId,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    entries.push(newEntry);
    this.entries.set(userId, entries);
    await this.save();
    return newEntry;
  }

  async updateEntry(userId: string, entryId: string, updates: Partial<JournalEntry>): Promise<JournalEntryEntity> {
    const entries = this.entries.get(userId) || [];
    const index = entries.findIndex(entry => entry.id === entryId);
    
    if (index === -1) {
      throw new Error('Journal entry not found');
    }

    const updatedEntry = new JournalEntryEntity({
      ...entries[index],
      ...updates,
      updatedAt: new Date()
    });

    entries[index] = updatedEntry;
    this.entries.set(userId, entries);
    await this.save();
    return updatedEntry;
  }

  async deleteEntry(userId: string, entryId: string): Promise<void> {
    const entries = this.entries.get(userId) || [];
    const filteredEntries = entries.filter(entry => entry.id !== entryId);
    this.entries.set(userId, filteredEntries);
    await this.save();
  }

  async addTags(userId: string, entryId: string, tags: string[]): Promise<JournalEntryEntity> {
    const entries = this.entries.get(userId) || [];
    const entry = entries.find(e => e.id === entryId);
    
    if (!entry) {
      throw new Error('Journal entry not found');
    }

    const uniqueTags = [...new Set([...entry.tags, ...tags])];
    return this.updateEntry(userId, entryId, { tags: uniqueTags });
  }

  async removeTags(userId: string, entryId: string, tags: string[]): Promise<JournalEntryEntity> {
    const entries = this.entries.get(userId) || [];
    const entry = entries.find(e => e.id === entryId);
    
    if (!entry) {
      throw new Error('Journal entry not found');
    }

    const updatedTags = entry.tags.filter(tag => !tags.includes(tag));
    return this.updateEntry(userId, entryId, { tags: updatedTags });
  }

  async updateRating(userId: string, entryId: string, rating: number): Promise<JournalEntryEntity> {
    return this.updateEntry(userId, entryId, { rating });
  }

  async updateAnalysis(userId: string, entryId: string, analysis: string): Promise<JournalEntryEntity> {
    return this.updateEntry(userId, entryId, { analysis });
  }

  async addLesson(userId: string, entryId: string, lesson: string): Promise<JournalEntryEntity> {
    const entries = this.entries.get(userId) || [];
    const entry = entries.find(e => e.id === entryId);
    
    if (!entry) {
      throw new Error('Journal entry not found');
    }

    const updatedLessons = [...entry.lessons, lesson];
    return this.updateEntry(userId, entryId, { lessons: updatedLessons });
  }

  async updateLessons(userId: string, entryId: string, lessons: string[]): Promise<JournalEntryEntity> {
    return this.updateEntry(userId, entryId, { lessons });
  }

  private async save(): Promise<void> {
    const allEntries = Array.from(this.entries.values()).flat();
    await this.storageService.set(JournalService.JOURNAL_KEY, allEntries);
  }
} 