import { 
  Settings, 
  SettingsEntity, 
  TradeDefaults, 
  DisplaySettings, 
  BacktestSettings,
  ChartTimeframe,
  RiskCalculationType,
  PnLDisplayMode
} from '../../models';
import { IStorageService } from '../storage/storage.service';

export class SettingsService {
  private static readonly SETTINGS_KEY = 'trade_reviewer_settings';
  private settings: SettingsEntity | null = null;

  constructor(private storageService: IStorageService) {}

  async initialize(userId: string): Promise<SettingsEntity> {
    const savedSettings = await this.storageService.get<Settings>(SettingsService.SETTINGS_KEY);

    if (savedSettings) {
      this.settings = new SettingsEntity(savedSettings);
    } else {
      this.settings = await this._createDefaultSettings(userId);
      await this.save();
    }

    await this.storageService.set(SettingsService.SETTINGS_KEY, this.settings);
    return this.settings;
  }

  async getSettings(): Promise<SettingsEntity> {
    if (!this.settings) {
      throw new Error('Settings not initialized. Call initialize() first.');
    }
    return this.settings;
  }

  async updateTradeDefaults(defaults: Partial<TradeDefaults>): Promise<SettingsEntity> {
    if (!this.settings) {
      throw new Error('Settings not initialized. Call initialize() first.');
    }

    this.settings.updateTradeDefaults(defaults);
    await this.save();
    return this.settings;
  }

  async updateDisplaySettings(display: Partial<DisplaySettings>): Promise<SettingsEntity> {
    if (!this.settings) {
      throw new Error('Settings not initialized. Call initialize() first.');
    }

    this.settings.updateDisplaySettings(display);
    await this.save();
    return this.settings;
  }

  async updateBacktestSettings(backtest: Partial<BacktestSettings>): Promise<SettingsEntity> {
    if (!this.settings) {
      throw new Error('Settings not initialized. Call initialize() first.');
    }

    this.settings.updateBacktestSettings(backtest);
    await this.save();
    return this.settings;
  }

  async updateAutoSync(enabled: boolean): Promise<SettingsEntity> {
    if (!this.settings) {
      throw new Error('Settings not initialized. Call initialize() first.');
    }

    this.settings.autoSync = enabled;
    this.settings.updatedAt = new Date();
    await this.save();
    return this.settings;
  }

  async updateAutoBackup(enabled: boolean, frequency?: number): Promise<SettingsEntity> {
    if (!this.settings) {
      throw new Error('Settings not initialized. Call initialize() first.');
    }

    this.settings.autoBackup = enabled;
    if (frequency !== undefined) {
      this.settings.backupFrequency = frequency;
    }
    this.settings.updatedAt = new Date();
    await this.save();
    return this.settings;
  }

  async reset(): Promise<SettingsEntity> {
    if (!this.settings) {
      throw new Error('Settings not initialized. Call initialize() first.');
    }

    const userId = this.settings.userId;
    await this.storageService.remove(SettingsService.SETTINGS_KEY);
    return this.initialize(userId);
  }

  private async save(): Promise<void> {
    if (!this.settings) {
      throw new Error('Settings not initialized. Call initialize() first.');
    }

    await this.storageService.set(SettingsService.SETTINGS_KEY, this.settings);
  }

  private async _createDefaultSettings(userId: string): Promise<SettingsEntity> {
    const settings = {
      userId,
      tradeDefaults: {
        defaultRisk: 1,
        defaultPosition: 100,
        defaultTimeframe: ChartTimeframe.D1,
        riskCalculationType: RiskCalculationType.PERCENTAGE,
        defaultCommission: 0,
        defaultFees: 0,
      },
      display: {
        theme: 'system',
        pnlDisplayMode: PnLDisplayMode.CURRENCY,
        showRunningPnL: true,
        showEquityCurve: true,
        showTradeStatistics: true,
        compactMode: false,
        dateFormat: 'yyyy-MM-dd',
        timeFormat: 'HH:mm:ss',
        timezone: 'UTC',
        goalNotificationsEnabled: true,
        weeklyReportsEnabled: true,
        monthlyReportsEnabled: true,
        tradeAlertsEnabled: true,
        marketNewsEnabled: true,
        priceAlertsEnabled: true,
        journalRemindersEnabled: true,
        emailNotificationsEnabled: true,
        pushNotificationsEnabled: true,
        smsNotificationsEnabled: false,
      },
      backtest: {
        initialCapital: 100000,
        useFixedPosition: false,
        maxRiskPerTrade: 2,
        maxOpenTrades: 5,
        includeFees: true,
        includeSlippage: true,
        slippageAmount: 0.1,
      },
      autoSync: true,
      autoBackup: true,
      backupFrequency: 7,
      updatedAt: new Date(),
    };

    return new SettingsEntity(settings);
  }
} 