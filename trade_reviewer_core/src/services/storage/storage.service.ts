import { Trade } from '../../models/trade';
import { User } from '../../models/user';

export interface IStorageService {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T): Promise<void>;
  remove(key: string): Promise<void>;
  clear(): Promise<void>;
}

export class StorageService implements IStorageService {
  private static readonly TRADES_KEY = 'trade_reviewer_trades';
  private static readonly PROFILES_KEY = 'trade_reviewer_profiles';

  async getTrades(): Promise<Trade[]> {
    const trades = await this.get<Trade[]>(StorageService.TRADES_KEY);
    return trades || [];
  }

  async saveTrade(trade: Trade): Promise<void> {
    const trades = await this.getTrades();
    trades.push(trade);
    await this.set(StorageService.TRADES_KEY, trades);
  }

  async updateTrade(trade: Trade): Promise<void> {
    const trades = await this.getTrades();
    const index = trades.findIndex(t => t.id === trade.id);
    if (index !== -1) {
      trades[index] = trade;
      await this.set(StorageService.TRADES_KEY, trades);
    }
  }

  async deleteTrade(tradeId: string): Promise<void> {
    const trades = await this.getTrades();
    const filteredTrades = trades.filter(t => t.id !== tradeId);
    await this.set(StorageService.TRADES_KEY, filteredTrades);
  }

  async getProfile(userId: string): Promise<User | null> {
    const profiles = await this.getProfiles();
    return profiles.find(p => p.id === userId) || null;
  }

  async saveProfile(profile: User): Promise<void> {
    const profiles = await this.getProfiles();
    const index = profiles.findIndex(p => p.id === profile.id);
    if (index !== -1) {
      profiles[index] = profile;
    } else {
      profiles.push(profile);
    }
    await this.set(StorageService.PROFILES_KEY, profiles);
  }

  async updateProfile(profile: User): Promise<void> {
    await this.saveProfile(profile);
  }

  async deleteProfile(userId: string): Promise<void> {
    const profiles = await this.getProfiles();
    const filteredProfiles = profiles.filter(p => p.id !== userId);
    await this.set(StorageService.PROFILES_KEY, filteredProfiles);
  }

  async get<T>(key: string): Promise<T | null> {
    throw new Error('Method not implemented');
  }

  async set<T>(key: string, value: T): Promise<void> {
    throw new Error('Method not implemented');
  }

  async remove(key: string): Promise<void> {
    throw new Error('Method not implemented');
  }

  async clear(): Promise<void> {
    throw new Error('Method not implemented');
  }

  private async getProfiles(): Promise<User[]> {
    const profiles = await this.get<User[]>(StorageService.PROFILES_KEY);
    return profiles || [];
  }
}
