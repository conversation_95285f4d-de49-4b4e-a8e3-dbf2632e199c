import { User, UserEntity, UserPreferences, UserRole, NotificationPreference } from '../../models';
import { StorageService } from '../storage/storage.service';

export class ProfileService {
  private profiles: Map<string, UserEntity> = new Map();

  constructor(private storageService: StorageService) {}

  async initialize(userId: string): Promise<UserEntity> {
    // Try to get the profile from storage service
    const storedProfile = await this.storageService.getProfile(userId);
    
    if (storedProfile) {
      const profile = new UserEntity(storedProfile);
      this.profiles.set(userId, profile);
      return profile;
    }

    // Create default profile if none exists
    const defaultProfile = new UserEntity({
      id: userId,
      email: '',
      name: '',
      role: UserRole.USER,
      preferences: {
        notificationPreference: NotificationPreference.IMPORTANT,
        darkMode: false,
        defaultCurrency: 'USD',
        timezone: 'UTC',
        language: 'en',
        showPnLInPercent: true,
        autoSyncEnabled: true
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      isEmailVerified: false
    });

    // Save to storage and local cache
    await this.storageService.saveProfile(defaultProfile);
    this.profiles.set(userId, defaultProfile);
    return defaultProfile;
  }

  async getProfile(userId: string): Promise<UserEntity | null> {
    // Try from cache first
    let profile = this.profiles.get(userId);
    if (profile) return profile;

    // Try from storage
    const storedProfile = await this.storageService.getProfile(userId);
    if (storedProfile) {
      profile = new UserEntity(storedProfile);
      this.profiles.set(userId, profile);
      return profile;
    }

    return null;
  }

  async updateProfile(userId: string, updates: Partial<User>): Promise<UserEntity> {
    const profile = await this.getProfile(userId);
    if (!profile) {
      throw new Error('Profile not found');
    }

    const updatedProfile = new UserEntity({
      ...profile,
      ...updates,
      id: userId, // Ensure userId cannot be changed
      updatedAt: new Date()
    });

    // Update storage and cache
    await this.storageService.updateProfile(updatedProfile);
    this.profiles.set(userId, updatedProfile);
    return updatedProfile;
  }

  async updatePreferences(userId: string, preferences: Partial<UserPreferences>): Promise<UserEntity> {
    const profile = await this.getProfile(userId);
    if (!profile) {
      throw new Error('Profile not found');
    }

    profile.updatePreferences(preferences);
    
    // Update storage and cache
    await this.storageService.updateProfile(profile);
    this.profiles.set(userId, profile);
    return profile;
  }

  async verifyEmail(userId: string): Promise<UserEntity> {
    const profile = await this.getProfile(userId);
    if (!profile) {
      throw new Error('Profile not found');
    }

    profile.verifyEmail();
    
    // Update storage and cache
    await this.storageService.updateProfile(profile);
    this.profiles.set(userId, profile);
    return profile;
  }

  async updateLastLogin(userId: string): Promise<UserEntity> {
    const profile = await this.getProfile(userId);
    if (!profile) {
      throw new Error('Profile not found');
    }

    profile.updateLastLogin();
    
    // Update storage and cache
    await this.storageService.updateProfile(profile);
    this.profiles.set(userId, profile);
    return profile;
  }
} 