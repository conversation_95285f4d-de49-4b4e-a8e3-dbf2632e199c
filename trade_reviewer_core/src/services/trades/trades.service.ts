import { 
  Trade, 
  TradeEntity, 
  TradeStatus,
  JournalEntry,
  JournalEntryEntity
} from '../../models';
import { StorageService } from '../storage/storage.service';

export class TradesService {
  private static readonly TRADES_KEY = 'trade_reviewer_trades';
  private static readonly JOURNAL_KEY = 'trade_reviewer_journal';
  private trades: TradeEntity[] = [];
  private journal: JournalEntryEntity[] = [];
  private storageService: StorageService;

  constructor(storageService: StorageService) {
    this.storageService = storageService;
  }

  async initialize(userId: string): Promise<{ trades: TradeEntity[], journal: JournalEntryEntity[] }> {
    const [savedTrades, savedJournal] = await Promise.all([
      this.storageService.get<Trade[]>(TradesService.TRADES_KEY),
      this.storageService.get<JournalEntry[]>(TradesService.JOURNAL_KEY)
    ]);
    
    if (savedTrades) {
      this.trades = savedTrades
        .filter(t => t.userId === userId)
        .map(t => new TradeEntity(t));
    }

    if (savedJournal) {
      this.journal = savedJournal
        .filter(j => j.userId === userId)
        .map(j => new JournalEntryEntity(j));
    }

    return { trades: this.trades, journal: this.journal };
  }

  async getTrades(userId: string): Promise<TradeEntity[]> {
    return this.trades.filter(t => t.userId === userId);
  }

  async getOpenTrades(userId: string): Promise<TradeEntity[]> {
    return this.trades.filter(t => t.userId === userId && t.status === TradeStatus.OPEN);
  }

  async getClosedTrades(userId: string): Promise<TradeEntity[]> {
    return this.trades.filter(t => t.userId === userId && t.status === TradeStatus.CLOSED);
  }

  async getTradeById(tradeId: string, userId: string): Promise<TradeEntity | null> {
    return this.trades.find(t => t.id === tradeId && t.userId === userId) || null;
  }

  async addTrade(trade: Trade): Promise<TradeEntity> {
    const entity = new TradeEntity(trade);
    this.trades.push(entity);
    await this.saveTrades();
    return entity;
  }

  async updateTrade(tradeId: string, userId: string, updates: Partial<Trade>): Promise<TradeEntity | null> {
    const trade = this.trades.find(t => t.id === tradeId && t.userId === userId);
    
    if (trade) {
      Object.assign(trade, updates);
      trade.updatedAt = new Date();
      await this.saveTrades();
      return trade;
    }

    return null;
  }

  async closeTrade(tradeId: string, userId: string, exitPrice: number, exitDate: Date): Promise<TradeEntity | null> {
    const trade = this.trades.find(t => t.id === tradeId && t.userId === userId);
    
    if (trade) {
      trade.exitPrice = exitPrice;
      trade.exitDate = exitDate;
      trade.status = TradeStatus.CLOSED;
      trade.updatedAt = new Date();
      await this.saveTrades();
      return trade;
    }

    return null;
  }

  async deleteTrade(tradeId: string, userId: string): Promise<void> {
    this.trades = this.trades.filter(t => !(t.id === tradeId && t.userId === userId));
    await this.saveTrades();
  }

  async getJournalEntries(userId: string): Promise<JournalEntryEntity[]> {
    return this.journal.filter(j => j.userId === userId);
  }

  async getJournalEntryByTradeId(tradeId: string, userId: string): Promise<JournalEntryEntity | null> {
    return this.journal.find(j => j.tradeId === tradeId && j.userId === userId) || null;
  }

  async addJournalEntry(entry: JournalEntry): Promise<JournalEntryEntity> {
    const entity = new JournalEntryEntity(entry);
    this.journal.push(entity);
    await this.saveJournal();
    return entity;
  }

  async updateJournalEntry(entryId: string, userId: string, updates: Partial<JournalEntry>): Promise<JournalEntryEntity | null> {
    const entry = this.journal.find(j => j.id === entryId && j.userId === userId);
    
    if (entry) {
      Object.assign(entry, updates);
      entry.updatedAt = new Date();
      await this.saveJournal();
      return entry;
    }

    return null;
  }

  async deleteJournalEntry(entryId: string, userId: string): Promise<void> {
    this.journal = this.journal.filter(j => !(j.id === entryId && j.userId === userId));
    await this.saveJournal();
  }

  async clear(userId: string): Promise<void> {
    this.trades = this.trades.filter(t => t.userId !== userId);
    this.journal = this.journal.filter(j => j.userId !== userId);
    await Promise.all([
      this.saveTrades(),
      this.saveJournal()
    ]);
  }

  private async saveTrades(): Promise<void> {
    await this.storageService.set(TradesService.TRADES_KEY, this.trades);
  }

  private async saveJournal(): Promise<void> {
    await this.storageService.set(TradesService.JOURNAL_KEY, this.journal);
  }
} 