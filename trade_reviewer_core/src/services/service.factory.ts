import { StorageService } from './storage/storage.service';
import { SettingsService } from './settings/settings.service';
import { TradeService } from './trade/trade.service';
import { GoalService } from './goal/goal.service';
import { AnalyticsService } from './analytics/analytics.service';
import { JournalService } from './journal/journal.service';
import { NotificationsService } from './notifications/notifications.service';
import { ProfileService } from './profile/profile.service';

export class ServiceFactory {
  private storageService: StorageService;
  private settingsService: SettingsService;
  private tradeService: TradeService;
  private goalService: GoalService;
  private analyticsService: AnalyticsService;
  private journalService: JournalService;
  private notificationsService: NotificationsService;
  private profileService: ProfileService;

  constructor() {
    // Initialize storage service first as it's required by other services
    this.storageService = new StorageService();

    // Initialize other services
    this.settingsService = new SettingsService(this.storageService);
    this.tradeService = new TradeService(this.storageService);
    this.goalService = new GoalService(this.storageService);
    this.analyticsService = new AnalyticsService(this.storageService, this.tradeService);
    this.journalService = new JournalService(this.storageService);
    this.notificationsService = new NotificationsService(this.storageService);
    this.profileService = new ProfileService(this.storageService);
  }

  getStorageService(): StorageService {
    return this.storageService;
  }

  getSettingsService(): SettingsService {
    return this.settingsService;
  }

  getTradeService(): TradeService {
    return this.tradeService;
  }

  getGoalService(): GoalService {
    return this.goalService;
  }

  getAnalyticsService(): AnalyticsService {
    return this.analyticsService;
  }

  getJournalService(): JournalService {
    return this.journalService;
  }

  getNotificationsService(): NotificationsService {
    return this.notificationsService;
  }

  getProfileService(): ProfileService {
    return this.profileService;
  }
} 