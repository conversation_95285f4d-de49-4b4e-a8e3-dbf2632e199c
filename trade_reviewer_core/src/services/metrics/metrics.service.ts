import { Metrics, MetricsEntity, MarketMetrics, PositionMetrics, AccountMetrics } from '../../models';
import { StorageService } from '../storage/storage.service';

export class MetricsService {
  private static readonly METRICS_KEY = 'trade_reviewer_metrics';
  private metrics: Map<string, MetricsEntity> = new Map();
  private storageService: StorageService;

  constructor(storageService: StorageService) {
    this.storageService = storageService;
  }

  async initialize(userId: string): Promise<MetricsEntity> {
    const savedMetrics = await this.storageService.get<Metrics[]>(MetricsService.METRICS_KEY);
    
    if (savedMetrics) {
      const userMetrics = savedMetrics.find(metrics => metrics.userId === userId);
      if (userMetrics) {
        const metrics = new MetricsEntity(userMetrics);
        this.metrics.set(userId, metrics);
        return metrics;
      }
    }

    const newMetrics = new MetricsEntity({
      id: crypto.randomUUID(),
      userId,
      timestamp: new Date(),
      market: {},
      positions: {},
      account: {
        balance: 0,
        equity: 0,
        openPositions: 0,
        marginUsed: 0,
        marginAvailable: 0,
        marginLevel: 0,
        dailyPnL: 0,
        dailyPnLPercent: 0,
        drawdown: 0,
        drawdownPercent: 0
      }
    });

    this.metrics.set(userId, newMetrics);
    await this.save();
    return newMetrics;
  }

  async getMetrics(userId: string): Promise<MetricsEntity | null> {
    const metrics = this.metrics.get(userId);
    return metrics || null;
  }

  async updateMarketMetrics(userId: string, symbol: string, metrics: MarketMetrics): Promise<MetricsEntity> {
    const userMetrics = this.metrics.get(userId);
    if (!userMetrics) {
      throw new Error('Metrics not initialized');
    }

    userMetrics.updateMarketMetrics(symbol, metrics);
    await this.save();
    return userMetrics;
  }

  async updatePositionMetrics(userId: string, symbol: string, metrics: PositionMetrics): Promise<MetricsEntity> {
    const userMetrics = this.metrics.get(userId);
    if (!userMetrics) {
      throw new Error('Metrics not initialized');
    }

    userMetrics.updatePositionMetrics(symbol, metrics);
    await this.save();
    return userMetrics;
  }

  async updateAccountMetrics(userId: string, metrics: AccountMetrics): Promise<MetricsEntity> {
    const userMetrics = this.metrics.get(userId);
    if (!userMetrics) {
      throw new Error('Metrics not initialized');
    }

    userMetrics.updateAccountMetrics(metrics);
    await this.save();
    return userMetrics;
  }

  async getMarketMetrics(userId: string, symbol: string): Promise<MarketMetrics | undefined> {
    const userMetrics = this.metrics.get(userId);
    if (!userMetrics) {
      throw new Error('Metrics not initialized');
    }

    return userMetrics.getMarketMetrics(symbol);
  }

  async getPositionMetrics(userId: string, symbol: string): Promise<PositionMetrics | undefined> {
    const userMetrics = this.metrics.get(userId);
    if (!userMetrics) {
      throw new Error('Metrics not initialized');
    }

    return userMetrics.getPositionMetrics(symbol);
  }

  async getAccountMetrics(userId: string): Promise<AccountMetrics> {
    const userMetrics = this.metrics.get(userId);
    if (!userMetrics) {
      throw new Error('Metrics not initialized');
    }

    return userMetrics.account;
  }

  async getTotalUnrealizedPnL(userId: string): Promise<number> {
    const userMetrics = this.metrics.get(userId);
    if (!userMetrics) {
      throw new Error('Metrics not initialized');
    }

    return userMetrics.totalUnrealizedPnL;
  }

  async getTotalRisk(userId: string): Promise<number> {
    const userMetrics = this.metrics.get(userId);
    if (!userMetrics) {
      throw new Error('Metrics not initialized');
    }

    return userMetrics.totalRisk;
  }

  async getAverageRRR(userId: string): Promise<number> {
    const userMetrics = this.metrics.get(userId);
    if (!userMetrics) {
      throw new Error('Metrics not initialized');
    }

    return userMetrics.averageRRR;
  }

  async getMarginUtilization(userId: string): Promise<number> {
    const userMetrics = this.metrics.get(userId);
    if (!userMetrics) {
      throw new Error('Metrics not initialized');
    }

    return userMetrics.marginUtilization;
  }

  private async save(): Promise<void> {
    const allMetrics = Array.from(this.metrics.values());
    await this.storageService.set(MetricsService.METRICS_KEY, allMetrics);
  }
} 