import { Trade, TradeEntity, TradeStatus, TradeType } from '../../models/trade';
import { Goal, GoalType } from '../../models/goal';
import { StorageService } from '../storage';
import { TimeFrame } from '../../models/timeframe';

export class GoalsService {
  constructor(private storageService: StorageService) {}

  async getGoalProgress(goal: Goal, timeFrame: TimeFrame): Promise<number> {
    const trades = await this.storageService.getTrades();
    const tradeEntities = trades.map(trade => new TradeEntity(trade));
    const validTrades = this.filterTradesByTimeFrame(tradeEntities, timeFrame);

    switch (goal.type) {
      case GoalType.WIN_RATE:
        return this.calculateWinRate(validTrades);
      case GoalType.PROFIT:
        return this.calculateTotalProfit(validTrades);
      case GoalType.AVERAGE_RRR:
        return this.calculateAverageRRR(validTrades);
      case GoalType.CONSECUTIVE_WINS:
        return this.calculateConsecutiveWins(validTrades);
      case GoalType.MAX_DRAWDOWN:
        return this.calculateMaxDrawdown(validTrades);
      case GoalType.PROFIT_FACTOR:
        return this.calculateProfitFactor(validTrades);
      case GoalType.SHARPE_RATIO:
        return this.calculateSharpeRatio(validTrades);
      default:
        return 0;
    }
  }

  private filterTradesByTimeFrame(trades: TradeEntity[], timeFrame: TimeFrame): TradeEntity[] {
    const now = new Date();
    const startDate = new Date();

    switch (timeFrame) {
      case TimeFrame.DAY:
        startDate.setDate(now.getDate() - 1);
        break;
      case TimeFrame.WEEK:
        startDate.setDate(now.getDate() - 7);
        break;
      case TimeFrame.MONTH:
        startDate.setMonth(now.getMonth() - 1);
        break;
      case TimeFrame.YEAR:
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      case TimeFrame.ALL:
        return trades;
    }

    return trades.filter(trade => trade.entryDate >= startDate);
  }

  private calculateWinRate(trades: TradeEntity[]): number {
    const closedTrades = trades.filter(trade => trade.status === TradeStatus.CLOSED);
    if (closedTrades.length === 0) return 0;

    const winningTrades = closedTrades.filter(trade => trade.calculatePnL() > 0);
    return (winningTrades.length / closedTrades.length) * 100;
  }

  private calculateTotalProfit(trades: TradeEntity[]): number {
    return trades
      .filter(trade => trade.status === TradeStatus.CLOSED)
      .reduce((sum, trade) => sum + trade.calculatePnL(), 0);
  }

  private calculateAverageRRR(trades: TradeEntity[]): number {
    const validTrades = trades.filter(trade => trade.status === TradeStatus.CLOSED);
    if (validTrades.length === 0) return 0;

    const totalRRR = validTrades.reduce((sum, trade) => {
      const rrr = trade.calculateRRR();
      return sum + (rrr ? rrr.ratio : 0);
    }, 0);

    return totalRRR / validTrades.length;
  }

  private calculateConsecutiveWins(trades: TradeEntity[]): number {
    const closedTrades = trades.filter(trade => trade.status === TradeStatus.CLOSED);
    let maxConsecutive = 0;
    let currentConsecutive = 0;

    for (const trade of closedTrades) {
      if (trade.calculatePnL() > 0) {
        currentConsecutive++;
        maxConsecutive = Math.max(maxConsecutive, currentConsecutive);
      } else {
        currentConsecutive = 0;
      }
    }

    return maxConsecutive;
  }

  private calculateMaxDrawdown(trades: TradeEntity[]): number {
    const closedTrades = trades.filter(trade => trade.status === TradeStatus.CLOSED);
    let peak = 0;
    let maxDrawdown = 0;
    let currentValue = 0;

    for (const trade of closedTrades) {
      currentValue += trade.calculatePnL();
      peak = Math.max(peak, currentValue);
      maxDrawdown = Math.max(maxDrawdown, peak - currentValue);
    }

    return maxDrawdown;
  }

  private calculateProfitFactor(trades: TradeEntity[]): number {
    const closedTrades = trades.filter(trade => trade.status === TradeStatus.CLOSED);
    const winningTrades = closedTrades.filter(trade => trade.calculatePnL() > 0);
    const losingTrades = closedTrades.filter(trade => trade.calculatePnL() < 0);

    const totalWinning = winningTrades.reduce((sum, trade) => sum + trade.calculatePnL(), 0);
    const totalLosing = Math.abs(losingTrades.reduce((sum, trade) => sum + trade.calculatePnL(), 0));

    return totalLosing === 0 ? 0 : totalWinning / totalLosing;
  }

  private calculateSharpeRatio(trades: TradeEntity[]): number {
    const closedTrades = trades.filter(trade => trade.status === TradeStatus.CLOSED);
    if (closedTrades.length < 2) return 0;

    const returns = closedTrades.map(trade => trade.calculatePnL());
    const averageReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - averageReturn, 2), 0) / (returns.length - 1);
    const standardDeviation = Math.sqrt(variance);

    return standardDeviation === 0 ? 0 : averageReturn / standardDeviation;
  }
} 