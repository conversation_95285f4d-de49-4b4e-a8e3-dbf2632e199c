import { Trade, TradeEntity, TradeStatus, TradeType } from '../../models';
import { StorageService } from '../storage/storage.service';

export interface TradeFilter {
  symbol?: string;
  type?: TradeType;
  status?: TradeStatus;
  startDate?: Date;
  endDate?: Date;
  tags?: string[];
  minPnL?: number;
  maxPnL?: number;
}

export class TradeService {
  private static readonly TRADES_KEY = 'trade_reviewer_trades';
  private trades: Map<string, TradeEntity[]> = new Map();
  private storageService: StorageService;

  constructor(storageService: StorageService) {
    this.storageService = storageService;
  }

  async initialize(userId: string): Promise<TradeEntity[]> {
    const savedTrades = await this.storageService.get<Trade[]>(TradeService.TRADES_KEY);
    
    if (savedTrades) {
      const trades = savedTrades
        .filter(trade => trade.userId === userId)
        .map(trade => new TradeEntity(trade));
      this.trades.set(userId, trades);
      return trades;
    }

    this.trades.set(userId, []);
    return [];
  }

  async getTrades(userId: string, filter?: TradeFilter): Promise<TradeEntity[]> {
    const trades = this.trades.get(userId) || [];
    if (!filter) return trades;

    return trades.filter(trade => {
      if (filter.symbol && trade.symbol !== filter.symbol) return false;
      if (filter.type && trade.type !== filter.type) return false;
      if (filter.status && trade.status !== filter.status) return false;
      
      if (filter.startDate && trade.entryDate < filter.startDate) return false;
      if (filter.endDate && trade.entryDate > filter.endDate) return false;
      
      if (filter.tags && filter.tags.length > 0) {
        if (!filter.tags.some(tag => trade.tags.includes(tag))) return false;
      }

      const pnl = trade.calculatePnL();
      if (filter.minPnL !== undefined && pnl < filter.minPnL) return false;
      if (filter.maxPnL !== undefined && pnl > filter.maxPnL) return false;

      return true;
    });
  }

  async getTradeById(userId: string, tradeId: string): Promise<TradeEntity | null> {
    const trades = this.trades.get(userId) || [];
    return trades.find(trade => trade.id === tradeId) || null;
  }

  async createTrade(userId: string, trade: Omit<Trade, 'id' | 'userId' | 'updatedAt'>): Promise<TradeEntity> {
    const newTrade = new TradeEntity({
      ...trade,
      id: this.generateTradeId(),
      userId,
      updatedAt: new Date()
    });

    const userTrades = this.trades.get(userId) || [];
    userTrades.push(newTrade);
    this.trades.set(userId, userTrades);
    
    await this.save();
    return newTrade;
  }

  async updateTrade(userId: string, tradeId: string, updates: Partial<Trade>): Promise<TradeEntity> {
    const trades = this.trades.get(userId) || [];
    const index = trades.findIndex(trade => trade.id === tradeId);
    
    if (index === -1) {
      throw new Error('Trade not found');
    }

    const currentTrade = trades[index];
    const updatedTrade = new TradeEntity({
      ...currentTrade,
      ...updates,
      userId, // Ensure userId cannot be changed
      updatedAt: new Date()
    });

    trades[index] = updatedTrade;
    await this.save();
    return updatedTrade;
  }

  async deleteTrade(userId: string, tradeId: string): Promise<void> {
    const trades = this.trades.get(userId) || [];
    const filteredTrades = trades.filter(trade => trade.id !== tradeId);
    
    if (filteredTrades.length === trades.length) {
      throw new Error('Trade not found');
    }

    this.trades.set(userId, filteredTrades);
    await this.save();
  }

  async closeTrade(
    userId: string, 
    tradeId: string, 
    exitPrice: number, 
    exitDate: Date = new Date()
  ): Promise<TradeEntity> {
    return this.updateTrade(userId, tradeId, {
      exitPrice,
      exitDate,
      status: TradeStatus.CLOSED
    });
  }

  async addTags(userId: string, tradeId: string, tags: string[]): Promise<TradeEntity> {
    const trade = await this.getTradeById(userId, tradeId);
    if (!trade) {
      throw new Error('Trade not found');
    }

    const uniqueTags = Array.from(new Set([...trade.tags, ...tags]));
    return this.updateTrade(userId, tradeId, { tags: uniqueTags });
  }

  async removeTags(userId: string, tradeId: string, tags: string[]): Promise<TradeEntity> {
    const trade = await this.getTradeById(userId, tradeId);
    if (!trade) {
      throw new Error('Trade not found');
    }

    const updatedTags = trade.tags.filter(tag => !tags.includes(tag));
    return this.updateTrade(userId, tradeId, { tags: updatedTags });
  }

  async getTradesBySymbol(userId: string, symbol: string): Promise<TradeEntity[]> {
    return this.getTrades(userId, { symbol });
  }

  async getTradesByType(userId: string, type: TradeType): Promise<TradeEntity[]> {
    return this.getTrades(userId, { type });
  }

  async getTradesByStatus(userId: string, status: TradeStatus): Promise<TradeEntity[]> {
    return this.getTrades(userId, { status });
  }

  async getTradesByDateRange(userId: string, startDate: Date, endDate: Date): Promise<TradeEntity[]> {
    return this.getTrades(userId, { startDate, endDate });
  }

  async getTradesByTag(userId: string, tag: string): Promise<TradeEntity[]> {
    return this.getTrades(userId, { tags: [tag] });
  }

  private generateTradeId(): string {
    return `tr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async save(): Promise<void> {
    const allTrades = Array.from(this.trades.values()).flat();
    await this.storageService.set(TradeService.TRADES_KEY, allTrades);
  }
} 