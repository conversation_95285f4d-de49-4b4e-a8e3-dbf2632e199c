{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/", "native_build": true, "dependencies": []}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": []}], "android": [{"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/", "native_build": true, "dependencies": []}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.0/", "native_build": true, "dependencies": []}], "macos": [{"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/", "native_build": true, "dependencies": []}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": []}], "linux": [{"name": "flutter_local_notifications_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/", "native_build": false, "dependencies": []}, {"name": "path_provider_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": []}, {"name": "shared_preferences_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/", "native_build": false, "dependencies": ["path_provider_linux"]}], "windows": [{"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": []}, {"name": "shared_preferences_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/", "native_build": false, "dependencies": ["path_provider_windows"]}], "web": [{"name": "firebase_core_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.17.5/", "dependencies": []}, {"name": "shared_preferences_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.2/", "dependencies": []}]}, "dependencyGraph": [{"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "flutter_local_notifications", "dependencies": ["flutter_local_notifications_linux"]}, {"name": "flutter_local_notifications_linux", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}], "date_created": "2025-01-04 09:17:20.595220", "version": "3.27.1", "swift_package_manager_enabled": false}