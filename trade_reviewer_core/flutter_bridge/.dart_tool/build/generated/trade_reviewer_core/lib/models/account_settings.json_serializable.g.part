// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TradingAccountImpl _$$TradingAccountImplFromJson(Map<String, dynamic> json) =>
    _$TradingAccountImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      provider: json['provider'] as String,
      status: $enumDecode(_$AccountStatusEnumMap, json['status']),
      lastSynced: DateTime.parse(json['lastSynced'] as String),
      credentials: Map<String, String>.from(json['credentials'] as Map),
      initialCapital: (json['initialCapital'] as num).toDouble(),
      isActive: json['isActive'] as bool? ?? true,
    );

Map<String, dynamic> _$$TradingAccountImplToJson(
        _$TradingAccountImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'provider': instance.provider,
      'status': _$AccountStatusEnumMap[instance.status]!,
      'lastSynced': instance.lastSynced.toIso8601String(),
      'credentials': instance.credentials,
      'initialCapital': instance.initialCapital,
      'isActive': instance.isActive,
    };

const _$AccountStatusEnumMap = {
  AccountStatus.CONNECTED: 'CONNECTED',
  AccountStatus.DISCONNECTED: 'DISCONNECTED',
  AccountStatus.PENDING: 'PENDING',
  AccountStatus.ERROR: 'ERROR',
};

_$ApiKeyImpl _$$ApiKeyImplFromJson(Map<String, dynamic> json) => _$ApiKeyImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      key: json['key'] as String,
      secret: json['secret'] as String?,
      status: $enumDecode(_$ApiKeyStatusEnumMap, json['status']),
      expiryDate: json['expiryDate'] == null
          ? null
          : DateTime.parse(json['expiryDate'] as String),
    );

Map<String, dynamic> _$$ApiKeyImplToJson(_$ApiKeyImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'key': instance.key,
      'secret': instance.secret,
      'status': _$ApiKeyStatusEnumMap[instance.status]!,
      'expiryDate': instance.expiryDate?.toIso8601String(),
    };

const _$ApiKeyStatusEnumMap = {
  ApiKeyStatus.ACTIVE: 'ACTIVE',
  ApiKeyStatus.INACTIVE: 'INACTIVE',
  ApiKeyStatus.EXPIRED: 'EXPIRED',
};

_$AccountSettingsImpl _$$AccountSettingsImplFromJson(
        Map<String, dynamic> json) =>
    _$AccountSettingsImpl(
      tradingAccounts: (json['tradingAccounts'] as List<dynamic>)
          .map((e) => TradingAccount.fromJson(e as Map<String, dynamic>))
          .toList(),
      apiKeys: (json['apiKeys'] as List<dynamic>)
          .map((e) => ApiKey.fromJson(e as Map<String, dynamic>))
          .toList(),
      autoSync: json['autoSync'] as bool,
      syncFrequency: (json['syncFrequency'] as num).toInt(),
    );

Map<String, dynamic> _$$AccountSettingsImplToJson(
        _$AccountSettingsImpl instance) =>
    <String, dynamic>{
      'tradingAccounts': instance.tradingAccounts,
      'apiKeys': instance.apiKeys,
      'autoSync': instance.autoSync,
      'syncFrequency': instance.syncFrequency,
    };
