// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$GoalImpl _$$GoalImplFromJson(Map<String, dynamic> json) => _$GoalImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      name: json['name'] as String,
      type: $enumDecode(_$GoalTypeEnumMap, json['type']),
      period: $enumDecode(_$GoalPeriodEnumMap, json['period']),
      target: (json['target'] as num).toDouble(),
      progress: (json['progress'] as num).toDouble(),
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      isActive: json['isActive'] as bool,
      description: json['description'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$GoalImplToJson(_$GoalImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'name': instance.name,
      'type': _$GoalTypeEnumMap[instance.type]!,
      'period': _$GoalPeriodEnumMap[instance.period]!,
      'target': instance.target,
      'progress': instance.progress,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'isActive': instance.isActive,
      'description': instance.description,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$GoalTypeEnumMap = {
  GoalType.profit: 'PROFIT',
  GoalType.winRate: 'WIN_RATE',
  GoalType.tradeCount: 'TRADE_COUNT',
  GoalType.profitFactor: 'PROFIT_FACTOR',
  GoalType.averageRrr: 'AVERAGE_RRR',
};

const _$GoalPeriodEnumMap = {
  GoalPeriod.daily: 'DAILY',
  GoalPeriod.weekly: 'WEEKLY',
  GoalPeriod.monthly: 'MONTHLY',
  GoalPeriod.quarterly: 'QUARTERLY',
  GoalPeriod.yearly: 'YEARLY',
  GoalPeriod.allTime: 'ALL_TIME',
};
