// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$NotificationImpl _$$NotificationImplFromJson(Map<String, dynamic> json) =>
    _$NotificationImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      recipient: json['recipient'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      priority: $enumDecode(_$NotificationPriorityEnumMap, json['priority']),
      type: $enumDecode(_$NotificationTypeEnumMap, json['type']),
      channels: (json['channels'] as List<dynamic>)
          .map((e) => $enumDecode(_$NotificationChannelEnumMap, e))
          .toList(),
      read: json['read'] as bool? ?? false,
      dismissed: json['dismissed'] as bool? ?? false,
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
      data: json['data'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$NotificationImplToJson(_$NotificationImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'title': instance.title,
      'message': instance.message,
      'recipient': instance.recipient,
      'createdAt': instance.createdAt.toIso8601String(),
      'priority': _$NotificationPriorityEnumMap[instance.priority]!,
      'type': _$NotificationTypeEnumMap[instance.type]!,
      'channels': instance.channels
          .map((e) => _$NotificationChannelEnumMap[e]!)
          .toList(),
      'read': instance.read,
      'dismissed': instance.dismissed,
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'expiresAt': instance.expiresAt?.toIso8601String(),
      'data': instance.data,
    };

const _$NotificationPriorityEnumMap = {
  NotificationPriority.low: 'LOW',
  NotificationPriority.medium: 'MEDIUM',
  NotificationPriority.high: 'HIGH',
  NotificationPriority.urgent: 'URGENT',
};

const _$NotificationTypeEnumMap = {
  NotificationType.tradeAlert: 'TRADE_ALERT',
  NotificationType.priceAlert: 'PRICE_ALERT',
  NotificationType.goalAchieved: 'GOAL_ACHIEVED',
  NotificationType.riskWarning: 'RISK_WARNING',
  NotificationType.systemUpdate: 'SYSTEM_UPDATE',
  NotificationType.backupReminder: 'BACKUP_REMINDER',
  NotificationType.journalReminder: 'JOURNAL_REMINDER',
  NotificationType.tradeStats: 'TRADE_STATS',
};

const _$NotificationChannelEnumMap = {
  NotificationChannel.inApp: 'IN_APP',
  NotificationChannel.email: 'EMAIL',
  NotificationChannel.push: 'PUSH',
  NotificationChannel.sms: 'SMS',
};
