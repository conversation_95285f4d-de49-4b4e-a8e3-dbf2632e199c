import '../models/trade.dart';
import '../models/journal.dart';
import 'storage_service.dart';

class TradesService {
  static const String _tradesKey = 'trade_reviewer_trades';
  static const String _journalKey = 'trade_reviewer_journal';
  final StorageService _storageService;
  List<Trade> _trades = [];
  List<JournalEntry> _journal = [];

  TradesService(this._storageService);

  Future<({List<Trade> trades, List<JournalEntry> journal})> initialize(
      String userId) async {
    final savedTrades = await _storageService.get<List<dynamic>>(_tradesKey);
    final savedJournal = await _storageService.get<List<dynamic>>(_journalKey);

    if (savedTrades != null) {
      _trades = savedTrades
          .map((json) => Trade.fromJson(json as Map<String, dynamic>))
          .where((t) => t.userId == userId)
          .toList();
    }

    if (savedJournal != null) {
      _journal = savedJournal
          .map((json) => JournalEntry.fromJson(json as Map<String, dynamic>))
          .where((j) => j.userId == userId)
          .toList();
    }

    return (trades: _trades, journal: _journal);
  }

  Future<List<Trade>> getTrades(String userId) async {
    return _trades.where((t) => t.userId == userId).toList();
  }

  Future<List<Trade>> getOpenTrades(String userId) async {
    return _trades
        .where((t) => t.userId == userId && t.status == TradeStatus.open)
        .toList();
  }

  Future<List<Trade>> getClosedTrades(String userId) async {
    return _trades
        .where((t) => t.userId == userId && t.status == TradeStatus.closed)
        .toList();
  }

  Future<Trade?> getTradeById(String tradeId, String userId) async {
    return _trades.firstWhere(
      (t) => t.id == tradeId && t.userId == userId,
      orElse: () => throw StateError('Trade not found'),
    );
  }

  Future<Trade> addTrade(Trade trade) async {
    _trades.add(trade);
    await _saveTrades();
    return trade;
  }

  Future<Trade?> updateTrade(
      String tradeId, String userId, Trade updates) async {
    final index =
        _trades.indexWhere((t) => t.id == tradeId && t.userId == userId);

    if (index != -1) {
      final trade = _trades[index];
      final updated = trade.copyWith(
        symbol: updates.symbol,
        type: updates.type,
        entryPrice: updates.entryPrice,
        exitPrice: updates.exitPrice,
        quantity: updates.quantity,
        entryDate: updates.entryDate,
        exitDate: updates.exitDate,
        status: updates.status,
        notes: updates.notes,
        tags: updates.tags,
        commission: updates.commission,
        fees: updates.fees,
        updatedAt: DateTime.now(),
      );
      _trades[index] = updated;
      await _saveTrades();
      return updated;
    }

    return null;
  }

  Future<Trade?> closeTrade(String tradeId, String userId, double exitPrice,
      DateTime exitDate) async {
    final index =
        _trades.indexWhere((t) => t.id == tradeId && t.userId == userId);

    if (index != -1) {
      final trade = _trades[index];
      final updated = trade.copyWith(
        exitPrice: exitPrice,
        exitDate: exitDate,
        status: TradeStatus.closed,
        updatedAt: DateTime.now(),
      );
      _trades[index] = updated;
      await _saveTrades();
      return updated;
    }

    return null;
  }

  Future<void> deleteTrade(String tradeId, String userId) async {
    _trades.removeWhere((t) => t.id == tradeId && t.userId == userId);
    await _saveTrades();
  }

  Future<List<JournalEntry>> getJournalEntries(String userId) async {
    return _journal.where((j) => j.userId == userId).toList();
  }

  Future<JournalEntry?> getJournalEntryByTradeId(
      String tradeId, String userId) async {
    return _journal.firstWhere(
      (j) => j.tradeId == tradeId && j.userId == userId,
      orElse: () => throw StateError('Journal entry not found'),
    );
  }

  Future<JournalEntry> addJournalEntry(JournalEntry entry) async {
    _journal.add(entry);
    await _saveJournal();
    return entry;
  }

  Future<JournalEntry?> updateJournalEntry(
      String entryId, String userId, JournalEntry updates) async {
    final index =
        _journal.indexWhere((j) => j.id == entryId && j.userId == userId);

    if (index != -1) {
      final entry = _journal[index];
      final updated = entry.copyWith(
        date: updates.date,
        marketCondition: updates.marketCondition,
        emotionalState: updates.emotionalState,
        tradePlan: updates.tradePlan,
        mistakes: updates.mistakes,
        analysis: updates.analysis,
        lessons: updates.lessons,
        screenshots: updates.screenshots,
        rating: updates.rating,
        tags: updates.tags,
        updatedAt: DateTime.now(),
      );
      _journal[index] = updated;
      await _saveJournal();
      return updated;
    }

    return null;
  }

  Future<void> deleteJournalEntry(String entryId, String userId) async {
    _journal.removeWhere((j) => j.id == entryId && j.userId == userId);
    await _saveJournal();
  }

  Future<void> clear(String userId) async {
    _trades.removeWhere((t) => t.userId == userId);
    _journal.removeWhere((j) => j.userId == userId);
    await Future.wait([
      _saveTrades(),
      _saveJournal(),
    ]);
  }

  Future<void> _saveTrades() async {
    await _storageService.set(
      _tradesKey,
      _trades.map((t) => t.toJson()).toList(),
    );
  }

  Future<void> _saveJournal() async {
    await _storageService.set(
      _journalKey,
      _journal.map((j) => j.toJson()).toList(),
    );
  }
}
