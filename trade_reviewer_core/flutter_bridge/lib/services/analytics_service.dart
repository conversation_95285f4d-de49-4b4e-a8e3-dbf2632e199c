import 'dart:convert';
import 'package:shared_preferences' as prefs;
import '../models/analytics.dart';
import '../models/trade.dart';

class AnalyticsService {
  static const String _analyticsKey = 'trade_reviewer_analytics';
  final prefs.SharedPreferences _prefs;
  Analytics? _analytics;

  AnalyticsService(this._prefs);

  Future<Analytics> initialize(String userId) async {
    final savedAnalytics = _prefs.getString(_analyticsKey);

    if (savedAnalytics != null) {
      final decoded = jsonDecode(savedAnalytics);
      final analytics = Analytics.fromJson(decoded);

      if (analytics.userId == userId) {
        _analytics = analytics;
        return analytics;
      }
    }

    final newAnalytics = Analytics(
      userId: userId,
      timeFrame: TimeFrame.allTime,
      startDate: DateTime.now(),
      endDate: DateTime.now(),
      metrics: const TradeMetrics(
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        breakEvenTrades: 0,
        winRate: 0,
        profitFactor: 0,
        averageWin: 0,
        averageLoss: 0,
        largestWin: 0,
        largestLoss: 0,
        totalPnL: 0,
        netPnL: 0,
        grossPnL: 0,
        totalCommissions: 0,
        totalFees: 0,
      ),
      updatedAt: DateTime.now(),
    );

    _analytics = newAnalytics;
    await _save();
    return newAnalytics;
  }

  Analytics? getAnalytics() => _analytics;

  Future<Analytics> calculateAnalytics(List<Trade> trades) async {
    if (_analytics == null) {
      throw Exception('Analytics not initialized. Call initialize() first.');
    }

    final closedTrades =
        trades.where((t) => t.status == TradeStatus.closed).toList();

    if (closedTrades.isEmpty) {
      return _analytics!;
    }

    var totalPnL = 0.0;
    var grossPnL = 0.0;
    var totalCommissions = 0.0;
    var totalFees = 0.0;
    var winningTrades = 0;
    var losingTrades = 0;
    var breakEvenTrades = 0;
    var totalWins = 0.0;
    var totalLosses = 0.0;
    var largestWin = 0.0;
    var largestLoss = 0.0;

    for (final trade in closedTrades) {
      final pnl = trade.calculatePnL().toDouble();
      final commission = (trade.commission ?? 0).toDouble();
      final fee = (trade.fees ?? 0).toDouble();

      totalPnL += pnl;
      grossPnL += pnl + commission + fee;
      totalCommissions += commission;
      totalFees += fee;

      if (pnl > 0) {
        winningTrades++;
        totalWins += pnl;
        largestWin = largestWin > pnl ? largestWin : pnl;
      } else if (pnl < 0) {
        losingTrades++;
        totalLosses += pnl.abs();
        largestLoss = largestLoss > pnl.abs() ? largestLoss : pnl.abs();
      } else {
        breakEvenTrades++;
      }
    }

    final totalTrades = closedTrades.length;
    final winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0.0;
    final profitFactor = totalLosses > 0
        ? totalWins / totalLosses
        : totalWins > 0
            ? double.infinity
            : 0.0;
    final averageWin = winningTrades > 0 ? totalWins / winningTrades : 0.0;
    final averageLoss = losingTrades > 0 ? totalLosses / losingTrades : 0.0;

    final metrics = TradeMetrics(
      totalTrades: totalTrades,
      winningTrades: winningTrades,
      losingTrades: losingTrades,
      breakEvenTrades: breakEvenTrades,
      winRate: winRate,
      profitFactor: profitFactor,
      averageWin: averageWin,
      averageLoss: averageLoss,
      largestWin: largestWin,
      largestLoss: largestLoss,
      totalPnL: totalPnL,
      netPnL: totalPnL,
      grossPnL: grossPnL,
      totalCommissions: totalCommissions,
      totalFees: totalFees,
    );

    _analytics = _analytics!.updateMetrics(metrics);

    // Calculate metrics by type
    final typeMetrics = <String, TradeMetrics>{};
    for (final trade in closedTrades) {
      final type = trade.type.name;
      final metrics = typeMetrics[type] ??
          const TradeMetrics(
            totalTrades: 0,
            winningTrades: 0,
            losingTrades: 0,
            breakEvenTrades: 0,
            winRate: 0,
            profitFactor: 0,
            averageWin: 0,
            averageLoss: 0,
            largestWin: 0,
            largestLoss: 0,
            totalPnL: 0,
            netPnL: 0,
            grossPnL: 0,
            totalCommissions: 0,
            totalFees: 0,
          );

      final pnl = trade.calculatePnL().toDouble();
      final commission = (trade.commission ?? 0).toDouble();
      final fee = (trade.fees ?? 0).toDouble();

      final wins = metrics.winningTrades + (pnl > 0 ? 1 : 0);
      final losses = metrics.losingTrades + (pnl < 0 ? 1 : 0);
      final breakEven = metrics.breakEvenTrades + (pnl == 0 ? 1 : 0);
      final total = wins + losses + breakEven;

      final totalPnL = metrics.totalPnL + pnl;
      final totalWins = pnl > 0 ? metrics.totalPnL + pnl : metrics.totalPnL;
      final totalLosses =
          pnl < 0 ? metrics.totalPnL + pnl.abs() : metrics.totalPnL;

      final winRate = total > 0 ? (wins / total) * 100 : 0.0;
      final profitFactor = totalLosses > 0
          ? totalWins / totalLosses
          : totalWins > 0
              ? double.infinity
              : 0.0;
      final averageWin = wins > 0 ? totalWins / wins : 0.0;
      final averageLoss = losses > 0 ? totalLosses / losses : 0.0;

      typeMetrics[type] = TradeMetrics(
        totalTrades: total,
        winningTrades: wins,
        losingTrades: losses,
        breakEvenTrades: breakEven,
        winRate: winRate,
        profitFactor: profitFactor,
        averageWin: averageWin,
        averageLoss: averageLoss,
        largestWin: pnl > metrics.largestWin ? pnl : metrics.largestWin,
        largestLoss:
            pnl.abs() > metrics.largestLoss ? pnl.abs() : metrics.largestLoss,
        totalPnL: totalPnL,
        netPnL: totalPnL,
        grossPnL: metrics.grossPnL + pnl + commission + fee,
        totalCommissions: metrics.totalCommissions + commission,
        totalFees: metrics.totalFees + fee,
      );

      _analytics = _analytics!.updateTypeMetrics(type, typeMetrics[type]!);
    }

    // Calculate metrics by symbol
    final symbolMetrics = <String, TradeMetrics>{};
    for (final trade in closedTrades) {
      final symbol = trade.symbol;
      final metrics = symbolMetrics[symbol] ??
          const TradeMetrics(
            totalTrades: 0,
            winningTrades: 0,
            losingTrades: 0,
            breakEvenTrades: 0,
            winRate: 0,
            profitFactor: 0,
            averageWin: 0,
            averageLoss: 0,
            largestWin: 0,
            largestLoss: 0,
            totalPnL: 0,
            netPnL: 0,
            grossPnL: 0,
            totalCommissions: 0,
            totalFees: 0,
          );

      final pnl = trade.calculatePnL().toDouble();
      final commission = (trade.commission ?? 0).toDouble();
      final fee = (trade.fees ?? 0).toDouble();

      final wins = metrics.winningTrades + (pnl > 0 ? 1 : 0);
      final losses = metrics.losingTrades + (pnl < 0 ? 1 : 0);
      final breakEven = metrics.breakEvenTrades + (pnl == 0 ? 1 : 0);
      final total = wins + losses + breakEven;

      final totalPnL = metrics.totalPnL + pnl;
      final totalWins = pnl > 0 ? metrics.totalPnL + pnl : metrics.totalPnL;
      final totalLosses =
          pnl < 0 ? metrics.totalPnL + pnl.abs() : metrics.totalPnL;

      final winRate = total > 0 ? (wins / total) * 100 : 0.0;
      final profitFactor = totalLosses > 0
          ? totalWins / totalLosses
          : totalWins > 0
              ? double.infinity
              : 0.0;
      final averageWin = wins > 0 ? totalWins / wins : 0.0;
      final averageLoss = losses > 0 ? totalLosses / losses : 0.0;

      symbolMetrics[symbol] = TradeMetrics(
        totalTrades: total,
        winningTrades: wins,
        losingTrades: losses,
        breakEvenTrades: breakEven,
        winRate: winRate,
        profitFactor: profitFactor,
        averageWin: averageWin,
        averageLoss: averageLoss,
        largestWin: pnl > metrics.largestWin ? pnl : metrics.largestWin,
        largestLoss:
            pnl.abs() > metrics.largestLoss ? pnl.abs() : metrics.largestLoss,
        totalPnL: totalPnL,
        netPnL: totalPnL,
        grossPnL: metrics.grossPnL + pnl + commission + fee,
        totalCommissions: metrics.totalCommissions + commission,
        totalFees: metrics.totalFees + fee,
      );

      _analytics =
          _analytics!.updateSymbolMetrics(symbol, symbolMetrics[symbol]!);
    }

    await _save();
    return _analytics!;
  }

  Future<void> clear() async {
    _analytics = null;
    await _prefs.remove(_analyticsKey);
  }

  Future<void> _save() async {
    if (_analytics != null) {
      final encoded = jsonEncode(_analytics!.toJson());
      await _prefs.setString(_analyticsKey, encoded);
    }
  }
}
