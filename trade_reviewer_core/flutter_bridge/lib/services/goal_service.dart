import 'dart:convert';
import 'package:shared_preferences' as prefs;
import '../models/goal.dart';

class GoalFilter {
  final GoalType? type;
  final GoalPeriod? period;
  final bool? isActive;
  final bool? isCompleted;
  final DateTime? startDate;
  final DateTime? endDate;

  const GoalFilter({
    this.type,
    this.period,
    this.isActive,
    this.isCompleted,
    this.startDate,
    this.endDate,
  });
}

class GoalService {
  static const String _goalsKey = 'trade_reviewer_goals';
  final prefs.SharedPreferences _prefs;
  final Map<String, List<Goal>> _goals = {};

  GoalService(this._prefs);

  Future<List<Goal>> initialize(String userId) async {
    final savedGoals = _prefs.getString(_goalsKey);

    if (savedGoals != null) {
      final List<dynamic> decoded = jsonDecode(savedGoals);
      final goals = decoded
          .map((json) => Goal.fromJson(json))
          .where((goal) => goal.userId == userId)
          .toList();
      _goals[userId] = goals;
      return goals;
    }

    _goals[userId] = [];
    return [];
  }

  List<Goal> getGoals(String userId, [GoalFilter? filter]) {
    final goals = _goals[userId] ?? [];
    if (filter == null) return goals;

    return goals.where((goal) {
      if (filter.type != null && goal.type != filter.type) return false;
      if (filter.period != null && goal.period != filter.period) return false;
      if (filter.isActive != null && goal.isActive != filter.isActive)
        return false;
      if (filter.isCompleted != null &&
          goal.isCompleted() != filter.isCompleted) return false;

      if (filter.startDate != null &&
          goal.startDate.isBefore(filter.startDate!)) {
        return false;
      }
      if (filter.endDate != null &&
          goal.endDate != null &&
          goal.endDate!.isAfter(filter.endDate!)) {
        return false;
      }

      return true;
    }).toList();
  }

  Goal? getGoalById(String userId, String goalId) {
    final goals = _goals[userId] ?? [];
    return goals.firstWhere(
      (goal) => goal.id == goalId,
      orElse: () => null as Goal, // This will never be reached due to orElse
    );
  }

  Future<Goal> createGoal(String userId, Goal goal) async {
    final newGoal = goal.copyWith(
      id: _generateGoalId(),
      userId: userId,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    final userGoals = _goals[userId] ?? [];
    userGoals.add(newGoal);
    _goals[userId] = userGoals;

    await _save();
    return newGoal;
  }

  Future<Goal> updateGoal(String userId, String goalId, Goal updates) async {
    final goals = _goals[userId] ?? [];
    final index = goals.indexWhere((goal) => goal.id == goalId);

    if (index == -1) {
      throw Exception('Goal not found');
    }

    final currentGoal = goals[index];
    final updatedGoal = updates.copyWith(
      id: currentGoal.id,
      userId: userId, // Ensure userId cannot be changed
      updatedAt: DateTime.now(),
    );

    goals[index] = updatedGoal;
    await _save();
    return updatedGoal;
  }

  Future<void> deleteGoal(String userId, String goalId) async {
    final goals = _goals[userId] ?? [];
    final filteredGoals = goals.where((goal) => goal.id != goalId).toList();

    if (filteredGoals.length == goals.length) {
      throw Exception('Goal not found');
    }

    _goals[userId] = filteredGoals;
    await _save();
  }

  Future<Goal> updateProgress(
      String userId, String goalId, double value) async {
    final goal = getGoalById(userId, goalId);
    if (goal == null) {
      throw Exception('Goal not found');
    }

    return updateGoal(
      userId,
      goalId,
      goal.copyWith(
        progress: value,
        updatedAt: DateTime.now(),
      ),
    );
  }

  List<Goal> getActiveGoals(String userId) {
    return getGoals(userId, const GoalFilter(isActive: true));
  }

  List<Goal> getCompletedGoals(String userId) {
    return getGoals(userId, const GoalFilter(isCompleted: true));
  }

  List<Goal> getGoalsByType(String userId, GoalType type) {
    return getGoals(userId, GoalFilter(type: type));
  }

  List<Goal> getGoalsByPeriod(String userId, GoalPeriod period) {
    return getGoals(userId, GoalFilter(period: period));
  }

  List<Goal> getGoalsByDateRange(
      String userId, DateTime startDate, DateTime endDate) {
    return getGoals(userId, GoalFilter(startDate: startDate, endDate: endDate));
  }

  String _generateGoalId() {
    return 'gl_${DateTime.now().millisecondsSinceEpoch}_${(1000000 + (DateTime.now().microsecondsSinceEpoch % 1000000)).toString().substring(1)}';
  }

  Future<void> _save() async {
    final allGoals = _goals.values.expand((goals) => goals).toList();
    final encoded = jsonEncode(allGoals.map((g) => g.toJson()).toList());
    await _prefs.setString(_goalsKey, encoded);
  }
}
