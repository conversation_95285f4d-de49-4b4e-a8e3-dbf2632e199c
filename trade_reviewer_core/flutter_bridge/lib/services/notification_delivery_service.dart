import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;
import '../models/notification.dart' as core;
import '../models/notification_settings.dart';
import 'package:mailer/mailer.dart' as mailer;
import 'package:mailer/smtp_server.dart';

class NotificationDeliveryService {
  final String _emailHost;
  final String _emailUsername;
  final String _emailPassword;
  final FlutterLocalNotificationsPlugin _localNotifications;
  NotificationSettings? _settings;

  NotificationDeliveryService({
    required String emailHost,
    required String emailUsername,
    required String emailPassword,
  })  : _emailHost = emailHost,
        _emailUsername = emailUsername,
        _emailPassword = emailPassword,
        _localNotifications = FlutterLocalNotificationsPlugin();

  Future<void> initialize() async {
    const androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings();
    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );
    await _localNotifications.initialize(initSettings);

    // Initialize timezone
    tz.initializeTimeZones();
  }

  void updateSettings(NotificationSettings settings) {
    _settings = settings;
  }

  Future<void> deliverNotification(core.Notification notification) async {
    if (_settings == null || !_settings!.enabled) return;

    // Check quiet hours
    if (_isInQuietHours()) {
      // If it's an urgent notification, deliver it anyway
      if (notification.priority != core.NotificationPriority.urgent) {
        return;
      }
    }

    // Check if notification type is enabled
    if (!_settings!.enabledTypes.contains(notification.type)) {
      return;
    }

    for (final channel in notification.channels) {
      // Check if channel is enabled in settings
      if (!_settings!.channels.contains(channel)) continue;

      switch (channel) {
        case core.NotificationChannel.inApp:
          await _showLocalNotification(notification);
          break;
        case core.NotificationChannel.push:
          await _sendPushNotification(notification);
          break;
        case core.NotificationChannel.email:
          await _sendEmailNotification(notification);
          break;
        case core.NotificationChannel.sms:
          // SMS implementation would go here
          break;
      }
    }
  }

  Future<void> scheduleNotification({
    required String title,
    required String message,
    required String userId,
    required String recipient,
    required core.NotificationType type,
    required DateTime scheduledDate,
    core.NotificationPriority? priority,
    List<core.NotificationChannel>? channels,
    Map<String, dynamic>? data,
  }) async {
    if (_settings == null || !_settings!.enabled) return;

    final notification = core.Notification(
      id: _generateId(),
      userId: userId,
      title: title,
      message: message,
      recipient: recipient,
      type: type,
      priority: priority ?? _settings!.defaultPriority,
      channels: channels ?? _settings!.channels,
      createdAt: DateTime.now(),
      data: data,
    );

    // Schedule local notification
    if (notification.channels.contains(core.NotificationChannel.inApp)) {
      final androidDetails = AndroidNotificationDetails(
        _getChannelId(notification.type),
        _getChannelName(notification.type),
        channelDescription: _getChannelDescription(notification.type),
        importance: _getAndroidImportance(notification.priority),
        priority: _getAndroidPriority(notification.priority),
        enableVibration: _settings!.hapticEnabled,
        enableLights: true,
        color: _getNotificationColor(notification.priority),
        ledColor: _getNotificationColor(notification.priority),
        playSound: _settings!.soundEnabled,
      );

      final iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: _settings!.soundEnabled,
        threadIdentifier: _getChannelId(notification.type),
        interruptionLevel: _getIOSInterruptionLevel(notification.priority),
      );

      final details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      final scheduledTz = tz.TZDateTime.from(scheduledDate, tz.local);

      await _localNotifications.zonedSchedule(
        notification.id.hashCode,
        notification.title,
        notification.message,
        scheduledTz,
        details,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
        androidAllowWhileIdle: true,
        payload: notification.data?.toString(),
      );
    }

    // Schedule email notification
    if (notification.channels.contains(core.NotificationChannel.email)) {
      // TODO: Implement email scheduling
    }
  }

  bool _isInQuietHours() {
    if (_settings == null) return false;

    final now = DateTime.now();
    final currentTime =
        '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';

    final startMinutes =
        TimeValidator.timeToMinutes(_settings!.quietHoursStart);
    final endMinutes = TimeValidator.timeToMinutes(_settings!.quietHoursEnd);
    final currentMinutes = TimeValidator.timeToMinutes(currentTime);

    // Handle cases where quiet hours span midnight
    if (startMinutes > endMinutes) {
      return currentMinutes >= startMinutes || currentMinutes <= endMinutes;
    }

    return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
  }

  String _generateId() {
    return 'nt_${DateTime.now().millisecondsSinceEpoch}_${DateTime.now().microsecondsSinceEpoch}';
  }

  Future<void> _showLocalNotification(core.Notification notification) async {
    final androidImportance = _getAndroidImportance(notification.priority);
    final androidPriority = _getAndroidPriority(notification.priority);
    final channelId = _getChannelId(notification.type);
    final channelName = _getChannelName(notification.type);
    final channelDescription = _getChannelDescription(notification.type);

    final androidDetails = AndroidNotificationDetails(
      channelId,
      channelName,
      channelDescription: channelDescription,
      importance: androidImportance,
      priority: androidPriority,
      enableVibration: true,
      enableLights: true,
      color: _getNotificationColor(notification.priority),
      ledColor: _getNotificationColor(notification.priority),
      ticker: 'Trade Reviewer Notification',
    );

    final iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      threadIdentifier: channelId,
      interruptionLevel: _getIOSInterruptionLevel(notification.priority),
    );

    final details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      notification.id.hashCode,
      notification.title,
      notification.message,
      details,
      payload: notification.data?.toString(),
    );
  }

  Future<void> _sendPushNotification(core.Notification notification) async {
    // Using local notifications as a fallback since push notifications are not implemented
    await _showLocalNotification(notification);
  }

  Future<void> _sendEmailNotification(core.Notification notification) async {
    final message = mailer.Message()
      ..from = mailer.Address(_emailUsername)
      ..recipients.add(notification.recipient)
      ..subject = notification.title
      ..text = notification.message;

    try {
      final smtpServer = SmtpServer(
        _emailHost,
        username: _emailUsername,
        password: _emailPassword,
      );
      await mailer.send(message, smtpServer);
    } catch (e) {
      print('Error sending email notification: $e');
    }
  }

  Future<void> cancelNotification(int id) async {
    await _localNotifications.cancel(id);
  }

  Future<void> cancelAllNotifications() async {
    await _localNotifications.cancelAll();
  }

  Importance _getAndroidImportance(core.NotificationPriority priority) {
    switch (priority) {
      case core.NotificationPriority.low:
        return Importance.low;
      case core.NotificationPriority.medium:
        return Importance.defaultImportance;
      case core.NotificationPriority.high:
        return Importance.high;
      case core.NotificationPriority.urgent:
        return Importance.max;
    }
  }

  Priority _getAndroidPriority(core.NotificationPriority priority) {
    switch (priority) {
      case core.NotificationPriority.low:
        return Priority.low;
      case core.NotificationPriority.medium:
        return Priority.defaultPriority;
      case core.NotificationPriority.high:
        return Priority.high;
      case core.NotificationPriority.urgent:
        return Priority.max;
    }
  }

  String _getChannelId(core.NotificationType type) {
    switch (type) {
      case core.NotificationType.tradeAlert:
        return 'trade_alerts';
      case core.NotificationType.priceAlert:
        return 'price_alerts';
      case core.NotificationType.goalAchieved:
        return 'goal_alerts';
      case core.NotificationType.riskWarning:
        return 'risk_alerts';
      case core.NotificationType.systemUpdate:
        return 'system_alerts';
      case core.NotificationType.backupReminder:
        return 'backup_alerts';
      case core.NotificationType.journalReminder:
        return 'journal_alerts';
      case core.NotificationType.tradeStats:
        return 'stats_alerts';
    }
  }

  String _getChannelName(core.NotificationType type) {
    switch (type) {
      case core.NotificationType.tradeAlert:
        return 'Trade Alerts';
      case core.NotificationType.priceAlert:
        return 'Price Alerts';
      case core.NotificationType.goalAchieved:
        return 'Goal Alerts';
      case core.NotificationType.riskWarning:
        return 'Risk Alerts';
      case core.NotificationType.systemUpdate:
        return 'System Updates';
      case core.NotificationType.backupReminder:
        return 'Backup Reminders';
      case core.NotificationType.journalReminder:
        return 'Journal Reminders';
      case core.NotificationType.tradeStats:
        return 'Trading Statistics';
    }
  }

  String _getChannelDescription(core.NotificationType type) {
    switch (type) {
      case core.NotificationType.tradeAlert:
        return 'Notifications about trade executions and opportunities';
      case core.NotificationType.priceAlert:
        return 'Alerts about price movements and targets';
      case core.NotificationType.goalAchieved:
        return 'Updates about your trading goals';
      case core.NotificationType.riskWarning:
        return 'Important risk management alerts';
      case core.NotificationType.systemUpdate:
        return 'System maintenance and update notifications';
      case core.NotificationType.backupReminder:
        return 'Reminders to backup your trading data';
      case core.NotificationType.journalReminder:
        return 'Reminders to update your trading journal';
      case core.NotificationType.tradeStats:
        return 'Updates about your trading statistics';
    }
  }

  Color _getNotificationColor(core.NotificationPriority priority) {
    switch (priority) {
      case core.NotificationPriority.low:
        return const Color(0xFF4CAF50); // Green
      case core.NotificationPriority.medium:
        return const Color(0xFFFFA726); // Orange
      case core.NotificationPriority.high:
        return const Color(0xFFF44336); // Red
      case core.NotificationPriority.urgent:
        return const Color(0xFF9C27B0); // Purple
    }
  }

  InterruptionLevel _getIOSInterruptionLevel(
      core.NotificationPriority priority) {
    switch (priority) {
      case core.NotificationPriority.low:
        return InterruptionLevel.passive;
      case core.NotificationPriority.medium:
        return InterruptionLevel.active;
      case core.NotificationPriority.high:
        return InterruptionLevel.timeSensitive;
      case core.NotificationPriority.urgent:
        return InterruptionLevel.critical;
    }
  }
}
