import 'package:timezone/timezone.dart' as tz;
import '../models/notification.dart' as core;
import '../models/notification_settings.dart';
import 'notification_delivery_service.dart';

class NotificationSchedulerService {
  final NotificationDeliveryService _deliveryService;
  NotificationSettings? _settings;

  NotificationSchedulerService(this._deliveryService);

  void updateSettings(NotificationSettings settings) {
    _settings = settings;
    _scheduleJournalReminders();
  }

  Future<void> _scheduleJournalReminders() async {
    if (_settings == null || !_settings!.enabled) return;
    if (!_settings!.enabledTypes
        .contains(core.NotificationType.journalReminder)) return;

    // Cancel existing journal reminders
    // TODO: Implement cancellation of existing reminders

    // Schedule new reminders based on frequency
    final now = DateTime.now();
    DateTime nextReminder;

    switch (_settings!.journalReminderFrequency) {
      case ReminderFrequency.daily:
        // Schedule for next day at 9 AM
        nextReminder = DateTime(
          now.year,
          now.month,
          now.day + 1,
          9, // 9 AM
          0,
        );
        break;
      case ReminderFrequency.weekly:
        // Schedule for next week, same day at 9 AM
        nextReminder = DateTime(
          now.year,
          now.month,
          now.day + 7,
          9,
          0,
        );
        break;
      case ReminderFrequency.monthly:
        // Schedule for next month, same day at 9 AM
        nextReminder = DateTime(
          now.year,
          now.month + 1,
          now.day,
          9,
          0,
        );
        break;
    }

    await _deliveryService.scheduleNotification(
      title: 'Trading Journal Reminder',
      message:
          'Time to update your trading journal! Keep track of your trades and insights.',
      userId: 'default_user', // TODO: Get from auth service
      recipient: '<EMAIL>', // TODO: Get from user profile
      type: core.NotificationType.journalReminder,
      scheduledDate: nextReminder,
      priority: core.NotificationPriority.medium,
      channels: [
        core.NotificationChannel.inApp,
        if (_settings!.channels.contains(core.NotificationChannel.push))
          core.NotificationChannel.push,
        if (_settings!.channels.contains(core.NotificationChannel.email))
          core.NotificationChannel.email,
      ],
    );
  }

  Future<void> scheduleTradeAlert({
    required String title,
    required String message,
    required DateTime scheduledDate,
    core.NotificationPriority priority = core.NotificationPriority.high,
  }) async {
    if (_settings == null || !_settings!.enabled) return;
    if (!_settings!.enabledTypes.contains(core.NotificationType.tradeAlert))
      return;

    await _deliveryService.scheduleNotification(
      title: title,
      message: message,
      userId: 'default_user', // TODO: Get from auth service
      recipient: '<EMAIL>', // TODO: Get from user profile
      type: core.NotificationType.tradeAlert,
      scheduledDate: scheduledDate,
      priority: priority,
      channels: _settings!.channels,
    );
  }

  Future<void> schedulePriceAlert({
    required String symbol,
    required double targetPrice,
    required DateTime scheduledDate,
    core.NotificationPriority priority = core.NotificationPriority.high,
  }) async {
    if (_settings == null || !_settings!.enabled) return;
    if (!_settings!.enabledTypes.contains(core.NotificationType.priceAlert))
      return;

    await _deliveryService.scheduleNotification(
      title: 'Price Alert: $symbol',
      message: 'Target price of \$$targetPrice has been reached for $symbol',
      userId: 'default_user', // TODO: Get from auth service
      recipient: '<EMAIL>', // TODO: Get from user profile
      type: core.NotificationType.priceAlert,
      scheduledDate: scheduledDate,
      priority: priority,
      channels: _settings!.channels,
      data: {
        'symbol': symbol,
        'targetPrice': targetPrice,
      },
    );
  }

  Future<void> scheduleGoalAlert({
    required String title,
    required String message,
    required DateTime scheduledDate,
    core.NotificationPriority priority = core.NotificationPriority.medium,
  }) async {
    if (_settings == null || !_settings!.enabled) return;
    if (!_settings!.enabledTypes.contains(core.NotificationType.goalAchieved))
      return;

    await _deliveryService.scheduleNotification(
      title: title,
      message: message,
      userId: 'default_user', // TODO: Get from auth service
      recipient: '<EMAIL>', // TODO: Get from user profile
      type: core.NotificationType.goalAchieved,
      scheduledDate: scheduledDate,
      priority: priority,
      channels: _settings!.channels,
    );
  }

  Future<void> scheduleRiskWarning({
    required String title,
    required String message,
    required DateTime scheduledDate,
  }) async {
    if (_settings == null || !_settings!.enabled) return;
    if (!_settings!.enabledTypes.contains(core.NotificationType.riskWarning))
      return;

    await _deliveryService.scheduleNotification(
      title: title,
      message: message,
      userId: 'default_user', // TODO: Get from auth service
      recipient: '<EMAIL>', // TODO: Get from user profile
      type: core.NotificationType.riskWarning,
      scheduledDate: scheduledDate,
      priority: core.NotificationPriority.urgent,
      channels: _settings!.channels,
    );
  }

  Future<void> scheduleSystemUpdate({
    required String title,
    required String message,
    required DateTime scheduledDate,
  }) async {
    if (_settings == null || !_settings!.enabled) return;
    if (!_settings!.enabledTypes.contains(core.NotificationType.systemUpdate))
      return;

    await _deliveryService.scheduleNotification(
      title: title,
      message: message,
      userId: 'default_user', // TODO: Get from auth service
      recipient: '<EMAIL>', // TODO: Get from user profile
      type: core.NotificationType.systemUpdate,
      scheduledDate: scheduledDate,
      priority: core.NotificationPriority.low,
      channels: _settings!.channels,
    );
  }

  Future<void> scheduleBackupReminder({
    required DateTime scheduledDate,
  }) async {
    if (_settings == null || !_settings!.enabled) return;
    if (!_settings!.enabledTypes.contains(core.NotificationType.backupReminder))
      return;

    await _deliveryService.scheduleNotification(
      title: 'Backup Reminder',
      message:
          'Time to backup your trading data! Keep your records safe and secure.',
      userId: 'default_user', // TODO: Get from auth service
      recipient: '<EMAIL>', // TODO: Get from user profile
      type: core.NotificationType.backupReminder,
      scheduledDate: scheduledDate,
      priority: core.NotificationPriority.medium,
      channels: _settings!.channels,
    );
  }

  Future<void> scheduleTradeStats({
    required String title,
    required String message,
    required DateTime scheduledDate,
    Map<String, dynamic>? stats,
  }) async {
    if (_settings == null || !_settings!.enabled) return;
    if (!_settings!.enabledTypes.contains(core.NotificationType.tradeStats))
      return;

    await _deliveryService.scheduleNotification(
      title: title,
      message: message,
      userId: 'default_user', // TODO: Get from auth service
      recipient: '<EMAIL>', // TODO: Get from user profile
      type: core.NotificationType.tradeStats,
      scheduledDate: scheduledDate,
      priority: core.NotificationPriority.low,
      channels: _settings!.channels,
      data: stats,
    );
  }
}
