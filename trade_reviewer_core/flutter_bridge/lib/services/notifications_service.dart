import 'dart:async';
import 'dart:convert';
import '../models/notification.dart';
import 'storage_service.dart';

class NotificationsService {
  static const String _notificationsKey = 'trade_reviewer_notifications';
  final StorageService _storageService;
  List<Notification> _notifications = [];
  final _notificationsController =
      StreamController<List<Notification>>.broadcast();

  NotificationsService(this._storageService);

  Stream<List<Notification>> getActiveNotificationsStream() {
    return _notificationsController.stream.map((notifications) {
      return notifications
          .where((n) =>
              !n.dismissed && n.expiresAt?.isAfter(DateTime.now()) != false)
          .toList();
    });
  }

  Future<List<Notification>> initialize(String userId) async {
    try {
      final savedNotifications =
          await _storageService.get<List<dynamic>>(_notificationsKey);

      if (savedNotifications != null) {
        _notifications = savedNotifications
            .map((json) {
              try {
                if (json is Map<String, dynamic>) {
                  return Notification.fromJson(json);
                } else {
                  print('Invalid notification format: $json');
                  return null;
                }
              } catch (e) {
                print('Error deserializing notification: $e');
                return null;
              }
            })
            .whereType<Notification>()
            .where((n) => n.userId == userId)
            .toList();
      } else {
        _notifications = [];
      }

      // Ensure we're not keeping any expired notifications
      final now = DateTime.now();
      _notifications = _notifications.where((n) {
        if (n.expiresAt == null) return true;
        return n.expiresAt!.isAfter(now);
      }).toList();

      await _save(); // Save the cleaned up list
      _notificationsController.add(_notifications);
      return getActiveNotifications(userId);
    } catch (e) {
      print('Error initializing notifications: $e');
      _notifications = [];
      _notificationsController.add(_notifications);
      return [];
    }
  }

  Future<List<Notification>> getNotifications(String userId) async {
    return _notifications.where((n) => n.userId == userId).toList();
  }

  Future<List<Notification>> getActiveNotifications(String userId) async {
    return _notifications
        .where((n) => n.userId == userId && !n.dismissed)
        .toList();
  }

  Future<List<Notification>> getUnreadNotifications(String userId) async {
    return _notifications
        .where((n) => n.userId == userId && !n.read && !n.dismissed)
        .toList();
  }

  Future<void> addNotification({
    required String userId,
    required String title,
    required String message,
    required String recipient,
    required NotificationPriority priority,
    required NotificationType type,
    required List<NotificationChannel> channels,
    DateTime? expiresAt,
    Map<String, dynamic>? data,
  }) async {
    final notification = Notification(
      id: _generateId(),
      userId: userId,
      title: title,
      message: message,
      recipient: recipient,
      createdAt: DateTime.now(),
      priority: priority,
      type: type,
      channels: channels,
      expiresAt: expiresAt,
      data: data,
    );

    _notifications.add(notification);
    await _save();
    _notificationsController.add(_notifications);
  }

  Future<Notification?> dismiss(String notificationId, String userId) async {
    try {
      final index = _notifications.indexWhere(
        (n) => n.id == notificationId && n.userId == userId,
      );

      if (index == -1) return null;

      // Get the notification before removing it
      final notification = _notifications[index];

      // Remove the notification instead of just marking it as dismissed
      _notifications.removeAt(index);
      await _save();

      // Add to stream after successful save
      _notificationsController.add(List<Notification>.from(_notifications));
      return notification;
    } catch (e) {
      print('Error dismissing notification: $e');
      return null;
    }
  }

  Future<Notification?> markAsRead(String notificationId, String userId) async {
    try {
      final index = _notifications.indexWhere(
        (n) => n.id == notificationId && n.userId == userId && !n.dismissed,
      );

      if (index == -1) return null;

      final updated = _notifications[index].copyWith(
        read: true,
        updatedAt: DateTime.now(),
      );

      _notifications[index] = updated;
      await _save();

      // Add to stream after successful save
      _notificationsController.add(List<Notification>.from(_notifications));
      return updated;
    } catch (e) {
      print('Error marking notification as read: $e');
      return null;
    }
  }

  Future<List<Notification>> markAllAsRead(String userId) async {
    try {
      // Find all unread notifications for the user that aren't dismissed
      var unreadNotifications = _notifications
          .where((n) => n.userId == userId && !n.read && !n.dismissed)
          .toList();

      if (unreadNotifications.isEmpty) {
        return [];
      }

      // Update all unread notifications
      for (var notification in unreadNotifications) {
        final index = _notifications.indexWhere((n) => n.id == notification.id);
        if (index != -1) {
          _notifications[index] = notification.copyWith(
            read: true,
            updatedAt: DateTime.now(),
          );
        }
      }

      // Save changes and update stream
      await _save();
      _notificationsController.add(List<Notification>.from(_notifications));

      // Return the updated notifications
      return unreadNotifications
          .map((n) => n.copyWith(
                read: true,
                updatedAt: DateTime.now(),
              ))
          .toList();
    } catch (e) {
      print('Error marking all notifications as read: $e');
      return [];
    }
  }

  Future<void> dismissAll(String userId) async {
    try {
      // Remove all notifications for the user instead of marking them as dismissed
      _notifications.removeWhere((n) => n.userId == userId);

      await _save();
      // Add to stream after successful save
      _notificationsController.add(List<Notification>.from(_notifications));
    } catch (e) {
      print('Error dismissing all notifications: $e');
    }
  }

  Future<void> removeExpired() async {
    final now = DateTime.now();
    _notifications = _notifications.where((n) {
      if (n.expiresAt == null) return true;
      return n.expiresAt!.isAfter(now);
    }).toList();
    await _save();
    _notificationsController.add(_notifications);
  }

  Future<void> clear(String userId) async {
    _notifications = _notifications.where((n) => n.userId != userId).toList();
    await _save();
    _notificationsController.add(_notifications);
  }

  String _generateId() {
    return 'nt_${DateTime.now().millisecondsSinceEpoch}_${_notifications.length}';
  }

  Future<void> _save() async {
    try {
      final jsonList = _notifications.map((n) => n.toJson()).toList();
      final success = await _storageService.set(_notificationsKey, jsonList);
      if (!success) {
        print('Failed to save notifications');
      }
    } catch (e) {
      print('Error saving notifications: $e');
      rethrow;
    }
  }

  void dispose() {
    _notificationsController.close();
  }
}
