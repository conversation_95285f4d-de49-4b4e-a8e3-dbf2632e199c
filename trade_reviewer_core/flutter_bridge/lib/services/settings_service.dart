import 'package:shared_preferences/shared_preferences.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart';
import 'storage_service.dart';
import '../models/settings.dart';
import '../models/account_settings.dart';

class SettingsService {
  final StorageService _storage;
  Settings? _settings;

  SettingsService(StorageService storage) : _storage = storage;

  Future<void> initialize(String userId) async {
    try {
      _settings = Settings(
        userId: userId,
        tradeDefaults: const TradeDefaults(
          defaultRisk: 1.0,
          defaultPosition: 1.0,
          defaultTimeframe: ChartTimeframe.h1,
          riskCalculationType: RiskCalculationType.percentage,
          defaultCommission: 0.0,
          defaultFees: 0.0,
        ),
        display: const DisplaySettings(
          theme: 'system',
          pnlDisplayMode: PnLDisplayMode.currency,
          showRunningPnL: true,
          showEquityCurve: true,
          showTradeStatistics: true,
          compactMode: false,
          dateFormat: 'yyyy-MM-dd',
          timeFormat: 'HH:mm:ss',
          timezone: 'UTC',
        ),
        backtest: const BacktestSettings(
          initialCapital: 10000,
          useFixedPosition: false,
          maxRiskPerTrade: 2.0,
          maxOpenTrades: 5,
          includeFees: true,
          includeSlippage: true,
          slippageAmount: 0.1,
        ),
        goals: const GoalSettings(
          activeMetrics: {},
          metricGoals: {},
        ),
        accounts: const AccountSettings(
          tradingAccounts: [],
          apiKeys: [],
          autoSync: true,
          syncFrequency: 15,
        ),
        notifications: const NotificationSettings(
          enabled: true,
          soundEnabled: true,
          hapticEnabled: true,
          channels: [
            NotificationChannel.inApp,
            NotificationChannel.push,
            NotificationChannel.email,
          ],
          enabledTypes: [
            NotificationType.tradeAlert,
            NotificationType.priceAlert,
            NotificationType.goalAchieved,
            NotificationType.riskWarning,
            NotificationType.systemUpdate,
            NotificationType.backupReminder,
            NotificationType.journalReminder,
            NotificationType.tradeStats,
          ],
          quietHoursStart: '21:00',
          quietHoursEnd: '09:00',
          journalReminderFrequency: ReminderFrequency.daily,
          defaultPriority: NotificationPriority.medium,
        ),
        autoSync: true,
        autoBackup: true,
        backupFrequency: 24,
        updatedAt: DateTime.now(),
      );

      await _storage.saveSettings(_settings!);
    } catch (e) {
      print('Error initializing settings: $e');
      rethrow;
    }
  }

  Future<Settings> getSettings() async {
    if (_settings != null) {
      return _settings!;
    }

    final json = await _storage.getSettings();
    if (json == null) {
      throw StateError('Settings not initialized. Call initialize() first.');
    }

    _settings = Settings.fromJson(json);
    return _settings!;
  }

  Future<Settings> updateDisplaySettings(DisplaySettings display) async {
    try {
      final settings = await getSettings();
      _settings = settings.copyWith(
        display: display,
        updatedAt: DateTime.now(),
      );
      await _storage.saveSettings(_settings!);
      return _settings!;
    } catch (e) {
      print('Error updating display settings: $e');
      rethrow;
    }
  }

  Future<Settings> updateNotificationSettings(
      NotificationSettings notifications) async {
    try {
      final settings = await getSettings();
      _settings = settings.copyWith(
        notifications: notifications,
        updatedAt: DateTime.now(),
      );
      await _storage.saveSettings(_settings!);
      return _settings!;
    } catch (e) {
      print('Error updating notification settings: $e');
      rethrow;
    }
  }

  Future<Settings> updateTradeDefaults(TradeDefaults defaults) async {
    try {
      final settings = await getSettings();
      _settings = settings.copyWith(
        tradeDefaults: defaults,
        updatedAt: DateTime.now(),
      );
      await _storage.saveSettings(_settings!);
      return _settings!;
    } catch (e) {
      print('Error updating trade defaults: $e');
      rethrow;
    }
  }

  Future<Settings> updateBacktestSettings(BacktestSettings backtest) async {
    try {
      final settings = await getSettings();
      _settings = settings.copyWith(
        backtest: backtest,
        updatedAt: DateTime.now(),
      );
      await _storage.saveSettings(_settings!);
      return _settings!;
    } catch (e) {
      print('Error updating backtest settings: $e');
      rethrow;
    }
  }

  Future<Settings> updateAutoBackup(bool enabled) async {
    try {
      final settings = await getSettings();
      _settings = settings.copyWith(
        autoBackup: enabled,
        updatedAt: DateTime.now(),
      );
      await _storage.saveSettings(_settings!);
      return _settings!;
    } catch (e) {
      print('Error updating auto backup: $e');
      rethrow;
    }
  }

  Future<Settings> updateGoalSettings(GoalSettings goals) async {
    try {
      final settings = await getSettings();
      _settings = settings.copyWith(
        goals: goals,
        updatedAt: DateTime.now(),
      );
      await _storage.saveSettings(_settings!);
      return _settings!;
    } catch (e) {
      print('Error updating goal settings: $e');
      rethrow;
    }
  }

  Future<Settings> _createDefaultSettings(String userId) async {
    final defaultSettings = Settings(
      userId: userId,
      tradeDefaults: const TradeDefaults(
        defaultRisk: 1.0,
        defaultPosition: 100000,
        defaultTimeframe: ChartTimeframe.h1,
        riskCalculationType: RiskCalculationType.percentage,
        defaultCommission: 0.0,
        defaultFees: 0.0,
      ),
      display: const DisplaySettings(
        theme: 'system',
        pnlDisplayMode: PnLDisplayMode.currency,
        showRunningPnL: true,
        showEquityCurve: true,
        showTradeStatistics: true,
        compactMode: false,
        dateFormat: 'MM/dd/yyyy',
        timeFormat: 'HH:mm:ss',
        timezone: 'UTC',
      ),
      backtest: const BacktestSettings(
        initialCapital: 100000,
        useFixedPosition: false,
        maxRiskPerTrade: 1.0,
        maxOpenTrades: 1,
        includeFees: true,
        includeSlippage: true,
        slippageAmount: 0.1,
      ),
      goals: const GoalSettings(
        activeMetrics: {
          'winRate': true,
          'profitFactor': true,
          'averageRR': true,
          'maxDrawdown': true,
          'riskPerTrade': true,
          'maxOpenTrades': true,
          'tradesPerWeek': true,
          'journalEntries': true,
          'planAdherence': true,
        },
        metricGoals: {
          'winRate': 60.0,
          'profitFactor': 2.0,
          'averageRR': 2.0,
          'maxDrawdown': 10.0,
          'riskPerTrade': 1.0,
          'maxOpenTrades': 3.0,
          'tradesPerWeek': 10.0,
          'journalEntries': 5.0,
          'planAdherence': 90.0,
        },
      ),
      accounts: const AccountSettings(
        tradingAccounts: [],
        apiKeys: [],
        autoSync: true,
        syncFrequency: 15,
      ),
      notifications: const NotificationSettings(
        enabled: true,
        soundEnabled: true,
        hapticEnabled: true,
        channels: [
          NotificationChannel.inApp,
          NotificationChannel.push,
          NotificationChannel.email,
        ],
        enabledTypes: [
          NotificationType.tradeAlert,
          NotificationType.priceAlert,
          NotificationType.goalAchieved,
          NotificationType.riskWarning,
          NotificationType.systemUpdate,
          NotificationType.backupReminder,
          NotificationType.journalReminder,
          NotificationType.tradeStats,
        ],
        quietHoursStart: '21:00',
        quietHoursEnd: '09:00',
        journalReminderFrequency: ReminderFrequency.daily,
        defaultPriority: NotificationPriority.medium,
      ),
      autoSync: true,
      autoBackup: true,
      backupFrequency: 24,
      updatedAt: DateTime.now(),
    );

    _settings = defaultSettings;
    await _storage.saveSettings(_settings!);
    return _settings!;
  }

  Future<Settings> addTradingAccount(TradingAccount account) async {
    final settings = await getSettings();
    final updatedAccounts = settings.accounts.copyWith(
      tradingAccounts: [...settings.accounts.tradingAccounts, account],
    );
    return _updateSettings(settings.updateAccountSettings(updatedAccounts));
  }

  Future<Settings> updateTradingAccount(TradingAccount account) async {
    final settings = await getSettings();
    final updatedAccounts = settings.accounts.copyWith(
      tradingAccounts: settings.accounts.tradingAccounts.map((a) {
        return a.id == account.id ? account : a;
      }).toList(),
    );
    return _updateSettings(settings.updateAccountSettings(updatedAccounts));
  }

  Future<Settings> removeTradingAccount(String accountId) async {
    final settings = await getSettings();
    final updatedAccounts = settings.accounts.copyWith(
      tradingAccounts: settings.accounts.tradingAccounts
          .where((a) => a.id != accountId)
          .toList(),
    );
    return _updateSettings(settings.updateAccountSettings(updatedAccounts));
  }

  Future<Settings> addApiKey(ApiKey apiKey) async {
    final settings = await getSettings();
    final updatedAccounts = settings.accounts.copyWith(
      apiKeys: [...settings.accounts.apiKeys, apiKey],
    );
    return _updateSettings(settings.updateAccountSettings(updatedAccounts));
  }

  Future<Settings> updateApiKey(ApiKey apiKey) async {
    final settings = await getSettings();
    final updatedAccounts = settings.accounts.copyWith(
      apiKeys: settings.accounts.apiKeys.map((k) {
        return k.id == apiKey.id ? apiKey : k;
      }).toList(),
    );
    return _updateSettings(settings.updateAccountSettings(updatedAccounts));
  }

  Future<Settings> removeApiKey(String apiKeyId) async {
    final settings = await getSettings();
    final updatedAccounts = settings.accounts.copyWith(
      apiKeys:
          settings.accounts.apiKeys.where((k) => k.id != apiKeyId).toList(),
    );
    return _updateSettings(settings.updateAccountSettings(updatedAccounts));
  }

  Future<Settings> updateAccountSync(bool autoSync, int syncFrequency) async {
    final settings = await getSettings();
    final updatedAccounts = settings.accounts.copyWith(
      autoSync: autoSync,
      syncFrequency: syncFrequency,
    );
    return _updateSettings(settings.updateAccountSettings(updatedAccounts));
  }

  Future<Settings> _updateSettings(Settings settings) async {
    await _storage.saveSettings(settings);
    _settings = settings;
    return settings;
  }
}
