import 'package:trade_reviewer_core/models/user.dart';
import 'package:trade_reviewer_core/services/storage_service.dart';

class ProfileService {
  static const String _profileKey = 'trade_reviewer_profiles';
  final StorageService _storage;

  ProfileService(this._storage);

  Future<User> initialize(String userId) async {
    try {
      print('Initializing profile for user: $userId');

      // Try to get existing profile
      final profile = await getProfile(userId);
      if (profile != null) {
        print('Found existing profile');
        return profile;
      }

      print('Creating default profile');
      // Create default profile if none exists
      final defaultProfile = User(
        id: userId,
        email: '',
        name: '',
        role: UserRole.user,
        preferences: const UserPreferences(
          language: 'en',
          timezone: 'UTC',
          darkMode: false,
          defaultCurrency: 'USD',
          showPnLInPercent: true,
          autoSyncEnabled: true,
          // Privacy settings with secure defaults
          profilePublic: false,
          showTradeHistory: false,
          showProfitLoss: false,
          showPortfolioValue: false,
          allowDataCollection: true,
          allowCrashReports: true,
          allowAnalytics: true,
        ),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isEmailVerified: false,
      );

      print('Saving default profile');
      await _storage.saveProfile(defaultProfile);

      // Verify the save
      final savedProfile = await getProfile(userId);
      if (savedProfile == null) {
        print('ERROR: Failed to verify saved profile');
        throw Exception('Failed to verify saved profile');
      }

      print('Profile initialized successfully');
      return savedProfile;
    } catch (e, stackTrace) {
      print('Error initializing profile: $e');
      print('Stack trace: $stackTrace');
      rethrow;
    }
  }

  Future<User?> getProfile(String userId) async {
    try {
      print('Getting profile for user: $userId');

      // Verify storage access first
      final storageAccessible = await _storage.verifyStorageAccess();
      if (!storageAccessible) {
        print('ERROR: Storage access verification failed');
        throw Exception('Cannot access storage system');
      }
      print('Storage access verified successfully');

      return await _storage.getProfile(userId);
    } catch (e, stackTrace) {
      print('Error getting profile: $e');
      print('Stack trace: $stackTrace');
      return null;
    }
  }

  Future<User?> updateProfile(
      String userId, Map<String, dynamic> updates) async {
    try {
      print('Starting profile update for user: $userId');
      print('Updates to apply: $updates');

      // Get current profile or initialize
      var currentProfile = await getProfile(userId);
      if (currentProfile == null) {
        print('No profile found, initializing...');
        currentProfile = await initialize(userId);
      }

      // Create updated profile
      final updatedProfile = User(
        id: userId,
        email: updates['email'] as String? ?? currentProfile.email,
        name: updates['name'] as String? ?? currentProfile.name,
        role: currentProfile.role,
        preferences: updates['preferences'] as UserPreferences? ??
            currentProfile.preferences,
        createdAt: currentProfile.createdAt,
        updatedAt: DateTime.now(),
        lastLoginAt: currentProfile.lastLoginAt,
        isEmailVerified: updates['isEmailVerified'] as bool? ??
            currentProfile.isEmailVerified,
        profileImageUrl: updates['profileImageUrl'] as String? ??
            currentProfile.profileImageUrl,
      );

      print('Updated profile data: ${updatedProfile.toJson()}');

      // Save the profile
      await _storage.saveProfile(updatedProfile);

      // Verify the update
      final verifiedProfile = await _storage.getProfile(userId);
      if (verifiedProfile == null) {
        throw Exception('Failed to verify profile update - profile not found');
      }

      // Verify specific fields that were updated
      for (final key in updates.keys) {
        final updatedValue = updates[key];
        final verifiedValue = verifiedProfile.toJson()[key];
        if (updatedValue != null &&
            updatedValue.toString() != verifiedValue.toString()) {
          print('Field verification failed for $key:');
          print('Expected: $updatedValue');
          print('Got: $verifiedValue');
          throw Exception('Profile update verification failed for field: $key');
        }
      }

      print('Profile updated and verified successfully');
      return verifiedProfile;
    } catch (e) {
      print('Error updating profile: $e');
      print('Stack trace: ${StackTrace.current}');
      rethrow;
    }
  }

  Future<User?> updatePreferences(
      String userId, Map<String, dynamic> preferences) async {
    try {
      final profile = await getProfile(userId);
      if (profile == null) return null;

      final updatedPreferences = profile.preferences.copyWith(
        notificationPreference: preferences['notificationPreference'] ??
            profile.preferences.notificationPreference,
        darkMode: preferences['darkMode'] ?? profile.preferences.darkMode,
        defaultCurrency: preferences['defaultCurrency'] ??
            profile.preferences.defaultCurrency,
        timezone: preferences['timezone'] ?? profile.preferences.timezone,
        language: preferences['language'] ?? profile.preferences.language,
        showPnLInPercent: preferences['showPnLInPercent'] ??
            profile.preferences.showPnLInPercent,
        autoSyncEnabled: preferences['autoSyncEnabled'] ??
            profile.preferences.autoSyncEnabled,
      );

      return updateProfile(userId, {'preferences': updatedPreferences});
    } catch (e) {
      print('Error updating preferences: $e');
      return null;
    }
  }

  Future<User?> verifyEmail(String userId) async {
    try {
      final profile = await getProfile(userId);
      if (profile == null) return null;

      return updateProfile(userId, {'isEmailVerified': true});
    } catch (e) {
      print('Error verifying email: $e');
      return null;
    }
  }

  Future<User?> updateLastLogin(String userId) async {
    try {
      final profile = await getProfile(userId);
      if (profile == null) return null;

      return updateProfile(userId, {'lastLoginAt': DateTime.now()});
    } catch (e) {
      print('Error updating last login: $e');
      return null;
    }
  }

  Future<void> clearProfile() async {
    try {
      await _storage.clear();
    } catch (e) {
      print('Error clearing profile: $e');
    }
  }
}
