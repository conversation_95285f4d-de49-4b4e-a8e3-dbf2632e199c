import '../models/goal.dart';
import '../models/trade.dart';
import 'storage_service.dart';

class GoalsService {
  static const String _goalsKey = 'trade_reviewer_goals';
  final StorageService _storageService;
  List<Goal> _goals = [];

  GoalsService(this._storageService);

  Future<List<Goal>> initialize(String userId) async {
    final savedGoals = await _storageService.get<List<dynamic>>(_goalsKey);

    if (savedGoals != null) {
      _goals = savedGoals
          .map((json) => Goal.fromJson(json as Map<String, dynamic>))
          .where((g) => g.userId == userId)
          .toList();
    }

    return _goals;
  }

  Future<List<Goal>> getGoals(String userId) async {
    return _goals.where((g) => g.userId == userId).toList();
  }

  Future<List<Goal>> getActiveGoals(String userId) async {
    return _goals.where((g) => g.userId == userId && g.isActive).toList();
  }

  Future<Goal?> getGoalById(String goalId, String userId) async {
    return _goals.firstWhere(
      (g) => g.id == goalId && g.userId == userId,
      orElse: () => throw StateError('Goal not found'),
    );
  }

  Future<Goal> addGoal(Goal goal) async {
    _goals.add(goal);
    await _save();
    return goal;
  }

  Future<Goal?> updateGoal(String goalId, String userId, Goal updates) async {
    final index =
        _goals.indexWhere((g) => g.id == goalId && g.userId == userId);

    if (index != -1) {
      final goal = _goals[index];
      final updated = goal.copyWith(
        name: updates.name,
        type: updates.type,
        period: updates.period,
        target: updates.target,
        startDate: updates.startDate,
        endDate: updates.endDate,
        isActive: updates.isActive,
        description: updates.description,
        updatedAt: DateTime.now(),
      );
      _goals[index] = updated;
      await _save();
      return updated;
    }

    return null;
  }

  Future<void> deleteGoal(String goalId, String userId) async {
    _goals.removeWhere((g) => g.id == goalId && g.userId == userId);
    await _save();
  }

  Future<List<Goal>> updateProgress(List<Trade> trades, String userId) async {
    final activeGoals = await getActiveGoals(userId);
    final now = DateTime.now();

    for (final goal in activeGoals) {
      final relevantTrades = trades.where((trade) {
        if (trade.exitDate == null) return false;

        // Check if trade falls within goal period
        switch (goal.period) {
          case GoalPeriod.daily:
            return _isSameDay(trade.exitDate!, now);
          case GoalPeriod.weekly:
            return _isSameWeek(trade.exitDate!, now);
          case GoalPeriod.monthly:
            return _isSameMonth(trade.exitDate!, now);
          case GoalPeriod.yearly:
            return _isSameYear(trade.exitDate!, now);
          case GoalPeriod.allTime:
            return true;
        }
      }).toList();

      // Calculate progress based on goal type
      double progress = 0;
      switch (goal.type) {
        case GoalType.profit:
          progress = relevantTrades.fold(
              0, (sum, trade) => sum + trade.calculatePnL());
          break;
        case GoalType.winRate:
          final totalTrades = relevantTrades.length;
          if (totalTrades > 0) {
            final winningTrades =
                relevantTrades.where((t) => t.calculatePnL() > 0).length;
            progress = (winningTrades / totalTrades) * 100;
          }
          break;
        case GoalType.tradeCount:
          progress = relevantTrades.length.toDouble();
          break;
        case GoalType.profitFactor:
          final wins = relevantTrades.fold(0.0, (sum, trade) {
            final pnl = trade.calculatePnL();
            return sum + (pnl > 0 ? pnl : 0);
          });
          final losses = relevantTrades.fold(0.0, (sum, trade) {
            final pnl = trade.calculatePnL();
            return sum + (pnl < 0 ? pnl.abs() : 0);
          });
          progress = losses > 0
              ? wins / losses
              : wins > 0
                  ? double.infinity
                  : 0;
          break;
        case GoalType.averageRrr:
          final validTrades =
              relevantTrades.where((t) => t.calculateRRR() != null).toList();
          if (validTrades.isNotEmpty) {
            final totalRRR = validTrades.fold(
                0.0, (sum, trade) => sum + (trade.calculateRRR() ?? 0));
            progress = totalRRR / validTrades.length;
          }
          break;
      }

      final index = _goals.indexOf(goal);
      _goals[index] = goal.copyWith(
        progress: progress,
        updatedAt: now,
      );
    }

    await _save();
    return activeGoals;
  }

  Future<void> clear(String userId) async {
    _goals.removeWhere((g) => g.userId == userId);
    await _save();
  }

  Future<void> _save() async {
    await _storageService.set(
      _goalsKey,
      _goals.map((g) => g.toJson()).toList(),
    );
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  bool _isSameWeek(DateTime date1, DateTime date2) {
    final d1 = DateTime(date1.year, date1.month, date1.day);
    final d2 = DateTime(date2.year, date2.month, date2.day);
    final diff = d2.difference(d1).inDays;
    final day1 = date1.weekday;
    final day2 = date2.weekday;
    return (diff + day1 - day2).abs() < 7;
  }

  bool _isSameMonth(DateTime date1, DateTime date2) {
    return date1.year == date2.year && date1.month == date2.month;
  }

  bool _isSameYear(DateTime date1, DateTime date2) {
    return date1.year == date2.year;
  }
}
