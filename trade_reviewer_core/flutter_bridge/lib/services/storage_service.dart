import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:trade_reviewer_core/trade_reviewer_core.dart' show User, Trade;

abstract class StorageService {
  Future<Map<String, dynamic>?> getSettings();
  Future<void> saveSettings(dynamic settings);
  Future<void> clearSettings();
  Future<T?> get<T>(String key);
  Future<bool> set<T>(String key, T value);
  Future<void> remove(String key);
  Future<User?> getProfile(String userId);
  Future<void> saveProfile(User profile);
  Future<void> updateProfile(User profile);
  Future<void> deleteProfile(String userId);
  Future<bool> verifyStorageAccess();
  Future<void> clear();
  Future<List<Trade>> getTrades();
  Future<void> saveTrade(Trade trade);
  Future<void> updateTrade(Trade trade);
  Future<void> deleteTrade(String tradeId);
}

class FlutterStorageService implements StorageService {
  static const String _settingsKey = 'trade_reviewer_settings';
  static const String _profilesKey = 'trade_reviewer_profiles';
  static const String _tradesKey = 'trade_reviewer_trades';
  final SharedPreferences _prefs;

  FlutterStorageService(this._prefs);

  @override
  Future<void> clear() async {
    try {
      await _prefs.clear();
    } catch (e) {
      print('Error clearing storage: $e');
      rethrow;
    }
  }

  @override
  Future<bool> verifyStorageAccess() async {
    try {
      final testKey = 'storage_test_key';
      final testValue = 'test_value_${DateTime.now().millisecondsSinceEpoch}';

      // Try to write
      final writeSuccess = await _prefs.setString(testKey, testValue);
      if (!writeSuccess) {
        print('ERROR: Failed to write test value');
        return false;
      }

      // Try to read
      final readValue = _prefs.getString(testKey);
      if (readValue != testValue) {
        print('ERROR: Read value does not match written value');
        return false;
      }

      // Try to remove
      final removeSuccess = await _prefs.remove(testKey);
      if (!removeSuccess) {
        print('WARNING: Failed to remove test key');
      }

      return true;
    } catch (e) {
      print('Error verifying storage access: $e');
      return false;
    }
  }

  @override
  Future<Map<String, dynamic>?> getSettings() async {
    final jsonStr = _prefs.getString(_settingsKey);
    if (jsonStr == null) {
      return null;
    }
    return json.decode(jsonStr) as Map<String, dynamic>;
  }

  @override
  Future<void> saveSettings(dynamic settings) async {
    final jsonStr = json.encode(settings.toJson());
    await _prefs.setString(_settingsKey, jsonStr);
  }

  @override
  Future<void> clearSettings() async {
    await _prefs.remove(_settingsKey);
  }

  @override
  Future<T?> get<T>(String key) async {
    final value = _prefs.getString(key);
    if (value == null) return null;
    return json.decode(value) as T;
  }

  @override
  Future<bool> set<T>(String key, T value) async {
    final jsonStr = json.encode(value);
    return await _prefs.setString(key, jsonStr);
  }

  @override
  Future<void> remove(String key) async {
    await _prefs.remove(key);
  }

  @override
  Future<User?> getProfile(String userId) async {
    try {
      final profiles = await _getProfiles();
      return profiles.firstWhere((p) => p.id == userId);
    } catch (e) {
      print('Error getting profile: $e');
      return null;
    }
  }

  @override
  Future<void> saveProfile(User profile) async {
    try {
      final profiles = await _getProfiles();
      final index = profiles.indexWhere((p) => p.id == profile.id);
      if (index != -1) {
        profiles[index] = profile;
      } else {
        profiles.add(profile);
      }
      await _saveProfilesToStorage(profiles);
    } catch (e) {
      print('Error saving profile: $e');
      rethrow;
    }
  }

  @override
  Future<void> updateProfile(User profile) async {
    await saveProfile(profile);
  }

  @override
  Future<void> deleteProfile(String userId) async {
    try {
      final profiles = await _getProfiles();
      profiles.removeWhere((p) => p.id == userId);
      await _saveProfilesToStorage(profiles);
    } catch (e) {
      print('Error deleting profile: $e');
      rethrow;
    }
  }

  @override
  Future<List<Trade>> getTrades() async {
    try {
      final String? tradesJson = _prefs.getString(_tradesKey);
      if (tradesJson == null) return [];

      final List<dynamic> tradesList = json.decode(tradesJson);
      return tradesList
          .map((json) => Trade.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      print('Error getting trades: $e');
      return [];
    }
  }

  @override
  Future<void> saveTrade(Trade trade) async {
    try {
      final trades = await getTrades();
      trades.add(trade);
      await _saveTradesToStorage(trades);
    } catch (e) {
      print('Error saving trade: $e');
      rethrow;
    }
  }

  @override
  Future<void> updateTrade(Trade trade) async {
    try {
      final trades = await getTrades();
      final index = trades.indexWhere((t) => t.id == trade.id);
      if (index != -1) {
        trades[index] = trade;
        await _saveTradesToStorage(trades);
      }
    } catch (e) {
      print('Error updating trade: $e');
      rethrow;
    }
  }

  @override
  Future<void> deleteTrade(String tradeId) async {
    try {
      final trades = await getTrades();
      trades.removeWhere((t) => t.id == tradeId);
      await _saveTradesToStorage(trades);
    } catch (e) {
      print('Error deleting trade: $e');
      rethrow;
    }
  }

  Future<List<User>> _getProfiles() async {
    try {
      final String? profilesJson = _prefs.getString(_profilesKey);
      if (profilesJson == null) return [];

      final List<dynamic> profilesList = json.decode(profilesJson);
      return profilesList
          .map((json) => User.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      print('Error getting profiles: $e');
      return [];
    }
  }

  Future<void> _saveProfilesToStorage(List<User> profiles) async {
    try {
      final profilesJson =
          json.encode(profiles.map((p) => p.toJson()).toList());
      final success = await _prefs.setString(_profilesKey, profilesJson);
      if (!success) {
        throw Exception('Failed to save profiles to SharedPreferences');
      }
    } catch (e) {
      print('Error saving profiles to storage: $e');
      rethrow;
    }
  }

  Future<void> _saveTradesToStorage(List<Trade> trades) async {
    try {
      final tradesJson = json.encode(trades.map((t) => t.toJson()).toList());
      final success = await _prefs.setString(_tradesKey, tradesJson);
      if (!success) {
        throw Exception('Failed to save trades to SharedPreferences');
      }
    } catch (e) {
      print('Error saving trades to storage: $e');
      rethrow;
    }
  }
}
