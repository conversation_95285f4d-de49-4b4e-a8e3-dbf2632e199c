import 'package:freezed_annotation/freezed_annotation.dart';

part 'account_settings.freezed.dart';
part 'account_settings.g.dart';

enum AccountStatus {
  @JsonValue('CONNECTED')
  CONNECTED,
  @JsonValue('DISCONNECTED')
  DISCONNECTED,
  @JsonValue('PENDING')
  PENDING,
  @JsonValue('ERROR')
  ERROR,
}

enum ApiKeyStatus {
  @JsonValue('ACTIVE')
  ACTIVE,
  @JsonValue('INACTIVE')
  INACTIVE,
  @JsonValue('EXPIRED')
  EXPIRED,
}

@freezed
class TradingAccount with _$TradingAccount {
  const factory TradingAccount({
    required String id,
    required String name,
    required String provider,
    required AccountStatus status,
    required DateTime lastSynced,
    required Map<String, String> credentials,
    required double initialCapital,
    @Default(true) bool isActive,
  }) = _TradingAccount;

  factory TradingAccount.fromJson(Map<String, dynamic> json) =>
      _$TradingAccountFromJson(json);
}

@freezed
class ApiKey with _$ApiKey {
  const factory ApiKey({
    required String id,
    required String name,
    required String key,
    String? secret,
    required ApiKeyStatus status,
    DateTime? expiryDate,
  }) = _ApiKey;

  factory ApiKey.fromJson(Map<String, dynamic> json) => _$ApiKeyFromJson(json);
}

@freezed
class AccountSettings with _$AccountSettings {
  const factory AccountSettings({
    required List<TradingAccount> tradingAccounts,
    required List<ApiKey> apiKeys,
    required bool autoSync,
    required int syncFrequency,
  }) = _AccountSettings;

  factory AccountSettings.fromJson(Map<String, dynamic> json) =>
      _$AccountSettingsFromJson(json);
}
