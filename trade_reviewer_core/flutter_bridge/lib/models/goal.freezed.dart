// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'goal.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Goal _$GoalFromJson(Map<String, dynamic> json) {
  return _Goal.fromJson(json);
}

/// @nodoc
mixin _$Goal {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  GoalType get type => throw _privateConstructorUsedError;
  GoalPeriod get period => throw _privateConstructorUsedError;
  double get target => throw _privateConstructorUsedError;
  double get progress => throw _privateConstructorUsedError;
  DateTime get startDate => throw _privateConstructorUsedError;
  DateTime? get endDate => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this Goal to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Goal
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GoalCopyWith<Goal> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GoalCopyWith<$Res> {
  factory $GoalCopyWith(Goal value, $Res Function(Goal) then) =
      _$GoalCopyWithImpl<$Res, Goal>;
  @useResult
  $Res call(
      {String id,
      String userId,
      String name,
      GoalType type,
      GoalPeriod period,
      double target,
      double progress,
      DateTime startDate,
      DateTime? endDate,
      bool isActive,
      String? description,
      DateTime createdAt,
      DateTime updatedAt});
}

/// @nodoc
class _$GoalCopyWithImpl<$Res, $Val extends Goal>
    implements $GoalCopyWith<$Res> {
  _$GoalCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Goal
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? name = null,
    Object? type = null,
    Object? period = null,
    Object? target = null,
    Object? progress = null,
    Object? startDate = null,
    Object? endDate = freezed,
    Object? isActive = null,
    Object? description = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as GoalType,
      period: null == period
          ? _value.period
          : period // ignore: cast_nullable_to_non_nullable
              as GoalPeriod,
      target: null == target
          ? _value.target
          : target // ignore: cast_nullable_to_non_nullable
              as double,
      progress: null == progress
          ? _value.progress
          : progress // ignore: cast_nullable_to_non_nullable
              as double,
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GoalImplCopyWith<$Res> implements $GoalCopyWith<$Res> {
  factory _$$GoalImplCopyWith(
          _$GoalImpl value, $Res Function(_$GoalImpl) then) =
      __$$GoalImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String name,
      GoalType type,
      GoalPeriod period,
      double target,
      double progress,
      DateTime startDate,
      DateTime? endDate,
      bool isActive,
      String? description,
      DateTime createdAt,
      DateTime updatedAt});
}

/// @nodoc
class __$$GoalImplCopyWithImpl<$Res>
    extends _$GoalCopyWithImpl<$Res, _$GoalImpl>
    implements _$$GoalImplCopyWith<$Res> {
  __$$GoalImplCopyWithImpl(_$GoalImpl _value, $Res Function(_$GoalImpl) _then)
      : super(_value, _then);

  /// Create a copy of Goal
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? name = null,
    Object? type = null,
    Object? period = null,
    Object? target = null,
    Object? progress = null,
    Object? startDate = null,
    Object? endDate = freezed,
    Object? isActive = null,
    Object? description = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_$GoalImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as GoalType,
      period: null == period
          ? _value.period
          : period // ignore: cast_nullable_to_non_nullable
              as GoalPeriod,
      target: null == target
          ? _value.target
          : target // ignore: cast_nullable_to_non_nullable
              as double,
      progress: null == progress
          ? _value.progress
          : progress // ignore: cast_nullable_to_non_nullable
              as double,
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GoalImpl extends _Goal {
  const _$GoalImpl(
      {required this.id,
      required this.userId,
      required this.name,
      required this.type,
      required this.period,
      required this.target,
      required this.progress,
      required this.startDate,
      this.endDate,
      required this.isActive,
      this.description,
      required this.createdAt,
      required this.updatedAt})
      : super._();

  factory _$GoalImpl.fromJson(Map<String, dynamic> json) =>
      _$$GoalImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String name;
  @override
  final GoalType type;
  @override
  final GoalPeriod period;
  @override
  final double target;
  @override
  final double progress;
  @override
  final DateTime startDate;
  @override
  final DateTime? endDate;
  @override
  final bool isActive;
  @override
  final String? description;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;

  @override
  String toString() {
    return 'Goal(id: $id, userId: $userId, name: $name, type: $type, period: $period, target: $target, progress: $progress, startDate: $startDate, endDate: $endDate, isActive: $isActive, description: $description, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GoalImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.period, period) || other.period == period) &&
            (identical(other.target, target) || other.target == target) &&
            (identical(other.progress, progress) ||
                other.progress == progress) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      name,
      type,
      period,
      target,
      progress,
      startDate,
      endDate,
      isActive,
      description,
      createdAt,
      updatedAt);

  /// Create a copy of Goal
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GoalImplCopyWith<_$GoalImpl> get copyWith =>
      __$$GoalImplCopyWithImpl<_$GoalImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GoalImplToJson(
      this,
    );
  }
}

abstract class _Goal extends Goal {
  const factory _Goal(
      {required final String id,
      required final String userId,
      required final String name,
      required final GoalType type,
      required final GoalPeriod period,
      required final double target,
      required final double progress,
      required final DateTime startDate,
      final DateTime? endDate,
      required final bool isActive,
      final String? description,
      required final DateTime createdAt,
      required final DateTime updatedAt}) = _$GoalImpl;
  const _Goal._() : super._();

  factory _Goal.fromJson(Map<String, dynamic> json) = _$GoalImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get name;
  @override
  GoalType get type;
  @override
  GoalPeriod get period;
  @override
  double get target;
  @override
  double get progress;
  @override
  DateTime get startDate;
  @override
  DateTime? get endDate;
  @override
  bool get isActive;
  @override
  String? get description;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;

  /// Create a copy of Goal
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GoalImplCopyWith<_$GoalImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$GoalMetrics {
  double get currentValue => throw _privateConstructorUsedError;
  double get progress => throw _privateConstructorUsedError;
  double get remainingValue => throw _privateConstructorUsedError;
  int get daysRemaining => throw _privateConstructorUsedError;

  /// Create a copy of GoalMetrics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GoalMetricsCopyWith<GoalMetrics> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GoalMetricsCopyWith<$Res> {
  factory $GoalMetricsCopyWith(
          GoalMetrics value, $Res Function(GoalMetrics) then) =
      _$GoalMetricsCopyWithImpl<$Res, GoalMetrics>;
  @useResult
  $Res call(
      {double currentValue,
      double progress,
      double remainingValue,
      int daysRemaining});
}

/// @nodoc
class _$GoalMetricsCopyWithImpl<$Res, $Val extends GoalMetrics>
    implements $GoalMetricsCopyWith<$Res> {
  _$GoalMetricsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of GoalMetrics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentValue = null,
    Object? progress = null,
    Object? remainingValue = null,
    Object? daysRemaining = null,
  }) {
    return _then(_value.copyWith(
      currentValue: null == currentValue
          ? _value.currentValue
          : currentValue // ignore: cast_nullable_to_non_nullable
              as double,
      progress: null == progress
          ? _value.progress
          : progress // ignore: cast_nullable_to_non_nullable
              as double,
      remainingValue: null == remainingValue
          ? _value.remainingValue
          : remainingValue // ignore: cast_nullable_to_non_nullable
              as double,
      daysRemaining: null == daysRemaining
          ? _value.daysRemaining
          : daysRemaining // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GoalMetricsImplCopyWith<$Res>
    implements $GoalMetricsCopyWith<$Res> {
  factory _$$GoalMetricsImplCopyWith(
          _$GoalMetricsImpl value, $Res Function(_$GoalMetricsImpl) then) =
      __$$GoalMetricsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double currentValue,
      double progress,
      double remainingValue,
      int daysRemaining});
}

/// @nodoc
class __$$GoalMetricsImplCopyWithImpl<$Res>
    extends _$GoalMetricsCopyWithImpl<$Res, _$GoalMetricsImpl>
    implements _$$GoalMetricsImplCopyWith<$Res> {
  __$$GoalMetricsImplCopyWithImpl(
      _$GoalMetricsImpl _value, $Res Function(_$GoalMetricsImpl) _then)
      : super(_value, _then);

  /// Create a copy of GoalMetrics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentValue = null,
    Object? progress = null,
    Object? remainingValue = null,
    Object? daysRemaining = null,
  }) {
    return _then(_$GoalMetricsImpl(
      currentValue: null == currentValue
          ? _value.currentValue
          : currentValue // ignore: cast_nullable_to_non_nullable
              as double,
      progress: null == progress
          ? _value.progress
          : progress // ignore: cast_nullable_to_non_nullable
              as double,
      remainingValue: null == remainingValue
          ? _value.remainingValue
          : remainingValue // ignore: cast_nullable_to_non_nullable
              as double,
      daysRemaining: null == daysRemaining
          ? _value.daysRemaining
          : daysRemaining // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$GoalMetricsImpl extends _GoalMetrics {
  const _$GoalMetricsImpl(
      {this.currentValue = 0,
      this.progress = 0,
      this.remainingValue = 0,
      this.daysRemaining = 0})
      : super._();

  @override
  @JsonKey()
  final double currentValue;
  @override
  @JsonKey()
  final double progress;
  @override
  @JsonKey()
  final double remainingValue;
  @override
  @JsonKey()
  final int daysRemaining;

  @override
  String toString() {
    return 'GoalMetrics(currentValue: $currentValue, progress: $progress, remainingValue: $remainingValue, daysRemaining: $daysRemaining)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GoalMetricsImpl &&
            (identical(other.currentValue, currentValue) ||
                other.currentValue == currentValue) &&
            (identical(other.progress, progress) ||
                other.progress == progress) &&
            (identical(other.remainingValue, remainingValue) ||
                other.remainingValue == remainingValue) &&
            (identical(other.daysRemaining, daysRemaining) ||
                other.daysRemaining == daysRemaining));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, currentValue, progress, remainingValue, daysRemaining);

  /// Create a copy of GoalMetrics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GoalMetricsImplCopyWith<_$GoalMetricsImpl> get copyWith =>
      __$$GoalMetricsImplCopyWithImpl<_$GoalMetricsImpl>(this, _$identity);
}

abstract class _GoalMetrics extends GoalMetrics {
  const factory _GoalMetrics(
      {final double currentValue,
      final double progress,
      final double remainingValue,
      final int daysRemaining}) = _$GoalMetricsImpl;
  const _GoalMetrics._() : super._();

  @override
  double get currentValue;
  @override
  double get progress;
  @override
  double get remainingValue;
  @override
  int get daysRemaining;

  /// Create a copy of GoalMetrics
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GoalMetricsImplCopyWith<_$GoalMetricsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
