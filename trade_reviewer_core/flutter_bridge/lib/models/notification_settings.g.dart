// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_settings.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$NotificationSettingsImpl _$$NotificationSettingsImplFromJson(
        Map<String, dynamic> json) =>
    _$NotificationSettingsImpl(
      enabled: json['enabled'] as bool? ?? true,
      soundEnabled: json['soundEnabled'] as bool? ?? true,
      hapticEnabled: json['hapticEnabled'] as bool? ?? true,
      channels: (json['channels'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$NotificationChannelEnumMap, e))
              .toList() ??
          const [
            NotificationChannel.inApp,
            NotificationChannel.push,
            NotificationChannel.email
          ],
      enabledTypes: (json['enabledTypes'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$NotificationTypeEnumMap, e))
              .toList() ??
          const [
            NotificationType.tradeAlert,
            NotificationType.priceAlert,
            NotificationType.goalAchieved,
            NotificationType.riskWarning,
            NotificationType.systemUpdate,
            NotificationType.backupReminder,
            NotificationType.journalReminder,
            NotificationType.tradeStats
          ],
      quietHoursStart: json['quietHoursStart'] as String? ?? "21:00",
      quietHoursEnd: json['quietHoursEnd'] as String? ?? "09:00",
      journalReminderFrequency: $enumDecodeNullable(
              _$ReminderFrequencyEnumMap, json['journalReminderFrequency']) ??
          ReminderFrequency.daily,
      defaultPriority: $enumDecodeNullable(
              _$NotificationPriorityEnumMap, json['defaultPriority']) ??
          NotificationPriority.medium,
    );

Map<String, dynamic> _$$NotificationSettingsImplToJson(
        _$NotificationSettingsImpl instance) =>
    <String, dynamic>{
      'enabled': instance.enabled,
      'soundEnabled': instance.soundEnabled,
      'hapticEnabled': instance.hapticEnabled,
      'channels': instance.channels
          .map((e) => _$NotificationChannelEnumMap[e]!)
          .toList(),
      'enabledTypes': instance.enabledTypes
          .map((e) => _$NotificationTypeEnumMap[e]!)
          .toList(),
      'quietHoursStart': instance.quietHoursStart,
      'quietHoursEnd': instance.quietHoursEnd,
      'journalReminderFrequency':
          _$ReminderFrequencyEnumMap[instance.journalReminderFrequency]!,
      'defaultPriority':
          _$NotificationPriorityEnumMap[instance.defaultPriority]!,
    };

const _$NotificationChannelEnumMap = {
  NotificationChannel.inApp: 'IN_APP',
  NotificationChannel.email: 'EMAIL',
  NotificationChannel.push: 'PUSH',
  NotificationChannel.sms: 'SMS',
};

const _$NotificationTypeEnumMap = {
  NotificationType.tradeAlert: 'TRADE_ALERT',
  NotificationType.priceAlert: 'PRICE_ALERT',
  NotificationType.goalAchieved: 'GOAL_ACHIEVED',
  NotificationType.riskWarning: 'RISK_WARNING',
  NotificationType.systemUpdate: 'SYSTEM_UPDATE',
  NotificationType.backupReminder: 'BACKUP_REMINDER',
  NotificationType.journalReminder: 'JOURNAL_REMINDER',
  NotificationType.tradeStats: 'TRADE_STATS',
};

const _$ReminderFrequencyEnumMap = {
  ReminderFrequency.daily: 'daily',
  ReminderFrequency.weekly: 'weekly',
  ReminderFrequency.monthly: 'monthly',
};

const _$NotificationPriorityEnumMap = {
  NotificationPriority.low: 'LOW',
  NotificationPriority.medium: 'MEDIUM',
  NotificationPriority.high: 'HIGH',
  NotificationPriority.urgent: 'URGENT',
};
