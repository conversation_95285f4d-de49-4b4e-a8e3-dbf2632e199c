// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'analytics.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TradeMetrics _$TradeMetricsFromJson(Map<String, dynamic> json) {
  return _TradeMetrics.fromJson(json);
}

/// @nodoc
mixin _$TradeMetrics {
  int get totalTrades => throw _privateConstructorUsedError;
  int get winningTrades => throw _privateConstructorUsedError;
  int get losingTrades => throw _privateConstructorUsedError;
  int get breakEvenTrades => throw _privateConstructorUsedError;
  double get winRate => throw _privateConstructorUsedError;
  double get profitFactor => throw _privateConstructorUsedError;
  double get averageWin => throw _privateConstructorUsedError;
  double get averageLoss => throw _privateConstructorUsedError;
  double get largestWin => throw _privateConstructorUsedError;
  double get largestLoss => throw _privateConstructorUsedError;
  double get totalPnL => throw _privateConstructorUsedError;
  double get netPnL => throw _privateConstructorUsedError;
  double get grossPnL => throw _privateConstructorUsedError;
  double get totalCommissions => throw _privateConstructorUsedError;
  double get totalFees => throw _privateConstructorUsedError;

  /// Serializes this TradeMetrics to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TradeMetrics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TradeMetricsCopyWith<TradeMetrics> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TradeMetricsCopyWith<$Res> {
  factory $TradeMetricsCopyWith(
          TradeMetrics value, $Res Function(TradeMetrics) then) =
      _$TradeMetricsCopyWithImpl<$Res, TradeMetrics>;
  @useResult
  $Res call(
      {int totalTrades,
      int winningTrades,
      int losingTrades,
      int breakEvenTrades,
      double winRate,
      double profitFactor,
      double averageWin,
      double averageLoss,
      double largestWin,
      double largestLoss,
      double totalPnL,
      double netPnL,
      double grossPnL,
      double totalCommissions,
      double totalFees});
}

/// @nodoc
class _$TradeMetricsCopyWithImpl<$Res, $Val extends TradeMetrics>
    implements $TradeMetricsCopyWith<$Res> {
  _$TradeMetricsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TradeMetrics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalTrades = null,
    Object? winningTrades = null,
    Object? losingTrades = null,
    Object? breakEvenTrades = null,
    Object? winRate = null,
    Object? profitFactor = null,
    Object? averageWin = null,
    Object? averageLoss = null,
    Object? largestWin = null,
    Object? largestLoss = null,
    Object? totalPnL = null,
    Object? netPnL = null,
    Object? grossPnL = null,
    Object? totalCommissions = null,
    Object? totalFees = null,
  }) {
    return _then(_value.copyWith(
      totalTrades: null == totalTrades
          ? _value.totalTrades
          : totalTrades // ignore: cast_nullable_to_non_nullable
              as int,
      winningTrades: null == winningTrades
          ? _value.winningTrades
          : winningTrades // ignore: cast_nullable_to_non_nullable
              as int,
      losingTrades: null == losingTrades
          ? _value.losingTrades
          : losingTrades // ignore: cast_nullable_to_non_nullable
              as int,
      breakEvenTrades: null == breakEvenTrades
          ? _value.breakEvenTrades
          : breakEvenTrades // ignore: cast_nullable_to_non_nullable
              as int,
      winRate: null == winRate
          ? _value.winRate
          : winRate // ignore: cast_nullable_to_non_nullable
              as double,
      profitFactor: null == profitFactor
          ? _value.profitFactor
          : profitFactor // ignore: cast_nullable_to_non_nullable
              as double,
      averageWin: null == averageWin
          ? _value.averageWin
          : averageWin // ignore: cast_nullable_to_non_nullable
              as double,
      averageLoss: null == averageLoss
          ? _value.averageLoss
          : averageLoss // ignore: cast_nullable_to_non_nullable
              as double,
      largestWin: null == largestWin
          ? _value.largestWin
          : largestWin // ignore: cast_nullable_to_non_nullable
              as double,
      largestLoss: null == largestLoss
          ? _value.largestLoss
          : largestLoss // ignore: cast_nullable_to_non_nullable
              as double,
      totalPnL: null == totalPnL
          ? _value.totalPnL
          : totalPnL // ignore: cast_nullable_to_non_nullable
              as double,
      netPnL: null == netPnL
          ? _value.netPnL
          : netPnL // ignore: cast_nullable_to_non_nullable
              as double,
      grossPnL: null == grossPnL
          ? _value.grossPnL
          : grossPnL // ignore: cast_nullable_to_non_nullable
              as double,
      totalCommissions: null == totalCommissions
          ? _value.totalCommissions
          : totalCommissions // ignore: cast_nullable_to_non_nullable
              as double,
      totalFees: null == totalFees
          ? _value.totalFees
          : totalFees // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TradeMetricsImplCopyWith<$Res>
    implements $TradeMetricsCopyWith<$Res> {
  factory _$$TradeMetricsImplCopyWith(
          _$TradeMetricsImpl value, $Res Function(_$TradeMetricsImpl) then) =
      __$$TradeMetricsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int totalTrades,
      int winningTrades,
      int losingTrades,
      int breakEvenTrades,
      double winRate,
      double profitFactor,
      double averageWin,
      double averageLoss,
      double largestWin,
      double largestLoss,
      double totalPnL,
      double netPnL,
      double grossPnL,
      double totalCommissions,
      double totalFees});
}

/// @nodoc
class __$$TradeMetricsImplCopyWithImpl<$Res>
    extends _$TradeMetricsCopyWithImpl<$Res, _$TradeMetricsImpl>
    implements _$$TradeMetricsImplCopyWith<$Res> {
  __$$TradeMetricsImplCopyWithImpl(
      _$TradeMetricsImpl _value, $Res Function(_$TradeMetricsImpl) _then)
      : super(_value, _then);

  /// Create a copy of TradeMetrics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalTrades = null,
    Object? winningTrades = null,
    Object? losingTrades = null,
    Object? breakEvenTrades = null,
    Object? winRate = null,
    Object? profitFactor = null,
    Object? averageWin = null,
    Object? averageLoss = null,
    Object? largestWin = null,
    Object? largestLoss = null,
    Object? totalPnL = null,
    Object? netPnL = null,
    Object? grossPnL = null,
    Object? totalCommissions = null,
    Object? totalFees = null,
  }) {
    return _then(_$TradeMetricsImpl(
      totalTrades: null == totalTrades
          ? _value.totalTrades
          : totalTrades // ignore: cast_nullable_to_non_nullable
              as int,
      winningTrades: null == winningTrades
          ? _value.winningTrades
          : winningTrades // ignore: cast_nullable_to_non_nullable
              as int,
      losingTrades: null == losingTrades
          ? _value.losingTrades
          : losingTrades // ignore: cast_nullable_to_non_nullable
              as int,
      breakEvenTrades: null == breakEvenTrades
          ? _value.breakEvenTrades
          : breakEvenTrades // ignore: cast_nullable_to_non_nullable
              as int,
      winRate: null == winRate
          ? _value.winRate
          : winRate // ignore: cast_nullable_to_non_nullable
              as double,
      profitFactor: null == profitFactor
          ? _value.profitFactor
          : profitFactor // ignore: cast_nullable_to_non_nullable
              as double,
      averageWin: null == averageWin
          ? _value.averageWin
          : averageWin // ignore: cast_nullable_to_non_nullable
              as double,
      averageLoss: null == averageLoss
          ? _value.averageLoss
          : averageLoss // ignore: cast_nullable_to_non_nullable
              as double,
      largestWin: null == largestWin
          ? _value.largestWin
          : largestWin // ignore: cast_nullable_to_non_nullable
              as double,
      largestLoss: null == largestLoss
          ? _value.largestLoss
          : largestLoss // ignore: cast_nullable_to_non_nullable
              as double,
      totalPnL: null == totalPnL
          ? _value.totalPnL
          : totalPnL // ignore: cast_nullable_to_non_nullable
              as double,
      netPnL: null == netPnL
          ? _value.netPnL
          : netPnL // ignore: cast_nullable_to_non_nullable
              as double,
      grossPnL: null == grossPnL
          ? _value.grossPnL
          : grossPnL // ignore: cast_nullable_to_non_nullable
              as double,
      totalCommissions: null == totalCommissions
          ? _value.totalCommissions
          : totalCommissions // ignore: cast_nullable_to_non_nullable
              as double,
      totalFees: null == totalFees
          ? _value.totalFees
          : totalFees // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TradeMetricsImpl implements _TradeMetrics {
  const _$TradeMetricsImpl(
      {required this.totalTrades,
      required this.winningTrades,
      required this.losingTrades,
      required this.breakEvenTrades,
      required this.winRate,
      required this.profitFactor,
      required this.averageWin,
      required this.averageLoss,
      required this.largestWin,
      required this.largestLoss,
      required this.totalPnL,
      required this.netPnL,
      required this.grossPnL,
      required this.totalCommissions,
      required this.totalFees});

  factory _$TradeMetricsImpl.fromJson(Map<String, dynamic> json) =>
      _$$TradeMetricsImplFromJson(json);

  @override
  final int totalTrades;
  @override
  final int winningTrades;
  @override
  final int losingTrades;
  @override
  final int breakEvenTrades;
  @override
  final double winRate;
  @override
  final double profitFactor;
  @override
  final double averageWin;
  @override
  final double averageLoss;
  @override
  final double largestWin;
  @override
  final double largestLoss;
  @override
  final double totalPnL;
  @override
  final double netPnL;
  @override
  final double grossPnL;
  @override
  final double totalCommissions;
  @override
  final double totalFees;

  @override
  String toString() {
    return 'TradeMetrics(totalTrades: $totalTrades, winningTrades: $winningTrades, losingTrades: $losingTrades, breakEvenTrades: $breakEvenTrades, winRate: $winRate, profitFactor: $profitFactor, averageWin: $averageWin, averageLoss: $averageLoss, largestWin: $largestWin, largestLoss: $largestLoss, totalPnL: $totalPnL, netPnL: $netPnL, grossPnL: $grossPnL, totalCommissions: $totalCommissions, totalFees: $totalFees)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TradeMetricsImpl &&
            (identical(other.totalTrades, totalTrades) ||
                other.totalTrades == totalTrades) &&
            (identical(other.winningTrades, winningTrades) ||
                other.winningTrades == winningTrades) &&
            (identical(other.losingTrades, losingTrades) ||
                other.losingTrades == losingTrades) &&
            (identical(other.breakEvenTrades, breakEvenTrades) ||
                other.breakEvenTrades == breakEvenTrades) &&
            (identical(other.winRate, winRate) || other.winRate == winRate) &&
            (identical(other.profitFactor, profitFactor) ||
                other.profitFactor == profitFactor) &&
            (identical(other.averageWin, averageWin) ||
                other.averageWin == averageWin) &&
            (identical(other.averageLoss, averageLoss) ||
                other.averageLoss == averageLoss) &&
            (identical(other.largestWin, largestWin) ||
                other.largestWin == largestWin) &&
            (identical(other.largestLoss, largestLoss) ||
                other.largestLoss == largestLoss) &&
            (identical(other.totalPnL, totalPnL) ||
                other.totalPnL == totalPnL) &&
            (identical(other.netPnL, netPnL) || other.netPnL == netPnL) &&
            (identical(other.grossPnL, grossPnL) ||
                other.grossPnL == grossPnL) &&
            (identical(other.totalCommissions, totalCommissions) ||
                other.totalCommissions == totalCommissions) &&
            (identical(other.totalFees, totalFees) ||
                other.totalFees == totalFees));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      totalTrades,
      winningTrades,
      losingTrades,
      breakEvenTrades,
      winRate,
      profitFactor,
      averageWin,
      averageLoss,
      largestWin,
      largestLoss,
      totalPnL,
      netPnL,
      grossPnL,
      totalCommissions,
      totalFees);

  /// Create a copy of TradeMetrics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TradeMetricsImplCopyWith<_$TradeMetricsImpl> get copyWith =>
      __$$TradeMetricsImplCopyWithImpl<_$TradeMetricsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TradeMetricsImplToJson(
      this,
    );
  }
}

abstract class _TradeMetrics implements TradeMetrics {
  const factory _TradeMetrics(
      {required final int totalTrades,
      required final int winningTrades,
      required final int losingTrades,
      required final int breakEvenTrades,
      required final double winRate,
      required final double profitFactor,
      required final double averageWin,
      required final double averageLoss,
      required final double largestWin,
      required final double largestLoss,
      required final double totalPnL,
      required final double netPnL,
      required final double grossPnL,
      required final double totalCommissions,
      required final double totalFees}) = _$TradeMetricsImpl;

  factory _TradeMetrics.fromJson(Map<String, dynamic> json) =
      _$TradeMetricsImpl.fromJson;

  @override
  int get totalTrades;
  @override
  int get winningTrades;
  @override
  int get losingTrades;
  @override
  int get breakEvenTrades;
  @override
  double get winRate;
  @override
  double get profitFactor;
  @override
  double get averageWin;
  @override
  double get averageLoss;
  @override
  double get largestWin;
  @override
  double get largestLoss;
  @override
  double get totalPnL;
  @override
  double get netPnL;
  @override
  double get grossPnL;
  @override
  double get totalCommissions;
  @override
  double get totalFees;

  /// Create a copy of TradeMetrics
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TradeMetricsImplCopyWith<_$TradeMetricsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Analytics _$AnalyticsFromJson(Map<String, dynamic> json) {
  return _Analytics.fromJson(json);
}

/// @nodoc
mixin _$Analytics {
  String get userId => throw _privateConstructorUsedError;
  TimeFrame get timeFrame => throw _privateConstructorUsedError;
  DateTime get startDate => throw _privateConstructorUsedError;
  DateTime get endDate => throw _privateConstructorUsedError;
  TradeMetrics get metrics => throw _privateConstructorUsedError;
  Map<String, TradeMetrics>? get byType => throw _privateConstructorUsedError;
  Map<String, TradeMetrics>? get bySymbol => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this Analytics to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Analytics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AnalyticsCopyWith<Analytics> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AnalyticsCopyWith<$Res> {
  factory $AnalyticsCopyWith(Analytics value, $Res Function(Analytics) then) =
      _$AnalyticsCopyWithImpl<$Res, Analytics>;
  @useResult
  $Res call(
      {String userId,
      TimeFrame timeFrame,
      DateTime startDate,
      DateTime endDate,
      TradeMetrics metrics,
      Map<String, TradeMetrics>? byType,
      Map<String, TradeMetrics>? bySymbol,
      DateTime updatedAt});

  $TradeMetricsCopyWith<$Res> get metrics;
}

/// @nodoc
class _$AnalyticsCopyWithImpl<$Res, $Val extends Analytics>
    implements $AnalyticsCopyWith<$Res> {
  _$AnalyticsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Analytics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? timeFrame = null,
    Object? startDate = null,
    Object? endDate = null,
    Object? metrics = null,
    Object? byType = freezed,
    Object? bySymbol = freezed,
    Object? updatedAt = null,
  }) {
    return _then(_value.copyWith(
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      timeFrame: null == timeFrame
          ? _value.timeFrame
          : timeFrame // ignore: cast_nullable_to_non_nullable
              as TimeFrame,
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      metrics: null == metrics
          ? _value.metrics
          : metrics // ignore: cast_nullable_to_non_nullable
              as TradeMetrics,
      byType: freezed == byType
          ? _value.byType
          : byType // ignore: cast_nullable_to_non_nullable
              as Map<String, TradeMetrics>?,
      bySymbol: freezed == bySymbol
          ? _value.bySymbol
          : bySymbol // ignore: cast_nullable_to_non_nullable
              as Map<String, TradeMetrics>?,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }

  /// Create a copy of Analytics
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TradeMetricsCopyWith<$Res> get metrics {
    return $TradeMetricsCopyWith<$Res>(_value.metrics, (value) {
      return _then(_value.copyWith(metrics: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AnalyticsImplCopyWith<$Res>
    implements $AnalyticsCopyWith<$Res> {
  factory _$$AnalyticsImplCopyWith(
          _$AnalyticsImpl value, $Res Function(_$AnalyticsImpl) then) =
      __$$AnalyticsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String userId,
      TimeFrame timeFrame,
      DateTime startDate,
      DateTime endDate,
      TradeMetrics metrics,
      Map<String, TradeMetrics>? byType,
      Map<String, TradeMetrics>? bySymbol,
      DateTime updatedAt});

  @override
  $TradeMetricsCopyWith<$Res> get metrics;
}

/// @nodoc
class __$$AnalyticsImplCopyWithImpl<$Res>
    extends _$AnalyticsCopyWithImpl<$Res, _$AnalyticsImpl>
    implements _$$AnalyticsImplCopyWith<$Res> {
  __$$AnalyticsImplCopyWithImpl(
      _$AnalyticsImpl _value, $Res Function(_$AnalyticsImpl) _then)
      : super(_value, _then);

  /// Create a copy of Analytics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? timeFrame = null,
    Object? startDate = null,
    Object? endDate = null,
    Object? metrics = null,
    Object? byType = freezed,
    Object? bySymbol = freezed,
    Object? updatedAt = null,
  }) {
    return _then(_$AnalyticsImpl(
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      timeFrame: null == timeFrame
          ? _value.timeFrame
          : timeFrame // ignore: cast_nullable_to_non_nullable
              as TimeFrame,
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      metrics: null == metrics
          ? _value.metrics
          : metrics // ignore: cast_nullable_to_non_nullable
              as TradeMetrics,
      byType: freezed == byType
          ? _value._byType
          : byType // ignore: cast_nullable_to_non_nullable
              as Map<String, TradeMetrics>?,
      bySymbol: freezed == bySymbol
          ? _value._bySymbol
          : bySymbol // ignore: cast_nullable_to_non_nullable
              as Map<String, TradeMetrics>?,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AnalyticsImpl extends _Analytics {
  const _$AnalyticsImpl(
      {required this.userId,
      required this.timeFrame,
      required this.startDate,
      required this.endDate,
      required this.metrics,
      final Map<String, TradeMetrics>? byType,
      final Map<String, TradeMetrics>? bySymbol,
      required this.updatedAt})
      : _byType = byType,
        _bySymbol = bySymbol,
        super._();

  factory _$AnalyticsImpl.fromJson(Map<String, dynamic> json) =>
      _$$AnalyticsImplFromJson(json);

  @override
  final String userId;
  @override
  final TimeFrame timeFrame;
  @override
  final DateTime startDate;
  @override
  final DateTime endDate;
  @override
  final TradeMetrics metrics;
  final Map<String, TradeMetrics>? _byType;
  @override
  Map<String, TradeMetrics>? get byType {
    final value = _byType;
    if (value == null) return null;
    if (_byType is EqualUnmodifiableMapView) return _byType;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  final Map<String, TradeMetrics>? _bySymbol;
  @override
  Map<String, TradeMetrics>? get bySymbol {
    final value = _bySymbol;
    if (value == null) return null;
    if (_bySymbol is EqualUnmodifiableMapView) return _bySymbol;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final DateTime updatedAt;

  @override
  String toString() {
    return 'Analytics(userId: $userId, timeFrame: $timeFrame, startDate: $startDate, endDate: $endDate, metrics: $metrics, byType: $byType, bySymbol: $bySymbol, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AnalyticsImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.timeFrame, timeFrame) ||
                other.timeFrame == timeFrame) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.metrics, metrics) || other.metrics == metrics) &&
            const DeepCollectionEquality().equals(other._byType, _byType) &&
            const DeepCollectionEquality().equals(other._bySymbol, _bySymbol) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      userId,
      timeFrame,
      startDate,
      endDate,
      metrics,
      const DeepCollectionEquality().hash(_byType),
      const DeepCollectionEquality().hash(_bySymbol),
      updatedAt);

  /// Create a copy of Analytics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AnalyticsImplCopyWith<_$AnalyticsImpl> get copyWith =>
      __$$AnalyticsImplCopyWithImpl<_$AnalyticsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AnalyticsImplToJson(
      this,
    );
  }
}

abstract class _Analytics extends Analytics {
  const factory _Analytics(
      {required final String userId,
      required final TimeFrame timeFrame,
      required final DateTime startDate,
      required final DateTime endDate,
      required final TradeMetrics metrics,
      final Map<String, TradeMetrics>? byType,
      final Map<String, TradeMetrics>? bySymbol,
      required final DateTime updatedAt}) = _$AnalyticsImpl;
  const _Analytics._() : super._();

  factory _Analytics.fromJson(Map<String, dynamic> json) =
      _$AnalyticsImpl.fromJson;

  @override
  String get userId;
  @override
  TimeFrame get timeFrame;
  @override
  DateTime get startDate;
  @override
  DateTime get endDate;
  @override
  TradeMetrics get metrics;
  @override
  Map<String, TradeMetrics>? get byType;
  @override
  Map<String, TradeMetrics>? get bySymbol;
  @override
  DateTime get updatedAt;

  /// Create a copy of Analytics
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AnalyticsImplCopyWith<_$AnalyticsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
