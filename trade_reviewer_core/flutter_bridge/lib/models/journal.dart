import 'package:freezed_annotation/freezed_annotation.dart';

part 'journal.freezed.dart';
part 'journal.g.dart';

enum MarketCondition {
  @JsonValue('BULLISH')
  bullish,
  @JsonValue('BEARISH')
  bearish,
  @JsonValue('SIDEWAYS')
  sideways,
  @JsonValue('VOLATILE')
  volatile,
  @JsonValue('TRENDING')
  trending,
  @JsonValue('RANGING')
  ranging
}

enum TradingMistake {
  @JsonValue('FOMO')
  fomo,
  @JsonValue('EARLY_ENTRY')
  earlyEntry,
  @JsonValue('LATE_ENTRY')
  lateEntry,
  @JsonValue('MOVED_STOP_LOSS')
  movedStopLoss,
  @JsonValue('EARLY_EXIT')
  earlyExit,
  @JsonValue('LATE_EXIT')
  lateExit,
  @JsonValue('POSITION_SIZING')
  positionSizing,
  @JsonValue('IGNORED_PLAN')
  ignoredPlan,
  @JsonValue('EMOTIONAL_TRADING')
  emotionalTrading,
  @JsonValue('REVENGE_TRADING')
  revengeTrading
}

enum EmotionalState {
  @JsonValue('CALM')
  calm,
  @JsonValue('CONFIDENT')
  confident,
  @JsonValue('ANXIOUS')
  anxious,
  @JsonValue('FEARFUL')
  fearful,
  @JsonValue('GREEDY')
  greedy,
  @JsonValue('FRUSTRATED')
  frustrated,
  @JsonValue('TILTED')
  tilted
}

@freezed
class TradePlan with _$TradePlan {
  const factory TradePlan({
    required String setup,
    required String entry,
    required String stopLoss,
    required List<String> targets,
    required String timeFrame,
    required double risk,
    required double reward,
  }) = _TradePlan;

  factory TradePlan.fromJson(Map<String, dynamic> json) =>
      _$TradePlanFromJson(json);
}

@freezed
class JournalEntry with _$JournalEntry {
  const JournalEntry._();

  const factory JournalEntry({
    required String id,
    required String userId,
    required String tradeId,
    required DateTime date,
    required MarketCondition marketCondition,
    required EmotionalState emotionalState,
    required TradePlan tradePlan,
    @Default([]) List<TradingMistake> mistakes,
    required String analysis,
    required List<String> lessons,
    @Default([]) List<String> screenshots,
    required int rating,
    @Default([]) List<String> tags,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _JournalEntry;

  factory JournalEntry.fromJson(Map<String, dynamic> json) =>
      _$JournalEntryFromJson(json);

  double get riskRewardRatio => tradePlan.reward / tradePlan.risk;

  bool get hasScreenshots => screenshots.isNotEmpty;

  bool get hasMistakes => mistakes.isNotEmpty;

  bool get isEmotionallyNeutral =>
      emotionalState == EmotionalState.calm ||
      emotionalState == EmotionalState.confident;

  JournalEntry addMistake(TradingMistake mistake) {
    if (!mistakes.contains(mistake)) {
      return copyWith(
        mistakes: [...mistakes, mistake],
        updatedAt: DateTime.now(),
      );
    }
    return this;
  }

  JournalEntry addLesson(String lesson) {
    if (!lessons.contains(lesson)) {
      return copyWith(
        lessons: [...lessons, lesson],
        updatedAt: DateTime.now(),
      );
    }
    return this;
  }

  JournalEntry addScreenshot(String url) {
    if (!screenshots.contains(url)) {
      return copyWith(
        screenshots: [...screenshots, url],
        updatedAt: DateTime.now(),
      );
    }
    return this;
  }

  JournalEntry updateAnalysis(String newAnalysis) => copyWith(
        analysis: newAnalysis,
        updatedAt: DateTime.now(),
      );

  JournalEntry updateRating(int newRating) {
    if (newRating >= 1 && newRating <= 5) {
      return copyWith(
        rating: newRating,
        updatedAt: DateTime.now(),
      );
    }
    return this;
  }
}
