import 'package:freezed_annotation/freezed_annotation.dart';

part 'analytics.freezed.dart';
part 'analytics.g.dart';

enum TimeFrame {
  @JsonValue('DAILY')
  daily,
  @JsonValue('WEEKLY')
  weekly,
  @JsonValue('MONTHLY')
  monthly,
  @JsonValue('QUARTERLY')
  quarterly,
  @JsonValue('YEARLY')
  yearly,
  @JsonValue('ALL_TIME')
  allTime,
}

@freezed
class TradeMetrics with _$TradeMetrics {
  const factory TradeMetrics({
    required int totalTrades,
    required int winningTrades,
    required int losingTrades,
    required int breakEvenTrades,
    required double winRate,
    required double profitFactor,
    required double averageWin,
    required double averageLoss,
    required double largestWin,
    required double largestLoss,
    required double totalPnL,
    required double netPnL,
    required double grossPnL,
    required double totalCommissions,
    required double totalFees,
  }) = _TradeMetrics;

  factory TradeMetrics.fromJson(Map<String, dynamic> json) =>
      _$TradeMetricsFromJson(json);
}

@freezed
class Analytics with _$Analytics {
  const Analytics._();

  const factory Analytics({
    required String userId,
    required TimeFrame timeFrame,
    required DateTime startDate,
    required DateTime endDate,
    required TradeMetrics metrics,
    Map<String, TradeMetrics>? byType,
    Map<String, TradeMetrics>? bySymbol,
    required DateTime updatedAt,
  }) = _Analytics;

  factory Analytics.fromJson(Map<String, dynamic> json) =>
      _$AnalyticsFromJson(json);

  Analytics updateMetrics(TradeMetrics newMetrics) {
    return copyWith(
      metrics: newMetrics,
      updatedAt: DateTime.now(),
    );
  }

  Analytics updateTypeMetrics(String type, TradeMetrics metrics) {
    final updatedByType = Map<String, TradeMetrics>.from(byType ?? {});
    updatedByType[type] = metrics;
    return copyWith(
      byType: updatedByType,
      updatedAt: DateTime.now(),
    );
  }

  Analytics updateSymbolMetrics(String symbol, TradeMetrics metrics) {
    final updatedBySymbol = Map<String, TradeMetrics>.from(bySymbol ?? {});
    updatedBySymbol[symbol] = metrics;
    return copyWith(
      bySymbol: updatedBySymbol,
      updatedAt: DateTime.now(),
    );
  }
}
