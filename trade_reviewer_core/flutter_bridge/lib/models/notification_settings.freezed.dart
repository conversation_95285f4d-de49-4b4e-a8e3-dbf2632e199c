// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notification_settings.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

NotificationSettings _$NotificationSettingsFromJson(Map<String, dynamic> json) {
  return _NotificationSettings.fromJson(json);
}

/// @nodoc
mixin _$NotificationSettings {
// Global Settings
  /// Master switch to enable/disable all notifications
  bool get enabled => throw _privateConstructorUsedError;

  /// Enable notification sounds
  bool get soundEnabled => throw _privateConstructorUsedError;

  /// Enable haptic feedback for notifications
  bool get hapticEnabled =>
      throw _privateConstructorUsedError; // Delivery Methods
  /// List of enabled notification channels
  List<NotificationChannel> get channels =>
      throw _privateConstructorUsedError; // Notification Types
  /// List of enabled notification types
  List<NotificationType> get enabledTypes =>
      throw _privateConstructorUsedError; // Notification Preferences
  /// Start time for quiet hours (24-hour format)
  String get quietHoursStart => throw _privateConstructorUsedError;

  /// End time for quiet hours (24-hour format)
  String get quietHoursEnd => throw _privateConstructorUsedError;

  /// Frequency of journal reminder notifications
  ReminderFrequency get journalReminderFrequency =>
      throw _privateConstructorUsedError;

  /// Default priority for notifications
  NotificationPriority get defaultPriority =>
      throw _privateConstructorUsedError;

  /// Serializes this NotificationSettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of NotificationSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NotificationSettingsCopyWith<NotificationSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationSettingsCopyWith<$Res> {
  factory $NotificationSettingsCopyWith(NotificationSettings value,
          $Res Function(NotificationSettings) then) =
      _$NotificationSettingsCopyWithImpl<$Res, NotificationSettings>;
  @useResult
  $Res call(
      {bool enabled,
      bool soundEnabled,
      bool hapticEnabled,
      List<NotificationChannel> channels,
      List<NotificationType> enabledTypes,
      String quietHoursStart,
      String quietHoursEnd,
      ReminderFrequency journalReminderFrequency,
      NotificationPriority defaultPriority});
}

/// @nodoc
class _$NotificationSettingsCopyWithImpl<$Res,
        $Val extends NotificationSettings>
    implements $NotificationSettingsCopyWith<$Res> {
  _$NotificationSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NotificationSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enabled = null,
    Object? soundEnabled = null,
    Object? hapticEnabled = null,
    Object? channels = null,
    Object? enabledTypes = null,
    Object? quietHoursStart = null,
    Object? quietHoursEnd = null,
    Object? journalReminderFrequency = null,
    Object? defaultPriority = null,
  }) {
    return _then(_value.copyWith(
      enabled: null == enabled
          ? _value.enabled
          : enabled // ignore: cast_nullable_to_non_nullable
              as bool,
      soundEnabled: null == soundEnabled
          ? _value.soundEnabled
          : soundEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      hapticEnabled: null == hapticEnabled
          ? _value.hapticEnabled
          : hapticEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      channels: null == channels
          ? _value.channels
          : channels // ignore: cast_nullable_to_non_nullable
              as List<NotificationChannel>,
      enabledTypes: null == enabledTypes
          ? _value.enabledTypes
          : enabledTypes // ignore: cast_nullable_to_non_nullable
              as List<NotificationType>,
      quietHoursStart: null == quietHoursStart
          ? _value.quietHoursStart
          : quietHoursStart // ignore: cast_nullable_to_non_nullable
              as String,
      quietHoursEnd: null == quietHoursEnd
          ? _value.quietHoursEnd
          : quietHoursEnd // ignore: cast_nullable_to_non_nullable
              as String,
      journalReminderFrequency: null == journalReminderFrequency
          ? _value.journalReminderFrequency
          : journalReminderFrequency // ignore: cast_nullable_to_non_nullable
              as ReminderFrequency,
      defaultPriority: null == defaultPriority
          ? _value.defaultPriority
          : defaultPriority // ignore: cast_nullable_to_non_nullable
              as NotificationPriority,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NotificationSettingsImplCopyWith<$Res>
    implements $NotificationSettingsCopyWith<$Res> {
  factory _$$NotificationSettingsImplCopyWith(_$NotificationSettingsImpl value,
          $Res Function(_$NotificationSettingsImpl) then) =
      __$$NotificationSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool enabled,
      bool soundEnabled,
      bool hapticEnabled,
      List<NotificationChannel> channels,
      List<NotificationType> enabledTypes,
      String quietHoursStart,
      String quietHoursEnd,
      ReminderFrequency journalReminderFrequency,
      NotificationPriority defaultPriority});
}

/// @nodoc
class __$$NotificationSettingsImplCopyWithImpl<$Res>
    extends _$NotificationSettingsCopyWithImpl<$Res, _$NotificationSettingsImpl>
    implements _$$NotificationSettingsImplCopyWith<$Res> {
  __$$NotificationSettingsImplCopyWithImpl(_$NotificationSettingsImpl _value,
      $Res Function(_$NotificationSettingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enabled = null,
    Object? soundEnabled = null,
    Object? hapticEnabled = null,
    Object? channels = null,
    Object? enabledTypes = null,
    Object? quietHoursStart = null,
    Object? quietHoursEnd = null,
    Object? journalReminderFrequency = null,
    Object? defaultPriority = null,
  }) {
    return _then(_$NotificationSettingsImpl(
      enabled: null == enabled
          ? _value.enabled
          : enabled // ignore: cast_nullable_to_non_nullable
              as bool,
      soundEnabled: null == soundEnabled
          ? _value.soundEnabled
          : soundEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      hapticEnabled: null == hapticEnabled
          ? _value.hapticEnabled
          : hapticEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      channels: null == channels
          ? _value._channels
          : channels // ignore: cast_nullable_to_non_nullable
              as List<NotificationChannel>,
      enabledTypes: null == enabledTypes
          ? _value._enabledTypes
          : enabledTypes // ignore: cast_nullable_to_non_nullable
              as List<NotificationType>,
      quietHoursStart: null == quietHoursStart
          ? _value.quietHoursStart
          : quietHoursStart // ignore: cast_nullable_to_non_nullable
              as String,
      quietHoursEnd: null == quietHoursEnd
          ? _value.quietHoursEnd
          : quietHoursEnd // ignore: cast_nullable_to_non_nullable
              as String,
      journalReminderFrequency: null == journalReminderFrequency
          ? _value.journalReminderFrequency
          : journalReminderFrequency // ignore: cast_nullable_to_non_nullable
              as ReminderFrequency,
      defaultPriority: null == defaultPriority
          ? _value.defaultPriority
          : defaultPriority // ignore: cast_nullable_to_non_nullable
              as NotificationPriority,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NotificationSettingsImpl implements _NotificationSettings {
  const _$NotificationSettingsImpl(
      {this.enabled = true,
      this.soundEnabled = true,
      this.hapticEnabled = true,
      final List<NotificationChannel> channels = const [
        NotificationChannel.inApp,
        NotificationChannel.push,
        NotificationChannel.email
      ],
      final List<NotificationType> enabledTypes = const [
        NotificationType.tradeAlert,
        NotificationType.priceAlert,
        NotificationType.goalAchieved,
        NotificationType.riskWarning,
        NotificationType.systemUpdate,
        NotificationType.backupReminder,
        NotificationType.journalReminder,
        NotificationType.tradeStats
      ],
      this.quietHoursStart = "21:00",
      this.quietHoursEnd = "09:00",
      this.journalReminderFrequency = ReminderFrequency.daily,
      this.defaultPriority = NotificationPriority.medium})
      : assert(quietHoursStart.length >= 4 && quietHoursStart.length <= 5,
            'Quiet hours start time must be in HH:mm format'),
        assert(quietHoursEnd.length >= 4 && quietHoursEnd.length <= 5,
            'Quiet hours end time must be in HH:mm format'),
        _channels = channels,
        _enabledTypes = enabledTypes;

  factory _$NotificationSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$NotificationSettingsImplFromJson(json);

// Global Settings
  /// Master switch to enable/disable all notifications
  @override
  @JsonKey()
  final bool enabled;

  /// Enable notification sounds
  @override
  @JsonKey()
  final bool soundEnabled;

  /// Enable haptic feedback for notifications
  @override
  @JsonKey()
  final bool hapticEnabled;
// Delivery Methods
  /// List of enabled notification channels
  final List<NotificationChannel> _channels;
// Delivery Methods
  /// List of enabled notification channels
  @override
  @JsonKey()
  List<NotificationChannel> get channels {
    if (_channels is EqualUnmodifiableListView) return _channels;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_channels);
  }

// Notification Types
  /// List of enabled notification types
  final List<NotificationType> _enabledTypes;
// Notification Types
  /// List of enabled notification types
  @override
  @JsonKey()
  List<NotificationType> get enabledTypes {
    if (_enabledTypes is EqualUnmodifiableListView) return _enabledTypes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_enabledTypes);
  }

// Notification Preferences
  /// Start time for quiet hours (24-hour format)
  @override
  @JsonKey()
  final String quietHoursStart;

  /// End time for quiet hours (24-hour format)
  @override
  @JsonKey()
  final String quietHoursEnd;

  /// Frequency of journal reminder notifications
  @override
  @JsonKey()
  final ReminderFrequency journalReminderFrequency;

  /// Default priority for notifications
  @override
  @JsonKey()
  final NotificationPriority defaultPriority;

  @override
  String toString() {
    return 'NotificationSettings(enabled: $enabled, soundEnabled: $soundEnabled, hapticEnabled: $hapticEnabled, channels: $channels, enabledTypes: $enabledTypes, quietHoursStart: $quietHoursStart, quietHoursEnd: $quietHoursEnd, journalReminderFrequency: $journalReminderFrequency, defaultPriority: $defaultPriority)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationSettingsImpl &&
            (identical(other.enabled, enabled) || other.enabled == enabled) &&
            (identical(other.soundEnabled, soundEnabled) ||
                other.soundEnabled == soundEnabled) &&
            (identical(other.hapticEnabled, hapticEnabled) ||
                other.hapticEnabled == hapticEnabled) &&
            const DeepCollectionEquality().equals(other._channels, _channels) &&
            const DeepCollectionEquality()
                .equals(other._enabledTypes, _enabledTypes) &&
            (identical(other.quietHoursStart, quietHoursStart) ||
                other.quietHoursStart == quietHoursStart) &&
            (identical(other.quietHoursEnd, quietHoursEnd) ||
                other.quietHoursEnd == quietHoursEnd) &&
            (identical(
                    other.journalReminderFrequency, journalReminderFrequency) ||
                other.journalReminderFrequency == journalReminderFrequency) &&
            (identical(other.defaultPriority, defaultPriority) ||
                other.defaultPriority == defaultPriority));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      enabled,
      soundEnabled,
      hapticEnabled,
      const DeepCollectionEquality().hash(_channels),
      const DeepCollectionEquality().hash(_enabledTypes),
      quietHoursStart,
      quietHoursEnd,
      journalReminderFrequency,
      defaultPriority);

  /// Create a copy of NotificationSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationSettingsImplCopyWith<_$NotificationSettingsImpl>
      get copyWith =>
          __$$NotificationSettingsImplCopyWithImpl<_$NotificationSettingsImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NotificationSettingsImplToJson(
      this,
    );
  }
}

abstract class _NotificationSettings implements NotificationSettings {
  const factory _NotificationSettings(
      {final bool enabled,
      final bool soundEnabled,
      final bool hapticEnabled,
      final List<NotificationChannel> channels,
      final List<NotificationType> enabledTypes,
      final String quietHoursStart,
      final String quietHoursEnd,
      final ReminderFrequency journalReminderFrequency,
      final NotificationPriority defaultPriority}) = _$NotificationSettingsImpl;

  factory _NotificationSettings.fromJson(Map<String, dynamic> json) =
      _$NotificationSettingsImpl.fromJson;

// Global Settings
  /// Master switch to enable/disable all notifications
  @override
  bool get enabled;

  /// Enable notification sounds
  @override
  bool get soundEnabled;

  /// Enable haptic feedback for notifications
  @override
  bool get hapticEnabled; // Delivery Methods
  /// List of enabled notification channels
  @override
  List<NotificationChannel> get channels; // Notification Types
  /// List of enabled notification types
  @override
  List<NotificationType> get enabledTypes; // Notification Preferences
  /// Start time for quiet hours (24-hour format)
  @override
  String get quietHoursStart;

  /// End time for quiet hours (24-hour format)
  @override
  String get quietHoursEnd;

  /// Frequency of journal reminder notifications
  @override
  ReminderFrequency get journalReminderFrequency;

  /// Default priority for notifications
  @override
  NotificationPriority get defaultPriority;

  /// Create a copy of NotificationSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NotificationSettingsImplCopyWith<_$NotificationSettingsImpl>
      get copyWith => throw _privateConstructorUsedError;
}
