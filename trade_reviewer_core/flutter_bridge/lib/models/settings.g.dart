// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'settings.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TradeDefaultsImpl _$$TradeDefaultsImplFromJson(Map<String, dynamic> json) =>
    _$TradeDefaultsImpl(
      defaultRisk: (json['defaultRisk'] as num).toDouble(),
      defaultPosition: (json['defaultPosition'] as num).toDouble(),
      defaultTimeframe:
          $enumDecode(_$ChartTimeframeEnumMap, json['defaultTimeframe']),
      riskCalculationType: $enumDecode(
          _$RiskCalculationTypeEnumMap, json['riskCalculationType']),
      defaultCommission: (json['defaultCommission'] as num).toDouble(),
      defaultFees: (json['defaultFees'] as num).toDouble(),
    );

Map<String, dynamic> _$$TradeDefaultsImplToJson(_$TradeDefaultsImpl instance) =>
    <String, dynamic>{
      'defaultRisk': instance.defaultRisk,
      'defaultPosition': instance.defaultPosition,
      'defaultTimeframe': _$ChartTimeframeEnumMap[instance.defaultTimeframe]!,
      'riskCalculationType':
          _$RiskCalculationTypeEnumMap[instance.riskCalculationType]!,
      'defaultCommission': instance.defaultCommission,
      'defaultFees': instance.defaultFees,
    };

const _$ChartTimeframeEnumMap = {
  ChartTimeframe.m1: 'M1',
  ChartTimeframe.m5: 'M5',
  ChartTimeframe.m15: 'M15',
  ChartTimeframe.m30: 'M30',
  ChartTimeframe.h1: 'H1',
  ChartTimeframe.h4: 'H4',
  ChartTimeframe.d1: 'D1',
  ChartTimeframe.w1: 'W1',
  ChartTimeframe.mn: 'MN',
};

const _$RiskCalculationTypeEnumMap = {
  RiskCalculationType.fixed: 'FIXED',
  RiskCalculationType.percentage: 'PERCENTAGE',
  RiskCalculationType.rMultiple: 'R_MULTIPLE',
};

_$DisplaySettingsImpl _$$DisplaySettingsImplFromJson(
        Map<String, dynamic> json) =>
    _$DisplaySettingsImpl(
      theme: json['theme'] as String,
      pnlDisplayMode:
          $enumDecode(_$PnLDisplayModeEnumMap, json['pnlDisplayMode']),
      showRunningPnL: json['showRunningPnL'] as bool,
      showEquityCurve: json['showEquityCurve'] as bool,
      showTradeStatistics: json['showTradeStatistics'] as bool,
      compactMode: json['compactMode'] as bool,
      dateFormat: json['dateFormat'] as String,
      timeFormat: json['timeFormat'] as String,
      timezone: json['timezone'] as String,
      goalNotificationsEnabled:
          json['goalNotificationsEnabled'] as bool? ?? true,
      weeklyReportsEnabled: json['weeklyReportsEnabled'] as bool? ?? true,
      monthlyReportsEnabled: json['monthlyReportsEnabled'] as bool? ?? true,
      tradeAlertsEnabled: json['tradeAlertsEnabled'] as bool? ?? true,
      marketNewsEnabled: json['marketNewsEnabled'] as bool? ?? true,
      priceAlertsEnabled: json['priceAlertsEnabled'] as bool? ?? true,
      journalRemindersEnabled: json['journalRemindersEnabled'] as bool? ?? true,
      emailNotificationsEnabled:
          json['emailNotificationsEnabled'] as bool? ?? true,
      pushNotificationsEnabled:
          json['pushNotificationsEnabled'] as bool? ?? true,
      smsNotificationsEnabled:
          json['smsNotificationsEnabled'] as bool? ?? false,
      defaultAccountView: json['defaultAccountView'] as String? ?? 'all',
      defaultTimePeriod: json['defaultTimePeriod'] as String? ?? 'month',
    );

Map<String, dynamic> _$$DisplaySettingsImplToJson(
        _$DisplaySettingsImpl instance) =>
    <String, dynamic>{
      'theme': instance.theme,
      'pnlDisplayMode': _$PnLDisplayModeEnumMap[instance.pnlDisplayMode]!,
      'showRunningPnL': instance.showRunningPnL,
      'showEquityCurve': instance.showEquityCurve,
      'showTradeStatistics': instance.showTradeStatistics,
      'compactMode': instance.compactMode,
      'dateFormat': instance.dateFormat,
      'timeFormat': instance.timeFormat,
      'timezone': instance.timezone,
      'goalNotificationsEnabled': instance.goalNotificationsEnabled,
      'weeklyReportsEnabled': instance.weeklyReportsEnabled,
      'monthlyReportsEnabled': instance.monthlyReportsEnabled,
      'tradeAlertsEnabled': instance.tradeAlertsEnabled,
      'marketNewsEnabled': instance.marketNewsEnabled,
      'priceAlertsEnabled': instance.priceAlertsEnabled,
      'journalRemindersEnabled': instance.journalRemindersEnabled,
      'emailNotificationsEnabled': instance.emailNotificationsEnabled,
      'pushNotificationsEnabled': instance.pushNotificationsEnabled,
      'smsNotificationsEnabled': instance.smsNotificationsEnabled,
      'defaultAccountView': instance.defaultAccountView,
      'defaultTimePeriod': instance.defaultTimePeriod,
    };

const _$PnLDisplayModeEnumMap = {
  PnLDisplayMode.currency: 'CURRENCY',
  PnLDisplayMode.percentage: 'PERCENTAGE',
  PnLDisplayMode.rMultiple: 'R_MULTIPLE',
};

_$BacktestSettingsImpl _$$BacktestSettingsImplFromJson(
        Map<String, dynamic> json) =>
    _$BacktestSettingsImpl(
      initialCapital: (json['initialCapital'] as num).toDouble(),
      useFixedPosition: json['useFixedPosition'] as bool,
      maxRiskPerTrade: (json['maxRiskPerTrade'] as num).toDouble(),
      maxOpenTrades: (json['maxOpenTrades'] as num).toInt(),
      includeFees: json['includeFees'] as bool,
      includeSlippage: json['includeSlippage'] as bool,
      slippageAmount: (json['slippageAmount'] as num).toDouble(),
    );

Map<String, dynamic> _$$BacktestSettingsImplToJson(
        _$BacktestSettingsImpl instance) =>
    <String, dynamic>{
      'initialCapital': instance.initialCapital,
      'useFixedPosition': instance.useFixedPosition,
      'maxRiskPerTrade': instance.maxRiskPerTrade,
      'maxOpenTrades': instance.maxOpenTrades,
      'includeFees': instance.includeFees,
      'includeSlippage': instance.includeSlippage,
      'slippageAmount': instance.slippageAmount,
    };

_$GoalSettingsImpl _$$GoalSettingsImplFromJson(Map<String, dynamic> json) =>
    _$GoalSettingsImpl(
      activeMetrics: Map<String, bool>.from(json['activeMetrics'] as Map),
      metricGoals: (json['metricGoals'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
    );

Map<String, dynamic> _$$GoalSettingsImplToJson(_$GoalSettingsImpl instance) =>
    <String, dynamic>{
      'activeMetrics': instance.activeMetrics,
      'metricGoals': instance.metricGoals,
    };

_$SettingsImpl _$$SettingsImplFromJson(Map<String, dynamic> json) =>
    _$SettingsImpl(
      userId: json['userId'] as String,
      tradeDefaults:
          TradeDefaults.fromJson(json['tradeDefaults'] as Map<String, dynamic>),
      display:
          DisplaySettings.fromJson(json['display'] as Map<String, dynamic>),
      backtest:
          BacktestSettings.fromJson(json['backtest'] as Map<String, dynamic>),
      goals: GoalSettings.fromJson(json['goals'] as Map<String, dynamic>),
      accounts:
          AccountSettings.fromJson(json['accounts'] as Map<String, dynamic>),
      notifications: NotificationSettings.fromJson(
          json['notifications'] as Map<String, dynamic>),
      autoSync: json['autoSync'] as bool,
      autoBackup: json['autoBackup'] as bool,
      backupFrequency: (json['backupFrequency'] as num).toInt(),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$SettingsImplToJson(_$SettingsImpl instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'tradeDefaults': instance.tradeDefaults,
      'display': instance.display,
      'backtest': instance.backtest,
      'goals': instance.goals,
      'accounts': instance.accounts,
      'notifications': instance.notifications,
      'autoSync': instance.autoSync,
      'autoBackup': instance.autoBackup,
      'backupFrequency': instance.backupFrequency,
      'updatedAt': instance.updatedAt.toIso8601String(),
    };
