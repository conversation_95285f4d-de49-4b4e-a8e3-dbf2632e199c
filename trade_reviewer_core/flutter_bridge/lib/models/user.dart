import 'package:freezed_annotation/freezed_annotation.dart';

part 'user.freezed.dart';
part 'user.g.dart';

enum UserRole {
  @JsonValue('USER')
  user,
  @JsonValue('PREMIUM')
  premium,
  @JsonValue('ADMIN')
  admin
}

enum NotificationPreference {
  @JsonValue('ALL')
  all,
  @JsonValue('IMPORTANT')
  important,
  @JsonValue('NONE')
  none
}

@freezed
class UserPreferences with _$UserPreferences {
  const factory UserPreferences({
    @Default(NotificationPreference.important)
    NotificationPreference notificationPreference,
    @Default(false) bool darkMode,
    @Default('USD') String defaultCurrency,
    @Default('UTC') String timezone,
    @Default('en') String language,
    @Default(true) bool showPnLInPercent,
    @Default(true) bool autoSyncEnabled,
    // Privacy settings
    @Default(false) bool profilePublic,
    @Default(false) bool showTradeHistory,
    @Default(false) bool showProfitLoss,
    @Default(false) bool showPortfolioValue,
    @Default(true) bool allowDataCollection,
    @Default(true) bool allowCrashReports,
    @Default(true) bool allowAnalytics,
  }) = _UserPreferences;

  factory UserPreferences.fromJson(Map<String, dynamic> json) =>
      _$UserPreferencesFromJson(json);
}

@freezed
class User with _$User {
  const User._();

  const factory User({
    required String id,
    required String email,
    required String name,
    @Default(UserRole.user) UserRole role,
    required UserPreferences preferences,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? lastLoginAt,
    @Default(false) bool isEmailVerified,
    String? profileImageUrl,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  bool get isAdmin => role == UserRole.admin;

  bool get isPremium => role == UserRole.premium || isAdmin;

  String get displayName => name.isNotEmpty ? name : email.split('@')[0];

  bool get canAccessPremiumFeatures => isPremium;

  User updatePreferences(UserPreferences newPreferences) {
    return copyWith(
      preferences: newPreferences,
      updatedAt: DateTime.now(),
    );
  }

  User verifyEmail() {
    return copyWith(
      isEmailVerified: true,
      updatedAt: DateTime.now(),
    );
  }

  User updateLastLogin() {
    return copyWith(
      lastLoginAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
}
