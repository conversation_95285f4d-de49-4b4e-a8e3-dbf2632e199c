import 'package:freezed_annotation/freezed_annotation.dart';

part 'goal.freezed.dart';
part 'goal.g.dart';

enum GoalType {
  @JsonValue('PROFIT')
  profit,
  @JsonValue('WIN_RATE')
  winRate,
  @JsonValue('TRADE_COUNT')
  tradeCount,
  @JsonValue('PROFIT_FACTOR')
  profitFactor,
  @JsonValue('AVERAGE_RRR')
  averageRrr,
}

enum GoalPeriod {
  @JsonValue('DAILY')
  daily,
  @JsonValue('WEEKLY')
  weekly,
  @JsonValue('MONTHLY')
  monthly,
  @JsonValue('QUARTERLY')
  quarterly,
  @JsonValue('YEARLY')
  yearly,
  @JsonValue('ALL_TIME')
  allTime,
}

@freezed
class Goal with _$Goal {
  const Goal._();

  const factory Goal({
    required String id,
    required String userId,
    required String name,
    required GoalType type,
    required GoalPeriod period,
    required double target,
    required double progress,
    required DateTime startDate,
    DateTime? endDate,
    required bool isActive,
    String? description,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _Goal;

  factory Goal.fromJson(Map<String, dynamic> json) => _$GoalFromJson(json);

  bool isCompleted() {
    return progress >= target;
  }

  bool isPeriodic() {
    return period != GoalPeriod.allTime;
  }

  bool isExpired() {
    if (endDate == null) return false;
    return DateTime.now().isAfter(endDate!);
  }
}

@freezed
class GoalMetrics with _$GoalMetrics {
  const GoalMetrics._();

  const factory GoalMetrics({
    @Default(0) double currentValue,
    @Default(0) double progress,
    @Default(0) double remainingValue,
    @Default(0) int daysRemaining,
  }) = _GoalMetrics;

  factory GoalMetrics.calculate(Goal goal, double currentValue) {
    final progress = (currentValue / goal.target * 100).clamp(0, 100);
    final remainingValue =
        (goal.target - currentValue).clamp(0, double.infinity);
    final daysRemaining = goal.endDate != null
        ? (goal.endDate!.difference(DateTime.now()).inDays)
            .clamp(0, double.infinity)
            .toInt()
        : 0;

    return GoalMetrics(
      currentValue: currentValue,
      progress: progress,
      remainingValue: remainingValue,
      daysRemaining: daysRemaining,
    );
  }

  bool get isAchieved => progress >= 100;

  String get formattedProgress => '${progress.round()}%';

  double get dailyRequired =>
      daysRemaining > 0 ? remainingValue / daysRemaining : 0;
}
