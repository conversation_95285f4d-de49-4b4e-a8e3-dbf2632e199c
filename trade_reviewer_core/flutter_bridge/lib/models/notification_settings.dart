import 'package:freezed_annotation/freezed_annotation.dart';
import 'notification.dart';

part 'notification_settings.freezed.dart';
part 'notification_settings.g.dart';

/// Frequency options for journal reminder notifications
enum ReminderFrequency {
  /// Reminders sent once per day
  @JsonValue('daily')
  daily,

  /// Reminders sent once per week
  @JsonValue('weekly')
  weekly,

  /// Reminders sent once per month
  @JsonValue('monthly')
  monthly,
}

/// Utility class for validating time-related settings
class TimeValidator {
  /// Regular expression for validating time in 24-hour format (HH:mm)
  static final _timeRegExp = RegExp(r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$');

  /// Validates if a string represents a valid time in 24-hour format
  static bool isValidTime(String time) {
    if (!_timeRegExp.hasMatch(time)) return false;

    final parts = time.split(':');
    final hours = int.parse(parts[0]);
    final minutes = int.parse(parts[1]);

    return hours >= 0 && hours < 24 && minutes >= 0 && minutes < 60;
  }

  /// Converts time string to minutes since midnight for comparison
  static int timeToMinutes(String time) {
    final parts = time.split(':');
    return int.parse(parts[0]) * 60 + int.parse(parts[1]);
  }

  /// Validates if end time comes after start time, considering 24-hour wraparound
  static bool isValidTimeRange(String start, String end) {
    if (!isValidTime(start) || !isValidTime(end)) return false;

    final startMinutes = timeToMinutes(start);
    final endMinutes = timeToMinutes(end);

    // Handle cases where quiet hours span midnight
    // e.g., start: 22:00, end: 06:00
    if (startMinutes > endMinutes) {
      return true; // Valid case for quiet hours spanning midnight
    }

    return startMinutes < endMinutes;
  }
}

/// Settings model for managing all notification-related preferences
@freezed
class NotificationSettings with _$NotificationSettings {
  // Using simple length checks that can be evaluated at compile-time
  @Assert('quietHoursStart.length >= 4 && quietHoursStart.length <= 5',
      'Quiet hours start time must be in HH:mm format')
  @Assert('quietHoursEnd.length >= 4 && quietHoursEnd.length <= 5',
      'Quiet hours end time must be in HH:mm format')
  const factory NotificationSettings({
    // Global Settings
    /// Master switch to enable/disable all notifications
    @Default(true) bool enabled,

    /// Enable notification sounds
    @Default(true) bool soundEnabled,

    /// Enable haptic feedback for notifications
    @Default(true) bool hapticEnabled,

    // Delivery Methods
    /// List of enabled notification channels
    @Default([
      NotificationChannel.inApp,
      NotificationChannel.push,
      NotificationChannel.email,
    ])
    List<NotificationChannel> channels,

    // Notification Types
    /// List of enabled notification types
    @Default([
      NotificationType.tradeAlert,
      NotificationType.priceAlert,
      NotificationType.goalAchieved,
      NotificationType.riskWarning,
      NotificationType.systemUpdate,
      NotificationType.backupReminder,
      NotificationType.journalReminder,
      NotificationType.tradeStats,
    ])
    List<NotificationType> enabledTypes,

    // Notification Preferences
    /// Start time for quiet hours (24-hour format)
    @Default("21:00") String quietHoursStart,

    /// End time for quiet hours (24-hour format)
    @Default("09:00") String quietHoursEnd,

    /// Frequency of journal reminder notifications
    @Default(ReminderFrequency.daily)
    ReminderFrequency journalReminderFrequency,

    /// Default priority for notifications
    @Default(NotificationPriority.medium) NotificationPriority defaultPriority,
  }) = _NotificationSettings;

  factory NotificationSettings.fromJson(Map<String, dynamic> json) =>
      _$NotificationSettingsFromJson(json);
}
