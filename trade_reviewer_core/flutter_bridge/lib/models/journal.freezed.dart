// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'journal.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TradePlan _$TradePlanFromJson(Map<String, dynamic> json) {
  return _TradePlan.fromJson(json);
}

/// @nodoc
mixin _$TradePlan {
  String get setup => throw _privateConstructorUsedError;
  String get entry => throw _privateConstructorUsedError;
  String get stopLoss => throw _privateConstructorUsedError;
  List<String> get targets => throw _privateConstructorUsedError;
  String get timeFrame => throw _privateConstructorUsedError;
  double get risk => throw _privateConstructorUsedError;
  double get reward => throw _privateConstructorUsedError;

  /// Serializes this TradePlan to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TradePlan
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TradePlanCopyWith<TradePlan> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TradePlanCopyWith<$Res> {
  factory $TradePlanCopyWith(TradePlan value, $Res Function(TradePlan) then) =
      _$TradePlanCopyWithImpl<$Res, TradePlan>;
  @useResult
  $Res call(
      {String setup,
      String entry,
      String stopLoss,
      List<String> targets,
      String timeFrame,
      double risk,
      double reward});
}

/// @nodoc
class _$TradePlanCopyWithImpl<$Res, $Val extends TradePlan>
    implements $TradePlanCopyWith<$Res> {
  _$TradePlanCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TradePlan
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? setup = null,
    Object? entry = null,
    Object? stopLoss = null,
    Object? targets = null,
    Object? timeFrame = null,
    Object? risk = null,
    Object? reward = null,
  }) {
    return _then(_value.copyWith(
      setup: null == setup
          ? _value.setup
          : setup // ignore: cast_nullable_to_non_nullable
              as String,
      entry: null == entry
          ? _value.entry
          : entry // ignore: cast_nullable_to_non_nullable
              as String,
      stopLoss: null == stopLoss
          ? _value.stopLoss
          : stopLoss // ignore: cast_nullable_to_non_nullable
              as String,
      targets: null == targets
          ? _value.targets
          : targets // ignore: cast_nullable_to_non_nullable
              as List<String>,
      timeFrame: null == timeFrame
          ? _value.timeFrame
          : timeFrame // ignore: cast_nullable_to_non_nullable
              as String,
      risk: null == risk
          ? _value.risk
          : risk // ignore: cast_nullable_to_non_nullable
              as double,
      reward: null == reward
          ? _value.reward
          : reward // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TradePlanImplCopyWith<$Res>
    implements $TradePlanCopyWith<$Res> {
  factory _$$TradePlanImplCopyWith(
          _$TradePlanImpl value, $Res Function(_$TradePlanImpl) then) =
      __$$TradePlanImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String setup,
      String entry,
      String stopLoss,
      List<String> targets,
      String timeFrame,
      double risk,
      double reward});
}

/// @nodoc
class __$$TradePlanImplCopyWithImpl<$Res>
    extends _$TradePlanCopyWithImpl<$Res, _$TradePlanImpl>
    implements _$$TradePlanImplCopyWith<$Res> {
  __$$TradePlanImplCopyWithImpl(
      _$TradePlanImpl _value, $Res Function(_$TradePlanImpl) _then)
      : super(_value, _then);

  /// Create a copy of TradePlan
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? setup = null,
    Object? entry = null,
    Object? stopLoss = null,
    Object? targets = null,
    Object? timeFrame = null,
    Object? risk = null,
    Object? reward = null,
  }) {
    return _then(_$TradePlanImpl(
      setup: null == setup
          ? _value.setup
          : setup // ignore: cast_nullable_to_non_nullable
              as String,
      entry: null == entry
          ? _value.entry
          : entry // ignore: cast_nullable_to_non_nullable
              as String,
      stopLoss: null == stopLoss
          ? _value.stopLoss
          : stopLoss // ignore: cast_nullable_to_non_nullable
              as String,
      targets: null == targets
          ? _value._targets
          : targets // ignore: cast_nullable_to_non_nullable
              as List<String>,
      timeFrame: null == timeFrame
          ? _value.timeFrame
          : timeFrame // ignore: cast_nullable_to_non_nullable
              as String,
      risk: null == risk
          ? _value.risk
          : risk // ignore: cast_nullable_to_non_nullable
              as double,
      reward: null == reward
          ? _value.reward
          : reward // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TradePlanImpl implements _TradePlan {
  const _$TradePlanImpl(
      {required this.setup,
      required this.entry,
      required this.stopLoss,
      required final List<String> targets,
      required this.timeFrame,
      required this.risk,
      required this.reward})
      : _targets = targets;

  factory _$TradePlanImpl.fromJson(Map<String, dynamic> json) =>
      _$$TradePlanImplFromJson(json);

  @override
  final String setup;
  @override
  final String entry;
  @override
  final String stopLoss;
  final List<String> _targets;
  @override
  List<String> get targets {
    if (_targets is EqualUnmodifiableListView) return _targets;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_targets);
  }

  @override
  final String timeFrame;
  @override
  final double risk;
  @override
  final double reward;

  @override
  String toString() {
    return 'TradePlan(setup: $setup, entry: $entry, stopLoss: $stopLoss, targets: $targets, timeFrame: $timeFrame, risk: $risk, reward: $reward)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TradePlanImpl &&
            (identical(other.setup, setup) || other.setup == setup) &&
            (identical(other.entry, entry) || other.entry == entry) &&
            (identical(other.stopLoss, stopLoss) ||
                other.stopLoss == stopLoss) &&
            const DeepCollectionEquality().equals(other._targets, _targets) &&
            (identical(other.timeFrame, timeFrame) ||
                other.timeFrame == timeFrame) &&
            (identical(other.risk, risk) || other.risk == risk) &&
            (identical(other.reward, reward) || other.reward == reward));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, setup, entry, stopLoss,
      const DeepCollectionEquality().hash(_targets), timeFrame, risk, reward);

  /// Create a copy of TradePlan
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TradePlanImplCopyWith<_$TradePlanImpl> get copyWith =>
      __$$TradePlanImplCopyWithImpl<_$TradePlanImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TradePlanImplToJson(
      this,
    );
  }
}

abstract class _TradePlan implements TradePlan {
  const factory _TradePlan(
      {required final String setup,
      required final String entry,
      required final String stopLoss,
      required final List<String> targets,
      required final String timeFrame,
      required final double risk,
      required final double reward}) = _$TradePlanImpl;

  factory _TradePlan.fromJson(Map<String, dynamic> json) =
      _$TradePlanImpl.fromJson;

  @override
  String get setup;
  @override
  String get entry;
  @override
  String get stopLoss;
  @override
  List<String> get targets;
  @override
  String get timeFrame;
  @override
  double get risk;
  @override
  double get reward;

  /// Create a copy of TradePlan
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TradePlanImplCopyWith<_$TradePlanImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

JournalEntry _$JournalEntryFromJson(Map<String, dynamic> json) {
  return _JournalEntry.fromJson(json);
}

/// @nodoc
mixin _$JournalEntry {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get tradeId => throw _privateConstructorUsedError;
  DateTime get date => throw _privateConstructorUsedError;
  MarketCondition get marketCondition => throw _privateConstructorUsedError;
  EmotionalState get emotionalState => throw _privateConstructorUsedError;
  TradePlan get tradePlan => throw _privateConstructorUsedError;
  List<TradingMistake> get mistakes => throw _privateConstructorUsedError;
  String get analysis => throw _privateConstructorUsedError;
  List<String> get lessons => throw _privateConstructorUsedError;
  List<String> get screenshots => throw _privateConstructorUsedError;
  int get rating => throw _privateConstructorUsedError;
  List<String> get tags => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this JournalEntry to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of JournalEntry
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $JournalEntryCopyWith<JournalEntry> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $JournalEntryCopyWith<$Res> {
  factory $JournalEntryCopyWith(
          JournalEntry value, $Res Function(JournalEntry) then) =
      _$JournalEntryCopyWithImpl<$Res, JournalEntry>;
  @useResult
  $Res call(
      {String id,
      String userId,
      String tradeId,
      DateTime date,
      MarketCondition marketCondition,
      EmotionalState emotionalState,
      TradePlan tradePlan,
      List<TradingMistake> mistakes,
      String analysis,
      List<String> lessons,
      List<String> screenshots,
      int rating,
      List<String> tags,
      DateTime createdAt,
      DateTime updatedAt});

  $TradePlanCopyWith<$Res> get tradePlan;
}

/// @nodoc
class _$JournalEntryCopyWithImpl<$Res, $Val extends JournalEntry>
    implements $JournalEntryCopyWith<$Res> {
  _$JournalEntryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of JournalEntry
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? tradeId = null,
    Object? date = null,
    Object? marketCondition = null,
    Object? emotionalState = null,
    Object? tradePlan = null,
    Object? mistakes = null,
    Object? analysis = null,
    Object? lessons = null,
    Object? screenshots = null,
    Object? rating = null,
    Object? tags = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      tradeId: null == tradeId
          ? _value.tradeId
          : tradeId // ignore: cast_nullable_to_non_nullable
              as String,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      marketCondition: null == marketCondition
          ? _value.marketCondition
          : marketCondition // ignore: cast_nullable_to_non_nullable
              as MarketCondition,
      emotionalState: null == emotionalState
          ? _value.emotionalState
          : emotionalState // ignore: cast_nullable_to_non_nullable
              as EmotionalState,
      tradePlan: null == tradePlan
          ? _value.tradePlan
          : tradePlan // ignore: cast_nullable_to_non_nullable
              as TradePlan,
      mistakes: null == mistakes
          ? _value.mistakes
          : mistakes // ignore: cast_nullable_to_non_nullable
              as List<TradingMistake>,
      analysis: null == analysis
          ? _value.analysis
          : analysis // ignore: cast_nullable_to_non_nullable
              as String,
      lessons: null == lessons
          ? _value.lessons
          : lessons // ignore: cast_nullable_to_non_nullable
              as List<String>,
      screenshots: null == screenshots
          ? _value.screenshots
          : screenshots // ignore: cast_nullable_to_non_nullable
              as List<String>,
      rating: null == rating
          ? _value.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as int,
      tags: null == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }

  /// Create a copy of JournalEntry
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TradePlanCopyWith<$Res> get tradePlan {
    return $TradePlanCopyWith<$Res>(_value.tradePlan, (value) {
      return _then(_value.copyWith(tradePlan: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$JournalEntryImplCopyWith<$Res>
    implements $JournalEntryCopyWith<$Res> {
  factory _$$JournalEntryImplCopyWith(
          _$JournalEntryImpl value, $Res Function(_$JournalEntryImpl) then) =
      __$$JournalEntryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String tradeId,
      DateTime date,
      MarketCondition marketCondition,
      EmotionalState emotionalState,
      TradePlan tradePlan,
      List<TradingMistake> mistakes,
      String analysis,
      List<String> lessons,
      List<String> screenshots,
      int rating,
      List<String> tags,
      DateTime createdAt,
      DateTime updatedAt});

  @override
  $TradePlanCopyWith<$Res> get tradePlan;
}

/// @nodoc
class __$$JournalEntryImplCopyWithImpl<$Res>
    extends _$JournalEntryCopyWithImpl<$Res, _$JournalEntryImpl>
    implements _$$JournalEntryImplCopyWith<$Res> {
  __$$JournalEntryImplCopyWithImpl(
      _$JournalEntryImpl _value, $Res Function(_$JournalEntryImpl) _then)
      : super(_value, _then);

  /// Create a copy of JournalEntry
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? tradeId = null,
    Object? date = null,
    Object? marketCondition = null,
    Object? emotionalState = null,
    Object? tradePlan = null,
    Object? mistakes = null,
    Object? analysis = null,
    Object? lessons = null,
    Object? screenshots = null,
    Object? rating = null,
    Object? tags = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_$JournalEntryImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      tradeId: null == tradeId
          ? _value.tradeId
          : tradeId // ignore: cast_nullable_to_non_nullable
              as String,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      marketCondition: null == marketCondition
          ? _value.marketCondition
          : marketCondition // ignore: cast_nullable_to_non_nullable
              as MarketCondition,
      emotionalState: null == emotionalState
          ? _value.emotionalState
          : emotionalState // ignore: cast_nullable_to_non_nullable
              as EmotionalState,
      tradePlan: null == tradePlan
          ? _value.tradePlan
          : tradePlan // ignore: cast_nullable_to_non_nullable
              as TradePlan,
      mistakes: null == mistakes
          ? _value._mistakes
          : mistakes // ignore: cast_nullable_to_non_nullable
              as List<TradingMistake>,
      analysis: null == analysis
          ? _value.analysis
          : analysis // ignore: cast_nullable_to_non_nullable
              as String,
      lessons: null == lessons
          ? _value._lessons
          : lessons // ignore: cast_nullable_to_non_nullable
              as List<String>,
      screenshots: null == screenshots
          ? _value._screenshots
          : screenshots // ignore: cast_nullable_to_non_nullable
              as List<String>,
      rating: null == rating
          ? _value.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as int,
      tags: null == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$JournalEntryImpl extends _JournalEntry {
  const _$JournalEntryImpl(
      {required this.id,
      required this.userId,
      required this.tradeId,
      required this.date,
      required this.marketCondition,
      required this.emotionalState,
      required this.tradePlan,
      final List<TradingMistake> mistakes = const [],
      required this.analysis,
      required final List<String> lessons,
      final List<String> screenshots = const [],
      required this.rating,
      final List<String> tags = const [],
      required this.createdAt,
      required this.updatedAt})
      : _mistakes = mistakes,
        _lessons = lessons,
        _screenshots = screenshots,
        _tags = tags,
        super._();

  factory _$JournalEntryImpl.fromJson(Map<String, dynamic> json) =>
      _$$JournalEntryImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String tradeId;
  @override
  final DateTime date;
  @override
  final MarketCondition marketCondition;
  @override
  final EmotionalState emotionalState;
  @override
  final TradePlan tradePlan;
  final List<TradingMistake> _mistakes;
  @override
  @JsonKey()
  List<TradingMistake> get mistakes {
    if (_mistakes is EqualUnmodifiableListView) return _mistakes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_mistakes);
  }

  @override
  final String analysis;
  final List<String> _lessons;
  @override
  List<String> get lessons {
    if (_lessons is EqualUnmodifiableListView) return _lessons;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_lessons);
  }

  final List<String> _screenshots;
  @override
  @JsonKey()
  List<String> get screenshots {
    if (_screenshots is EqualUnmodifiableListView) return _screenshots;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_screenshots);
  }

  @override
  final int rating;
  final List<String> _tags;
  @override
  @JsonKey()
  List<String> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;

  @override
  String toString() {
    return 'JournalEntry(id: $id, userId: $userId, tradeId: $tradeId, date: $date, marketCondition: $marketCondition, emotionalState: $emotionalState, tradePlan: $tradePlan, mistakes: $mistakes, analysis: $analysis, lessons: $lessons, screenshots: $screenshots, rating: $rating, tags: $tags, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$JournalEntryImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.tradeId, tradeId) || other.tradeId == tradeId) &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.marketCondition, marketCondition) ||
                other.marketCondition == marketCondition) &&
            (identical(other.emotionalState, emotionalState) ||
                other.emotionalState == emotionalState) &&
            (identical(other.tradePlan, tradePlan) ||
                other.tradePlan == tradePlan) &&
            const DeepCollectionEquality().equals(other._mistakes, _mistakes) &&
            (identical(other.analysis, analysis) ||
                other.analysis == analysis) &&
            const DeepCollectionEquality().equals(other._lessons, _lessons) &&
            const DeepCollectionEquality()
                .equals(other._screenshots, _screenshots) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      tradeId,
      date,
      marketCondition,
      emotionalState,
      tradePlan,
      const DeepCollectionEquality().hash(_mistakes),
      analysis,
      const DeepCollectionEquality().hash(_lessons),
      const DeepCollectionEquality().hash(_screenshots),
      rating,
      const DeepCollectionEquality().hash(_tags),
      createdAt,
      updatedAt);

  /// Create a copy of JournalEntry
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$JournalEntryImplCopyWith<_$JournalEntryImpl> get copyWith =>
      __$$JournalEntryImplCopyWithImpl<_$JournalEntryImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$JournalEntryImplToJson(
      this,
    );
  }
}

abstract class _JournalEntry extends JournalEntry {
  const factory _JournalEntry(
      {required final String id,
      required final String userId,
      required final String tradeId,
      required final DateTime date,
      required final MarketCondition marketCondition,
      required final EmotionalState emotionalState,
      required final TradePlan tradePlan,
      final List<TradingMistake> mistakes,
      required final String analysis,
      required final List<String> lessons,
      final List<String> screenshots,
      required final int rating,
      final List<String> tags,
      required final DateTime createdAt,
      required final DateTime updatedAt}) = _$JournalEntryImpl;
  const _JournalEntry._() : super._();

  factory _JournalEntry.fromJson(Map<String, dynamic> json) =
      _$JournalEntryImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get tradeId;
  @override
  DateTime get date;
  @override
  MarketCondition get marketCondition;
  @override
  EmotionalState get emotionalState;
  @override
  TradePlan get tradePlan;
  @override
  List<TradingMistake> get mistakes;
  @override
  String get analysis;
  @override
  List<String> get lessons;
  @override
  List<String> get screenshots;
  @override
  int get rating;
  @override
  List<String> get tags;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;

  /// Create a copy of JournalEntry
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$JournalEntryImplCopyWith<_$JournalEntryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
