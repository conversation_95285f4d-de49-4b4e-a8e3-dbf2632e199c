// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserPreferences _$UserPreferencesFromJson(Map<String, dynamic> json) {
  return _UserPreferences.fromJson(json);
}

/// @nodoc
mixin _$UserPreferences {
  NotificationPreference get notificationPreference =>
      throw _privateConstructorUsedError;
  bool get darkMode => throw _privateConstructorUsedError;
  String get defaultCurrency => throw _privateConstructorUsedError;
  String get timezone => throw _privateConstructorUsedError;
  String get language => throw _privateConstructorUsedError;
  bool get showPnLInPercent => throw _privateConstructorUsedError;
  bool get autoSyncEnabled =>
      throw _privateConstructorUsedError; // Privacy settings
  bool get profilePublic => throw _privateConstructorUsedError;
  bool get showTradeHistory => throw _privateConstructorUsedError;
  bool get showProfitLoss => throw _privateConstructorUsedError;
  bool get showPortfolioValue => throw _privateConstructorUsedError;
  bool get allowDataCollection => throw _privateConstructorUsedError;
  bool get allowCrashReports => throw _privateConstructorUsedError;
  bool get allowAnalytics => throw _privateConstructorUsedError;

  /// Serializes this UserPreferences to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserPreferences
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserPreferencesCopyWith<UserPreferences> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserPreferencesCopyWith<$Res> {
  factory $UserPreferencesCopyWith(
          UserPreferences value, $Res Function(UserPreferences) then) =
      _$UserPreferencesCopyWithImpl<$Res, UserPreferences>;
  @useResult
  $Res call(
      {NotificationPreference notificationPreference,
      bool darkMode,
      String defaultCurrency,
      String timezone,
      String language,
      bool showPnLInPercent,
      bool autoSyncEnabled,
      bool profilePublic,
      bool showTradeHistory,
      bool showProfitLoss,
      bool showPortfolioValue,
      bool allowDataCollection,
      bool allowCrashReports,
      bool allowAnalytics});
}

/// @nodoc
class _$UserPreferencesCopyWithImpl<$Res, $Val extends UserPreferences>
    implements $UserPreferencesCopyWith<$Res> {
  _$UserPreferencesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserPreferences
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notificationPreference = null,
    Object? darkMode = null,
    Object? defaultCurrency = null,
    Object? timezone = null,
    Object? language = null,
    Object? showPnLInPercent = null,
    Object? autoSyncEnabled = null,
    Object? profilePublic = null,
    Object? showTradeHistory = null,
    Object? showProfitLoss = null,
    Object? showPortfolioValue = null,
    Object? allowDataCollection = null,
    Object? allowCrashReports = null,
    Object? allowAnalytics = null,
  }) {
    return _then(_value.copyWith(
      notificationPreference: null == notificationPreference
          ? _value.notificationPreference
          : notificationPreference // ignore: cast_nullable_to_non_nullable
              as NotificationPreference,
      darkMode: null == darkMode
          ? _value.darkMode
          : darkMode // ignore: cast_nullable_to_non_nullable
              as bool,
      defaultCurrency: null == defaultCurrency
          ? _value.defaultCurrency
          : defaultCurrency // ignore: cast_nullable_to_non_nullable
              as String,
      timezone: null == timezone
          ? _value.timezone
          : timezone // ignore: cast_nullable_to_non_nullable
              as String,
      language: null == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String,
      showPnLInPercent: null == showPnLInPercent
          ? _value.showPnLInPercent
          : showPnLInPercent // ignore: cast_nullable_to_non_nullable
              as bool,
      autoSyncEnabled: null == autoSyncEnabled
          ? _value.autoSyncEnabled
          : autoSyncEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      profilePublic: null == profilePublic
          ? _value.profilePublic
          : profilePublic // ignore: cast_nullable_to_non_nullable
              as bool,
      showTradeHistory: null == showTradeHistory
          ? _value.showTradeHistory
          : showTradeHistory // ignore: cast_nullable_to_non_nullable
              as bool,
      showProfitLoss: null == showProfitLoss
          ? _value.showProfitLoss
          : showProfitLoss // ignore: cast_nullable_to_non_nullable
              as bool,
      showPortfolioValue: null == showPortfolioValue
          ? _value.showPortfolioValue
          : showPortfolioValue // ignore: cast_nullable_to_non_nullable
              as bool,
      allowDataCollection: null == allowDataCollection
          ? _value.allowDataCollection
          : allowDataCollection // ignore: cast_nullable_to_non_nullable
              as bool,
      allowCrashReports: null == allowCrashReports
          ? _value.allowCrashReports
          : allowCrashReports // ignore: cast_nullable_to_non_nullable
              as bool,
      allowAnalytics: null == allowAnalytics
          ? _value.allowAnalytics
          : allowAnalytics // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserPreferencesImplCopyWith<$Res>
    implements $UserPreferencesCopyWith<$Res> {
  factory _$$UserPreferencesImplCopyWith(_$UserPreferencesImpl value,
          $Res Function(_$UserPreferencesImpl) then) =
      __$$UserPreferencesImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {NotificationPreference notificationPreference,
      bool darkMode,
      String defaultCurrency,
      String timezone,
      String language,
      bool showPnLInPercent,
      bool autoSyncEnabled,
      bool profilePublic,
      bool showTradeHistory,
      bool showProfitLoss,
      bool showPortfolioValue,
      bool allowDataCollection,
      bool allowCrashReports,
      bool allowAnalytics});
}

/// @nodoc
class __$$UserPreferencesImplCopyWithImpl<$Res>
    extends _$UserPreferencesCopyWithImpl<$Res, _$UserPreferencesImpl>
    implements _$$UserPreferencesImplCopyWith<$Res> {
  __$$UserPreferencesImplCopyWithImpl(
      _$UserPreferencesImpl _value, $Res Function(_$UserPreferencesImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserPreferences
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notificationPreference = null,
    Object? darkMode = null,
    Object? defaultCurrency = null,
    Object? timezone = null,
    Object? language = null,
    Object? showPnLInPercent = null,
    Object? autoSyncEnabled = null,
    Object? profilePublic = null,
    Object? showTradeHistory = null,
    Object? showProfitLoss = null,
    Object? showPortfolioValue = null,
    Object? allowDataCollection = null,
    Object? allowCrashReports = null,
    Object? allowAnalytics = null,
  }) {
    return _then(_$UserPreferencesImpl(
      notificationPreference: null == notificationPreference
          ? _value.notificationPreference
          : notificationPreference // ignore: cast_nullable_to_non_nullable
              as NotificationPreference,
      darkMode: null == darkMode
          ? _value.darkMode
          : darkMode // ignore: cast_nullable_to_non_nullable
              as bool,
      defaultCurrency: null == defaultCurrency
          ? _value.defaultCurrency
          : defaultCurrency // ignore: cast_nullable_to_non_nullable
              as String,
      timezone: null == timezone
          ? _value.timezone
          : timezone // ignore: cast_nullable_to_non_nullable
              as String,
      language: null == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String,
      showPnLInPercent: null == showPnLInPercent
          ? _value.showPnLInPercent
          : showPnLInPercent // ignore: cast_nullable_to_non_nullable
              as bool,
      autoSyncEnabled: null == autoSyncEnabled
          ? _value.autoSyncEnabled
          : autoSyncEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      profilePublic: null == profilePublic
          ? _value.profilePublic
          : profilePublic // ignore: cast_nullable_to_non_nullable
              as bool,
      showTradeHistory: null == showTradeHistory
          ? _value.showTradeHistory
          : showTradeHistory // ignore: cast_nullable_to_non_nullable
              as bool,
      showProfitLoss: null == showProfitLoss
          ? _value.showProfitLoss
          : showProfitLoss // ignore: cast_nullable_to_non_nullable
              as bool,
      showPortfolioValue: null == showPortfolioValue
          ? _value.showPortfolioValue
          : showPortfolioValue // ignore: cast_nullable_to_non_nullable
              as bool,
      allowDataCollection: null == allowDataCollection
          ? _value.allowDataCollection
          : allowDataCollection // ignore: cast_nullable_to_non_nullable
              as bool,
      allowCrashReports: null == allowCrashReports
          ? _value.allowCrashReports
          : allowCrashReports // ignore: cast_nullable_to_non_nullable
              as bool,
      allowAnalytics: null == allowAnalytics
          ? _value.allowAnalytics
          : allowAnalytics // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserPreferencesImpl implements _UserPreferences {
  const _$UserPreferencesImpl(
      {this.notificationPreference = NotificationPreference.important,
      this.darkMode = false,
      this.defaultCurrency = 'USD',
      this.timezone = 'UTC',
      this.language = 'en',
      this.showPnLInPercent = true,
      this.autoSyncEnabled = true,
      this.profilePublic = false,
      this.showTradeHistory = false,
      this.showProfitLoss = false,
      this.showPortfolioValue = false,
      this.allowDataCollection = true,
      this.allowCrashReports = true,
      this.allowAnalytics = true});

  factory _$UserPreferencesImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserPreferencesImplFromJson(json);

  @override
  @JsonKey()
  final NotificationPreference notificationPreference;
  @override
  @JsonKey()
  final bool darkMode;
  @override
  @JsonKey()
  final String defaultCurrency;
  @override
  @JsonKey()
  final String timezone;
  @override
  @JsonKey()
  final String language;
  @override
  @JsonKey()
  final bool showPnLInPercent;
  @override
  @JsonKey()
  final bool autoSyncEnabled;
// Privacy settings
  @override
  @JsonKey()
  final bool profilePublic;
  @override
  @JsonKey()
  final bool showTradeHistory;
  @override
  @JsonKey()
  final bool showProfitLoss;
  @override
  @JsonKey()
  final bool showPortfolioValue;
  @override
  @JsonKey()
  final bool allowDataCollection;
  @override
  @JsonKey()
  final bool allowCrashReports;
  @override
  @JsonKey()
  final bool allowAnalytics;

  @override
  String toString() {
    return 'UserPreferences(notificationPreference: $notificationPreference, darkMode: $darkMode, defaultCurrency: $defaultCurrency, timezone: $timezone, language: $language, showPnLInPercent: $showPnLInPercent, autoSyncEnabled: $autoSyncEnabled, profilePublic: $profilePublic, showTradeHistory: $showTradeHistory, showProfitLoss: $showProfitLoss, showPortfolioValue: $showPortfolioValue, allowDataCollection: $allowDataCollection, allowCrashReports: $allowCrashReports, allowAnalytics: $allowAnalytics)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserPreferencesImpl &&
            (identical(other.notificationPreference, notificationPreference) ||
                other.notificationPreference == notificationPreference) &&
            (identical(other.darkMode, darkMode) ||
                other.darkMode == darkMode) &&
            (identical(other.defaultCurrency, defaultCurrency) ||
                other.defaultCurrency == defaultCurrency) &&
            (identical(other.timezone, timezone) ||
                other.timezone == timezone) &&
            (identical(other.language, language) ||
                other.language == language) &&
            (identical(other.showPnLInPercent, showPnLInPercent) ||
                other.showPnLInPercent == showPnLInPercent) &&
            (identical(other.autoSyncEnabled, autoSyncEnabled) ||
                other.autoSyncEnabled == autoSyncEnabled) &&
            (identical(other.profilePublic, profilePublic) ||
                other.profilePublic == profilePublic) &&
            (identical(other.showTradeHistory, showTradeHistory) ||
                other.showTradeHistory == showTradeHistory) &&
            (identical(other.showProfitLoss, showProfitLoss) ||
                other.showProfitLoss == showProfitLoss) &&
            (identical(other.showPortfolioValue, showPortfolioValue) ||
                other.showPortfolioValue == showPortfolioValue) &&
            (identical(other.allowDataCollection, allowDataCollection) ||
                other.allowDataCollection == allowDataCollection) &&
            (identical(other.allowCrashReports, allowCrashReports) ||
                other.allowCrashReports == allowCrashReports) &&
            (identical(other.allowAnalytics, allowAnalytics) ||
                other.allowAnalytics == allowAnalytics));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      notificationPreference,
      darkMode,
      defaultCurrency,
      timezone,
      language,
      showPnLInPercent,
      autoSyncEnabled,
      profilePublic,
      showTradeHistory,
      showProfitLoss,
      showPortfolioValue,
      allowDataCollection,
      allowCrashReports,
      allowAnalytics);

  /// Create a copy of UserPreferences
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserPreferencesImplCopyWith<_$UserPreferencesImpl> get copyWith =>
      __$$UserPreferencesImplCopyWithImpl<_$UserPreferencesImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserPreferencesImplToJson(
      this,
    );
  }
}

abstract class _UserPreferences implements UserPreferences {
  const factory _UserPreferences(
      {final NotificationPreference notificationPreference,
      final bool darkMode,
      final String defaultCurrency,
      final String timezone,
      final String language,
      final bool showPnLInPercent,
      final bool autoSyncEnabled,
      final bool profilePublic,
      final bool showTradeHistory,
      final bool showProfitLoss,
      final bool showPortfolioValue,
      final bool allowDataCollection,
      final bool allowCrashReports,
      final bool allowAnalytics}) = _$UserPreferencesImpl;

  factory _UserPreferences.fromJson(Map<String, dynamic> json) =
      _$UserPreferencesImpl.fromJson;

  @override
  NotificationPreference get notificationPreference;
  @override
  bool get darkMode;
  @override
  String get defaultCurrency;
  @override
  String get timezone;
  @override
  String get language;
  @override
  bool get showPnLInPercent;
  @override
  bool get autoSyncEnabled; // Privacy settings
  @override
  bool get profilePublic;
  @override
  bool get showTradeHistory;
  @override
  bool get showProfitLoss;
  @override
  bool get showPortfolioValue;
  @override
  bool get allowDataCollection;
  @override
  bool get allowCrashReports;
  @override
  bool get allowAnalytics;

  /// Create a copy of UserPreferences
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserPreferencesImplCopyWith<_$UserPreferencesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

User _$UserFromJson(Map<String, dynamic> json) {
  return _User.fromJson(json);
}

/// @nodoc
mixin _$User {
  String get id => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  UserRole get role => throw _privateConstructorUsedError;
  UserPreferences get preferences => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  DateTime? get lastLoginAt => throw _privateConstructorUsedError;
  bool get isEmailVerified => throw _privateConstructorUsedError;
  String? get profileImageUrl => throw _privateConstructorUsedError;

  /// Serializes this User to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserCopyWith<User> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserCopyWith<$Res> {
  factory $UserCopyWith(User value, $Res Function(User) then) =
      _$UserCopyWithImpl<$Res, User>;
  @useResult
  $Res call(
      {String id,
      String email,
      String name,
      UserRole role,
      UserPreferences preferences,
      DateTime createdAt,
      DateTime updatedAt,
      DateTime? lastLoginAt,
      bool isEmailVerified,
      String? profileImageUrl});

  $UserPreferencesCopyWith<$Res> get preferences;
}

/// @nodoc
class _$UserCopyWithImpl<$Res, $Val extends User>
    implements $UserCopyWith<$Res> {
  _$UserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? email = null,
    Object? name = null,
    Object? role = null,
    Object? preferences = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? lastLoginAt = freezed,
    Object? isEmailVerified = null,
    Object? profileImageUrl = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      role: null == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as UserRole,
      preferences: null == preferences
          ? _value.preferences
          : preferences // ignore: cast_nullable_to_non_nullable
              as UserPreferences,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      lastLoginAt: freezed == lastLoginAt
          ? _value.lastLoginAt
          : lastLoginAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isEmailVerified: null == isEmailVerified
          ? _value.isEmailVerified
          : isEmailVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      profileImageUrl: freezed == profileImageUrl
          ? _value.profileImageUrl
          : profileImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserPreferencesCopyWith<$Res> get preferences {
    return $UserPreferencesCopyWith<$Res>(_value.preferences, (value) {
      return _then(_value.copyWith(preferences: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UserImplCopyWith<$Res> implements $UserCopyWith<$Res> {
  factory _$$UserImplCopyWith(
          _$UserImpl value, $Res Function(_$UserImpl) then) =
      __$$UserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String email,
      String name,
      UserRole role,
      UserPreferences preferences,
      DateTime createdAt,
      DateTime updatedAt,
      DateTime? lastLoginAt,
      bool isEmailVerified,
      String? profileImageUrl});

  @override
  $UserPreferencesCopyWith<$Res> get preferences;
}

/// @nodoc
class __$$UserImplCopyWithImpl<$Res>
    extends _$UserCopyWithImpl<$Res, _$UserImpl>
    implements _$$UserImplCopyWith<$Res> {
  __$$UserImplCopyWithImpl(_$UserImpl _value, $Res Function(_$UserImpl) _then)
      : super(_value, _then);

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? email = null,
    Object? name = null,
    Object? role = null,
    Object? preferences = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? lastLoginAt = freezed,
    Object? isEmailVerified = null,
    Object? profileImageUrl = freezed,
  }) {
    return _then(_$UserImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      role: null == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as UserRole,
      preferences: null == preferences
          ? _value.preferences
          : preferences // ignore: cast_nullable_to_non_nullable
              as UserPreferences,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      lastLoginAt: freezed == lastLoginAt
          ? _value.lastLoginAt
          : lastLoginAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isEmailVerified: null == isEmailVerified
          ? _value.isEmailVerified
          : isEmailVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      profileImageUrl: freezed == profileImageUrl
          ? _value.profileImageUrl
          : profileImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserImpl extends _User {
  const _$UserImpl(
      {required this.id,
      required this.email,
      required this.name,
      this.role = UserRole.user,
      required this.preferences,
      required this.createdAt,
      required this.updatedAt,
      this.lastLoginAt,
      this.isEmailVerified = false,
      this.profileImageUrl})
      : super._();

  factory _$UserImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserImplFromJson(json);

  @override
  final String id;
  @override
  final String email;
  @override
  final String name;
  @override
  @JsonKey()
  final UserRole role;
  @override
  final UserPreferences preferences;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
  @override
  final DateTime? lastLoginAt;
  @override
  @JsonKey()
  final bool isEmailVerified;
  @override
  final String? profileImageUrl;

  @override
  String toString() {
    return 'User(id: $id, email: $email, name: $name, role: $role, preferences: $preferences, createdAt: $createdAt, updatedAt: $updatedAt, lastLoginAt: $lastLoginAt, isEmailVerified: $isEmailVerified, profileImageUrl: $profileImageUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.preferences, preferences) ||
                other.preferences == preferences) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.lastLoginAt, lastLoginAt) ||
                other.lastLoginAt == lastLoginAt) &&
            (identical(other.isEmailVerified, isEmailVerified) ||
                other.isEmailVerified == isEmailVerified) &&
            (identical(other.profileImageUrl, profileImageUrl) ||
                other.profileImageUrl == profileImageUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      email,
      name,
      role,
      preferences,
      createdAt,
      updatedAt,
      lastLoginAt,
      isEmailVerified,
      profileImageUrl);

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      __$$UserImplCopyWithImpl<_$UserImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserImplToJson(
      this,
    );
  }
}

abstract class _User extends User {
  const factory _User(
      {required final String id,
      required final String email,
      required final String name,
      final UserRole role,
      required final UserPreferences preferences,
      required final DateTime createdAt,
      required final DateTime updatedAt,
      final DateTime? lastLoginAt,
      final bool isEmailVerified,
      final String? profileImageUrl}) = _$UserImpl;
  const _User._() : super._();

  factory _User.fromJson(Map<String, dynamic> json) = _$UserImpl.fromJson;

  @override
  String get id;
  @override
  String get email;
  @override
  String get name;
  @override
  UserRole get role;
  @override
  UserPreferences get preferences;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  DateTime? get lastLoginAt;
  @override
  bool get isEmailVerified;
  @override
  String? get profileImageUrl;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
