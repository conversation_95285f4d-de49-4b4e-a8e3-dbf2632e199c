import 'package:freezed_annotation/freezed_annotation.dart';
import 'notification_settings.dart';

part 'notification.freezed.dart';
part 'notification.g.dart';

enum NotificationType {
  @JsonValue('TRADE_ALERT')
  tradeAlert,
  @JsonValue('PRICE_ALERT')
  priceAlert,
  @JsonValue('GOAL_ACHIEVED')
  goalAchieved,
  @JsonValue('RISK_WARNING')
  riskWarning,
  @<PERSON>sonValue('SYSTEM_UPDATE')
  systemUpdate,
  @JsonValue('BACKUP_REMINDER')
  backupReminder,
  @JsonValue('JOURNAL_REMINDER')
  journalReminder,
  @JsonValue('TRADE_STATS')
  tradeStats
}

enum NotificationPriority {
  @JsonValue('LOW')
  low,
  @JsonValue('MEDIUM')
  medium,
  @JsonValue('HIGH')
  high,
  @JsonValue('URGENT')
  urgent
}

enum NotificationChannel {
  @JsonValue('IN_APP')
  inApp,
  @JsonValue('EMAIL')
  email,
  @JsonValue('PUSH')
  push,
  @JsonValue('SMS')
  sms
}

@freezed
class Notification with _$Notification {
  const factory Notification({
    required String id,
    required String userId,
    required String title,
    required String message,
    required String recipient,
    required DateTime createdAt,
    required NotificationPriority priority,
    required NotificationType type,
    required List<NotificationChannel> channels,
    @Default(false) bool read,
    @Default(false) bool dismissed,
    DateTime? updatedAt,
    DateTime? expiresAt,
    Map<String, dynamic>? data,
  }) = _Notification;

  const Notification._();

  bool get isExpired => expiresAt != null && DateTime.now().isAfter(expiresAt!);

  bool get isActive => !dismissed && !isExpired;

  factory Notification.fromJson(Map<String, dynamic> json) =>
      _$NotificationFromJson(json);

  bool get isUrgent => priority == NotificationPriority.urgent;

  bool get shouldDisplay => !dismissed && !isExpired;

  List<String> get channelPreferences =>
      channels.map((c) => c.toString().split('.').last.toLowerCase()).toList();

  Notification markAsRead() => copyWith(
        read: true,
        updatedAt: DateTime.now(),
      );

  Notification dismiss() => copyWith(
        dismissed: true,
        updatedAt: DateTime.now(),
      );

  Notification updateExpiry(DateTime newDate) => copyWith(
        expiresAt: newDate,
        updatedAt: DateTime.now(),
      );

  Notification addChannel(NotificationChannel channel) {
    if (!channels.contains(channel)) {
      return copyWith(
        channels: [...channels, channel],
        updatedAt: DateTime.now(),
      );
    }
    return this;
  }

  Notification removeChannel(NotificationChannel channel) {
    final index = channels.indexOf(channel);
    if (index > -1) {
      final newChannels = [...channels]..removeAt(index);
      return copyWith(
        channels: newChannels,
        updatedAt: DateTime.now(),
      );
    }
    return this;
  }

  Notification updatePriority(NotificationPriority newPriority) => copyWith(
        priority: newPriority,
        updatedAt: DateTime.now(),
      );
}
