// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'analytics.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TradeMetricsImpl _$$TradeMetricsImplFromJson(Map<String, dynamic> json) =>
    _$TradeMetricsImpl(
      totalTrades: (json['totalTrades'] as num).toInt(),
      winningTrades: (json['winningTrades'] as num).toInt(),
      losingTrades: (json['losingTrades'] as num).toInt(),
      breakEvenTrades: (json['breakEvenTrades'] as num).toInt(),
      winRate: (json['winRate'] as num).toDouble(),
      profitFactor: (json['profitFactor'] as num).toDouble(),
      averageWin: (json['averageWin'] as num).toDouble(),
      averageLoss: (json['averageLoss'] as num).toDouble(),
      largestWin: (json['largestWin'] as num).toDouble(),
      largestLoss: (json['largestLoss'] as num).toDouble(),
      totalPnL: (json['totalPnL'] as num).toDouble(),
      netPnL: (json['netPnL'] as num).toDouble(),
      grossPnL: (json['grossPnL'] as num).toDouble(),
      totalCommissions: (json['totalCommissions'] as num).toDouble(),
      totalFees: (json['totalFees'] as num).toDouble(),
    );

Map<String, dynamic> _$$TradeMetricsImplToJson(_$TradeMetricsImpl instance) =>
    <String, dynamic>{
      'totalTrades': instance.totalTrades,
      'winningTrades': instance.winningTrades,
      'losingTrades': instance.losingTrades,
      'breakEvenTrades': instance.breakEvenTrades,
      'winRate': instance.winRate,
      'profitFactor': instance.profitFactor,
      'averageWin': instance.averageWin,
      'averageLoss': instance.averageLoss,
      'largestWin': instance.largestWin,
      'largestLoss': instance.largestLoss,
      'totalPnL': instance.totalPnL,
      'netPnL': instance.netPnL,
      'grossPnL': instance.grossPnL,
      'totalCommissions': instance.totalCommissions,
      'totalFees': instance.totalFees,
    };

_$AnalyticsImpl _$$AnalyticsImplFromJson(Map<String, dynamic> json) =>
    _$AnalyticsImpl(
      userId: json['userId'] as String,
      timeFrame: $enumDecode(_$TimeFrameEnumMap, json['timeFrame']),
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      metrics: TradeMetrics.fromJson(json['metrics'] as Map<String, dynamic>),
      byType: (json['byType'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, TradeMetrics.fromJson(e as Map<String, dynamic>)),
      ),
      bySymbol: (json['bySymbol'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, TradeMetrics.fromJson(e as Map<String, dynamic>)),
      ),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$AnalyticsImplToJson(_$AnalyticsImpl instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'timeFrame': _$TimeFrameEnumMap[instance.timeFrame]!,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'metrics': instance.metrics,
      'byType': instance.byType,
      'bySymbol': instance.bySymbol,
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$TimeFrameEnumMap = {
  TimeFrame.daily: 'DAILY',
  TimeFrame.weekly: 'WEEKLY',
  TimeFrame.monthly: 'MONTHLY',
  TimeFrame.quarterly: 'QUARTERLY',
  TimeFrame.yearly: 'YEARLY',
  TimeFrame.allTime: 'ALL_TIME',
};
