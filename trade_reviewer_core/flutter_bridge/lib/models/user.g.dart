// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserPreferencesImpl _$$UserPreferencesImplFromJson(
        Map<String, dynamic> json) =>
    _$UserPreferencesImpl(
      notificationPreference: $enumDecodeNullable(
              _$NotificationPreferenceEnumMap,
              json['notificationPreference']) ??
          NotificationPreference.important,
      darkMode: json['darkMode'] as bool? ?? false,
      defaultCurrency: json['defaultCurrency'] as String? ?? 'USD',
      timezone: json['timezone'] as String? ?? 'UTC',
      language: json['language'] as String? ?? 'en',
      showPnLInPercent: json['showPnLInPercent'] as bool? ?? true,
      autoSyncEnabled: json['autoSyncEnabled'] as bool? ?? true,
      profilePublic: json['profilePublic'] as bool? ?? false,
      showTradeHistory: json['showTradeHistory'] as bool? ?? false,
      showProfitLoss: json['showProfitLoss'] as bool? ?? false,
      showPortfolioValue: json['showPortfolioValue'] as bool? ?? false,
      allowDataCollection: json['allowDataCollection'] as bool? ?? true,
      allowCrashReports: json['allowCrashReports'] as bool? ?? true,
      allowAnalytics: json['allowAnalytics'] as bool? ?? true,
    );

Map<String, dynamic> _$$UserPreferencesImplToJson(
        _$UserPreferencesImpl instance) =>
    <String, dynamic>{
      'notificationPreference':
          _$NotificationPreferenceEnumMap[instance.notificationPreference]!,
      'darkMode': instance.darkMode,
      'defaultCurrency': instance.defaultCurrency,
      'timezone': instance.timezone,
      'language': instance.language,
      'showPnLInPercent': instance.showPnLInPercent,
      'autoSyncEnabled': instance.autoSyncEnabled,
      'profilePublic': instance.profilePublic,
      'showTradeHistory': instance.showTradeHistory,
      'showProfitLoss': instance.showProfitLoss,
      'showPortfolioValue': instance.showPortfolioValue,
      'allowDataCollection': instance.allowDataCollection,
      'allowCrashReports': instance.allowCrashReports,
      'allowAnalytics': instance.allowAnalytics,
    };

const _$NotificationPreferenceEnumMap = {
  NotificationPreference.all: 'ALL',
  NotificationPreference.important: 'IMPORTANT',
  NotificationPreference.none: 'NONE',
};

_$UserImpl _$$UserImplFromJson(Map<String, dynamic> json) => _$UserImpl(
      id: json['id'] as String,
      email: json['email'] as String,
      name: json['name'] as String,
      role:
          $enumDecodeNullable(_$UserRoleEnumMap, json['role']) ?? UserRole.user,
      preferences:
          UserPreferences.fromJson(json['preferences'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      lastLoginAt: json['lastLoginAt'] == null
          ? null
          : DateTime.parse(json['lastLoginAt'] as String),
      isEmailVerified: json['isEmailVerified'] as bool? ?? false,
      profileImageUrl: json['profileImageUrl'] as String?,
    );

Map<String, dynamic> _$$UserImplToJson(_$UserImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'name': instance.name,
      'role': _$UserRoleEnumMap[instance.role]!,
      'preferences': instance.preferences,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'lastLoginAt': instance.lastLoginAt?.toIso8601String(),
      'isEmailVerified': instance.isEmailVerified,
      'profileImageUrl': instance.profileImageUrl,
    };

const _$UserRoleEnumMap = {
  UserRole.user: 'USER',
  UserRole.premium: 'PREMIUM',
  UserRole.admin: 'ADMIN',
};
