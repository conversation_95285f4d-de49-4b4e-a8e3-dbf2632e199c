// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'settings.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TradeDefaults _$TradeDefaultsFromJson(Map<String, dynamic> json) {
  return _TradeDefaults.fromJson(json);
}

/// @nodoc
mixin _$TradeDefaults {
  double get defaultRisk => throw _privateConstructorUsedError;
  double get defaultPosition => throw _privateConstructorUsedError;
  ChartTimeframe get defaultTimeframe => throw _privateConstructorUsedError;
  RiskCalculationType get riskCalculationType =>
      throw _privateConstructorUsedError;
  double get defaultCommission => throw _privateConstructorUsedError;
  double get defaultFees => throw _privateConstructorUsedError;

  /// Serializes this TradeDefaults to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TradeDefaults
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TradeDefaultsCopyWith<TradeDefaults> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TradeDefaultsCopyWith<$Res> {
  factory $TradeDefaultsCopyWith(
          TradeDefaults value, $Res Function(TradeDefaults) then) =
      _$TradeDefaultsCopyWithImpl<$Res, TradeDefaults>;
  @useResult
  $Res call(
      {double defaultRisk,
      double defaultPosition,
      ChartTimeframe defaultTimeframe,
      RiskCalculationType riskCalculationType,
      double defaultCommission,
      double defaultFees});
}

/// @nodoc
class _$TradeDefaultsCopyWithImpl<$Res, $Val extends TradeDefaults>
    implements $TradeDefaultsCopyWith<$Res> {
  _$TradeDefaultsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TradeDefaults
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? defaultRisk = null,
    Object? defaultPosition = null,
    Object? defaultTimeframe = null,
    Object? riskCalculationType = null,
    Object? defaultCommission = null,
    Object? defaultFees = null,
  }) {
    return _then(_value.copyWith(
      defaultRisk: null == defaultRisk
          ? _value.defaultRisk
          : defaultRisk // ignore: cast_nullable_to_non_nullable
              as double,
      defaultPosition: null == defaultPosition
          ? _value.defaultPosition
          : defaultPosition // ignore: cast_nullable_to_non_nullable
              as double,
      defaultTimeframe: null == defaultTimeframe
          ? _value.defaultTimeframe
          : defaultTimeframe // ignore: cast_nullable_to_non_nullable
              as ChartTimeframe,
      riskCalculationType: null == riskCalculationType
          ? _value.riskCalculationType
          : riskCalculationType // ignore: cast_nullable_to_non_nullable
              as RiskCalculationType,
      defaultCommission: null == defaultCommission
          ? _value.defaultCommission
          : defaultCommission // ignore: cast_nullable_to_non_nullable
              as double,
      defaultFees: null == defaultFees
          ? _value.defaultFees
          : defaultFees // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TradeDefaultsImplCopyWith<$Res>
    implements $TradeDefaultsCopyWith<$Res> {
  factory _$$TradeDefaultsImplCopyWith(
          _$TradeDefaultsImpl value, $Res Function(_$TradeDefaultsImpl) then) =
      __$$TradeDefaultsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double defaultRisk,
      double defaultPosition,
      ChartTimeframe defaultTimeframe,
      RiskCalculationType riskCalculationType,
      double defaultCommission,
      double defaultFees});
}

/// @nodoc
class __$$TradeDefaultsImplCopyWithImpl<$Res>
    extends _$TradeDefaultsCopyWithImpl<$Res, _$TradeDefaultsImpl>
    implements _$$TradeDefaultsImplCopyWith<$Res> {
  __$$TradeDefaultsImplCopyWithImpl(
      _$TradeDefaultsImpl _value, $Res Function(_$TradeDefaultsImpl) _then)
      : super(_value, _then);

  /// Create a copy of TradeDefaults
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? defaultRisk = null,
    Object? defaultPosition = null,
    Object? defaultTimeframe = null,
    Object? riskCalculationType = null,
    Object? defaultCommission = null,
    Object? defaultFees = null,
  }) {
    return _then(_$TradeDefaultsImpl(
      defaultRisk: null == defaultRisk
          ? _value.defaultRisk
          : defaultRisk // ignore: cast_nullable_to_non_nullable
              as double,
      defaultPosition: null == defaultPosition
          ? _value.defaultPosition
          : defaultPosition // ignore: cast_nullable_to_non_nullable
              as double,
      defaultTimeframe: null == defaultTimeframe
          ? _value.defaultTimeframe
          : defaultTimeframe // ignore: cast_nullable_to_non_nullable
              as ChartTimeframe,
      riskCalculationType: null == riskCalculationType
          ? _value.riskCalculationType
          : riskCalculationType // ignore: cast_nullable_to_non_nullable
              as RiskCalculationType,
      defaultCommission: null == defaultCommission
          ? _value.defaultCommission
          : defaultCommission // ignore: cast_nullable_to_non_nullable
              as double,
      defaultFees: null == defaultFees
          ? _value.defaultFees
          : defaultFees // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TradeDefaultsImpl implements _TradeDefaults {
  const _$TradeDefaultsImpl(
      {required this.defaultRisk,
      required this.defaultPosition,
      required this.defaultTimeframe,
      required this.riskCalculationType,
      required this.defaultCommission,
      required this.defaultFees});

  factory _$TradeDefaultsImpl.fromJson(Map<String, dynamic> json) =>
      _$$TradeDefaultsImplFromJson(json);

  @override
  final double defaultRisk;
  @override
  final double defaultPosition;
  @override
  final ChartTimeframe defaultTimeframe;
  @override
  final RiskCalculationType riskCalculationType;
  @override
  final double defaultCommission;
  @override
  final double defaultFees;

  @override
  String toString() {
    return 'TradeDefaults(defaultRisk: $defaultRisk, defaultPosition: $defaultPosition, defaultTimeframe: $defaultTimeframe, riskCalculationType: $riskCalculationType, defaultCommission: $defaultCommission, defaultFees: $defaultFees)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TradeDefaultsImpl &&
            (identical(other.defaultRisk, defaultRisk) ||
                other.defaultRisk == defaultRisk) &&
            (identical(other.defaultPosition, defaultPosition) ||
                other.defaultPosition == defaultPosition) &&
            (identical(other.defaultTimeframe, defaultTimeframe) ||
                other.defaultTimeframe == defaultTimeframe) &&
            (identical(other.riskCalculationType, riskCalculationType) ||
                other.riskCalculationType == riskCalculationType) &&
            (identical(other.defaultCommission, defaultCommission) ||
                other.defaultCommission == defaultCommission) &&
            (identical(other.defaultFees, defaultFees) ||
                other.defaultFees == defaultFees));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, defaultRisk, defaultPosition,
      defaultTimeframe, riskCalculationType, defaultCommission, defaultFees);

  /// Create a copy of TradeDefaults
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TradeDefaultsImplCopyWith<_$TradeDefaultsImpl> get copyWith =>
      __$$TradeDefaultsImplCopyWithImpl<_$TradeDefaultsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TradeDefaultsImplToJson(
      this,
    );
  }
}

abstract class _TradeDefaults implements TradeDefaults {
  const factory _TradeDefaults(
      {required final double defaultRisk,
      required final double defaultPosition,
      required final ChartTimeframe defaultTimeframe,
      required final RiskCalculationType riskCalculationType,
      required final double defaultCommission,
      required final double defaultFees}) = _$TradeDefaultsImpl;

  factory _TradeDefaults.fromJson(Map<String, dynamic> json) =
      _$TradeDefaultsImpl.fromJson;

  @override
  double get defaultRisk;
  @override
  double get defaultPosition;
  @override
  ChartTimeframe get defaultTimeframe;
  @override
  RiskCalculationType get riskCalculationType;
  @override
  double get defaultCommission;
  @override
  double get defaultFees;

  /// Create a copy of TradeDefaults
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TradeDefaultsImplCopyWith<_$TradeDefaultsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DisplaySettings _$DisplaySettingsFromJson(Map<String, dynamic> json) {
  return _DisplaySettings.fromJson(json);
}

/// @nodoc
mixin _$DisplaySettings {
  String get theme => throw _privateConstructorUsedError;
  PnLDisplayMode get pnlDisplayMode => throw _privateConstructorUsedError;
  bool get showRunningPnL => throw _privateConstructorUsedError;
  bool get showEquityCurve => throw _privateConstructorUsedError;
  bool get showTradeStatistics => throw _privateConstructorUsedError;
  bool get compactMode => throw _privateConstructorUsedError;
  String get dateFormat => throw _privateConstructorUsedError;
  String get timeFormat => throw _privateConstructorUsedError;
  String get timezone => throw _privateConstructorUsedError;
  bool get goalNotificationsEnabled => throw _privateConstructorUsedError;
  bool get weeklyReportsEnabled => throw _privateConstructorUsedError;
  bool get monthlyReportsEnabled => throw _privateConstructorUsedError;
  bool get tradeAlertsEnabled => throw _privateConstructorUsedError;
  bool get marketNewsEnabled => throw _privateConstructorUsedError;
  bool get priceAlertsEnabled => throw _privateConstructorUsedError;
  bool get journalRemindersEnabled => throw _privateConstructorUsedError;
  bool get emailNotificationsEnabled => throw _privateConstructorUsedError;
  bool get pushNotificationsEnabled => throw _privateConstructorUsedError;
  bool get smsNotificationsEnabled => throw _privateConstructorUsedError;
  String get defaultAccountView => throw _privateConstructorUsedError;
  String get defaultTimePeriod => throw _privateConstructorUsedError;

  /// Serializes this DisplaySettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DisplaySettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DisplaySettingsCopyWith<DisplaySettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DisplaySettingsCopyWith<$Res> {
  factory $DisplaySettingsCopyWith(
          DisplaySettings value, $Res Function(DisplaySettings) then) =
      _$DisplaySettingsCopyWithImpl<$Res, DisplaySettings>;
  @useResult
  $Res call(
      {String theme,
      PnLDisplayMode pnlDisplayMode,
      bool showRunningPnL,
      bool showEquityCurve,
      bool showTradeStatistics,
      bool compactMode,
      String dateFormat,
      String timeFormat,
      String timezone,
      bool goalNotificationsEnabled,
      bool weeklyReportsEnabled,
      bool monthlyReportsEnabled,
      bool tradeAlertsEnabled,
      bool marketNewsEnabled,
      bool priceAlertsEnabled,
      bool journalRemindersEnabled,
      bool emailNotificationsEnabled,
      bool pushNotificationsEnabled,
      bool smsNotificationsEnabled,
      String defaultAccountView,
      String defaultTimePeriod});
}

/// @nodoc
class _$DisplaySettingsCopyWithImpl<$Res, $Val extends DisplaySettings>
    implements $DisplaySettingsCopyWith<$Res> {
  _$DisplaySettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DisplaySettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? theme = null,
    Object? pnlDisplayMode = null,
    Object? showRunningPnL = null,
    Object? showEquityCurve = null,
    Object? showTradeStatistics = null,
    Object? compactMode = null,
    Object? dateFormat = null,
    Object? timeFormat = null,
    Object? timezone = null,
    Object? goalNotificationsEnabled = null,
    Object? weeklyReportsEnabled = null,
    Object? monthlyReportsEnabled = null,
    Object? tradeAlertsEnabled = null,
    Object? marketNewsEnabled = null,
    Object? priceAlertsEnabled = null,
    Object? journalRemindersEnabled = null,
    Object? emailNotificationsEnabled = null,
    Object? pushNotificationsEnabled = null,
    Object? smsNotificationsEnabled = null,
    Object? defaultAccountView = null,
    Object? defaultTimePeriod = null,
  }) {
    return _then(_value.copyWith(
      theme: null == theme
          ? _value.theme
          : theme // ignore: cast_nullable_to_non_nullable
              as String,
      pnlDisplayMode: null == pnlDisplayMode
          ? _value.pnlDisplayMode
          : pnlDisplayMode // ignore: cast_nullable_to_non_nullable
              as PnLDisplayMode,
      showRunningPnL: null == showRunningPnL
          ? _value.showRunningPnL
          : showRunningPnL // ignore: cast_nullable_to_non_nullable
              as bool,
      showEquityCurve: null == showEquityCurve
          ? _value.showEquityCurve
          : showEquityCurve // ignore: cast_nullable_to_non_nullable
              as bool,
      showTradeStatistics: null == showTradeStatistics
          ? _value.showTradeStatistics
          : showTradeStatistics // ignore: cast_nullable_to_non_nullable
              as bool,
      compactMode: null == compactMode
          ? _value.compactMode
          : compactMode // ignore: cast_nullable_to_non_nullable
              as bool,
      dateFormat: null == dateFormat
          ? _value.dateFormat
          : dateFormat // ignore: cast_nullable_to_non_nullable
              as String,
      timeFormat: null == timeFormat
          ? _value.timeFormat
          : timeFormat // ignore: cast_nullable_to_non_nullable
              as String,
      timezone: null == timezone
          ? _value.timezone
          : timezone // ignore: cast_nullable_to_non_nullable
              as String,
      goalNotificationsEnabled: null == goalNotificationsEnabled
          ? _value.goalNotificationsEnabled
          : goalNotificationsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      weeklyReportsEnabled: null == weeklyReportsEnabled
          ? _value.weeklyReportsEnabled
          : weeklyReportsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      monthlyReportsEnabled: null == monthlyReportsEnabled
          ? _value.monthlyReportsEnabled
          : monthlyReportsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      tradeAlertsEnabled: null == tradeAlertsEnabled
          ? _value.tradeAlertsEnabled
          : tradeAlertsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      marketNewsEnabled: null == marketNewsEnabled
          ? _value.marketNewsEnabled
          : marketNewsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      priceAlertsEnabled: null == priceAlertsEnabled
          ? _value.priceAlertsEnabled
          : priceAlertsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      journalRemindersEnabled: null == journalRemindersEnabled
          ? _value.journalRemindersEnabled
          : journalRemindersEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      emailNotificationsEnabled: null == emailNotificationsEnabled
          ? _value.emailNotificationsEnabled
          : emailNotificationsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      pushNotificationsEnabled: null == pushNotificationsEnabled
          ? _value.pushNotificationsEnabled
          : pushNotificationsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      smsNotificationsEnabled: null == smsNotificationsEnabled
          ? _value.smsNotificationsEnabled
          : smsNotificationsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      defaultAccountView: null == defaultAccountView
          ? _value.defaultAccountView
          : defaultAccountView // ignore: cast_nullable_to_non_nullable
              as String,
      defaultTimePeriod: null == defaultTimePeriod
          ? _value.defaultTimePeriod
          : defaultTimePeriod // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DisplaySettingsImplCopyWith<$Res>
    implements $DisplaySettingsCopyWith<$Res> {
  factory _$$DisplaySettingsImplCopyWith(_$DisplaySettingsImpl value,
          $Res Function(_$DisplaySettingsImpl) then) =
      __$$DisplaySettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String theme,
      PnLDisplayMode pnlDisplayMode,
      bool showRunningPnL,
      bool showEquityCurve,
      bool showTradeStatistics,
      bool compactMode,
      String dateFormat,
      String timeFormat,
      String timezone,
      bool goalNotificationsEnabled,
      bool weeklyReportsEnabled,
      bool monthlyReportsEnabled,
      bool tradeAlertsEnabled,
      bool marketNewsEnabled,
      bool priceAlertsEnabled,
      bool journalRemindersEnabled,
      bool emailNotificationsEnabled,
      bool pushNotificationsEnabled,
      bool smsNotificationsEnabled,
      String defaultAccountView,
      String defaultTimePeriod});
}

/// @nodoc
class __$$DisplaySettingsImplCopyWithImpl<$Res>
    extends _$DisplaySettingsCopyWithImpl<$Res, _$DisplaySettingsImpl>
    implements _$$DisplaySettingsImplCopyWith<$Res> {
  __$$DisplaySettingsImplCopyWithImpl(
      _$DisplaySettingsImpl _value, $Res Function(_$DisplaySettingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of DisplaySettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? theme = null,
    Object? pnlDisplayMode = null,
    Object? showRunningPnL = null,
    Object? showEquityCurve = null,
    Object? showTradeStatistics = null,
    Object? compactMode = null,
    Object? dateFormat = null,
    Object? timeFormat = null,
    Object? timezone = null,
    Object? goalNotificationsEnabled = null,
    Object? weeklyReportsEnabled = null,
    Object? monthlyReportsEnabled = null,
    Object? tradeAlertsEnabled = null,
    Object? marketNewsEnabled = null,
    Object? priceAlertsEnabled = null,
    Object? journalRemindersEnabled = null,
    Object? emailNotificationsEnabled = null,
    Object? pushNotificationsEnabled = null,
    Object? smsNotificationsEnabled = null,
    Object? defaultAccountView = null,
    Object? defaultTimePeriod = null,
  }) {
    return _then(_$DisplaySettingsImpl(
      theme: null == theme
          ? _value.theme
          : theme // ignore: cast_nullable_to_non_nullable
              as String,
      pnlDisplayMode: null == pnlDisplayMode
          ? _value.pnlDisplayMode
          : pnlDisplayMode // ignore: cast_nullable_to_non_nullable
              as PnLDisplayMode,
      showRunningPnL: null == showRunningPnL
          ? _value.showRunningPnL
          : showRunningPnL // ignore: cast_nullable_to_non_nullable
              as bool,
      showEquityCurve: null == showEquityCurve
          ? _value.showEquityCurve
          : showEquityCurve // ignore: cast_nullable_to_non_nullable
              as bool,
      showTradeStatistics: null == showTradeStatistics
          ? _value.showTradeStatistics
          : showTradeStatistics // ignore: cast_nullable_to_non_nullable
              as bool,
      compactMode: null == compactMode
          ? _value.compactMode
          : compactMode // ignore: cast_nullable_to_non_nullable
              as bool,
      dateFormat: null == dateFormat
          ? _value.dateFormat
          : dateFormat // ignore: cast_nullable_to_non_nullable
              as String,
      timeFormat: null == timeFormat
          ? _value.timeFormat
          : timeFormat // ignore: cast_nullable_to_non_nullable
              as String,
      timezone: null == timezone
          ? _value.timezone
          : timezone // ignore: cast_nullable_to_non_nullable
              as String,
      goalNotificationsEnabled: null == goalNotificationsEnabled
          ? _value.goalNotificationsEnabled
          : goalNotificationsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      weeklyReportsEnabled: null == weeklyReportsEnabled
          ? _value.weeklyReportsEnabled
          : weeklyReportsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      monthlyReportsEnabled: null == monthlyReportsEnabled
          ? _value.monthlyReportsEnabled
          : monthlyReportsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      tradeAlertsEnabled: null == tradeAlertsEnabled
          ? _value.tradeAlertsEnabled
          : tradeAlertsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      marketNewsEnabled: null == marketNewsEnabled
          ? _value.marketNewsEnabled
          : marketNewsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      priceAlertsEnabled: null == priceAlertsEnabled
          ? _value.priceAlertsEnabled
          : priceAlertsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      journalRemindersEnabled: null == journalRemindersEnabled
          ? _value.journalRemindersEnabled
          : journalRemindersEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      emailNotificationsEnabled: null == emailNotificationsEnabled
          ? _value.emailNotificationsEnabled
          : emailNotificationsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      pushNotificationsEnabled: null == pushNotificationsEnabled
          ? _value.pushNotificationsEnabled
          : pushNotificationsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      smsNotificationsEnabled: null == smsNotificationsEnabled
          ? _value.smsNotificationsEnabled
          : smsNotificationsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      defaultAccountView: null == defaultAccountView
          ? _value.defaultAccountView
          : defaultAccountView // ignore: cast_nullable_to_non_nullable
              as String,
      defaultTimePeriod: null == defaultTimePeriod
          ? _value.defaultTimePeriod
          : defaultTimePeriod // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DisplaySettingsImpl implements _DisplaySettings {
  const _$DisplaySettingsImpl(
      {required this.theme,
      required this.pnlDisplayMode,
      required this.showRunningPnL,
      required this.showEquityCurve,
      required this.showTradeStatistics,
      required this.compactMode,
      required this.dateFormat,
      required this.timeFormat,
      required this.timezone,
      this.goalNotificationsEnabled = true,
      this.weeklyReportsEnabled = true,
      this.monthlyReportsEnabled = true,
      this.tradeAlertsEnabled = true,
      this.marketNewsEnabled = true,
      this.priceAlertsEnabled = true,
      this.journalRemindersEnabled = true,
      this.emailNotificationsEnabled = true,
      this.pushNotificationsEnabled = true,
      this.smsNotificationsEnabled = false,
      this.defaultAccountView = 'all',
      this.defaultTimePeriod = 'month'});

  factory _$DisplaySettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$DisplaySettingsImplFromJson(json);

  @override
  final String theme;
  @override
  final PnLDisplayMode pnlDisplayMode;
  @override
  final bool showRunningPnL;
  @override
  final bool showEquityCurve;
  @override
  final bool showTradeStatistics;
  @override
  final bool compactMode;
  @override
  final String dateFormat;
  @override
  final String timeFormat;
  @override
  final String timezone;
  @override
  @JsonKey()
  final bool goalNotificationsEnabled;
  @override
  @JsonKey()
  final bool weeklyReportsEnabled;
  @override
  @JsonKey()
  final bool monthlyReportsEnabled;
  @override
  @JsonKey()
  final bool tradeAlertsEnabled;
  @override
  @JsonKey()
  final bool marketNewsEnabled;
  @override
  @JsonKey()
  final bool priceAlertsEnabled;
  @override
  @JsonKey()
  final bool journalRemindersEnabled;
  @override
  @JsonKey()
  final bool emailNotificationsEnabled;
  @override
  @JsonKey()
  final bool pushNotificationsEnabled;
  @override
  @JsonKey()
  final bool smsNotificationsEnabled;
  @override
  @JsonKey()
  final String defaultAccountView;
  @override
  @JsonKey()
  final String defaultTimePeriod;

  @override
  String toString() {
    return 'DisplaySettings(theme: $theme, pnlDisplayMode: $pnlDisplayMode, showRunningPnL: $showRunningPnL, showEquityCurve: $showEquityCurve, showTradeStatistics: $showTradeStatistics, compactMode: $compactMode, dateFormat: $dateFormat, timeFormat: $timeFormat, timezone: $timezone, goalNotificationsEnabled: $goalNotificationsEnabled, weeklyReportsEnabled: $weeklyReportsEnabled, monthlyReportsEnabled: $monthlyReportsEnabled, tradeAlertsEnabled: $tradeAlertsEnabled, marketNewsEnabled: $marketNewsEnabled, priceAlertsEnabled: $priceAlertsEnabled, journalRemindersEnabled: $journalRemindersEnabled, emailNotificationsEnabled: $emailNotificationsEnabled, pushNotificationsEnabled: $pushNotificationsEnabled, smsNotificationsEnabled: $smsNotificationsEnabled, defaultAccountView: $defaultAccountView, defaultTimePeriod: $defaultTimePeriod)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DisplaySettingsImpl &&
            (identical(other.theme, theme) || other.theme == theme) &&
            (identical(other.pnlDisplayMode, pnlDisplayMode) ||
                other.pnlDisplayMode == pnlDisplayMode) &&
            (identical(other.showRunningPnL, showRunningPnL) ||
                other.showRunningPnL == showRunningPnL) &&
            (identical(other.showEquityCurve, showEquityCurve) ||
                other.showEquityCurve == showEquityCurve) &&
            (identical(other.showTradeStatistics, showTradeStatistics) ||
                other.showTradeStatistics == showTradeStatistics) &&
            (identical(other.compactMode, compactMode) ||
                other.compactMode == compactMode) &&
            (identical(other.dateFormat, dateFormat) ||
                other.dateFormat == dateFormat) &&
            (identical(other.timeFormat, timeFormat) ||
                other.timeFormat == timeFormat) &&
            (identical(other.timezone, timezone) ||
                other.timezone == timezone) &&
            (identical(
                    other.goalNotificationsEnabled, goalNotificationsEnabled) ||
                other.goalNotificationsEnabled == goalNotificationsEnabled) &&
            (identical(other.weeklyReportsEnabled, weeklyReportsEnabled) ||
                other.weeklyReportsEnabled == weeklyReportsEnabled) &&
            (identical(other.monthlyReportsEnabled, monthlyReportsEnabled) ||
                other.monthlyReportsEnabled == monthlyReportsEnabled) &&
            (identical(other.tradeAlertsEnabled, tradeAlertsEnabled) ||
                other.tradeAlertsEnabled == tradeAlertsEnabled) &&
            (identical(other.marketNewsEnabled, marketNewsEnabled) ||
                other.marketNewsEnabled == marketNewsEnabled) &&
            (identical(other.priceAlertsEnabled, priceAlertsEnabled) ||
                other.priceAlertsEnabled == priceAlertsEnabled) &&
            (identical(
                    other.journalRemindersEnabled, journalRemindersEnabled) ||
                other.journalRemindersEnabled == journalRemindersEnabled) &&
            (identical(other.emailNotificationsEnabled,
                    emailNotificationsEnabled) ||
                other.emailNotificationsEnabled == emailNotificationsEnabled) &&
            (identical(
                    other.pushNotificationsEnabled, pushNotificationsEnabled) ||
                other.pushNotificationsEnabled == pushNotificationsEnabled) &&
            (identical(
                    other.smsNotificationsEnabled, smsNotificationsEnabled) ||
                other.smsNotificationsEnabled == smsNotificationsEnabled) &&
            (identical(other.defaultAccountView, defaultAccountView) ||
                other.defaultAccountView == defaultAccountView) &&
            (identical(other.defaultTimePeriod, defaultTimePeriod) ||
                other.defaultTimePeriod == defaultTimePeriod));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        theme,
        pnlDisplayMode,
        showRunningPnL,
        showEquityCurve,
        showTradeStatistics,
        compactMode,
        dateFormat,
        timeFormat,
        timezone,
        goalNotificationsEnabled,
        weeklyReportsEnabled,
        monthlyReportsEnabled,
        tradeAlertsEnabled,
        marketNewsEnabled,
        priceAlertsEnabled,
        journalRemindersEnabled,
        emailNotificationsEnabled,
        pushNotificationsEnabled,
        smsNotificationsEnabled,
        defaultAccountView,
        defaultTimePeriod
      ]);

  /// Create a copy of DisplaySettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DisplaySettingsImplCopyWith<_$DisplaySettingsImpl> get copyWith =>
      __$$DisplaySettingsImplCopyWithImpl<_$DisplaySettingsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DisplaySettingsImplToJson(
      this,
    );
  }
}

abstract class _DisplaySettings implements DisplaySettings {
  const factory _DisplaySettings(
      {required final String theme,
      required final PnLDisplayMode pnlDisplayMode,
      required final bool showRunningPnL,
      required final bool showEquityCurve,
      required final bool showTradeStatistics,
      required final bool compactMode,
      required final String dateFormat,
      required final String timeFormat,
      required final String timezone,
      final bool goalNotificationsEnabled,
      final bool weeklyReportsEnabled,
      final bool monthlyReportsEnabled,
      final bool tradeAlertsEnabled,
      final bool marketNewsEnabled,
      final bool priceAlertsEnabled,
      final bool journalRemindersEnabled,
      final bool emailNotificationsEnabled,
      final bool pushNotificationsEnabled,
      final bool smsNotificationsEnabled,
      final String defaultAccountView,
      final String defaultTimePeriod}) = _$DisplaySettingsImpl;

  factory _DisplaySettings.fromJson(Map<String, dynamic> json) =
      _$DisplaySettingsImpl.fromJson;

  @override
  String get theme;
  @override
  PnLDisplayMode get pnlDisplayMode;
  @override
  bool get showRunningPnL;
  @override
  bool get showEquityCurve;
  @override
  bool get showTradeStatistics;
  @override
  bool get compactMode;
  @override
  String get dateFormat;
  @override
  String get timeFormat;
  @override
  String get timezone;
  @override
  bool get goalNotificationsEnabled;
  @override
  bool get weeklyReportsEnabled;
  @override
  bool get monthlyReportsEnabled;
  @override
  bool get tradeAlertsEnabled;
  @override
  bool get marketNewsEnabled;
  @override
  bool get priceAlertsEnabled;
  @override
  bool get journalRemindersEnabled;
  @override
  bool get emailNotificationsEnabled;
  @override
  bool get pushNotificationsEnabled;
  @override
  bool get smsNotificationsEnabled;
  @override
  String get defaultAccountView;
  @override
  String get defaultTimePeriod;

  /// Create a copy of DisplaySettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DisplaySettingsImplCopyWith<_$DisplaySettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

BacktestSettings _$BacktestSettingsFromJson(Map<String, dynamic> json) {
  return _BacktestSettings.fromJson(json);
}

/// @nodoc
mixin _$BacktestSettings {
  double get initialCapital => throw _privateConstructorUsedError;
  bool get useFixedPosition => throw _privateConstructorUsedError;
  double get maxRiskPerTrade => throw _privateConstructorUsedError;
  int get maxOpenTrades => throw _privateConstructorUsedError;
  bool get includeFees => throw _privateConstructorUsedError;
  bool get includeSlippage => throw _privateConstructorUsedError;
  double get slippageAmount => throw _privateConstructorUsedError;

  /// Serializes this BacktestSettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BacktestSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BacktestSettingsCopyWith<BacktestSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BacktestSettingsCopyWith<$Res> {
  factory $BacktestSettingsCopyWith(
          BacktestSettings value, $Res Function(BacktestSettings) then) =
      _$BacktestSettingsCopyWithImpl<$Res, BacktestSettings>;
  @useResult
  $Res call(
      {double initialCapital,
      bool useFixedPosition,
      double maxRiskPerTrade,
      int maxOpenTrades,
      bool includeFees,
      bool includeSlippage,
      double slippageAmount});
}

/// @nodoc
class _$BacktestSettingsCopyWithImpl<$Res, $Val extends BacktestSettings>
    implements $BacktestSettingsCopyWith<$Res> {
  _$BacktestSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BacktestSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? initialCapital = null,
    Object? useFixedPosition = null,
    Object? maxRiskPerTrade = null,
    Object? maxOpenTrades = null,
    Object? includeFees = null,
    Object? includeSlippage = null,
    Object? slippageAmount = null,
  }) {
    return _then(_value.copyWith(
      initialCapital: null == initialCapital
          ? _value.initialCapital
          : initialCapital // ignore: cast_nullable_to_non_nullable
              as double,
      useFixedPosition: null == useFixedPosition
          ? _value.useFixedPosition
          : useFixedPosition // ignore: cast_nullable_to_non_nullable
              as bool,
      maxRiskPerTrade: null == maxRiskPerTrade
          ? _value.maxRiskPerTrade
          : maxRiskPerTrade // ignore: cast_nullable_to_non_nullable
              as double,
      maxOpenTrades: null == maxOpenTrades
          ? _value.maxOpenTrades
          : maxOpenTrades // ignore: cast_nullable_to_non_nullable
              as int,
      includeFees: null == includeFees
          ? _value.includeFees
          : includeFees // ignore: cast_nullable_to_non_nullable
              as bool,
      includeSlippage: null == includeSlippage
          ? _value.includeSlippage
          : includeSlippage // ignore: cast_nullable_to_non_nullable
              as bool,
      slippageAmount: null == slippageAmount
          ? _value.slippageAmount
          : slippageAmount // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BacktestSettingsImplCopyWith<$Res>
    implements $BacktestSettingsCopyWith<$Res> {
  factory _$$BacktestSettingsImplCopyWith(_$BacktestSettingsImpl value,
          $Res Function(_$BacktestSettingsImpl) then) =
      __$$BacktestSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double initialCapital,
      bool useFixedPosition,
      double maxRiskPerTrade,
      int maxOpenTrades,
      bool includeFees,
      bool includeSlippage,
      double slippageAmount});
}

/// @nodoc
class __$$BacktestSettingsImplCopyWithImpl<$Res>
    extends _$BacktestSettingsCopyWithImpl<$Res, _$BacktestSettingsImpl>
    implements _$$BacktestSettingsImplCopyWith<$Res> {
  __$$BacktestSettingsImplCopyWithImpl(_$BacktestSettingsImpl _value,
      $Res Function(_$BacktestSettingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of BacktestSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? initialCapital = null,
    Object? useFixedPosition = null,
    Object? maxRiskPerTrade = null,
    Object? maxOpenTrades = null,
    Object? includeFees = null,
    Object? includeSlippage = null,
    Object? slippageAmount = null,
  }) {
    return _then(_$BacktestSettingsImpl(
      initialCapital: null == initialCapital
          ? _value.initialCapital
          : initialCapital // ignore: cast_nullable_to_non_nullable
              as double,
      useFixedPosition: null == useFixedPosition
          ? _value.useFixedPosition
          : useFixedPosition // ignore: cast_nullable_to_non_nullable
              as bool,
      maxRiskPerTrade: null == maxRiskPerTrade
          ? _value.maxRiskPerTrade
          : maxRiskPerTrade // ignore: cast_nullable_to_non_nullable
              as double,
      maxOpenTrades: null == maxOpenTrades
          ? _value.maxOpenTrades
          : maxOpenTrades // ignore: cast_nullable_to_non_nullable
              as int,
      includeFees: null == includeFees
          ? _value.includeFees
          : includeFees // ignore: cast_nullable_to_non_nullable
              as bool,
      includeSlippage: null == includeSlippage
          ? _value.includeSlippage
          : includeSlippage // ignore: cast_nullable_to_non_nullable
              as bool,
      slippageAmount: null == slippageAmount
          ? _value.slippageAmount
          : slippageAmount // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BacktestSettingsImpl implements _BacktestSettings {
  const _$BacktestSettingsImpl(
      {required this.initialCapital,
      required this.useFixedPosition,
      required this.maxRiskPerTrade,
      required this.maxOpenTrades,
      required this.includeFees,
      required this.includeSlippage,
      required this.slippageAmount});

  factory _$BacktestSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$BacktestSettingsImplFromJson(json);

  @override
  final double initialCapital;
  @override
  final bool useFixedPosition;
  @override
  final double maxRiskPerTrade;
  @override
  final int maxOpenTrades;
  @override
  final bool includeFees;
  @override
  final bool includeSlippage;
  @override
  final double slippageAmount;

  @override
  String toString() {
    return 'BacktestSettings(initialCapital: $initialCapital, useFixedPosition: $useFixedPosition, maxRiskPerTrade: $maxRiskPerTrade, maxOpenTrades: $maxOpenTrades, includeFees: $includeFees, includeSlippage: $includeSlippage, slippageAmount: $slippageAmount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BacktestSettingsImpl &&
            (identical(other.initialCapital, initialCapital) ||
                other.initialCapital == initialCapital) &&
            (identical(other.useFixedPosition, useFixedPosition) ||
                other.useFixedPosition == useFixedPosition) &&
            (identical(other.maxRiskPerTrade, maxRiskPerTrade) ||
                other.maxRiskPerTrade == maxRiskPerTrade) &&
            (identical(other.maxOpenTrades, maxOpenTrades) ||
                other.maxOpenTrades == maxOpenTrades) &&
            (identical(other.includeFees, includeFees) ||
                other.includeFees == includeFees) &&
            (identical(other.includeSlippage, includeSlippage) ||
                other.includeSlippage == includeSlippage) &&
            (identical(other.slippageAmount, slippageAmount) ||
                other.slippageAmount == slippageAmount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      initialCapital,
      useFixedPosition,
      maxRiskPerTrade,
      maxOpenTrades,
      includeFees,
      includeSlippage,
      slippageAmount);

  /// Create a copy of BacktestSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BacktestSettingsImplCopyWith<_$BacktestSettingsImpl> get copyWith =>
      __$$BacktestSettingsImplCopyWithImpl<_$BacktestSettingsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BacktestSettingsImplToJson(
      this,
    );
  }
}

abstract class _BacktestSettings implements BacktestSettings {
  const factory _BacktestSettings(
      {required final double initialCapital,
      required final bool useFixedPosition,
      required final double maxRiskPerTrade,
      required final int maxOpenTrades,
      required final bool includeFees,
      required final bool includeSlippage,
      required final double slippageAmount}) = _$BacktestSettingsImpl;

  factory _BacktestSettings.fromJson(Map<String, dynamic> json) =
      _$BacktestSettingsImpl.fromJson;

  @override
  double get initialCapital;
  @override
  bool get useFixedPosition;
  @override
  double get maxRiskPerTrade;
  @override
  int get maxOpenTrades;
  @override
  bool get includeFees;
  @override
  bool get includeSlippage;
  @override
  double get slippageAmount;

  /// Create a copy of BacktestSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BacktestSettingsImplCopyWith<_$BacktestSettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

GoalSettings _$GoalSettingsFromJson(Map<String, dynamic> json) {
  return _GoalSettings.fromJson(json);
}

/// @nodoc
mixin _$GoalSettings {
  Map<String, bool> get activeMetrics => throw _privateConstructorUsedError;
  Map<String, double> get metricGoals => throw _privateConstructorUsedError;

  /// Serializes this GoalSettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of GoalSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GoalSettingsCopyWith<GoalSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GoalSettingsCopyWith<$Res> {
  factory $GoalSettingsCopyWith(
          GoalSettings value, $Res Function(GoalSettings) then) =
      _$GoalSettingsCopyWithImpl<$Res, GoalSettings>;
  @useResult
  $Res call({Map<String, bool> activeMetrics, Map<String, double> metricGoals});
}

/// @nodoc
class _$GoalSettingsCopyWithImpl<$Res, $Val extends GoalSettings>
    implements $GoalSettingsCopyWith<$Res> {
  _$GoalSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of GoalSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activeMetrics = null,
    Object? metricGoals = null,
  }) {
    return _then(_value.copyWith(
      activeMetrics: null == activeMetrics
          ? _value.activeMetrics
          : activeMetrics // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
      metricGoals: null == metricGoals
          ? _value.metricGoals
          : metricGoals // ignore: cast_nullable_to_non_nullable
              as Map<String, double>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GoalSettingsImplCopyWith<$Res>
    implements $GoalSettingsCopyWith<$Res> {
  factory _$$GoalSettingsImplCopyWith(
          _$GoalSettingsImpl value, $Res Function(_$GoalSettingsImpl) then) =
      __$$GoalSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Map<String, bool> activeMetrics, Map<String, double> metricGoals});
}

/// @nodoc
class __$$GoalSettingsImplCopyWithImpl<$Res>
    extends _$GoalSettingsCopyWithImpl<$Res, _$GoalSettingsImpl>
    implements _$$GoalSettingsImplCopyWith<$Res> {
  __$$GoalSettingsImplCopyWithImpl(
      _$GoalSettingsImpl _value, $Res Function(_$GoalSettingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of GoalSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activeMetrics = null,
    Object? metricGoals = null,
  }) {
    return _then(_$GoalSettingsImpl(
      activeMetrics: null == activeMetrics
          ? _value._activeMetrics
          : activeMetrics // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
      metricGoals: null == metricGoals
          ? _value._metricGoals
          : metricGoals // ignore: cast_nullable_to_non_nullable
              as Map<String, double>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GoalSettingsImpl implements _GoalSettings {
  const _$GoalSettingsImpl(
      {required final Map<String, bool> activeMetrics,
      required final Map<String, double> metricGoals})
      : _activeMetrics = activeMetrics,
        _metricGoals = metricGoals;

  factory _$GoalSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$GoalSettingsImplFromJson(json);

  final Map<String, bool> _activeMetrics;
  @override
  Map<String, bool> get activeMetrics {
    if (_activeMetrics is EqualUnmodifiableMapView) return _activeMetrics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_activeMetrics);
  }

  final Map<String, double> _metricGoals;
  @override
  Map<String, double> get metricGoals {
    if (_metricGoals is EqualUnmodifiableMapView) return _metricGoals;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metricGoals);
  }

  @override
  String toString() {
    return 'GoalSettings(activeMetrics: $activeMetrics, metricGoals: $metricGoals)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GoalSettingsImpl &&
            const DeepCollectionEquality()
                .equals(other._activeMetrics, _activeMetrics) &&
            const DeepCollectionEquality()
                .equals(other._metricGoals, _metricGoals));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_activeMetrics),
      const DeepCollectionEquality().hash(_metricGoals));

  /// Create a copy of GoalSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GoalSettingsImplCopyWith<_$GoalSettingsImpl> get copyWith =>
      __$$GoalSettingsImplCopyWithImpl<_$GoalSettingsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GoalSettingsImplToJson(
      this,
    );
  }
}

abstract class _GoalSettings implements GoalSettings {
  const factory _GoalSettings(
      {required final Map<String, bool> activeMetrics,
      required final Map<String, double> metricGoals}) = _$GoalSettingsImpl;

  factory _GoalSettings.fromJson(Map<String, dynamic> json) =
      _$GoalSettingsImpl.fromJson;

  @override
  Map<String, bool> get activeMetrics;
  @override
  Map<String, double> get metricGoals;

  /// Create a copy of GoalSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GoalSettingsImplCopyWith<_$GoalSettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Settings _$SettingsFromJson(Map<String, dynamic> json) {
  return _Settings.fromJson(json);
}

/// @nodoc
mixin _$Settings {
  String get userId => throw _privateConstructorUsedError;
  TradeDefaults get tradeDefaults => throw _privateConstructorUsedError;
  DisplaySettings get display => throw _privateConstructorUsedError;
  BacktestSettings get backtest => throw _privateConstructorUsedError;
  GoalSettings get goals => throw _privateConstructorUsedError;
  AccountSettings get accounts => throw _privateConstructorUsedError;
  NotificationSettings get notifications => throw _privateConstructorUsedError;
  bool get autoSync => throw _privateConstructorUsedError;
  bool get autoBackup => throw _privateConstructorUsedError;
  int get backupFrequency => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this Settings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Settings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SettingsCopyWith<Settings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SettingsCopyWith<$Res> {
  factory $SettingsCopyWith(Settings value, $Res Function(Settings) then) =
      _$SettingsCopyWithImpl<$Res, Settings>;
  @useResult
  $Res call(
      {String userId,
      TradeDefaults tradeDefaults,
      DisplaySettings display,
      BacktestSettings backtest,
      GoalSettings goals,
      AccountSettings accounts,
      NotificationSettings notifications,
      bool autoSync,
      bool autoBackup,
      int backupFrequency,
      DateTime updatedAt});

  $TradeDefaultsCopyWith<$Res> get tradeDefaults;
  $DisplaySettingsCopyWith<$Res> get display;
  $BacktestSettingsCopyWith<$Res> get backtest;
  $GoalSettingsCopyWith<$Res> get goals;
  $AccountSettingsCopyWith<$Res> get accounts;
  $NotificationSettingsCopyWith<$Res> get notifications;
}

/// @nodoc
class _$SettingsCopyWithImpl<$Res, $Val extends Settings>
    implements $SettingsCopyWith<$Res> {
  _$SettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Settings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? tradeDefaults = null,
    Object? display = null,
    Object? backtest = null,
    Object? goals = null,
    Object? accounts = null,
    Object? notifications = null,
    Object? autoSync = null,
    Object? autoBackup = null,
    Object? backupFrequency = null,
    Object? updatedAt = null,
  }) {
    return _then(_value.copyWith(
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      tradeDefaults: null == tradeDefaults
          ? _value.tradeDefaults
          : tradeDefaults // ignore: cast_nullable_to_non_nullable
              as TradeDefaults,
      display: null == display
          ? _value.display
          : display // ignore: cast_nullable_to_non_nullable
              as DisplaySettings,
      backtest: null == backtest
          ? _value.backtest
          : backtest // ignore: cast_nullable_to_non_nullable
              as BacktestSettings,
      goals: null == goals
          ? _value.goals
          : goals // ignore: cast_nullable_to_non_nullable
              as GoalSettings,
      accounts: null == accounts
          ? _value.accounts
          : accounts // ignore: cast_nullable_to_non_nullable
              as AccountSettings,
      notifications: null == notifications
          ? _value.notifications
          : notifications // ignore: cast_nullable_to_non_nullable
              as NotificationSettings,
      autoSync: null == autoSync
          ? _value.autoSync
          : autoSync // ignore: cast_nullable_to_non_nullable
              as bool,
      autoBackup: null == autoBackup
          ? _value.autoBackup
          : autoBackup // ignore: cast_nullable_to_non_nullable
              as bool,
      backupFrequency: null == backupFrequency
          ? _value.backupFrequency
          : backupFrequency // ignore: cast_nullable_to_non_nullable
              as int,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }

  /// Create a copy of Settings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TradeDefaultsCopyWith<$Res> get tradeDefaults {
    return $TradeDefaultsCopyWith<$Res>(_value.tradeDefaults, (value) {
      return _then(_value.copyWith(tradeDefaults: value) as $Val);
    });
  }

  /// Create a copy of Settings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DisplaySettingsCopyWith<$Res> get display {
    return $DisplaySettingsCopyWith<$Res>(_value.display, (value) {
      return _then(_value.copyWith(display: value) as $Val);
    });
  }

  /// Create a copy of Settings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BacktestSettingsCopyWith<$Res> get backtest {
    return $BacktestSettingsCopyWith<$Res>(_value.backtest, (value) {
      return _then(_value.copyWith(backtest: value) as $Val);
    });
  }

  /// Create a copy of Settings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GoalSettingsCopyWith<$Res> get goals {
    return $GoalSettingsCopyWith<$Res>(_value.goals, (value) {
      return _then(_value.copyWith(goals: value) as $Val);
    });
  }

  /// Create a copy of Settings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AccountSettingsCopyWith<$Res> get accounts {
    return $AccountSettingsCopyWith<$Res>(_value.accounts, (value) {
      return _then(_value.copyWith(accounts: value) as $Val);
    });
  }

  /// Create a copy of Settings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $NotificationSettingsCopyWith<$Res> get notifications {
    return $NotificationSettingsCopyWith<$Res>(_value.notifications, (value) {
      return _then(_value.copyWith(notifications: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SettingsImplCopyWith<$Res>
    implements $SettingsCopyWith<$Res> {
  factory _$$SettingsImplCopyWith(
          _$SettingsImpl value, $Res Function(_$SettingsImpl) then) =
      __$$SettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String userId,
      TradeDefaults tradeDefaults,
      DisplaySettings display,
      BacktestSettings backtest,
      GoalSettings goals,
      AccountSettings accounts,
      NotificationSettings notifications,
      bool autoSync,
      bool autoBackup,
      int backupFrequency,
      DateTime updatedAt});

  @override
  $TradeDefaultsCopyWith<$Res> get tradeDefaults;
  @override
  $DisplaySettingsCopyWith<$Res> get display;
  @override
  $BacktestSettingsCopyWith<$Res> get backtest;
  @override
  $GoalSettingsCopyWith<$Res> get goals;
  @override
  $AccountSettingsCopyWith<$Res> get accounts;
  @override
  $NotificationSettingsCopyWith<$Res> get notifications;
}

/// @nodoc
class __$$SettingsImplCopyWithImpl<$Res>
    extends _$SettingsCopyWithImpl<$Res, _$SettingsImpl>
    implements _$$SettingsImplCopyWith<$Res> {
  __$$SettingsImplCopyWithImpl(
      _$SettingsImpl _value, $Res Function(_$SettingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of Settings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? tradeDefaults = null,
    Object? display = null,
    Object? backtest = null,
    Object? goals = null,
    Object? accounts = null,
    Object? notifications = null,
    Object? autoSync = null,
    Object? autoBackup = null,
    Object? backupFrequency = null,
    Object? updatedAt = null,
  }) {
    return _then(_$SettingsImpl(
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      tradeDefaults: null == tradeDefaults
          ? _value.tradeDefaults
          : tradeDefaults // ignore: cast_nullable_to_non_nullable
              as TradeDefaults,
      display: null == display
          ? _value.display
          : display // ignore: cast_nullable_to_non_nullable
              as DisplaySettings,
      backtest: null == backtest
          ? _value.backtest
          : backtest // ignore: cast_nullable_to_non_nullable
              as BacktestSettings,
      goals: null == goals
          ? _value.goals
          : goals // ignore: cast_nullable_to_non_nullable
              as GoalSettings,
      accounts: null == accounts
          ? _value.accounts
          : accounts // ignore: cast_nullable_to_non_nullable
              as AccountSettings,
      notifications: null == notifications
          ? _value.notifications
          : notifications // ignore: cast_nullable_to_non_nullable
              as NotificationSettings,
      autoSync: null == autoSync
          ? _value.autoSync
          : autoSync // ignore: cast_nullable_to_non_nullable
              as bool,
      autoBackup: null == autoBackup
          ? _value.autoBackup
          : autoBackup // ignore: cast_nullable_to_non_nullable
              as bool,
      backupFrequency: null == backupFrequency
          ? _value.backupFrequency
          : backupFrequency // ignore: cast_nullable_to_non_nullable
              as int,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SettingsImpl extends _Settings {
  const _$SettingsImpl(
      {required this.userId,
      required this.tradeDefaults,
      required this.display,
      required this.backtest,
      required this.goals,
      required this.accounts,
      required this.notifications,
      required this.autoSync,
      required this.autoBackup,
      required this.backupFrequency,
      required this.updatedAt})
      : super._();

  factory _$SettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$SettingsImplFromJson(json);

  @override
  final String userId;
  @override
  final TradeDefaults tradeDefaults;
  @override
  final DisplaySettings display;
  @override
  final BacktestSettings backtest;
  @override
  final GoalSettings goals;
  @override
  final AccountSettings accounts;
  @override
  final NotificationSettings notifications;
  @override
  final bool autoSync;
  @override
  final bool autoBackup;
  @override
  final int backupFrequency;
  @override
  final DateTime updatedAt;

  @override
  String toString() {
    return 'Settings(userId: $userId, tradeDefaults: $tradeDefaults, display: $display, backtest: $backtest, goals: $goals, accounts: $accounts, notifications: $notifications, autoSync: $autoSync, autoBackup: $autoBackup, backupFrequency: $backupFrequency, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SettingsImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.tradeDefaults, tradeDefaults) ||
                other.tradeDefaults == tradeDefaults) &&
            (identical(other.display, display) || other.display == display) &&
            (identical(other.backtest, backtest) ||
                other.backtest == backtest) &&
            (identical(other.goals, goals) || other.goals == goals) &&
            (identical(other.accounts, accounts) ||
                other.accounts == accounts) &&
            (identical(other.notifications, notifications) ||
                other.notifications == notifications) &&
            (identical(other.autoSync, autoSync) ||
                other.autoSync == autoSync) &&
            (identical(other.autoBackup, autoBackup) ||
                other.autoBackup == autoBackup) &&
            (identical(other.backupFrequency, backupFrequency) ||
                other.backupFrequency == backupFrequency) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      userId,
      tradeDefaults,
      display,
      backtest,
      goals,
      accounts,
      notifications,
      autoSync,
      autoBackup,
      backupFrequency,
      updatedAt);

  /// Create a copy of Settings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SettingsImplCopyWith<_$SettingsImpl> get copyWith =>
      __$$SettingsImplCopyWithImpl<_$SettingsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SettingsImplToJson(
      this,
    );
  }
}

abstract class _Settings extends Settings {
  const factory _Settings(
      {required final String userId,
      required final TradeDefaults tradeDefaults,
      required final DisplaySettings display,
      required final BacktestSettings backtest,
      required final GoalSettings goals,
      required final AccountSettings accounts,
      required final NotificationSettings notifications,
      required final bool autoSync,
      required final bool autoBackup,
      required final int backupFrequency,
      required final DateTime updatedAt}) = _$SettingsImpl;
  const _Settings._() : super._();

  factory _Settings.fromJson(Map<String, dynamic> json) =
      _$SettingsImpl.fromJson;

  @override
  String get userId;
  @override
  TradeDefaults get tradeDefaults;
  @override
  DisplaySettings get display;
  @override
  BacktestSettings get backtest;
  @override
  GoalSettings get goals;
  @override
  AccountSettings get accounts;
  @override
  NotificationSettings get notifications;
  @override
  bool get autoSync;
  @override
  bool get autoBackup;
  @override
  int get backupFrequency;
  @override
  DateTime get updatedAt;

  /// Create a copy of Settings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SettingsImplCopyWith<_$SettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
