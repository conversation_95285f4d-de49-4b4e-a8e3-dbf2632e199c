import 'package:freezed_annotation/freezed_annotation.dart';
import 'account_settings.dart';
import 'notification_settings.dart';

part 'settings.freezed.dart';
part 'settings.g.dart';

enum ChartTimeframe {
  @JsonValue('M1')
  m1,
  @JsonValue('M5')
  m5,
  @JsonValue('M15')
  m15,
  @JsonValue('M30')
  m30,
  @JsonValue('H1')
  h1,
  @JsonValue('H4')
  h4,
  @JsonValue('D1')
  d1,
  @JsonValue('W1')
  w1,
  @JsonValue('MN')
  mn,
}

enum RiskCalculationType {
  @JsonValue('FIXED')
  fixed,
  @JsonValue('PERCENTAGE')
  percentage,
  @JsonValue('R_MULTIPLE')
  rMultiple,
}

enum PnLDisplayMode {
  @JsonValue('CURRENCY')
  currency,
  @JsonValue('PERCENTAGE')
  percentage,
  @JsonValue('R_MULTIPLE')
  rMultiple,
}

@freezed
class TradeDefaults with _$TradeDefaults {
  const factory TradeDefaults({
    required double defaultRisk,
    required double defaultPosition,
    required ChartTimeframe defaultTimeframe,
    required RiskCalculationType riskCalculationType,
    required double defaultCommission,
    required double defaultFees,
  }) = _TradeDefaults;

  factory TradeDefaults.fromJson(Map<String, dynamic> json) =>
      _$TradeDefaultsFromJson(json);
}

@freezed
class DisplaySettings with _$DisplaySettings {
  const factory DisplaySettings({
    required String theme,
    required PnLDisplayMode pnlDisplayMode,
    required bool showRunningPnL,
    required bool showEquityCurve,
    required bool showTradeStatistics,
    required bool compactMode,
    required String dateFormat,
    required String timeFormat,
    required String timezone,
    @Default(true) bool goalNotificationsEnabled,
    @Default(true) bool weeklyReportsEnabled,
    @Default(true) bool monthlyReportsEnabled,
    @Default(true) bool tradeAlertsEnabled,
    @Default(true) bool marketNewsEnabled,
    @Default(true) bool priceAlertsEnabled,
    @Default(true) bool journalRemindersEnabled,
    @Default(true) bool emailNotificationsEnabled,
    @Default(true) bool pushNotificationsEnabled,
    @Default(false) bool smsNotificationsEnabled,
    @Default('all') String defaultAccountView,
    @Default('month') String defaultTimePeriod,
  }) = _DisplaySettings;

  factory DisplaySettings.fromJson(Map<String, dynamic> json) =>
      _$DisplaySettingsFromJson(json);
}

@freezed
class BacktestSettings with _$BacktestSettings {
  const factory BacktestSettings({
    required double initialCapital,
    required bool useFixedPosition,
    required double maxRiskPerTrade,
    required int maxOpenTrades,
    required bool includeFees,
    required bool includeSlippage,
    required double slippageAmount,
  }) = _BacktestSettings;

  factory BacktestSettings.fromJson(Map<String, dynamic> json) =>
      _$BacktestSettingsFromJson(json);
}

@freezed
class GoalSettings with _$GoalSettings {
  const factory GoalSettings({
    required Map<String, bool> activeMetrics,
    required Map<String, double> metricGoals,
  }) = _GoalSettings;

  factory GoalSettings.fromJson(Map<String, dynamic> json) =>
      _$GoalSettingsFromJson(json);
}

@freezed
class Settings with _$Settings {
  const Settings._();

  const factory Settings({
    required String userId,
    required TradeDefaults tradeDefaults,
    required DisplaySettings display,
    required BacktestSettings backtest,
    required GoalSettings goals,
    required AccountSettings accounts,
    required NotificationSettings notifications,
    required bool autoSync,
    required bool autoBackup,
    required int backupFrequency,
    required DateTime updatedAt,
  }) = _Settings;

  factory Settings.fromJson(Map<String, dynamic> json) =>
      _$SettingsFromJson(json);

  Settings updateTradeDefaults(TradeDefaults newDefaults) {
    return copyWith(
      tradeDefaults: newDefaults,
      updatedAt: DateTime.now(),
    );
  }

  Settings updateDisplaySettings(DisplaySettings newDisplay) {
    return copyWith(
      display: newDisplay,
      updatedAt: DateTime.now(),
    );
  }

  Settings updateBacktestSettings(BacktestSettings newBacktest) {
    return copyWith(
      backtest: newBacktest,
      updatedAt: DateTime.now(),
    );
  }

  Settings updateAccountSettings(AccountSettings newAccounts) {
    return copyWith(
      accounts: newAccounts,
      updatedAt: DateTime.now(),
    );
  }

  Settings updateNotificationSettings(NotificationSettings newNotifications) {
    return copyWith(
      notifications: newNotifications,
      updatedAt: DateTime.now(),
    );
  }
}
