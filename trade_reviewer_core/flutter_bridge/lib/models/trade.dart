import 'package:freezed_annotation/freezed_annotation.dart';

enum TradeType {
  @JsonValue('LONG')
  long,
  @JsonValue('SHORT')
  short,
}

enum TradeStatus {
  @JsonValue('OPEN')
  open,
  @JsonValue('CLOSED')
  closed,
}

class TradeFilter {
  final String? symbol;
  final TradeType? type;
  final TradeStatus? status;
  final DateTime? startDate;
  final DateTime? endDate;
  final List<String>? tags;
  final double? minPnL;
  final double? maxPnL;
  final String? accountId;

  const TradeFilter({
    this.symbol,
    this.type,
    this.status,
    this.startDate,
    this.endDate,
    this.tags,
    this.minPnL,
    this.maxPnL,
    this.accountId,
  });
}

class Trade {
  final String id;
  final String userId;
  final String accountId;
  final String symbol;
  final TradeType type;
  final TradeStatus status;
  final double entryPrice;
  final double? exitPrice;
  final double quantity;
  final DateTime entryDate;
  final DateTime? exitDate;
  final String? notes;
  final List<String> tags;
  final double commission;
  final double fees;
  final double? stopLoss;
  final double? takeProfit;
  final double? riskRewardRatio;
  final double? predefinedPnL;
  final DateTime updatedAt;

  const Trade({
    required this.id,
    required this.userId,
    required this.accountId,
    required this.symbol,
    required this.type,
    required this.status,
    required this.entryPrice,
    this.exitPrice,
    required this.quantity,
    required this.entryDate,
    this.exitDate,
    this.notes,
    this.tags = const [],
    this.commission = 0.0,
    this.fees = 0.0,
    this.stopLoss,
    this.takeProfit,
    this.riskRewardRatio,
    this.predefinedPnL,
    required this.updatedAt,
  });

  factory Trade.fromJson(Map<String, dynamic> json) {
    return Trade(
      id: json['id'] as String,
      userId: json['userId'] as String,
      accountId: json['accountId'] as String,
      symbol: json['symbol'] as String,
      type: $enumDecode(_$TradeTypeEnumMap, json['type']),
      status: $enumDecode(_$TradeStatusEnumMap, json['status']),
      entryPrice: (json['entryPrice'] as num).toDouble(),
      exitPrice: (json['exitPrice'] as num?)?.toDouble(),
      quantity: (json['quantity'] as num).toDouble(),
      entryDate: DateTime.parse(json['entryDate'] as String),
      exitDate: json['exitDate'] == null
          ? null
          : DateTime.parse(json['exitDate'] as String),
      notes: json['notes'] as String?,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      commission: (json['commission'] as num?)?.toDouble() ?? 0.0,
      fees: (json['fees'] as num?)?.toDouble() ?? 0.0,
      stopLoss: (json['stopLoss'] as num?)?.toDouble(),
      takeProfit: (json['takeProfit'] as num?)?.toDouble(),
      riskRewardRatio: (json['riskRewardRatio'] as num?)?.toDouble(),
      predefinedPnL: (json['predefinedPnL'] as num?)?.toDouble(),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'accountId': accountId,
      'symbol': symbol,
      'type': _$TradeTypeEnumMap[type]!,
      'status': _$TradeStatusEnumMap[status]!,
      'entryPrice': entryPrice,
      'exitPrice': exitPrice,
      'quantity': quantity,
      'entryDate': entryDate.toIso8601String(),
      'exitDate': exitDate?.toIso8601String(),
      'notes': notes,
      'tags': tags,
      'commission': commission,
      'fees': fees,
      'stopLoss': stopLoss,
      'takeProfit': takeProfit,
      'riskRewardRatio': riskRewardRatio,
      'predefinedPnL': predefinedPnL,
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  double calculatePnL() {
    if (predefinedPnL != null) return predefinedPnL!;
    if (exitPrice == null) return 0;
    final grossPnL = (exitPrice! - entryPrice) * quantity;
    final totalCosts = commission + fees;
    return type == TradeType.long
        ? grossPnL - totalCosts
        : -grossPnL - totalCosts;
  }
}

const _$TradeTypeEnumMap = {
  TradeType.long: 'LONG',
  TradeType.short: 'SHORT',
};

const _$TradeStatusEnumMap = {
  TradeStatus.open: 'OPEN',
  TradeStatus.closed: 'CLOSED',
};
