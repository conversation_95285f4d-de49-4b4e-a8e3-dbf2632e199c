// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_settings.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

TradingAccount _$TradingAccountFromJson(Map<String, dynamic> json) {
  return _TradingAccount.fromJson(json);
}

/// @nodoc
mixin _$TradingAccount {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get provider => throw _privateConstructorUsedError;
  AccountStatus get status => throw _privateConstructorUsedError;
  DateTime get lastSynced => throw _privateConstructorUsedError;
  Map<String, String> get credentials => throw _privateConstructorUsedError;
  double get initialCapital => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;

  /// Serializes this TradingAccount to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TradingAccount
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TradingAccountCopyWith<TradingAccount> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TradingAccountCopyWith<$Res> {
  factory $TradingAccountCopyWith(
          TradingAccount value, $Res Function(TradingAccount) then) =
      _$TradingAccountCopyWithImpl<$Res, TradingAccount>;
  @useResult
  $Res call(
      {String id,
      String name,
      String provider,
      AccountStatus status,
      DateTime lastSynced,
      Map<String, String> credentials,
      double initialCapital,
      bool isActive});
}

/// @nodoc
class _$TradingAccountCopyWithImpl<$Res, $Val extends TradingAccount>
    implements $TradingAccountCopyWith<$Res> {
  _$TradingAccountCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TradingAccount
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? provider = null,
    Object? status = null,
    Object? lastSynced = null,
    Object? credentials = null,
    Object? initialCapital = null,
    Object? isActive = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      provider: null == provider
          ? _value.provider
          : provider // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as AccountStatus,
      lastSynced: null == lastSynced
          ? _value.lastSynced
          : lastSynced // ignore: cast_nullable_to_non_nullable
              as DateTime,
      credentials: null == credentials
          ? _value.credentials
          : credentials // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      initialCapital: null == initialCapital
          ? _value.initialCapital
          : initialCapital // ignore: cast_nullable_to_non_nullable
              as double,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TradingAccountImplCopyWith<$Res>
    implements $TradingAccountCopyWith<$Res> {
  factory _$$TradingAccountImplCopyWith(_$TradingAccountImpl value,
          $Res Function(_$TradingAccountImpl) then) =
      __$$TradingAccountImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String provider,
      AccountStatus status,
      DateTime lastSynced,
      Map<String, String> credentials,
      double initialCapital,
      bool isActive});
}

/// @nodoc
class __$$TradingAccountImplCopyWithImpl<$Res>
    extends _$TradingAccountCopyWithImpl<$Res, _$TradingAccountImpl>
    implements _$$TradingAccountImplCopyWith<$Res> {
  __$$TradingAccountImplCopyWithImpl(
      _$TradingAccountImpl _value, $Res Function(_$TradingAccountImpl) _then)
      : super(_value, _then);

  /// Create a copy of TradingAccount
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? provider = null,
    Object? status = null,
    Object? lastSynced = null,
    Object? credentials = null,
    Object? initialCapital = null,
    Object? isActive = null,
  }) {
    return _then(_$TradingAccountImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      provider: null == provider
          ? _value.provider
          : provider // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as AccountStatus,
      lastSynced: null == lastSynced
          ? _value.lastSynced
          : lastSynced // ignore: cast_nullable_to_non_nullable
              as DateTime,
      credentials: null == credentials
          ? _value._credentials
          : credentials // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      initialCapital: null == initialCapital
          ? _value.initialCapital
          : initialCapital // ignore: cast_nullable_to_non_nullable
              as double,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TradingAccountImpl implements _TradingAccount {
  const _$TradingAccountImpl(
      {required this.id,
      required this.name,
      required this.provider,
      required this.status,
      required this.lastSynced,
      required final Map<String, String> credentials,
      required this.initialCapital,
      this.isActive = true})
      : _credentials = credentials;

  factory _$TradingAccountImpl.fromJson(Map<String, dynamic> json) =>
      _$$TradingAccountImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String provider;
  @override
  final AccountStatus status;
  @override
  final DateTime lastSynced;
  final Map<String, String> _credentials;
  @override
  Map<String, String> get credentials {
    if (_credentials is EqualUnmodifiableMapView) return _credentials;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_credentials);
  }

  @override
  final double initialCapital;
  @override
  @JsonKey()
  final bool isActive;

  @override
  String toString() {
    return 'TradingAccount(id: $id, name: $name, provider: $provider, status: $status, lastSynced: $lastSynced, credentials: $credentials, initialCapital: $initialCapital, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TradingAccountImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.provider, provider) ||
                other.provider == provider) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.lastSynced, lastSynced) ||
                other.lastSynced == lastSynced) &&
            const DeepCollectionEquality()
                .equals(other._credentials, _credentials) &&
            (identical(other.initialCapital, initialCapital) ||
                other.initialCapital == initialCapital) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      provider,
      status,
      lastSynced,
      const DeepCollectionEquality().hash(_credentials),
      initialCapital,
      isActive);

  /// Create a copy of TradingAccount
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TradingAccountImplCopyWith<_$TradingAccountImpl> get copyWith =>
      __$$TradingAccountImplCopyWithImpl<_$TradingAccountImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TradingAccountImplToJson(
      this,
    );
  }
}

abstract class _TradingAccount implements TradingAccount {
  const factory _TradingAccount(
      {required final String id,
      required final String name,
      required final String provider,
      required final AccountStatus status,
      required final DateTime lastSynced,
      required final Map<String, String> credentials,
      required final double initialCapital,
      final bool isActive}) = _$TradingAccountImpl;

  factory _TradingAccount.fromJson(Map<String, dynamic> json) =
      _$TradingAccountImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get provider;
  @override
  AccountStatus get status;
  @override
  DateTime get lastSynced;
  @override
  Map<String, String> get credentials;
  @override
  double get initialCapital;
  @override
  bool get isActive;

  /// Create a copy of TradingAccount
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TradingAccountImplCopyWith<_$TradingAccountImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ApiKey _$ApiKeyFromJson(Map<String, dynamic> json) {
  return _ApiKey.fromJson(json);
}

/// @nodoc
mixin _$ApiKey {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get key => throw _privateConstructorUsedError;
  String? get secret => throw _privateConstructorUsedError;
  ApiKeyStatus get status => throw _privateConstructorUsedError;
  DateTime? get expiryDate => throw _privateConstructorUsedError;

  /// Serializes this ApiKey to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ApiKey
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ApiKeyCopyWith<ApiKey> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ApiKeyCopyWith<$Res> {
  factory $ApiKeyCopyWith(ApiKey value, $Res Function(ApiKey) then) =
      _$ApiKeyCopyWithImpl<$Res, ApiKey>;
  @useResult
  $Res call(
      {String id,
      String name,
      String key,
      String? secret,
      ApiKeyStatus status,
      DateTime? expiryDate});
}

/// @nodoc
class _$ApiKeyCopyWithImpl<$Res, $Val extends ApiKey>
    implements $ApiKeyCopyWith<$Res> {
  _$ApiKeyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ApiKey
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? key = null,
    Object? secret = freezed,
    Object? status = null,
    Object? expiryDate = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      secret: freezed == secret
          ? _value.secret
          : secret // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ApiKeyStatus,
      expiryDate: freezed == expiryDate
          ? _value.expiryDate
          : expiryDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ApiKeyImplCopyWith<$Res> implements $ApiKeyCopyWith<$Res> {
  factory _$$ApiKeyImplCopyWith(
          _$ApiKeyImpl value, $Res Function(_$ApiKeyImpl) then) =
      __$$ApiKeyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String key,
      String? secret,
      ApiKeyStatus status,
      DateTime? expiryDate});
}

/// @nodoc
class __$$ApiKeyImplCopyWithImpl<$Res>
    extends _$ApiKeyCopyWithImpl<$Res, _$ApiKeyImpl>
    implements _$$ApiKeyImplCopyWith<$Res> {
  __$$ApiKeyImplCopyWithImpl(
      _$ApiKeyImpl _value, $Res Function(_$ApiKeyImpl) _then)
      : super(_value, _then);

  /// Create a copy of ApiKey
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? key = null,
    Object? secret = freezed,
    Object? status = null,
    Object? expiryDate = freezed,
  }) {
    return _then(_$ApiKeyImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      secret: freezed == secret
          ? _value.secret
          : secret // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ApiKeyStatus,
      expiryDate: freezed == expiryDate
          ? _value.expiryDate
          : expiryDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ApiKeyImpl implements _ApiKey {
  const _$ApiKeyImpl(
      {required this.id,
      required this.name,
      required this.key,
      this.secret,
      required this.status,
      this.expiryDate});

  factory _$ApiKeyImpl.fromJson(Map<String, dynamic> json) =>
      _$$ApiKeyImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String key;
  @override
  final String? secret;
  @override
  final ApiKeyStatus status;
  @override
  final DateTime? expiryDate;

  @override
  String toString() {
    return 'ApiKey(id: $id, name: $name, key: $key, secret: $secret, status: $status, expiryDate: $expiryDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ApiKeyImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.key, key) || other.key == key) &&
            (identical(other.secret, secret) || other.secret == secret) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.expiryDate, expiryDate) ||
                other.expiryDate == expiryDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, name, key, secret, status, expiryDate);

  /// Create a copy of ApiKey
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ApiKeyImplCopyWith<_$ApiKeyImpl> get copyWith =>
      __$$ApiKeyImplCopyWithImpl<_$ApiKeyImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ApiKeyImplToJson(
      this,
    );
  }
}

abstract class _ApiKey implements ApiKey {
  const factory _ApiKey(
      {required final String id,
      required final String name,
      required final String key,
      final String? secret,
      required final ApiKeyStatus status,
      final DateTime? expiryDate}) = _$ApiKeyImpl;

  factory _ApiKey.fromJson(Map<String, dynamic> json) = _$ApiKeyImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get key;
  @override
  String? get secret;
  @override
  ApiKeyStatus get status;
  @override
  DateTime? get expiryDate;

  /// Create a copy of ApiKey
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ApiKeyImplCopyWith<_$ApiKeyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AccountSettings _$AccountSettingsFromJson(Map<String, dynamic> json) {
  return _AccountSettings.fromJson(json);
}

/// @nodoc
mixin _$AccountSettings {
  List<TradingAccount> get tradingAccounts =>
      throw _privateConstructorUsedError;
  List<ApiKey> get apiKeys => throw _privateConstructorUsedError;
  bool get autoSync => throw _privateConstructorUsedError;
  int get syncFrequency => throw _privateConstructorUsedError;

  /// Serializes this AccountSettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AccountSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AccountSettingsCopyWith<AccountSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountSettingsCopyWith<$Res> {
  factory $AccountSettingsCopyWith(
          AccountSettings value, $Res Function(AccountSettings) then) =
      _$AccountSettingsCopyWithImpl<$Res, AccountSettings>;
  @useResult
  $Res call(
      {List<TradingAccount> tradingAccounts,
      List<ApiKey> apiKeys,
      bool autoSync,
      int syncFrequency});
}

/// @nodoc
class _$AccountSettingsCopyWithImpl<$Res, $Val extends AccountSettings>
    implements $AccountSettingsCopyWith<$Res> {
  _$AccountSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tradingAccounts = null,
    Object? apiKeys = null,
    Object? autoSync = null,
    Object? syncFrequency = null,
  }) {
    return _then(_value.copyWith(
      tradingAccounts: null == tradingAccounts
          ? _value.tradingAccounts
          : tradingAccounts // ignore: cast_nullable_to_non_nullable
              as List<TradingAccount>,
      apiKeys: null == apiKeys
          ? _value.apiKeys
          : apiKeys // ignore: cast_nullable_to_non_nullable
              as List<ApiKey>,
      autoSync: null == autoSync
          ? _value.autoSync
          : autoSync // ignore: cast_nullable_to_non_nullable
              as bool,
      syncFrequency: null == syncFrequency
          ? _value.syncFrequency
          : syncFrequency // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AccountSettingsImplCopyWith<$Res>
    implements $AccountSettingsCopyWith<$Res> {
  factory _$$AccountSettingsImplCopyWith(_$AccountSettingsImpl value,
          $Res Function(_$AccountSettingsImpl) then) =
      __$$AccountSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<TradingAccount> tradingAccounts,
      List<ApiKey> apiKeys,
      bool autoSync,
      int syncFrequency});
}

/// @nodoc
class __$$AccountSettingsImplCopyWithImpl<$Res>
    extends _$AccountSettingsCopyWithImpl<$Res, _$AccountSettingsImpl>
    implements _$$AccountSettingsImplCopyWith<$Res> {
  __$$AccountSettingsImplCopyWithImpl(
      _$AccountSettingsImpl _value, $Res Function(_$AccountSettingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tradingAccounts = null,
    Object? apiKeys = null,
    Object? autoSync = null,
    Object? syncFrequency = null,
  }) {
    return _then(_$AccountSettingsImpl(
      tradingAccounts: null == tradingAccounts
          ? _value._tradingAccounts
          : tradingAccounts // ignore: cast_nullable_to_non_nullable
              as List<TradingAccount>,
      apiKeys: null == apiKeys
          ? _value._apiKeys
          : apiKeys // ignore: cast_nullable_to_non_nullable
              as List<ApiKey>,
      autoSync: null == autoSync
          ? _value.autoSync
          : autoSync // ignore: cast_nullable_to_non_nullable
              as bool,
      syncFrequency: null == syncFrequency
          ? _value.syncFrequency
          : syncFrequency // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AccountSettingsImpl implements _AccountSettings {
  const _$AccountSettingsImpl(
      {required final List<TradingAccount> tradingAccounts,
      required final List<ApiKey> apiKeys,
      required this.autoSync,
      required this.syncFrequency})
      : _tradingAccounts = tradingAccounts,
        _apiKeys = apiKeys;

  factory _$AccountSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$AccountSettingsImplFromJson(json);

  final List<TradingAccount> _tradingAccounts;
  @override
  List<TradingAccount> get tradingAccounts {
    if (_tradingAccounts is EqualUnmodifiableListView) return _tradingAccounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tradingAccounts);
  }

  final List<ApiKey> _apiKeys;
  @override
  List<ApiKey> get apiKeys {
    if (_apiKeys is EqualUnmodifiableListView) return _apiKeys;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_apiKeys);
  }

  @override
  final bool autoSync;
  @override
  final int syncFrequency;

  @override
  String toString() {
    return 'AccountSettings(tradingAccounts: $tradingAccounts, apiKeys: $apiKeys, autoSync: $autoSync, syncFrequency: $syncFrequency)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountSettingsImpl &&
            const DeepCollectionEquality()
                .equals(other._tradingAccounts, _tradingAccounts) &&
            const DeepCollectionEquality().equals(other._apiKeys, _apiKeys) &&
            (identical(other.autoSync, autoSync) ||
                other.autoSync == autoSync) &&
            (identical(other.syncFrequency, syncFrequency) ||
                other.syncFrequency == syncFrequency));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_tradingAccounts),
      const DeepCollectionEquality().hash(_apiKeys),
      autoSync,
      syncFrequency);

  /// Create a copy of AccountSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountSettingsImplCopyWith<_$AccountSettingsImpl> get copyWith =>
      __$$AccountSettingsImplCopyWithImpl<_$AccountSettingsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AccountSettingsImplToJson(
      this,
    );
  }
}

abstract class _AccountSettings implements AccountSettings {
  const factory _AccountSettings(
      {required final List<TradingAccount> tradingAccounts,
      required final List<ApiKey> apiKeys,
      required final bool autoSync,
      required final int syncFrequency}) = _$AccountSettingsImpl;

  factory _AccountSettings.fromJson(Map<String, dynamic> json) =
      _$AccountSettingsImpl.fromJson;

  @override
  List<TradingAccount> get tradingAccounts;
  @override
  List<ApiKey> get apiKeys;
  @override
  bool get autoSync;
  @override
  int get syncFrequency;

  /// Create a copy of AccountSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountSettingsImplCopyWith<_$AccountSettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
