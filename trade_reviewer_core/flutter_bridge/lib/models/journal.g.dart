// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'journal.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TradePlanImpl _$$TradePlanImplFromJson(Map<String, dynamic> json) =>
    _$TradePlanImpl(
      setup: json['setup'] as String,
      entry: json['entry'] as String,
      stopLoss: json['stopLoss'] as String,
      targets:
          (json['targets'] as List<dynamic>).map((e) => e as String).toList(),
      timeFrame: json['timeFrame'] as String,
      risk: (json['risk'] as num).toDouble(),
      reward: (json['reward'] as num).toDouble(),
    );

Map<String, dynamic> _$$TradePlanImplToJson(_$TradePlanImpl instance) =>
    <String, dynamic>{
      'setup': instance.setup,
      'entry': instance.entry,
      'stopLoss': instance.stopLoss,
      'targets': instance.targets,
      'timeFrame': instance.timeFrame,
      'risk': instance.risk,
      'reward': instance.reward,
    };

_$JournalEntryImpl _$$JournalEntryImplFromJson(Map<String, dynamic> json) =>
    _$JournalEntryImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      tradeId: json['tradeId'] as String,
      date: DateTime.parse(json['date'] as String),
      marketCondition:
          $enumDecode(_$MarketConditionEnumMap, json['marketCondition']),
      emotionalState:
          $enumDecode(_$EmotionalStateEnumMap, json['emotionalState']),
      tradePlan: TradePlan.fromJson(json['tradePlan'] as Map<String, dynamic>),
      mistakes: (json['mistakes'] as List<dynamic>?)
              ?.map((e) => $enumDecode(_$TradingMistakeEnumMap, e))
              .toList() ??
          const [],
      analysis: json['analysis'] as String,
      lessons:
          (json['lessons'] as List<dynamic>).map((e) => e as String).toList(),
      screenshots: (json['screenshots'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      rating: (json['rating'] as num).toInt(),
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$JournalEntryImplToJson(_$JournalEntryImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'tradeId': instance.tradeId,
      'date': instance.date.toIso8601String(),
      'marketCondition': _$MarketConditionEnumMap[instance.marketCondition]!,
      'emotionalState': _$EmotionalStateEnumMap[instance.emotionalState]!,
      'tradePlan': instance.tradePlan,
      'mistakes':
          instance.mistakes.map((e) => _$TradingMistakeEnumMap[e]!).toList(),
      'analysis': instance.analysis,
      'lessons': instance.lessons,
      'screenshots': instance.screenshots,
      'rating': instance.rating,
      'tags': instance.tags,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$MarketConditionEnumMap = {
  MarketCondition.bullish: 'BULLISH',
  MarketCondition.bearish: 'BEARISH',
  MarketCondition.sideways: 'SIDEWAYS',
  MarketCondition.volatile: 'VOLATILE',
  MarketCondition.trending: 'TRENDING',
  MarketCondition.ranging: 'RANGING',
};

const _$EmotionalStateEnumMap = {
  EmotionalState.calm: 'CALM',
  EmotionalState.confident: 'CONFIDENT',
  EmotionalState.anxious: 'ANXIOUS',
  EmotionalState.fearful: 'FEARFUL',
  EmotionalState.greedy: 'GREEDY',
  EmotionalState.frustrated: 'FRUSTRATED',
  EmotionalState.tilted: 'TILTED',
};

const _$TradingMistakeEnumMap = {
  TradingMistake.fomo: 'FOMO',
  TradingMistake.earlyEntry: 'EARLY_ENTRY',
  TradingMistake.lateEntry: 'LATE_ENTRY',
  TradingMistake.movedStopLoss: 'MOVED_STOP_LOSS',
  TradingMistake.earlyExit: 'EARLY_EXIT',
  TradingMistake.lateExit: 'LATE_EXIT',
  TradingMistake.positionSizing: 'POSITION_SIZING',
  TradingMistake.ignoredPlan: 'IGNORED_PLAN',
  TradingMistake.emotionalTrading: 'EMOTIONAL_TRADING',
  TradingMistake.revengeTrading: 'REVENGE_TRADING',
};
