import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:trade_reviewer_core/services/settings_service.dart';
import 'package:trade_reviewer_core/models/settings.dart';

void main() {
  late SettingsService settingsService;
  const testUserId = 'test_user';

  setUp(() async {
    SharedPreferences.setMockInitialValues({});
    settingsService = SettingsService();
  });

  group('General Settings Tests', () {
    test('initialize should create default settings for new user', () async {
      final settings = await settingsService.initialize(testUserId);

      expect(settings.userId, equals(testUserId));
      expect(settings.autoSync, isTrue);
      expect(settings.autoBackup, isTrue);
      expect(settings.backupFrequency, equals(7));
    });

    test('settings should persist after initialization', () async {
      // Initialize settings
      await settingsService.initialize(testUserId);

      // Get a new instance of SettingsService
      final newSettingsService = SettingsService();
      final loadedSettings = await newSettingsService.initialize(testUserId);

      expect(loadedSettings.userId, equals(testUserId));
      expect(loadedSettings.autoSync, isTrue);
      expect(loadedSettings.autoBackup, isTrue);
      expect(loadedSettings.backupFrequency, equals(7));
    });

    test('updateAutoSync should update and persist the setting', () async {
      // Initialize settings
      await settingsService.initialize(testUserId);

      // Update autoSync
      final updatedSettings = await settingsService.updateAutoSync(false);
      expect(updatedSettings.autoSync, isFalse);

      // Verify persistence
      final newSettingsService = SettingsService();
      final loadedSettings = await newSettingsService.initialize(testUserId);
      expect(loadedSettings.autoSync, isFalse);
    });

    test(
        'updateAutoBackup should update and persist both enabled state and frequency',
        () async {
      // Initialize settings
      await settingsService.initialize(testUserId);

      // Update autoBackup
      final updatedSettings =
          await settingsService.updateAutoBackup(false, frequency: 14);
      expect(updatedSettings.autoBackup, isFalse);
      expect(updatedSettings.backupFrequency, equals(14));

      // Verify persistence
      final newSettingsService = SettingsService();
      final loadedSettings = await newSettingsService.initialize(testUserId);
      expect(loadedSettings.autoBackup, isFalse);
      expect(loadedSettings.backupFrequency, equals(14));
    });

    test('reset should restore default settings', () async {
      // Initialize and modify settings
      await settingsService.initialize(testUserId);
      await settingsService.updateAutoSync(false);
      await settingsService.updateAutoBackup(false, frequency: 14);

      // Reset settings
      final resetSettings = await settingsService.reset();

      // Verify defaults are restored
      expect(resetSettings.userId, equals(testUserId));
      expect(resetSettings.autoSync, isTrue);
      expect(resetSettings.autoBackup, isTrue);
      expect(resetSettings.backupFrequency, equals(7));

      // Verify persistence of reset
      final newSettingsService = SettingsService();
      final loadedSettings = await newSettingsService.initialize(testUserId);
      expect(loadedSettings.autoSync, isTrue);
      expect(loadedSettings.autoBackup, isTrue);
      expect(loadedSettings.backupFrequency, equals(7));
    });
  });
}
