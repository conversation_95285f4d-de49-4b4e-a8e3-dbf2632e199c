const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

function generateDart() {
  console.log('Generating Dart code...');
  
  // Ensure we're in the flutter_bridge directory
  const flutterBridgePath = path.join(__dirname, 'flutter_bridge');
  process.chdir(flutterBridgePath);
  
  // Run flutter pub get to ensure dependencies
  console.log('Installing Flutter dependencies...');
  execSync('flutter pub get', { stdio: 'inherit' });
  
  // Run build_runner to generate code
  console.log('Running build_runner...');
  execSync('flutter pub run build_runner build --delete-conflicting-outputs', { stdio: 'inherit' });
}

function buildTypeScript() {
  console.log('Building TypeScript...');
  
  // Ensure we're in the root directory
  process.chdir(__dirname);
  
  // Install dependencies if needed
  if (!fs.existsSync('node_modules')) {
    console.log('Installing npm dependencies...');
    execSync('npm install', { stdio: 'inherit' });
  }
  
  // Build TypeScript
  console.log('Running TypeScript build...');
  execSync('npm run build', { stdio: 'inherit' });
}

function main() {
  const command = process.argv[2];
  
  switch (command) {
    case 'generateDart':
      generateDart();
      break;
    case 'buildTs':
      buildTypeScript();
      break;
    case 'all':
      buildTypeScript();
      generateDart();
      break;
    default:
      console.log('Please specify a command: generateDart, buildTs, or all');
      process.exit(1);
  }
}

main(); 