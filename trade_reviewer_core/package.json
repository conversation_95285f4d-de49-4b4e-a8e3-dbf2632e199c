{"name": "trade_reviewer_core", "version": "1.0.0", "description": "Core business logic for TradeREVIEWER application", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "build:flutter": "node build.js generateDart", "test": "jest", "lint": "eslint src/**/*.ts", "prepare": "npm run build"}, "dependencies": {"date-fns": "^2.30.0", "decimal.js": "^10.4.3", "typescript": "^5.0.0", "zod": "^3.22.4"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "ts-jest": "^29.0.0"}}