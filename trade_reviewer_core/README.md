# TradeREVIEWER Core Package

This package contains the shared business logic and interfaces for the TradeREVIEWER application, supporting both React (Web) and Flutter (Mobile) implementations.

## Project Structure
```
trade_reviewer_core/
├── src/                           # TypeScript source
│   ├── models/                    # Shared data models
│   │   ├── trade.ts
│   │   ├── user.ts
│   │   ├── goal.ts
│   │   └── index.ts
│   ├── services/                  # Business logic
│   │   ├── trade/
│   │   │   ├── trade.service.ts
│   │   │   └── trade.validator.ts
│   │   ├── auth/
│   │   │   ├── auth.service.ts
│   │   │   └── auth.validator.ts
│   │   └── goals/
│   │       ├── goal.service.ts
│   │       └── metrics.calculator.ts
│   ├── utils/                     # Shared utilities
│   │   ├── date.utils.ts
│   │   ├── number.utils.ts
│   │   └── validation.utils.ts
│   ├── interfaces/                # Core interfaces
│   │   ├── repository.interface.ts
│   │   └── service.interface.ts
│   └── constants/                 # Shared constants
│       ├── trade.constants.ts
│       └── validation.constants.ts
├── flutter_bridge/               # Flutter compatibility layer
│   ├── lib/
│   │   ├── models/              # Generated Dart models
│   │   ├── services/           # Dart service implementations
│   │   └── utils/             # Dart utility implementations
│   └── pubspec.yaml
├── package.json                 # NPM package configuration
├── tsconfig.json               # TypeScript configuration
└── build.js                   # Build script for both platforms
```

## Implementation Steps

### 1. TypeScript Core Implementation

#### 1.1 Data Models
```typescript
// src/models/trade.ts
export interface Trade {
  id: string;
  symbol: string;
  entryPrice: number;
  exitPrice: number;
  quantity: number;
  entryDate: Date;
  exitDate?: Date;
  status: TradeStatus;
  notes?: string;
}

// src/models/user.ts
export interface User {
  id: string;
  email: string;
  name: string;
  preferences: UserPreferences;
}
```

#### 1.2 Service Interfaces
```typescript
// src/interfaces/service.interface.ts
export interface ITradeService {
  calculateProfitLoss(trade: Trade): number;
  validateTrade(trade: Trade): ValidationResult;
  aggregateMetrics(trades: Trade[]): TradeMetrics;
}
```

#### 1.3 Business Logic Implementation
```typescript
// src/services/trade/trade.service.ts
export class TradeService implements ITradeService {
  calculateProfitLoss(trade: Trade): number {
    return (trade.exitPrice - trade.entryPrice) * trade.quantity;
  }
  // ... other implementations
}
```

### 2. Flutter Bridge Generation

#### 2.1 Model Generation
```dart
// flutter_bridge/lib/models/trade.dart
class Trade {
  final String id;
  final String symbol;
  final double entryPrice;
  final double exitPrice;
  final double quantity;
  final DateTime entryDate;
  final DateTime? exitDate;
  final TradeStatus status;
  final String? notes;

  Trade({
    required this.id,
    required this.symbol,
    // ... other required fields
  });

  factory Trade.fromJson(Map<String, dynamic> json) => _$TradeFromJson(json);
  Map<String, dynamic> toJson() => _$TradeToJson(this);
}
```

### 3. Build Pipeline Setup

#### 3.1 TypeScript Build
```json
// package.json
{
  "scripts": {
    "build": "tsc",
    "build:flutter": "node build.js generateDart",
    "test": "jest",
    "lint": "eslint src/**/*.ts"
  }
}
```

#### 3.2 Flutter Bridge Build
```yaml
# flutter_bridge/pubspec.yaml
name: trade_reviewer_core
description: Core package for TradeREVIEWER
version: 1.0.0

dependencies:
  json_annotation: ^4.8.1

dev_dependencies:
  build_runner: ^2.4.6
  json_serializable: ^6.7.1
```

## Usage Examples

### In React Application
```typescript
import { Trade, TradeService } from 'trade_reviewer_core';

const tradeService = new TradeService();
const trade: Trade = {
  id: '1',
  symbol: 'AAPL',
  // ... other properties
};

const pnl = tradeService.calculateProfitLoss(trade);
```

### In Flutter Application
```dart
import 'package:trade_reviewer_core/flutter_bridge.dart';

final trade = Trade(
  id: '1',
  symbol: 'AAPL',
  // ... other properties
);

final tradeService = TradeService();
final pnl = tradeService.calculateProfitLoss(trade);
```

## Development Workflow

1. Implement features in TypeScript first
2. Run tests on TypeScript implementation
3. Generate Flutter bridge code
4. Test Flutter bridge implementation
5. Publish updates to both platforms

## Testing

```bash
# Run TypeScript tests
npm test

# Run Flutter tests
cd flutter_bridge && flutter test
```

## Contributing

1. Create feature branch
2. Implement in TypeScript
3. Generate Flutter code
4. Test both implementations
5. Create pull request

_Note: Keep the implementations as platform-agnostic as possible. Platform-specific code should be handled in the respective platform projects._ 