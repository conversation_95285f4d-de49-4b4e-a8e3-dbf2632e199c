# TradeREVIEWER

A comprehensive trading journal and analysis platform for tracking and improving your trading performance.

## Project Structure

```
tradedge/
├── client/               # Frontend React application
│   ├── src/
│   │   ├── components/  # React components
│   │   ├── contexts/    # React contexts
│   │   ├── services/    # API services
│   │   └── models/      # Data models
│   └── package.json
│
└── server/              # Backend Express application
    ├── src/
    │   ├── controllers/ # Route controllers
    │   ├── routes/      # API routes
    │   ├── models/      # Database models
    │   ├── middleware/  # Custom middleware
    │   └── config/      # Configuration files
    └── package.json
```

## Setup Instructions

### Prerequisites
- Node.js (v14 or higher)
- PostgreSQL (v12 or higher)
- npm or yarn

### Database Setup
1. Create PostgreSQL database:
```sql
CREATE DATABASE tradedge;
```

2. Configure environment variables:
- Copy `.env.example` to `.env` in both client and server directories
- Update the values according to your environment

### Server Setup
```bash
cd server
npm install
npm run dev
```

### Client Setup
```bash
cd client
npm install
npm start
```

## API Endpoints

### Authentication
- POST `/api/auth/register` - Register new user
- POST `/api/auth/login` - User login

### User Management (Protected)
- GET `/api/users/profile` - Get user profile
- PUT `/api/users/profile` - Update user profile
- GET `/api/users/settings` - Get user settings
- PUT `/api/users/settings` - Update user settings

## Recent Updates

### Backend Enhancements
- Implemented PostgreSQL database integration
- Added JWT-based authentication
- Created protected API routes
- Added user profile and settings management

### Frontend Improvements
- Added distinct empty states for different sections
- Enhanced profile and settings UI
- Implemented account dropdown menu
- Added database persistence for user data

## Roadmap

### Phase 1: Core Features ✅
- User authentication
- Basic profile management
- Settings configuration
- Empty state handling

### Phase 2: Trading Features 🚧
- Trade entry form
- Trade journal
- Basic analytics
- Performance metrics

### Phase 3: Advanced Analytics 📊
- Advanced performance metrics
- Pattern recognition
- Risk analysis
- Trading statistics

### Phase 4: Social Features 🤝
- User profiles
- Trade sharing
- Community insights
- Mentorship system

### Phase 5: Premium Features 💎
- Advanced reporting
- AI-powered insights
- Custom indicators
- Strategy backtesting

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details. 